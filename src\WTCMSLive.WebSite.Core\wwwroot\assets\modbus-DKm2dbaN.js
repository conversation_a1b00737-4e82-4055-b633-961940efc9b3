import{u as oe,W as E}from"./table-RP3jLHlo.js";import{O as le}from"./index-CzSbT6op.js";import{W as ie}from"./index-QXLii0rw.js";import{S as ne,d as A,f as ue}from"./tools-zTE6InS0.js";import{r,j as de,u as ce,y as re,w as be,f as C,d as G,o as T,c as U,b as F,m as u,aR as $}from"./index-BjOW8S1L.js";import{u as pe}from"./configModbus-CP03_5wA.js";import{u as me}from"./collectionUnitConfig-BbRKo_Zp.js";import{u as he}from"./devTree-Dwa9wLl9.js";import{M as De}from"./index-BnSFuLp6.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./ActionButton-C_grUmdF.js";import"./styleChecker-CFtINSLw.js";import"./index-7iPMz_Qy.js";import"./initDefaultProps-P4j1rGDC.js";import"./shallowequal-gCpTBdTi.js";import"./index-DTxROkTj.js";import"./index-kP-mINcM.js";import"./index-BJEkaghg.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";const ve={key:2},Ne={__name:"modbus",setup(ye){const l=pe(),x=me(),j=he(),z=oe();let L=[{label:"串口服务器",value:"0",text:"串口服务器"},{label:"串口直连",value:"1",text:"串口直连"}];const O=(e={isForm:!1})=>[{title:"ModbusID",dataIndex:"modbusID",columnWidth:100,validateRules:A({type:"number",title:"设备ID",required:!0})},{title:"Modbus名称",dataIndex:"modbusDeviceName",isrequired:!0},{title:"Modbus类型",dataIndex:"modbusType",columnWidth:150,isrequired:!0,inputType:"select",selectOptions:[],...e&&e.isForm?{}:{customRender:({text:a,record:t})=>{const s=l.modbusDevTypes.find(i=>i.value==t.modbusType);return s?s.label:a}}},{title:"采集单元",dataIndex:"dauID",isrequired:!0,inputType:"select",selectOptions:[],...e&&e.isForm?{}:{customRender:({text:a,record:t})=>{const s=x.dAUOptionList.find(i=>i.value==t.dauID);return s?s.label:a}}},{title:"连接方式",dataIndex:"comType",isrequired:!0,inputType:"radio",hasChangeEvent:!0,selectOptions:L,headerOperations:{filters:L},...e&&e.isForm?{}:{customRender:({text:a,record:t})=>{const s=L.find(i=>i.value==t.comType);return s?s.label:a}}}],_=(e={isForm:!1})=>[{title:"设备名称",dataIndex:"modbusDeviceID",columnWidth:400,isrequired:!0,inputType:"select",selectOptions:[],hasChangeEvent:!0,...e&&e.isForm?{}:{customRender:({text:a,record:t})=>t.modbusDeviceName||""}},{title:"通道编号",dataIndex:"channelNumber",validateRules:A({type:"number",title:"通道编号",required:!0})},{title:"测量位置",dataIndex:"measLocationID",columnWidth:400,isrequired:!0,inputType:"select",selectOptions:[],tableList:[],...e&&e.isForm?{}:{customRender:({text:a,record:t})=>t.measLocationName}}],b=()=>[{title:"串口服务器IP",dataIndex:"comIP",validateRules:A({type:"ip",title:"串口服务器IP",required:!0})},{title:"串口服务器端口",dataIndex:"comPort",isrequired:!0,columnWidth:100,validateRules:A({type:"port",title:"串口服务器端口"})},{title:"串口名称",dataIndex:"portName",isrequired:!0},{title:"波特率",dataIndex:"baudRate",columnWidth:100,validateRules:A({type:"number",title:"波特率",required:!0})},{title:"数据位",dataIndex:"dataBit",isrequired:!0,columnWidth:100},{title:"校验位",dataIndex:"parity",isrequired:!0,columnWidth:100,inputType:"radio",selectOptions:[{label:"无",value:0},{label:"奇",value:1},{label:"偶",value:2}]},{title:"停止位",dataIndex:"stopBit",isrequired:!0,columnWidth:100}],S=r({}),f=r(!1),v=r(""),d=r(""),V=de(()=>d.value==="batchAdd"?"1200px":"600px"),m=r(""),M=r(!1),W=ce(),I=r({}),c=r([]),n=r(W.params.id),R=r(),o=re({tableColumns:[...O(),{title:"串口服务器IP",dataIndex:"comIP"},{title:"串口服务器端口",dataIndex:"comPort"},{title:"串口名称",dataIndex:"portName"},{title:"波特率",dataIndex:"baudRate"}],tableColumns2:_(),tableData1:[],tableData2:[],batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{},bathApplyResponse2:{}}),H=async e=>{R.value&&await z.fetchDevTreedDevicelist({windParkID:R.value,useTobath:!0})},J=()=>{let e=j.findAncestorsWithNodes(n.value);e&&e.length&&e.length>1&&(R.value=e[e.length-2].id)},Q=async()=>{await l.fetchGetModbusDevType()},X=async()=>{await x.fetchGetDAUList({WindTurbineID:n.value,WindParkId:R.value})},Y=async e=>{n.value&&await l.fetchGetModbusMeasLocByDeviceID({turbineID:n.value,modbusDeviceID:e})},g=async()=>{f.value=!0;let e=await l.fetchGetModbusDeviceList({turbineID:n.value});e&&(f.value=!1,o.tableData1=e)},w=async()=>{f.value=!0;let e=await l.fetchGetModbusChannelList({turbineID:n.value});e&&(f.value=!1,o.tableData2=e)};be(()=>W.params.id,async e=>{n.value=e,e&&(J(),await H(),Q(),X(),g(),w())},{immediate:!0});const q=e=>{const{title:a,operateType:t,tableKey:s}=e;switch(d.value=t,m.value=s,s){case"1":v.value="增加Modbus设备";break;case"2":v.value="批量增加Modbus通道";break}K(),B()},K=()=>{switch(m.value){case"1":let e=[...O({isForm:!0}),b()[0],b()[1]];e[2].selectOptions=l.modbusDevTypes,e[3].selectOptions=[{label:"无",value:-1},...x.dAUOptionList],d.value==="edit"&&(e[3].disabled=!0),I.value={comType:"0",dataBit:8,parity:0,stopBit:0},c.value=e;break;case"2":let a=[..._({isForm:!0})];a[0].selectOptions=l.modbusDeviceOptions,c.value=a;break}},P=e=>{const{tableKey:a,rowData:t,operateType:s}=e;switch(d.value=s,m.value=a,K(),a){case"1":v.value="编辑Modbus设备",k({value:t.comType,dataIndex:"comType"});break;case"2":v.value="编辑Modbus通道";break}I.value={...t,dauID:t.dauID&&t.dauID!==""?t.dauID:-1},B()},Z=async e=>{switch(d.value){case"add":await te(e);break;case"edit":await ae(e);break}},ee=async e=>{let a={windTurbineID:n.value},t=ue(e,a);const s=await l.fetchAddModbusChannel({sourceData:t,targetTurbineIds:o.batchApplyData});s&&s.code==1?(u.success("提交成功"),w(),o.bathApplyResponse2=s.batchResults||{},h()):u.error("提交失败:"+s.msg)},te=async e=>{if(m.value==="1"){let a={...e,turbineID:n.value,dauID:e.dauID&&e.dauID!==-1?e.dauID:""};const t=await l.fetchAddModbusDevice({sourceData:a,targetTurbineIds:o.batchApplyData});t&&t.code==1?(g(),o.bathApplyResponse1=t.batchResults||{},h(),u.success("提交成功")):u.error("提交失败:"+t.msg)}},ae=async e=>{if(m.value==="1"){let a={...I.value,...e,modbusID:e.modbusID,dauID:e.dauID&&e.dauID!==-1?e.dauID:""};const t=await l.fetchEditModbusDevice({sourceData:a,targetTurbineIds:o.batchApplyData});t&&t.code===1?(g(),o.bathApplyResponse1=t.batchResults||{},h(),u.success("提交成功")):u.error("提交失败:"+t.msg)}else if(m.value==="2"){let a={windTurbineID:n.value,description:"",...e};const t=await l.fetchEditModbusChannel({sourceData:a,targetTurbineIds:o.batchApplyData});t&&t.code===1?(w(),o.bathApplyResponse2=t.batchResults||{},h(),u.success("提交成功")):u.error("提交失败:"+t.msg)}},N=async e=>{const{tableKey:a,selectedkeys:t,record:s}=e;if(m.value=a,a==="1"){let i=[];if(s)i.push({modbusDeviceID:s.modbusDeviceID,turbineID:n.value,modbusID:s.modbusID});else for(let D=0;D<t.length;D++){let y=t[D].split("&&");i.push({turbineID:n.value,modbusDeviceID:y[0],modbusID:y[1]})}const p=await l.fetchBatchDeleteModbusDevice({sourceData:i,targetTurbineIds:o.batchApplyData});p&&p.code==1?(g(),o.bathApplyResponse1=p.batchResults||{},h(),u.success("删除成功")):u.error("删除失败:"+p.msg)}else if(a==="2"){let i=[];if(s)i.push({windTurbineID:n.value,modbusDeviceID:s.modbusDeviceID,channelNumber:s.channelNumber,measLocationID:s.measLocationID});else for(let D=0;D<t.length;D++){let y=t[D].split("&&");i.push({windTurbineID:n.value,modbusDeviceID:y[1],channelNumber:y[0],measLocationID:y[2]})}const p=await l.fetchBatchDeleteModbusChannel({sourceData:i,targetTurbineIds:o.batchApplyData});p&&p.code==1?(w(),o.bathApplyResponse2=p.batchResults||{},u.success("删除成功"),h()):u.error("删除失败:"+p.msg)}},k=async e=>{if(e.dataIndex&&e.value&&e.dataIndex==="comType"&&(e.value=="0"?c.value=[...c.value.slice(0,5),b()[0],b()[1]]:e.value=="1"&&(c.value=[...c.value.slice(0,5),b()[2],b()[3],b()[4],b()[5],b()[6]])),e.dataIndex&&e.value&&e.dataIndex==="modbusDeviceID"){console.log(e);let a=c.value;if(await Y(e.value),d.value=="batchAdd"){if(e.index>=a[2].tableList.length)for(let t=a[2].tableList.length;t<=e.index;t++)a[2].tableList.push({});a[2].tableList[e.index].selectOptions=l.modbusMeasLocoptions,S.value.setTableFieldValue({formDataIndex:`measLocationID[${e.index}]`,tableDataIndex:"measLocationID",index:e.index,value:l.modbusMeasLocoptions&&l.modbusMeasLocoptions.length?l.modbusMeasLocoptions[0].value:""})}else a[2].selectOptions=l.modbusMeasLocoptions,formRef.value.setFieldValue("measLocationID",l.modbusMeasLocoptions&&l.modbusMeasLocoptions.length?l.modbusMeasLocoptions[0].value:"")}},B=()=>{M.value=!0},h=e=>{M.value=!1,c.value=[],I.value={},d.value="",v.value="",m.value=""},se=async e=>{e.type&&e.type=="close"?(o.batchApplyData=[],o.batchApplyKey="",o[`bathApplyResponse${e.key}`]={}):(o.batchApplyData=e.turbines,o.batchApplyKey=e.key)};return $("deviceId",n),$("bathApplySubmit",se),(e,a)=>{const t=De,s=ne;return T(),C(s,{spinning:f.value,size:"large"},{default:G(()=>[(T(),U("div",{key:n.value},[F(E,{ref:"table",size:"default","table-key":"1","table-title":"Modbus设备列表","table-columns":o.tableColumns,borderLight:o.batchApplyKey=="1",bathApplyResponse:o.bathApplyResponse1,"table-operate":["edit","delete","add","batchDelete"],recordKey:i=>`${i.modbusDeviceID}&&${i.modbusID}`,"table-datas":o.tableData1,onAddRow:q,onDeleteRow:N,onEditRow:P},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"]),F(E,{ref:"table",size:"default","table-key":"2","table-title":"通道列表","table-columns":o.tableColumns2,borderLight:o.batchApplyKey=="2",bathApplyResponse:o.bathApplyResponse2,"table-operate":["delete","add","batchDelete","batchAdd"],recordKey:i=>`${i.channelNumber}&&${i.modbusDeviceID}&&${i.measLocationID}`,"table-datas":o.tableData2,onAddRow:q,onDeleteRow:N,onEditRow:P},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"])])),F(t,{maskClosable:!1,width:V.value,open:M.value,title:v.value,footer:"",destroyOnClose:!0,onCancel:h},{default:G(()=>[d.value==="add"||d.value==="edit"?(T(),C(le,{key:0,titleCol:c.value,initFormData:I.value,onChange:k,onSubmit:Z},null,8,["titleCol","initFormData"])):d.value==="batchAdd"?(T(),C(ie,{key:1,ref_key:"tableFormRef",ref:S,size:"default","table-key":"0","table-columns":c.value,"table-operate":["copyUp","delete"],"table-datas":[],onSubmit:ee,onHangeTableFormChange:k,onCancel:h},null,8,["table-columns"])):(T(),U("div",ve))]),_:1},8,["width","open","title"])]),_:1},8,["spinning"])}}};export{Ne as default};
