﻿using CMSFramework.BusinessEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace WTCMSLive.BusinessModel
{
    /// <summary>
    /// 用户权限操作接口
    /// </summary>
    public class RoleManagement
    {
        /// <summary>
        /// 用户是否有模块操作权限
        /// </summary>
        /// <param name="_UserID"></param>
        /// <param name="_module"></param>
        /// <returns></returns>
        public static bool IsModuleOperation(string _userID, int _module)
        {
            bool result = false;
           /* SysUser user = sysService.GetSysUserByID(_userID);
            if (user != null)
            {
                List<SysRole> listRole = user.SysRoles.ToList();
                for (int i = 0; i < listRole.Count; i++)
                {
                    result = listRole[i].SysModules.ToList().Exists(m => m.ModuleID == (int)_module);
                    if (result)
                    {
                        break;
                    }
                }
            }*/
            return result;
        }
        /// <summary>
        /// 用户是否有模块对应下的方法操作权
        /// </summary>
        /// <param name="_UserID"></param>
        /// <param name="_module"></param>
        /// <param name="_fun"></param>
        /// <returns></returns>
        public static bool IsFunOperation(string _userID, ModuleName _module, FunctionName _fun)
        {
            bool result = false;
            string functionID = ((int)_module).ToString() + ((int)_fun).ToString("00");
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                User user = ctx.SysUsers.Find(_userID);
                if (user != null)
                {
                    List<UserRoleMapping> userRoleMappingList = ctx.SysUserRoleMapping.Where(item=>item.UserID==_userID).ToList();
                    List<RoleFunctionMapping> roleFunctionMappingList = ctx.SysRoleFunctionMapping.ToList();
                    for (int i = 0; i < userRoleMappingList.Count; i++)
                    {
                        RoleFunctionMapping roleMapping = roleFunctionMappingList.Where(item => item.RoleID == userRoleMappingList[i].RoleID && item.FunctionID == functionID).FirstOrDefault();
                        if (roleMapping != null)
                        {
                            return true;
                        }
                    }
                }
            }
            return result;
        }
    }
}
