using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text.Json;
#pragma warning disable CA1416 // 验证平台兼容性
using System.ServiceProcess;
using WTCMSLive.WebSite.Core.DTOs;
#pragma warning restore CA1416

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 服务管理服务接口
    /// </summary>
    public interface IServiceManagementService
    {
        Task<List<ServiceConfigDTO>> GetServiceConfigsAsync();
        Task<ServiceConfigDTO> GetServiceConfigAsync(string serviceId);
        Task<bool> SaveServiceConfigAsync(ServiceConfigDTO config);
        Task<bool> DeleteServiceConfigAsync(string serviceId);
        Task<List<ServiceStatusDTO>> GetServiceStatusesAsync();
        Task<ServiceStatusDTO> GetServiceStatusAsync(string serviceId);
        Task<bool> StartServiceAsync(string serviceId);
        Task<bool> StopServiceAsync(string serviceId);
        Task<bool> RestartServiceAsync(string serviceId);
    }

    /// <summary>
    /// 服务管理服务实现
    /// </summary>
    public class ServiceManagementService : IServiceManagementService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<ServiceManagementService> _logger;
        private readonly string _configFilePath;
        private static readonly bool IsWindows = RuntimeInformation.IsOSPlatform(OSPlatform.Windows);

        public ServiceManagementService(
            IConfiguration configuration,
            ILogger<ServiceManagementService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ServiceConfigs.json");

            _logger.LogInformation("服务管理系统已初始化，配置文件路径: {ConfigPath}", _configFilePath);
        }

        #region 配置管理

        /// <summary>
        /// 获取所有服务配置
        /// </summary>
        public async Task<List<ServiceConfigDTO>> GetServiceConfigsAsync()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    return new List<ServiceConfigDTO>();
                }

                var json = await File.ReadAllTextAsync(_configFilePath);
                var configs = JsonSerializer.Deserialize<List<ServiceConfigDTO>>(json) ?? new List<ServiceConfigDTO>();
                return configs;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取服务配置失败");
                return new List<ServiceConfigDTO>();
            }
        }

        /// <summary>
        /// 获取单个服务配置
        /// </summary>
        public async Task<ServiceConfigDTO> GetServiceConfigAsync(string serviceId)
        {
            var configs = await GetServiceConfigsAsync();
            return configs.FirstOrDefault(c => c.ServiceId == serviceId);
        }

        /// <summary>
        /// 保存服务配置
        /// </summary>
        public async Task<bool> SaveServiceConfigAsync(ServiceConfigDTO config)
        {
            try
            {
                var configs = await GetServiceConfigsAsync();
                var existingIndex = configs.FindIndex(c => c.ServiceId == config.ServiceId);

                if (string.IsNullOrEmpty(config.ServiceId))
                {
                    config.ServiceId = Guid.NewGuid().ToString();
                }

                if (existingIndex >= 0)
                {
                    configs[existingIndex] = config;
                }
                else
                {
                    configs.Add(config);
                }

                var json = JsonSerializer.Serialize(configs, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(_configFilePath, json);

                _logger.LogInformation("服务配置保存成功: {ServiceName}", config.ServiceName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存服务配置失败: {ServiceName}", config.ServiceName);
                return false;
            }
        }

        /// <summary>
        /// 删除服务配置
        /// </summary>
        public async Task<bool> DeleteServiceConfigAsync(string serviceId)
        {
            try
            {
                var configs = await GetServiceConfigsAsync();
                var config = configs.FirstOrDefault(c => c.ServiceId == serviceId);
                if (config == null)
                {
                    return false;
                }

                configs.RemoveAll(c => c.ServiceId == serviceId);

                var json = JsonSerializer.Serialize(configs, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(_configFilePath, json);

                _logger.LogInformation("服务配置删除成功: {ServiceId}", serviceId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除服务配置失败: {ServiceId}", serviceId);
                return false;
            }
        }

        #endregion

        #region 服务状态管理

        /// <summary>
        /// 获取所有服务状态
        /// </summary>
        public async Task<List<ServiceStatusDTO>> GetServiceStatusesAsync()
        {
            var configs = await GetServiceConfigsAsync();
            var statuses = new List<ServiceStatusDTO>();

            foreach (var config in configs)
            {
                var status = await GetServiceStatusAsync(config.ServiceId);
                statuses.Add(status);
            }

            return statuses;
        }

        /// <summary>
        /// 获取单个服务状态
        /// </summary>
        public async Task<ServiceStatusDTO> GetServiceStatusAsync(string serviceId)
        {
            var config = await GetServiceConfigAsync(serviceId);
            if (config == null)
            {
                return new ServiceStatusDTO
                {
                    ServiceId = serviceId,
                    Status = ServiceState.Error,
                    StatusDescription = "服务配置不存在",
                    IsInstalled = false,
                    LastCheckTime = DateTime.Now
                };
            }

            try
            {
                if (IsWindows)
                {
                    return await GetWindowsServiceStatusAsync(config);
                }
                else
                {
                    return await GetLinuxServiceStatusAsync(config);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取服务状态失败: {ServiceName}", config.ServiceName);
                return new ServiceStatusDTO
                {
                    ServiceId = serviceId,
                    ServiceName = config.ServiceName,
                    SystemServiceName = config.SystemServiceName,
                    Status = ServiceState.Error,
                    StatusDescription = $"获取状态失败: {ex.Message}",
                    IsInstalled = false,
                    LastCheckTime = DateTime.Now,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 获取Windows服务状态
        /// </summary>
        private async Task<ServiceStatusDTO> GetWindowsServiceStatusAsync(ServiceConfigDTO config)
        {
            try
            {
                // 在非Windows平台上直接返回不支持的状态
                if (!IsWindows)
                {
                    return new ServiceStatusDTO
                    {
                        ServiceId = config.ServiceId,
                        ServiceName = config.ServiceName,
                        SystemServiceName = config.SystemServiceName,
                        Status = ServiceState.Error,
                        StatusDescription = "Windows服务管理在当前平台不受支持",
                        IsInstalled = false,
                        LastCheckTime = DateTime.Now,
                        ErrorMessage = "Platform not supported"
                    };
                }

                using var service = new ServiceController(config.SystemServiceName);

                // 检查服务是否存在
                try
                {
                    var status = service.Status;
                    var serviceState = ConvertWindowsServiceState(status);
                    var statusDescription = GetWindowsServiceStatusDescription(status);

                    return new ServiceStatusDTO
                    {
                        ServiceId = config.ServiceId,
                        ServiceName = config.ServiceName,
                        SystemServiceName = config.SystemServiceName,
                        Status = serviceState,
                        StatusDescription = statusDescription,
                        IsInstalled = true,
                        LastCheckTime = DateTime.Now
                    };
                }
                catch (InvalidOperationException)
                {
                    // 服务不存在
                    return new ServiceStatusDTO
                    {
                        ServiceId = config.ServiceId,
                        ServiceName = config.ServiceName,
                        SystemServiceName = config.SystemServiceName,
                        Status = ServiceState.NotInstalled,
                        StatusDescription = "服务未安装",
                        IsInstalled = false,
                        LastCheckTime = DateTime.Now
                    };
                }
            }
            catch (PlatformNotSupportedException ex)
            {
                _logger.LogWarning(ex, "Windows服务管理在当前平台不受支持: {ServiceName}", config.ServiceName);
                return new ServiceStatusDTO
                {
                    ServiceId = config.ServiceId,
                    ServiceName = config.ServiceName,
                    SystemServiceName = config.SystemServiceName,
                    Status = ServiceState.Error,
                    StatusDescription = "Windows服务管理在当前平台不受支持",
                    IsInstalled = false,
                    LastCheckTime = DateTime.Now,
                    ErrorMessage = ex.Message
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Windows服务状态失败: {ServiceName}", config.ServiceName);
                return new ServiceStatusDTO
                {
                    ServiceId = config.ServiceId,
                    ServiceName = config.ServiceName,
                    SystemServiceName = config.SystemServiceName,
                    Status = ServiceState.Error,
                    StatusDescription = $"状态检查失败: {ex.Message}",
                    IsInstalled = false,
                    LastCheckTime = DateTime.Now,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 获取Linux服务状态
        /// </summary>
        private async Task<ServiceStatusDTO> GetLinuxServiceStatusAsync(ServiceConfigDTO config)
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = "systemctl",
                    Arguments = $"is-active {config.SystemServiceName}",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(processInfo);
                if (process == null)
                {
                    throw new InvalidOperationException("无法启动systemctl进程");
                }

                await process.WaitForExitAsync();
                var output = await process.StandardOutput.ReadToEndAsync();

                var isActive = output.Trim() == "active";

                // 检查服务是否存在
                var isInstalled = await CheckLinuxServiceInstalledAsync(config.SystemServiceName);

                ServiceState state;
                string statusDescription;

                if (!isInstalled)
                {
                    state = ServiceState.NotInstalled;
                    statusDescription = "服务未安装";
                }
                else if (isActive)
                {
                    state = ServiceState.Running;
                    statusDescription = "正在运行";
                }
                else
                {
                    state = ServiceState.Stopped;
                    statusDescription = "已停止";
                }

                return new ServiceStatusDTO
                {
                    ServiceId = config.ServiceId,
                    ServiceName = config.ServiceName,
                    SystemServiceName = config.SystemServiceName,
                    Status = state,
                    StatusDescription = statusDescription,
                    IsInstalled = isInstalled,
                    LastCheckTime = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Linux服务状态失败: {ServiceName}", config.ServiceName);
                return new ServiceStatusDTO
                {
                    ServiceId = config.ServiceId,
                    ServiceName = config.ServiceName,
                    SystemServiceName = config.SystemServiceName,
                    Status = ServiceState.Error,
                    StatusDescription = $"状态检查失败: {ex.Message}",
                    IsInstalled = false,
                    LastCheckTime = DateTime.Now,
                    ErrorMessage = ex.Message
                };
            }
        }

        #endregion

        #region 服务操作

        /// <summary>
        /// 启动服务
        /// </summary>
        public async Task<bool> StartServiceAsync(string serviceId)
        {
            var config = await GetServiceConfigAsync(serviceId);
            if (config == null)
            {
                _logger.LogError("服务配置不存在: {ServiceId}", serviceId);
                return false;
            }

            try
            {
                if (IsWindows)
                {
                    return await StartWindowsServiceAsync(config);
                }
                else
                {
                    return await StartLinuxServiceAsync(config);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动服务失败: {ServiceName}", config.ServiceName);
                return false;
            }
        }

        /// <summary>
        /// 停止服务
        /// </summary>
        public async Task<bool> StopServiceAsync(string serviceId)
        {
            var config = await GetServiceConfigAsync(serviceId);
            if (config == null)
            {
                _logger.LogError("服务配置不存在: {ServiceId}", serviceId);
                return false;
            }

            try
            {
                if (IsWindows)
                {
                    return await StopWindowsServiceAsync(config);
                }
                else
                {
                    return await StopLinuxServiceAsync(config);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止服务失败: {ServiceName}", config.ServiceName);
                return false;
            }
        }

        /// <summary>
        /// 重启服务
        /// </summary>
        public async Task<bool> RestartServiceAsync(string serviceId)
        {
            _logger.LogInformation("开始重启服务: {ServiceId}", serviceId);

            // 先停止服务
            var stopResult = await StopServiceAsync(serviceId);
            if (!stopResult)
            {
                _logger.LogWarning("停止服务失败，继续尝试启动: {ServiceId}", serviceId);
            }

            // 等待一秒确保服务完全停止
            await Task.Delay(1000);

            // 启动服务
            var startResult = await StartServiceAsync(serviceId);

            _logger.LogInformation("重启服务完成: {ServiceId}, 结果: {Result}", serviceId, startResult);
            return startResult;
        }

        #endregion

        #region Windows服务操作

        /// <summary>
        /// 启动Windows服务
        /// </summary>
        private async Task<bool> StartWindowsServiceAsync(ServiceConfigDTO config)
        {
            try
            {
                // 在非Windows平台上直接返回失败
                if (!IsWindows)
                {
                    _logger.LogWarning("Windows服务管理在当前平台不受支持: {ServiceName}", config.ServiceName);
                    return false;
                }

                using var service = new ServiceController(config.SystemServiceName);

                if (service.Status == ServiceControllerStatus.Running)
                {
                    _logger.LogInformation("Windows服务已在运行: {ServiceName}", config.ServiceName);
                    return true;
                }

                if (service.Status == ServiceControllerStatus.StartPending)
                {
                    _logger.LogInformation("Windows服务正在启动: {ServiceName}", config.ServiceName);
                    service.WaitForStatus(ServiceControllerStatus.Running, TimeSpan.FromSeconds(30));
                    return service.Status == ServiceControllerStatus.Running;
                }

                service.Start();
                service.WaitForStatus(ServiceControllerStatus.Running, TimeSpan.FromSeconds(30));

                _logger.LogInformation("Windows服务启动成功: {ServiceName}", config.ServiceName);
                return service.Status == ServiceControllerStatus.Running;
            }
            catch (PlatformNotSupportedException ex)
            {
                _logger.LogWarning(ex, "Windows服务管理在当前平台不受支持: {ServiceName}", config.ServiceName);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动Windows服务失败: {ServiceName}", config.ServiceName);
                return false;
            }
        }

        /// <summary>
        /// 停止Windows服务
        /// </summary>
        private async Task<bool> StopWindowsServiceAsync(ServiceConfigDTO config)
        {
            try
            {
                // 在非Windows平台上直接返回失败
                if (!IsWindows)
                {
                    _logger.LogWarning("Windows服务管理在当前平台不受支持: {ServiceName}", config.ServiceName);
                    return false;
                }

                using var service = new ServiceController(config.SystemServiceName);

                if (service.Status == ServiceControllerStatus.Stopped)
                {
                    _logger.LogInformation("Windows服务已停止: {ServiceName}", config.ServiceName);
                    return true;
                }

                if (service.Status == ServiceControllerStatus.StopPending)
                {
                    _logger.LogInformation("Windows服务正在停止: {ServiceName}", config.ServiceName);
                    service.WaitForStatus(ServiceControllerStatus.Stopped, TimeSpan.FromSeconds(30));
                    return service.Status == ServiceControllerStatus.Stopped;
                }

                service.Stop();
                service.WaitForStatus(ServiceControllerStatus.Stopped, TimeSpan.FromSeconds(30));

                _logger.LogInformation("Windows服务停止成功: {ServiceName}", config.ServiceName);
                return service.Status == ServiceControllerStatus.Stopped;
            }
            catch (PlatformNotSupportedException ex)
            {
                _logger.LogWarning(ex, "Windows服务管理在当前平台不受支持: {ServiceName}", config.ServiceName);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止Windows服务失败: {ServiceName}", config.ServiceName);
                return false;
            }
        }

        #endregion

        #region Linux服务操作

        /// <summary>
        /// 启动Linux服务
        /// </summary>
        private async Task<bool> StartLinuxServiceAsync(ServiceConfigDTO config)
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = "systemctl",
                    Arguments = $"start {config.SystemServiceName}",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(processInfo);
                if (process == null)
                {
                    throw new InvalidOperationException("无法启动systemctl进程");
                }

                await process.WaitForExitAsync();
                var error = await process.StandardError.ReadToEndAsync();

                if (process.ExitCode == 0)
                {
                    _logger.LogInformation("Linux服务启动成功: {ServiceName}", config.ServiceName);
                    return true;
                }
                else
                {
                    _logger.LogError("Linux服务启动失败: {ServiceName}, 错误: {Error}", config.ServiceName, error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动Linux服务失败: {ServiceName}", config.ServiceName);
                return false;
            }
        }

        /// <summary>
        /// 停止Linux服务
        /// </summary>
        private async Task<bool> StopLinuxServiceAsync(ServiceConfigDTO config)
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = "systemctl",
                    Arguments = $"stop {config.SystemServiceName}",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(processInfo);
                if (process == null)
                {
                    throw new InvalidOperationException("无法启动systemctl进程");
                }

                await process.WaitForExitAsync();
                var error = await process.StandardError.ReadToEndAsync();

                if (process.ExitCode == 0)
                {
                    _logger.LogInformation("Linux服务停止成功: {ServiceName}", config.ServiceName);
                    return true;
                }
                else
                {
                    _logger.LogError("Linux服务停止失败: {ServiceName}, 错误: {Error}", config.ServiceName, error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止Linux服务失败: {ServiceName}", config.ServiceName);
                return false;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 转换Windows服务状态
        /// </summary>
        private ServiceState ConvertWindowsServiceState(ServiceControllerStatus status)
        {
            return status switch
            {
                ServiceControllerStatus.Running => ServiceState.Running,
                ServiceControllerStatus.Stopped => ServiceState.Stopped,
                ServiceControllerStatus.StartPending => ServiceState.Starting,
                ServiceControllerStatus.StopPending => ServiceState.Stopping,
                _ => ServiceState.Error
            };
        }

        /// <summary>
        /// 获取Windows服务状态描述
        /// </summary>
        private string GetWindowsServiceStatusDescription(ServiceControllerStatus status)
        {
            return status switch
            {
                ServiceControllerStatus.Running => "正在运行",
                ServiceControllerStatus.Stopped => "已停止",
                ServiceControllerStatus.StartPending => "正在启动",
                ServiceControllerStatus.StopPending => "正在停止",
                ServiceControllerStatus.Paused => "已暂停",
                ServiceControllerStatus.PausePending => "正在暂停",
                ServiceControllerStatus.ContinuePending => "正在恢复",
                _ => "未知状态"
            };
        }

        /// <summary>
        /// 检查Linux服务是否已安装
        /// </summary>
        private async Task<bool> CheckLinuxServiceInstalledAsync(string serviceName)
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = "systemctl",
                    Arguments = $"list-unit-files {serviceName}.service",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(processInfo);
                if (process == null)
                {
                    return false;
                }

                await process.WaitForExitAsync();
                var output = await process.StandardOutput.ReadToEndAsync();

                // 如果输出包含服务名称，说明服务已安装
                return output.Contains($"{serviceName}.service");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查Linux服务安装状态失败: {ServiceName}", serviceName);
                return false;
            }
        }

        #endregion
    }
}