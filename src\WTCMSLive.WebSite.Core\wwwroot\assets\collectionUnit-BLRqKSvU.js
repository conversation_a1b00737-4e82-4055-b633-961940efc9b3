import{u as oe,W as re}from"./table-RP3jLHlo.js";import{O as ue}from"./index-CzSbT6op.js";import{W as he}from"./index-QXLii0rw.js";import{Z as ge,_ as pe,$ as we,a0 as k,a1 as ve,a2 as me,a3 as be,a4 as Ie,a5 as u,a6 as fe,j as B,k as Se,r as w,w as ae,a7 as ye,h as te,a8 as $e,b as I,a9 as Ce,aa as X,ab as Me,ac as Y,u as De,f as O,d as W,A as xe,o as _,i as K,c as G,s as Q,F as ee,t as Te,g as Ae,m as S}from"./index-BjOW8S1L.js";import{u as Pe}from"./collectionUnitConfig-BbRKo_Zp.js";import{S as _e,d as Z,f as ie}from"./tools-zTE6InS0.js";import{_ as We}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{K as ne}from"./shallowequal-gCpTBdTi.js";import{W as ze,B as Ee}from"./index-7iPMz_Qy.js";import{u as Ue}from"./index-DTxROkTj.js";import{o as Le}from"./styleChecker-CFtINSLw.js";import{M as He}from"./index-BnSFuLp6.js";import"./ActionButton-C_grUmdF.js";import"./initDefaultProps-P4j1rGDC.js";import"./index-kP-mINcM.js";import"./index-BJEkaghg.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";const Be=e=>{const{componentCls:i}=e,n=`${i}-inner`;return{[i]:{[`&${i}-small`]:{minWidth:e.switchMinWidthSM,height:e.switchHeightSM,lineHeight:`${e.switchHeightSM}px`,[`${i}-inner`]:{paddingInlineStart:e.switchInnerMarginMaxSM,paddingInlineEnd:e.switchInnerMarginMinSM,[`${n}-checked`]:{marginInlineStart:`calc(-100% + ${e.switchPinSizeSM+e.switchPadding*2}px - ${e.switchInnerMarginMaxSM*2}px)`,marginInlineEnd:`calc(100% - ${e.switchPinSizeSM+e.switchPadding*2}px + ${e.switchInnerMarginMaxSM*2}px)`},[`${n}-unchecked`]:{marginTop:-e.switchHeightSM,marginInlineStart:0,marginInlineEnd:0}},[`${i}-handle`]:{width:e.switchPinSizeSM,height:e.switchPinSizeSM},[`${i}-loading-icon`]:{top:(e.switchPinSizeSM-e.switchLoadingIconSize)/2,fontSize:e.switchLoadingIconSize},[`&${i}-checked`]:{[`${i}-inner`]:{paddingInlineStart:e.switchInnerMarginMinSM,paddingInlineEnd:e.switchInnerMarginMaxSM,[`${n}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${n}-unchecked`]:{marginInlineStart:`calc(100% - ${e.switchPinSizeSM+e.switchPadding*2}px + ${e.switchInnerMarginMaxSM*2}px)`,marginInlineEnd:`calc(-100% + ${e.switchPinSizeSM+e.switchPadding*2}px - ${e.switchInnerMarginMaxSM*2}px)`}},[`${i}-handle`]:{insetInlineStart:`calc(100% - ${e.switchPinSizeSM+e.switchPadding}px)`}},[`&:not(${i}-disabled):active`]:{[`&:not(${i}-checked) ${n}`]:{[`${n}-unchecked`]:{marginInlineStart:e.marginXXS/2,marginInlineEnd:-e.marginXXS/2}},[`&${i}-checked ${n}`]:{[`${n}-checked`]:{marginInlineStart:-e.marginXXS/2,marginInlineEnd:e.marginXXS/2}}}}}}},ke=e=>{const{componentCls:i}=e;return{[i]:{[`${i}-loading-icon${e.iconCls}`]:{position:"relative",top:(e.switchPinSize-e.fontSize)/2,color:e.switchLoadingIconColor,verticalAlign:"top"},[`&${i}-checked ${i}-loading-icon`]:{color:e.switchColor}}}},Oe=e=>{const{componentCls:i}=e,n=`${i}-handle`;return{[i]:{[n]:{position:"absolute",top:e.switchPadding,insetInlineStart:e.switchPadding,width:e.switchPinSize,height:e.switchPinSize,transition:`all ${e.switchDuration} ease-in-out`,"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:e.colorWhite,borderRadius:e.switchPinSize/2,boxShadow:e.switchHandleShadow,transition:`all ${e.switchDuration} ease-in-out`,content:'""'}},[`&${i}-checked ${n}`]:{insetInlineStart:`calc(100% - ${e.switchPinSize+e.switchPadding}px)`},[`&:not(${i}-disabled):active`]:{[`${n}::before`]:{insetInlineEnd:e.switchHandleActiveInset,insetInlineStart:0},[`&${i}-checked ${n}::before`]:{insetInlineEnd:0,insetInlineStart:e.switchHandleActiveInset}}}}},Fe=e=>{const{componentCls:i}=e,n=`${i}-inner`;return{[i]:{[n]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:e.switchInnerMarginMax,paddingInlineEnd:e.switchInnerMarginMin,transition:`padding-inline-start ${e.switchDuration} ease-in-out, padding-inline-end ${e.switchDuration} ease-in-out`,[`${n}-checked, ${n}-unchecked`]:{display:"block",color:e.colorTextLightSolid,fontSize:e.fontSizeSM,transition:`margin-inline-start ${e.switchDuration} ease-in-out, margin-inline-end ${e.switchDuration} ease-in-out`,pointerEvents:"none"},[`${n}-checked`]:{marginInlineStart:`calc(-100% + ${e.switchPinSize+e.switchPadding*2}px - ${e.switchInnerMarginMax*2}px)`,marginInlineEnd:`calc(100% - ${e.switchPinSize+e.switchPadding*2}px + ${e.switchInnerMarginMax*2}px)`},[`${n}-unchecked`]:{marginTop:-e.switchHeight,marginInlineStart:0,marginInlineEnd:0}},[`&${i}-checked ${n}`]:{paddingInlineStart:e.switchInnerMarginMin,paddingInlineEnd:e.switchInnerMarginMax,[`${n}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${n}-unchecked`]:{marginInlineStart:`calc(100% - ${e.switchPinSize+e.switchPadding*2}px + ${e.switchInnerMarginMax*2}px)`,marginInlineEnd:`calc(-100% + ${e.switchPinSize+e.switchPadding*2}px - ${e.switchInnerMarginMax*2}px)`}},[`&:not(${i}-disabled):active`]:{[`&:not(${i}-checked) ${n}`]:{[`${n}-unchecked`]:{marginInlineStart:e.switchPadding*2,marginInlineEnd:-e.switchPadding*2}},[`&${i}-checked ${n}`]:{[`${n}-checked`]:{marginInlineStart:-e.switchPadding*2,marginInlineEnd:e.switchPadding*2}}}}}},Ve=e=>{const{componentCls:i}=e;return{[i]:k(k(k(k({},ve(e)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:e.switchMinWidth,height:e.switchHeight,lineHeight:`${e.switchHeight}px`,verticalAlign:"middle",background:e.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:`all ${e.motionDurationMid}`,userSelect:"none",[`&:hover:not(${i}-disabled)`]:{background:e.colorTextTertiary}}),me(e)),{[`&${i}-checked`]:{background:e.switchColor,[`&:hover:not(${i}-disabled)`]:{background:e.colorPrimaryHover}},[`&${i}-loading, &${i}-disabled`]:{cursor:"not-allowed",opacity:e.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},[`&${i}-rtl`]:{direction:"rtl"}})}},Re=ge("Switch",e=>{const i=e.fontSize*e.lineHeight,n=e.controlHeight/2,g=2,y=i-g*2,h=n-g*2,d=pe(e,{switchMinWidth:y*2+g*4,switchHeight:i,switchDuration:e.motionDurationMid,switchColor:e.colorPrimary,switchDisabledOpacity:e.opacityLoading,switchInnerMarginMin:y/2,switchInnerMarginMax:y+g+g*2,switchPadding:g,switchPinSize:y,switchBg:e.colorBgContainer,switchMinWidthSM:h*2+g*2,switchHeightSM:n,switchInnerMarginMinSM:h/2,switchInnerMarginMaxSM:h+g+g*2,switchPinSizeSM:h,switchHandleShadow:`0 2px 4px 0 ${new we("#00230b").setAlpha(.2).toRgbString()}`,switchLoadingIconSize:e.fontSizeIcon*.75,switchLoadingIconColor:`rgba(0, 0, 0, ${e.opacityLoading})`,switchHandleActiveInset:"-30%"});return[Ve(d),Fe(d),Oe(d),ke(d),Be(d)]}),Ne=Ce("small","default"),je=()=>({id:String,prefixCls:String,size:u.oneOf(Ne),disabled:{type:Boolean,default:void 0},checkedChildren:u.any,unCheckedChildren:u.any,tabindex:u.oneOfType([u.string,u.number]),autofocus:{type:Boolean,default:void 0},loading:{type:Boolean,default:void 0},checked:u.oneOfType([u.string,u.number,u.looseBool]),checkedValue:u.oneOfType([u.string,u.number,u.looseBool]).def(!0),unCheckedValue:u.oneOfType([u.string,u.number,u.looseBool]).def(!1),onChange:{type:Function},onClick:{type:Function},onKeydown:{type:Function},onMouseup:{type:Function},"onUpdate:checked":{type:Function},onBlur:Function,onFocus:Function}),qe=Ie({compatConfig:{MODE:3},name:"ASwitch",__ANT_SWITCH:!0,inheritAttrs:!1,props:je(),slots:Object,setup(e,i){let{attrs:n,slots:g,expose:y,emit:h}=i;const d=Ue(),A=fe(),v=B(()=>{var l;return(l=e.disabled)!==null&&l!==void 0?l:A.value});Se(()=>{});const m=w(e.checked!==void 0?e.checked:n.defaultChecked),z=B(()=>m.value===e.checkedValue);ae(()=>e.checked,()=>{m.value=e.checked});const{prefixCls:o,direction:E,size:P}=ye("switch",e),[F,V]=Re(o),b=w(),U=()=>{var l;(l=b.value)===null||l===void 0||l.focus()};y({focus:U,blur:()=>{var l;(l=b.value)===null||l===void 0||l.blur()}}),te(()=>{$e(()=>{e.autofocus&&!v.value&&b.value.focus()})});const L=(l,f)=>{v.value||(h("update:checked",l),h("change",l,f),d.onFieldChange())},R=l=>{h("blur",l)},N=l=>{U();const f=z.value?e.unCheckedValue:e.checkedValue;L(f,l),h("click",f,l)},j=l=>{l.keyCode===ne.LEFT?L(e.unCheckedValue,l):l.keyCode===ne.RIGHT&&L(e.checkedValue,l),h("keydown",l)},H=l=>{var f;(f=b.value)===null||f===void 0||f.blur(),h("mouseup",l)},D=B(()=>({[`${o.value}-small`]:P.value==="small",[`${o.value}-loading`]:e.loading,[`${o.value}-checked`]:z.value,[`${o.value}-disabled`]:v.value,[o.value]:!0,[`${o.value}-rtl`]:E.value==="rtl",[V.value]:!0}));return()=>{var l;return F(I(ze,null,{default:()=>[I("button",X(X(X({},Le(e,["prefixCls","checkedChildren","unCheckedChildren","checked","autofocus","checkedValue","unCheckedValue","id","onChange","onUpdate:checked"])),n),{},{id:(l=e.id)!==null&&l!==void 0?l:d.id.value,onKeydown:j,onClick:N,onBlur:R,onMouseup:H,type:"button",role:"switch","aria-checked":m.value,disabled:v.value||e.loading,class:[n.class,D.value],ref:b}),[I("div",{class:`${o.value}-handle`},[e.loading?I(Me,{class:`${o.value}-loading-icon`},null):null]),I("span",{class:`${o.value}-inner`},[I("span",{class:`${o.value}-inner-checked`},[Y(g,e,"checkedChildren")]),I("span",{class:`${o.value}-inner-unchecked`},[Y(g,e,"unCheckedChildren")])])])]}))}}}),Xe=be(qe),Ke={class:"status"},Ge={key:2},M=320,Qe={__name:"collectionUnit",setup(e){const i=Pe(),n=oe(),g=w(!1),y=De(),h=w(""),d=w(""),A=w({}),v=w([]),m=w([]),z=w(!1),o=w(y.params.id),E=w(!1),P=w([]),F=B(()=>d.value==="batchAdd"||d.value==="batchEdit"?"1200px":"600px"),V=B(()=>d.value=="batchEdit"?["windTurbineID","windTurbineName","dauID","dauName"]:["windTurbineID","windTurbineName"]),b=(a,t)=>[{title:"设备",dataIndex:"windTurbineName",labelInValue:!t,headerOperations:{filters:[]},columnWidth:100,inputType:"select",selectOptions:n.deviceOptions,disabled:t,isrequired:!0,formItemWidth:M},{title:"采集单元名称",dataIndex:"dauName",columnWidth:100,formItemWidth:M,headerOperations:{filters:[]},isrequired:!0},{title:"采集单元类型",dataIndex:"dauType",hasChangeEvent:!0,columnWidth:140,inputType:"select",isrequired:!0,selectOptions:i.dAUTypeList,isdisplay:!t,formItemWidth:M,headerOperations:{filterOptions:i.dAUTypeList,filters:[]},...a?{}:{customRender:({record:c})=>{var r;return xe("span",{},((r=i.dAUTypeList.find(p=>p.value===c.dauType))==null?void 0:r.label)||"")}}},{title:"IP地址",dataIndex:"ip",columnWidth:150,formItemWidth:M,columnOperate:{type:"ip"},validateRules:Z({type:"ip",title:"IP地址",required:!0})},{title:"端口号",dataIndex:"port",columnWidth:70,formItemWidth:M,validateRules:Z({type:"port",title:"端口号",required:!0})},{title:"采集间隔(分钟)",dataIndex:"dataAcquisitionInterval",columnWidth:120,formItemWidth:M,validateRules:Z({type:"number",title:"采集间隔",required:!0})},{title:"设备ID",dataIndex:"deviceID",columnWidth:80,formItemWidth:M,tableList:[]},{title:"固件版本",dataIndex:"dauSoftwareVersion",columnWidth:80,formItemWidth:M,isdisplay:!1,columnHidden:!0},{title:"状态",dataIndex:"isAvailable",otherColumn:!0,inputType:"checkbox",selectOptions:[{label:"启用",value:!0}],formItemWidth:M}],U=w(b()),$=async()=>{E.value=!0,await L(),await R();const a=await i.fetchGetDAUList({WindParkId:o.value});a&&(v.value=a),E.value=!1,U.value=b()},L=async()=>{(!i.dAUTypeList||!i.dAUTypeList.length)&&await i.fetchGetDAUType()},R=async()=>{(!n.devTreedDevicelist||!n.devTreedDevicelist.length)&&await n.fetchDevTreedDevicelist({windParkID:o.value})};te(()=>{$()}),ae(()=>y.params.id,a=>{i.reset(),o.value=a,$()});const N=async a=>{const t=await i.fetchChangeDAUStateBat({WindParkId:o.value,IsAvailable:a});t&&t.code===1&&(S.success("操作成功"),$())},j=async(a,t)=>{const c=await i.fetchChangeDAUState({WindTurbineID:t.windTurbineID,IsAvailable:a,dauId:t.dauID});c&&c.code===1&&(S.success("操作成功"),$())},H=()=>{g.value=!0},D=a=>{g.value=!1,m.value=[],A.value={},d.value="",h.value="",P.value=[]},l=a=>{const{title:t,operateType:c}=a;d.value=c,h.value="批量添加采集单元",m.value=b(!0,!1),H()},f=a=>{d.value="batchEdit",h.value="批量修改ip和端口";let t=b(!0,!0);t[1].disabled=!0,m.value=[{...t[0],columnWidth:160},{...t[1],columnWidth:200},{...t[3],columnWidth:220},{...t[4],columnWidth:160}],P.value=v.value.filter(c=>a.includes(`${c.windTurbineID}&&${c.dauID}`)),H()},le=async(a={})=>{const{selectedkeys:t,record:c}=a;let r=[];if(c)r.push({dauID:c.dauID,WindTurbineID:c.windTurbineID,WindParkID:o.value});else for(let C=0;C<t.length;C++){let s=t[C].split("&&");r.push({dauID:s[1],WindTurbineID:s[0],WindParkID:o.value})}const p=await i.fetchBatchDeleteDAU(r);p&&p.code===1?($(),D(),S.success("提交成功")):S.error("提交失败:"+p.msg)},se=a=>{const{rowData:t,title:c,operateType:r}=a;d.value=r,h.value="编辑采集单元",A.value={...t,isAvailable:t.isAvailable?[!0]:[!1]};let p=b(!0,!0);p[6].isdisplay=t.dauType==2||t.dauType==3,m.value=[...p],H()},J=(a,t,c)=>{if(a&&a.dataIndex&&a.dataIndex=="dauType"){let r=m.value;if(a.index>=r[6].tableList.length)for(let p=r[6].tableList.length;p<=a.index;p++)r[6].tableList.push({});a.value==2||a.value==3?r[6].tableList[a.index].disabled=!1:r[6].tableList[a.index].disabled=!0}},ce=async a=>{let t={...a,isAvailable:a.isAvailable&&a.isAvailable.length?a.isAvailable[0]:!1,windTurbineID:A.value.windTurbineID,windParkID:A.value.windParkID};const c=await i.fetchDAUEditDAU(t);c&&c.code===1?($(),D(),S.success("提交成功")):S.error("提交失败!"+(c?c.msg:""))},de=async a=>{if(d.value==="batchEdit"){let s=ie(a);for(let T=0;T<s.length;T++)s[T].WindTurbineID=P.value[T].windTurbineID,s[T].dauID=P.value[T].dauID;const x=await i.fetchBatchUpdateDAUNetwork(s);x&&x.code===1?($(),D(),S.success("提交成功")):S.error("提交失败!"+(x?x.msg:""));return}let t={WindParkID:o.value},p=ie(a,t,{windTurbineName:{label:"windTurbineName",value:"windTurbineID"}}).map(s=>({...s,...t,dauID:"",serialNumber:"",deviceID:s.deviceID||0,WaveSaveInterval:3600,TrendSaveInterval:60,DataAcquisitionInterval:2,MeasDefVersion:3,DAUMeasDefVersion:3,DAUSoftwareVersion:"v2.5.0",isAvailable:s.isAvailable&&s.isAvailable.length?s.isAvailable[0]:!1}));const C=await i.fetchBatchAddDAU(p);C&&C.code===1?($(),D(),S.success("提交成功")):S.error("提交失败:"+C.msg)};return(a,t)=>{const c=Ee,r=Xe,p=He,C=_e;return _(),O(C,{spinning:E.value,size:"large"},{default:W(()=>[K("div",null,[I(re,{ref:"table",size:"default","table-key":"0","table-title":"采集单元列表","table-columns":U.value,"table-operate":["edit","delete","add","batchDelete","batchAdd"],recordKey:s=>`${s.windTurbineID}&&${s.dauID}`,"table-datas":v.value,onAddRow:l,onDeleteRow:le,onEditRow:se,noBatchApply:!0},{rightButtons:W(({selectedRowKeys:s})=>[v.value&&v.value.length?(_(),O(c,{key:0,type:"primary",onClick:x=>f(s),disabled:!s.length},{default:W(()=>t[1]||(t[1]=[Ae(" 批量修改 ",-1)])),_:2,__:[1]},1032,["onClick","disabled"])):Q("",!0)]),headerCell:W(({column:s})=>[s.dataIndex==="isAvailable"?(_(),G(ee,{key:0},[t[2]||(t[2]=K("span",{class:"status"},"状态",-1)),I(r,{checked:z.value,"onUpdate:checked":t[0]||(t[0]=x=>z.value=x),disabled:!v.value||v.value.length<1,"checked-children":"全部开","un-checked-children":"全部关",onChange:N},null,8,["checked","disabled"])],64)):Q("",!0)]),otherColumn:W(({record:s,text:x,column:T})=>[T.dataIndex==="isAvailable"?(_(),G(ee,{key:0},[K("span",Ke,Te(s.isAvailable?"启用":"禁用"),1),I(r,{checked:s.isAvailable,"onUpdate:checked":q=>s.isAvailable=q,"checked-children":"开","un-checked-children":"关",onChange:q=>j(q,s)},null,8,["checked","onUpdate:checked","onChange"])],64)):Q("",!0)]),_:1},8,["table-columns","recordKey","table-datas"]),I(p,{maskClosable:!1,width:F.value,open:g.value,title:h.value,footer:"",onCancel:D},{default:W(()=>[d.value==="add"||d.value==="edit"?(_(),O(ue,{key:0,titleCol:m.value,initFormData:A.value,onChange:J,onSubmit:ce},null,8,["titleCol","initFormData"])):d.value==="batchAdd"||d.value=="batchEdit"?(_(),O(he,{key:1,ref:"table",size:"default","table-key":"0","table-columns":m.value,"table-operate":d.value=="batchEdit"?["copyUp","noAdd"]:["copyUp","delete"],"table-datas":P.value,"noCopyUp-keys":V.value,onSubmit:de,onHangeTableFormChange:J,onCancel:D},null,8,["table-columns","table-operate","table-datas","noCopyUp-keys"])):(_(),G("div",Ge))]),_:1},8,["width","open","title"])])]),_:1},8,["spinning"])}}},wi=We(Qe,[["__scopeId","data-v-d20d0e43"]]);export{wi as default};
