﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.Entity.Models;

namespace WTCMSLive.BusinessEntityConvert
{
    // Author: Guo<PERSON>aile
    // Create: 2013-09-02
    /// <summary>
    /// 实体转换， 从业务层实体转换为DA层实体
    /// </summary>
    public static class ConvertEntityBusinessToDADAU
    {
        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 采集单元实体转换
        /// </summary>
        /// <param name="_DAUnit"></param>
        /// <returns></returns>
        public static DAUnit ConvertDAU(WTCMSLive.BusinessEntity.DataSourceV2 _datasource)
        {
            if (_datasource == null)
            {
                return null;
            }

            DAUnit dau = new DAUnit();
            
            dau.WindTurbineID = _datasource.AssocWindTurbineID;
            
            // dau实时状态
            if (_datasource.RTAlarmStatusDAU != null)
            {
                _datasource.RTAlarmStatusDAU.Ds_asset_id = _datasource.AssocWindTurbineID;
            }
            // DAU实时状态
            dau.AlarmStatusRTDAU = ConvertAlarmStatusDAU(_datasource.RTAlarmStatusDAU);

            dau.DataAcquisitionInterval = _datasource.DataAcquisitionInterval;

            if (!string.IsNullOrEmpty(_datasource.DAUMeasDefVersion))
            {
                dau.DAUMeasDefVersion = Convert.ToInt32(_datasource.DAUMeasDefVersion);
            }

            dau.DAUSoftwareVersion = _datasource.DAUSoftwareVersion;
            dau.MeasDefVersion = Convert.ToInt32(_datasource.MeasDefVersion);
            dau.IPAddress = _datasource.IP;
            dau.Enable = (short)(_datasource.IsAvailable == false ? 0 : 1);
            dau.DAUnitName = _datasource.Name;
            dau.DAUSN = string.Empty;
            if (_datasource.DAUChannelList != null)
            {
                // 振动通道转换
                foreach (WTCMSLive.BusinessEntity.DAUChannelV2 item in _datasource.DAUChannelList)
                {
                    item.Ds_asset_id = dau.WindTurbineID.ToString();
                    dau.DAUVibChannels.Add(
                        ConvertDAUChannelVib(item)
                    );
                }
            }

            if (_datasource.ProcessChannelList != null)
            {
                // 过程量通道转换
                foreach (WTCMSLive.BusinessEntity.DAUChannel_Process item in _datasource.ProcessChannelList)
                {
                    item.Ds_asset_id = dau.WindTurbineID.ToString();
                    dau.DAUProcessChannels.Add(
                            ConvertDAUChannelProcess(item)
                        );
                }
            }

            if (_datasource.RotSpeedChannelList != null)
            {
                // 转速通道转换
                foreach (WTCMSLive.BusinessEntity.RotSpeedChannelV2 item in _datasource.RotSpeedChannelList)
                {
                    item.Ds_asset_id = dau.WindTurbineID.ToString();
                    DAURotSpdChannel spdChan = ConvertDAUChannelRotSpd(item);
                    spdChan.DevMeasLocRotSpd = null;
                    dau.DAURotSpdChannels.Add(spdChan);
                }
            }
            
            dau.DAUSN = _datasource.user_tag_ident;
            dau.WaveSaveInterval = _datasource.WaveSaveInterval;
            dau.WindParkID = _datasource.WindParkID;
            dau.TrendSaveInterval = _datasource.TrendSaveInterval;

            return dau;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 采集单元通道实体转换，振动
        /// </summary>
        /// <param name="_vibChannel"></param>
        /// <returns></returns>
        public static DAUVibChannel ConvertDAUChannelVib(WTCMSLive.BusinessEntity.DAUChannelV2 _vibChannel)
        {
            DAUVibChannel dauVibChannel = new DAUVibChannel();

            if (!string.IsNullOrEmpty(_vibChannel.Ds_asset_id))
            {
                dauVibChannel.WindTurbineID = _vibChannel.Ds_asset_id;
            }

            dauVibChannel.ChannelNumber = _vibChannel.ChannelNumber;
            dauVibChannel.CoeffA = Convert.ToDecimal(_vibChannel.Coeff_a);
            dauVibChannel.CoeffB = Convert.ToDecimal(_vibChannel.Coeff_b);
            dauVibChannel.Description = _vibChannel.Description;
            dauVibChannel.MaxVolt = (decimal)_vibChannel.MaxBiasVolt;
            if (!string.IsNullOrEmpty(_vibChannel.MeasLocVibID))
            {
                dauVibChannel.MeasLocationID = _vibChannel.MeasLocVibID;
            }
            dauVibChannel.MinVolt = Convert.ToInt32(_vibChannel.MinBiasVolt);
            dauVibChannel.WindTurbineID = _vibChannel.Ds_asset_id;

            return dauVibChannel;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 采集单元通道实体转换，过程量
        /// </summary>
        /// <param name="_vibChannel"></param>
        /// <returns></returns>
        public static DAUProcessChannel ConvertDAUChannelProcess(WTCMSLive.BusinessEntity.DAUChannel_Process _proChannel)
        {
            DAUProcessChannel dauProcessChannel = new DAUProcessChannel();

            if (!string.IsNullOrEmpty(_proChannel.Ds_asset_id))
            {
                dauProcessChannel.WindTurbineID = _proChannel.Ds_asset_id;
            }

            dauProcessChannel.ChannelNumber = _proChannel.ChannelNumber;
            dauProcessChannel.CoeffA = (decimal)_proChannel.PowerCoeff_a;//.Coeff_a;
            dauProcessChannel.CoeffB = (decimal)_proChannel.PowerCoeff_b;//Coeff_b;
            if (!string.IsNullOrEmpty(_proChannel.MeasLoc_ProcessId))
            {
                dauProcessChannel.MeasLocationID = _proChannel.MeasLoc_ProcessId;
            }
            return dauProcessChannel;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 采集单元通道实体转换，转速
        /// </summary>
        /// <param name="_vibChannel"></param>
        /// <returns></returns>
        public static DAURotSpdChannel ConvertDAUChannelRotSpd(WTCMSLive.BusinessEntity.RotSpeedChannelV2 _rotSpdChannel)
        {
            DAURotSpdChannel chan = new DAURotSpdChannel();

            if (!string.IsNullOrEmpty(_rotSpdChannel.Ds_asset_id))
            {
                chan.WindTurbineID = _rotSpdChannel.Ds_asset_id;
            }

            chan.ChannelNumber = _rotSpdChannel.ChannelNumber;
            if (!string.IsNullOrEmpty(_rotSpdChannel.MeasLocRotSpdID))
            {
                chan.MeasLocationID = _rotSpdChannel.MeasLocRotSpdID;
            }
            chan.DevMeasLocRotSpd = new DevMeasLocRotSpd();
            chan.DevMeasLocRotSpd.CoderLineCounts = _rotSpdChannel.LineCounts;
            chan.DevMeasLocRotSpd.MeasLocationName = _rotSpdChannel.MeasLocRotSpdName;
            chan.DevMeasLocRotSpd.WindTurbineID = chan.WindTurbineID;
            chan.DevMeasLocRotSpd.MeasLocationID = chan.MeasLocationID;
            return chan;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 采集单元状态实体转换
        /// </summary>
        /// <param name="_alarmStateDAU"></param>
        /// <returns></returns>
        public static AlarmStatusRTDAU ConvertAlarmStatusDAU(WTCMSLive.BusinessEntity.RTAlarmStatus_DAU _alarmStateDAU)
        {
            if (_alarmStateDAU == null)
            {
                return null;
            }

            AlarmStatusRTDAU alarmStateDAU = new AlarmStatusRTDAU();

            alarmStateDAU.AlarmState = (int)_alarmStateDAU.AlarmState;

            if (!string.IsNullOrEmpty(_alarmStateDAU.Ds_asset_id))
            {
                alarmStateDAU.WindTurbineID = _alarmStateDAU.Ds_asset_id;
            }

            alarmStateDAU.StatusUpdateTime = _alarmStateDAU.StatusUpdateTime;

            if (_alarmStateDAU.sensorRTList != null)
            {
                foreach (WTCMSLive.BusinessEntity.RTAlarmStatus_Channel item in _alarmStateDAU.sensorRTList)
                {
                    AlarmStatusRTSensor entity = ConvertEntityBusinessToDADAU.ConvertAlarmSatusChannel(item);
                    
                    entity.WindTurbineID = _alarmStateDAU.Ds_asset_id;
                    
                    alarmStateDAU.AlarmStatusRTSensors.Add(entity);
                }
            }

            return alarmStateDAU;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 采集单元通道实体转换
        /// </summary>
        /// <param name="_alarmStateSensor"></param>
        /// <returns></returns>
        public static AlarmStatusRTSensor ConvertAlarmSatusChannel(WTCMSLive.BusinessEntity.RTAlarmStatus_Channel _alarmStateChan)
        {
            AlarmStatusRTSensor alarmStateChan = new AlarmStatusRTSensor();
            alarmStateChan.AlarmState = (short)_alarmStateChan.AlarmState;
            alarmStateChan.ChannelNumber = _alarmStateChan.ChannelNumber;
            alarmStateChan.DCDataValue = (float)_alarmStateChan.DCDataValue;
            alarmStateChan.WindTurbineID = _alarmStateChan.Ds_asset_id;
            alarmStateChan.StatusUpdateTime = _alarmStateChan.StatusUpdateTime;

            return alarmStateChan;
        }

        /// <summary>
        ///转速通道实体转换
        /// </summary>
        /// <param name="_alarmStateChan"></param>
        /// <returns></returns>
        public static AlarmStatusRTRSSensor ConvertAlarmSatusRSChannel(WTCMSLive.BusinessEntity.RTAlarmStatus_RSChannel _alarmStateChan)
        {
            AlarmStatusRTRSSensor alarmStateChan = new AlarmStatusRTRSSensor();
            alarmStateChan.AlarmState = (short)_alarmStateChan.AlarmState;
            alarmStateChan.ChannelNumber = _alarmStateChan.ChannelNumber;
            alarmStateChan.WindTurbineID = _alarmStateChan.WindTurbineID;
            alarmStateChan.StatusUpdateTime = _alarmStateChan.StatusUpdateTime;

            return alarmStateChan;
        }

        /// <summary>
        /// 转换DAU运行日志
        /// </summary>
        /// <param name="log"></param>
        /// <returns></returns>
        public static DAURunLog ConvertDAURunLog(BusinessEntity.DAURunLog log)
        {
            DAURunLog dauLog = new DAURunLog();
            dauLog.WindTurbineID = log.WindTurbineID;
            char[] logTile = log.LogTitle.ToCharArray();
            if (logTile.Count() > 200)
            {
                logTile = logTile.Take(200).ToArray();
            }
            dauLog.LogTitle = new string(logTile);
            dauLog.EventTime = log.EventTime;
            dauLog.AlarmState = log.AlarmState;
            return dauLog;
        }
        /// <summary>
        /// 获取转速通道实体
        /// </summary>
        /// <param name="trubineid"></param>
        /// <param name="Channelnumber"></param>
        /// <param name="MeasLocId"></param>
        /// <returns></returns>
        public static DAURotSpdChannel ConvertRotSpdChannel(string trubineid, int Channelnumber, string MeasLocId)
        {
            DAURotSpdChannel channel = new DAURotSpdChannel();
            channel.WindTurbineID = trubineid;
            channel.ChannelNumber = Channelnumber;
            channel.MeasLocationID = MeasLocId;
            return channel ;
        }

        #region  转速偏置电压
        public static AlarmStatusRTRSSensor ConvertRSChannel(WTCMSLive.BusinessEntity.RTAlarmStatus_RSChannel rsChannel)
        {
            if (rsChannel == null) return null;
            AlarmStatusRTRSSensor mySensor = new AlarmStatusRTRSSensor();
            mySensor.WindTurbineID = rsChannel.WindTurbineID;
            mySensor.ChannelNumber = rsChannel.ChannelNumber;
            mySensor.AlarmState = (short)rsChannel.AlarmState;
            mySensor.StatusUpdateTime = rsChannel.StatusUpdateTime;
            return mySensor;
        }
        #endregion
    }
}
