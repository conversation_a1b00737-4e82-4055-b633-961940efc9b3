using System.Globalization;
using System.IO.Compression;
using System.Text;
using System.Text.Json;
using WTCMSLive.WebSite.Core.DTOs;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 性能数据存储服务实现
    /// </summary>
    public class MetricsStorageService : IMetricsStorageService
    {
        private readonly ILogger<MetricsStorageService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _dataStoragePath;
        private readonly int _retentionDays;
        private readonly object _fileLock = new object();
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        public MetricsStorageService(ILogger<MetricsStorageService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _dataStoragePath = _configuration["MetricsCollection:DataStoragePath"] ?? "MetricsData";
            _retentionDays = _configuration.GetValue("MetricsCollection:RetentionDays", 30);

            // 确保数据存储目录存在
            if (!Path.IsPathRooted(_dataStoragePath))
            {
                _dataStoragePath = Path.Combine(Directory.GetCurrentDirectory(), _dataStoragePath);
            }
            Directory.CreateDirectory(_dataStoragePath);
        }

        public async Task<bool> StoreMetricsDataAsync(MetricsDataDTO data)
        {
            return await StoreBatchMetricsDataAsync(new List<MetricsDataDTO> { data });
        }

        public async Task<bool> StoreBatchMetricsDataAsync(List<MetricsDataDTO> dataList)
        {
            if (dataList == null || !dataList.Any())
                return false;

            await _semaphore.WaitAsync();
            try
            {
                var date = DateTime.Now.ToString("yyyy-MM-dd");
                var fileName = $"metrics_{date}.csv";
                var filePath = Path.Combine(_dataStoragePath, fileName);

                // 检查文件是否存在，如果不存在则写入CSV头部
                bool fileExists = File.Exists(filePath);

                using (var fileStream = new FileStream(filePath, FileMode.Append, FileAccess.Write, FileShare.Read))
                using (var writer = new StreamWriter(fileStream, Encoding.UTF8))
                {
                    // 如果是新文件，写入CSV头部
                    if (!fileExists)
                    {
                        await writer.WriteLineAsync("Timestamp,DateTime,Cpu,Memory,DiskRead,DiskWrite");
                    }

                    foreach (var data in dataList)
                    {
                        var dateTime = DateTimeOffset.FromUnixTimeMilliseconds(data.Timestamp).ToString("yyyy-MM-dd HH:mm:ss");
                        var line = $"{data.Timestamp},{dateTime},{data.Cpu.ToString("F2", CultureInfo.InvariantCulture)},{data.Memory.ToString("F2", CultureInfo.InvariantCulture)},{data.DiskRead.ToString("F2", CultureInfo.InvariantCulture)},{data.DiskWrite.ToString("F2", CultureInfo.InvariantCulture)}";
                        await writer.WriteLineAsync(line);
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[StoreBatchMetricsDataAsync]存储性能数据失败");
                return false;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        public async Task<List<MetricsDataDTO>> QueryMetricsDataAsync(MetricsQueryRequestDTO request)
        {
            var result = new List<MetricsDataDTO>();

            try
            {
                var startDate = request.StartTime.Date;
                var endDate = request.EndTime.Date;
                var currentDate = startDate;

                while (currentDate <= endDate)
                {
                    var fileName = $"metrics_{currentDate:yyyy-MM-dd}.csv";
                    var filePath = Path.Combine(_dataStoragePath, fileName);

                    if (File.Exists(filePath))
                    {
                        var fileData = await ReadCsvMetricsFileAsync(filePath);
                        var filteredData = fileData.Where(d =>
                        {
                            var dataTime = DateTimeOffset.FromUnixTimeMilliseconds(d.Timestamp).DateTime;
                            return dataTime >= request.StartTime && dataTime <= request.EndTime;
                        }).ToList();

                        result.AddRange(filteredData);
                    }

                    currentDate = currentDate.AddDays(1);
                }

                // 如果指定了采样间隔，进行数据压缩
                if (request.SampleInterval.HasValue && request.SampleInterval.Value > 1)
                {
                    result = CompressData(result, request.SampleInterval.Value);
                }

                return result.OrderBy(d => d.Timestamp).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[QueryMetricsDataAsync]查询性能数据失败");
                return new List<MetricsDataDTO>();
            }
        }

        private async Task<List<MetricsDataDTO>> ReadCsvMetricsFileAsync(string filePath)
        {
            var result = new List<MetricsDataDTO>();

            try
            {
                using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                using (var reader = new StreamReader(fileStream, Encoding.UTF8))
                {
                    // 跳过CSV头部
                    var header = await reader.ReadLineAsync();

                    string line;
                    while ((line = await reader.ReadLineAsync()) != null)
                    {
                        try
                        {
                            var parts = line.Split(',');
                            if (parts.Length >= 6)
                            {
                                var data = new MetricsDataDTO
                                {
                                    Timestamp = long.Parse(parts[0]),
                                    Cpu = double.Parse(parts[2], CultureInfo.InvariantCulture),
                                    Memory = double.Parse(parts[3], CultureInfo.InvariantCulture),
                                    DiskRead = double.Parse(parts[4], CultureInfo.InvariantCulture),
                                    DiskWrite = double.Parse(parts[5], CultureInfo.InvariantCulture)
                                };
                                result.Add(data);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "[ReadCsvMetricsFileAsync]解析CSV行失败: {Line}", line);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ReadCsvMetricsFileAsync]读取CSV文件失败: {FilePath}", filePath);
            }

            return result;
        }

        private List<MetricsDataDTO> CompressData(List<MetricsDataDTO> data, int intervalSeconds)
        {
            if (!data.Any()) return data;

            var compressed = new List<MetricsDataDTO>();
            var intervalMs = intervalSeconds * 1000;
            var groups = data.GroupBy(d => d.Timestamp / intervalMs);

            foreach (var group in groups)
            {
                var items = group.ToList();
                compressed.Add(new MetricsDataDTO
                {
                    Timestamp = items.First().Timestamp,
                    Cpu = items.Average(i => i.Cpu),
                    Memory = items.Average(i => i.Memory),
                    DiskRead = items.Average(i => i.DiskRead),
                    DiskWrite = items.Average(i => i.DiskWrite)
                });
            }

            return compressed;
        }

        public async Task<List<MetricsFileInfoDTO>> GetMetricsFileInfoAsync(DateTime startTime, DateTime endTime)
        {
            var result = new List<MetricsFileInfoDTO>();

            try
            {
                var files = Directory.GetFiles(_dataStoragePath, "metrics_*.csv");

                foreach (var filePath in files)
                {
                    var fileName = Path.GetFileName(filePath);
                    var fileInfo = new FileInfo(filePath);

                    if (IsFileInDateRange(fileName, startTime, endTime))
                    {
                        var data = await ReadCsvMetricsFileAsync(filePath);
                        var info = new MetricsFileInfoDTO
                        {
                            FileName = fileName,
                            FilePath = filePath,
                            FileSize = fileInfo.Length,
                            CreatedTime = fileInfo.CreationTime,
                            LastModifiedTime = fileInfo.LastWriteTime,
                            RecordCount = data.Count,
                            StartTime = data.Any() ? DateTimeOffset.FromUnixTimeMilliseconds(data.Min(d => d.Timestamp)).DateTime : DateTime.MinValue,
                            EndTime = data.Any() ? DateTimeOffset.FromUnixTimeMilliseconds(data.Max(d => d.Timestamp)).DateTime : DateTime.MinValue
                        };
                        result.Add(info);
                    }
                }

                return result.OrderBy(f => f.StartTime).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetMetricsFileInfoAsync]获取文件信息失败");
                return new List<MetricsFileInfoDTO>();
            }
        }

        private bool IsFileInDateRange(string fileName, DateTime startTime, DateTime endTime)
        {
            try
            {
                var dateStr = fileName.Replace("metrics_", "").Replace(".csv", "");
                if (DateTime.TryParseExact(dateStr, "yyyy-MM-dd", null, DateTimeStyles.None, out var fileDate))
                {
                    return fileDate.Date >= startTime.Date && fileDate.Date <= endTime.Date;
                }
            }
            catch
            {
                // 忽略解析错误
            }
            return false;
        }

        public async Task<MetricsDownloadResponseDTO> DownloadAllMetricsDataAsync()
        {
            try
            {
                var files = Directory.GetFiles(_dataStoragePath, "metrics_*.csv");

                if (!files.Any())
                {
                    return new MetricsDownloadResponseDTO
                    {
                        FileName = "empty_metrics.csv",
                        ContentType = "text/csv",
                        FileData = Encoding.UTF8.GetBytes("Timestamp,DateTime,Cpu,Memory,DiskRead,DiskWrite\n"),
                        RecordCount = 0,
                        TimeRange = "无数据"
                    };
                }

                // 创建一个合并的CSV文件
                var csvContent = new StringBuilder();
                csvContent.AppendLine("Timestamp,DateTime,Cpu,Memory,DiskRead,DiskWrite");

                int totalRecords = 0;
                DateTime? minTime = null;
                DateTime? maxTime = null;

                foreach (var filePath in files.OrderBy(f => f))
                {
                    try
                    {
                        using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                        using (var reader = new StreamReader(fileStream, Encoding.UTF8))
                        {
                            // 跳过CSV头部
                            await reader.ReadLineAsync();

                            string line;
                            while ((line = await reader.ReadLineAsync()) != null)
                            {
                                if (!string.IsNullOrWhiteSpace(line))
                                {
                                    csvContent.AppendLine(line);
                                    totalRecords++;

                                    // 解析时间戳以获取时间范围
                                    var parts = line.Split(',');
                                    if (parts.Length >= 2 && long.TryParse(parts[0], out var timestamp))
                                    {
                                        var dateTime = DateTimeOffset.FromUnixTimeMilliseconds(timestamp).DateTime;
                                        if (!minTime.HasValue || dateTime < minTime.Value)
                                            minTime = dateTime;
                                        if (!maxTime.HasValue || dateTime > maxTime.Value)
                                            maxTime = dateTime;
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "[DownloadAllMetricsDataAsync]读取文件失败: {FilePath}", filePath);
                    }
                }

                var fileName = $"all_metrics_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                var timeRange = minTime.HasValue && maxTime.HasValue
                    ? $"{minTime:yyyy-MM-dd HH:mm:ss} - {maxTime:yyyy-MM-dd HH:mm:ss}"
                    : "未知时间范围";

                return new MetricsDownloadResponseDTO
                {
                    FileName = fileName,
                    ContentType = "text/csv",
                    FileData = Encoding.UTF8.GetBytes(csvContent.ToString()),
                    RecordCount = totalRecords,
                    TimeRange = timeRange
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DownloadAllMetricsDataAsync]下载所有性能数据失败");
                throw;
            }
        }

        public async Task<MetricsDownloadResponseDTO> DownloadMetricsDataAsync(MetricsQueryRequestDTO request)
        {
            // 为了保持接口兼容性，这里调用下载所有数据的方法
            return await DownloadAllMetricsDataAsync();
        }

        private byte[] CompressString(string text)
        {
            var bytes = Encoding.UTF8.GetBytes(text);
            using (var memoryStream = new MemoryStream())
            {
                using (var gzipStream = new GZipStream(memoryStream, CompressionMode.Compress))
                {
                    gzipStream.Write(bytes, 0, bytes.Length);
                }
                return memoryStream.ToArray();
            }
        }

        public async Task<int> CleanupExpiredDataAsync()
        {
            var cleanedCount = 0;

            try
            {
                var cutoffDate = DateTime.Now.AddDays(-_retentionDays);
                var files = Directory.GetFiles(_dataStoragePath, "metrics_*.csv");

                foreach (var filePath in files)
                {
                    var fileName = Path.GetFileName(filePath);
                    var dateStr = fileName.Replace("metrics_", "").Replace(".csv", "");

                    if (DateTime.TryParseExact(dateStr, "yyyy-MM-dd", null, DateTimeStyles.None, out var fileDate))
                    {
                        if (fileDate < cutoffDate)
                        {
                            File.Delete(filePath);
                            cleanedCount++;
                            _logger.LogInformation("[CleanupExpiredDataAsync]删除过期文件: {FileName}", fileName);
                        }
                    }
                }

                return cleanedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[CleanupExpiredDataAsync]清理过期数据失败");
                return cleanedCount;
            }
        }

        public async Task<object> GetStorageStatisticsAsync()
        {
            try
            {
                var files = Directory.GetFiles(_dataStoragePath, "metrics_*.csv");
                var totalSize = files.Sum(f => new FileInfo(f).Length);
                var totalRecords = 0;

                foreach (var filePath in files)
                {
                    var data = await ReadCsvMetricsFileAsync(filePath);
                    totalRecords += data.Count;
                }

                return new
                {
                    TotalFiles = files.Length,
                    TotalSizeBytes = totalSize,
                    TotalSizeMB = Math.Round(totalSize / (1024.0 * 1024.0), 2),
                    TotalRecords = totalRecords,
                    RetentionDays = _retentionDays,
                    StoragePath = _dataStoragePath,
                    FileFormat = "CSV",
                    OldestFile = files.Any() ? files.Min(f => new FileInfo(f).CreationTime) : (DateTime?)null,
                    NewestFile = files.Any() ? files.Max(f => new FileInfo(f).LastWriteTime) : (DateTime?)null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetStorageStatisticsAsync]获取存储统计信息失败");
                return new { Error = ex.Message };
            }
        }
    }
}
