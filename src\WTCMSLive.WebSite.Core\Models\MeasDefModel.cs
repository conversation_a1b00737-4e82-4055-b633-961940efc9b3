﻿using CMSFramework.BusinessEntity;

namespace WTCMSLive.WebSite.Models
{
    public class DAUChannel_RotSpeedModel : DAUChannel_RotSpeed
    {
        public string MeasLocRotSpdName { get; set; }
    }

    public class Meas_MeasDef : MeasDefinition
    {
        public List<MeasLoc_Process> MeasLocList_Process { get; set; }
        public List<MeasLoc_RotSpd> MeasLocation_RotSpd { get; set; }
        public string DauName { get; set; }
        public string MeasRotSpdName { get; set; }
        public int WaveLineCounts { get; set; }
        public string DauID { get; internal set; }
        /*      public List<CMSFramework.BusinessEntity.MeasDef_Supervise_Process> MeasDef_Supervise_ProcessList { get; set; }*/
        public string SuperviseEvName { get; set; }
        public bool IsAvailable { get; set; }

        public List<ModbusUnit> modbusUnits {get;set;}
    }

    public class Dau_DAUChannelV2 : DAUChannelV2
    {
        public string MeasLocVibName { get; set; }
        public string SectionName { get; set; }
    }

    public class Dau_DAUChannel_Process : DAUChannel_Process
    {
        public string MeasLocName { get; set; }
    }


}