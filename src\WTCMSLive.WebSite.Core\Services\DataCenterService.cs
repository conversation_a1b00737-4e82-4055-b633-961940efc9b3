using System.Text;
using Newtonsoft.Json;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 数据中心服务实现
    /// </summary>
    public class DataCenterService : IDataCenterService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<DataCenterService> _logger;
        private readonly string? _baseUrl;
        private readonly int _timeoutSeconds;
        private readonly bool _isServiceAvailable;

        public DataCenterService(
            HttpClient httpClient,
            IConfiguration configuration,
            ILogger<DataCenterService> logger)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 读取配置
            _baseUrl = _configuration["DataCenter:Url"] ?? _configuration["DataCenterUrl"];
            _timeoutSeconds = _configuration.GetValue<int>("DataCenter:TimeoutSeconds", 30);

            // 检查URL配置是否可用
            if (string.IsNullOrWhiteSpace(_baseUrl))
            {
                _isServiceAvailable = false;
                _logger.LogInformation("数据中心URL配置为空，DataCenter服务不可用，将使用本地数据源");
            }
            else
            {
                _isServiceAvailable = true;
                // 配置HttpClient
                _httpClient.BaseAddress = new Uri(_baseUrl);
                _httpClient.Timeout = TimeSpan.FromSeconds(_timeoutSeconds);
                _logger.LogInformation("数据中心服务已初始化，URL: {BaseUrl}, 超时时间: {TimeoutSeconds}秒", _baseUrl, _timeoutSeconds);
            }
        }

        /// <summary>
        /// 获取集团公司代码数据
        /// </summary>
        public async Task<Dictionary<string, string>?> GetGroupCompanyCodeAsync(int? timeoutSeconds = null)
        {
            // 检查服务是否可用
            if (!_isServiceAvailable)
            {
                _logger.LogDebug("DataCenter服务不可用，返回null");
                return null;
            }

            try
            {
                var jsonResult = await GetAsync("GroupCompanyCode", null, timeoutSeconds);

                // 解析JSON响应
                var response = JsonConvert.DeserializeObject<GroupCompanyResponse>(jsonResult);

                if (response?.Code == "00000" && response.Data != null)
                {
                    var result = response.Data.ToDictionary(item => item.Name, item => item.Code);
                    return result;
                }
                else
                {
                    _logger.LogWarning("获取集团公司代码数据失败，响应: {Response}", jsonResult);
                    return null;
                }
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "解析集团公司代码数据失败，JSON: {JsonResult}", "");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取集团公司代码数据失败");
                return null ;
            }
        }

        /// <summary>
        /// 通用GET请求方法
        /// </summary>
        public async Task<string> GetAsync(string endpoint, Dictionary<string, string>? parameters = null, int? timeoutSeconds = null)
        {
            // 检查服务是否可用
            if (!_isServiceAvailable)
            {
                _logger.LogDebug("DataCenter服务不可用，无法执行GET请求: {Endpoint}", endpoint);
                return "{\"error\": \"DataCenter服务不可用\"}";
            }

            try
            {
                var url = BuildUrl(endpoint, parameters);
                var timeout = timeoutSeconds ?? _timeoutSeconds;
                _logger.LogDebug("发送GET请求: {Url}, 超时时间: {TimeoutSeconds}秒", url, timeout);

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeout));
                var response = await _httpClient.GetAsync(url, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    _logger.LogDebug("GET请求成功: {Endpoint}", endpoint);
                    return content;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("GET请求失败: {StatusCode} - {Content}", response.StatusCode, errorContent);
                    return $"{{\"error\": \"HTTP {response.StatusCode}: {errorContent}\"}}";
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("GET请求超时: {Endpoint}, 超时时间: {TimeoutSeconds}秒", endpoint, timeoutSeconds ?? _timeoutSeconds);
                return $"{{\"error\": \"请求超时，超时时间: {timeoutSeconds ?? _timeoutSeconds}秒\"}}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GET请求异常: {Endpoint}", endpoint);
                return $"{{\"error\": \"请求异常: {ex.Message}\"}}";
            }
        }

        /// <summary>
        /// 通用POST请求方法
        /// </summary>
        public async Task<string> PostAsync(string endpoint, string? jsonData = null, int? timeoutSeconds = null)
        {
            // 检查服务是否可用
            if (!_isServiceAvailable)
            {
                _logger.LogDebug("DataCenter服务不可用，无法执行POST请求: {Endpoint}", endpoint);
                return "{\"error\": \"DataCenter服务不可用\"}";
            }

            try
            {
                var timeout = timeoutSeconds ?? _timeoutSeconds;
                _logger.LogDebug("发送POST请求: {Endpoint}, 超时时间: {TimeoutSeconds}秒", endpoint, timeout);

                StringContent? content = null;
                if (!string.IsNullOrEmpty(jsonData))
                {
                    content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                }

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeout));
                var response = await _httpClient.PostAsync(endpoint, content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogDebug("POST请求成功: {Endpoint}", endpoint);
                    return responseContent;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("POST请求失败: {StatusCode} - {Content}", response.StatusCode, errorContent);
                    return $"{{\"error\": \"HTTP {response.StatusCode}: {errorContent}\"}}";
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("POST请求超时: {Endpoint}, 超时时间: {TimeoutSeconds}秒", endpoint, timeoutSeconds ?? _timeoutSeconds);
                return $"{{\"error\": \"请求超时，超时时间: {timeoutSeconds ?? _timeoutSeconds}秒\"}}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "POST请求异常: {Endpoint}", endpoint);
                return $"{{\"error\": \"请求异常: {ex.Message}\"}}";
            }
        }

        /// <summary>
        /// 构建URL
        /// </summary>
        private static string BuildUrl(string endpoint, Dictionary<string, string>? parameters)
        {
            if (parameters == null || !parameters.Any())
                return endpoint;

            var queryString = string.Join("&", parameters.Select(p => $"{Uri.EscapeDataString(p.Key)}={Uri.EscapeDataString(p.Value)}"));
            return $"{endpoint}?{queryString}";
        }
    }

    /// <summary>
    /// 集团公司响应数据模型
    /// </summary>
    internal class GroupCompanyResponse
    {
        [JsonProperty("code")]
        public string Code { get; set; } = string.Empty;

        [JsonProperty("message")]
        public string Message { get; set; } = string.Empty;

        [JsonProperty("data")]
        public List<GroupCompanyItem>? Data { get; set; }
    }

    /// <summary>
    /// 集团公司项目数据模型
    /// </summary>
    internal class GroupCompanyItem
    {
        [JsonProperty("name")]
        public string Name { get; set; } = string.Empty;

        [JsonProperty("code")]
        public string Code { get; set; } = string.Empty;
    }
}
