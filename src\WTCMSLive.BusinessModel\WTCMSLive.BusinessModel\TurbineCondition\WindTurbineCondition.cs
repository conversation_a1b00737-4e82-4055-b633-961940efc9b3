﻿using CMSFramework.BusinessEntity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace WTCMSLive.BusinessModel
{
    /// <summary>
    /// 数据库交互层 机组实时状态
    /// </summary>
    public static class WindTurbineCondition
    {
        /// <summary>
        /// 获取机组标签组列表
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <returns></returns>
        public static List<LabelGroup> GetLabelGroupList(string _turbineId)
        {
            List<LabelGroup> list = null;
            List<EigenValueData_Vib> eigenList = null;
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                eigenList = ctx.EVData_Vibs.Where(item => item.WindTurbineID == _turbineId).ToList();
            }
            using (CMSFramework.EF.OViewContext ctx = new CMSFramework.EF.OViewContext(ConfigInfo.DBConnName))
            {
                list = ctx.OViewLabelGroups.Where(item => item.WindTurbineID == _turbineId).ToList();
                List<LabelData_EigenValue> lableList = ctx.OViewLabelEVs.Where(item => item.WindTurbineID == _turbineId).ToList();
                list.ForEach(item => {
                    item.LabelDataList = lableList.OfType<LabelData>().Where(lab => lab.LabelGroupID == item.LabelGroupID).ToList();
                    item.LabelDataList.ForEach(lable => {
                        ((LabelData_EigenValue)lable).EVData = eigenList.FindAll(ev => ev.EigenValueID == ((LabelData_EigenValue)lable).EigenValueID).OrderByDescending(ev=>ev.AlarmDegree).FirstOrDefault();
                    });
                });
            }
            return list;
        }

        /// <summary>
        /// 保存标签组列表
        /// </summary>
        /// <param name="_labelGroupList"></param>
        public static void AddLabelGroupList(List<LabelGroup> _labelGroupList)
        {
            using (CMSFramework.EF.OViewContext ctx = new CMSFramework.EF.OViewContext(ConfigInfo.DBConnName))
            {
                ctx.OViewLabelGroups.AddRange(_labelGroupList);
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 添加传感器位置标签
        /// </summary>
        /// <param name="_transLoc"></param>
        public static void AddTransLocation(Lable_TransLocation _transLoc)
        {
            using (CMSFramework.EF.OViewContext ctx = new CMSFramework.EF.OViewContext(ConfigInfo.DBConnName))
            {
                ctx.OViewLabelSensorLocs.Add(_transLoc);
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 传感器位置是否存在
        /// </summary>
        /// <param name="_transLoc"></param>
        /// <returns></returns>
        public static bool IsExistTransLoc(Lable_TransLocation _transLoc)
        {
            using (CMSFramework.EF.OViewContext ctx = new CMSFramework.EF.OViewContext(ConfigInfo.DBConnName))
            {
                _transLoc = ctx.OViewLabelSensorLocs.Where(item => item.LabelGroupID == _transLoc.LabelGroupID && item.MeasLocationID == _transLoc.MeasLocationID).FirstOrDefault();
            }
            return _transLoc != null;
        }

        /// <summary>
        /// 编辑传感器位置标签
        /// </summary>
        /// <param name="_transLoc"></param>
        public static void EditTransLocation(Lable_TransLocation _transLoc)
        {
            using (CMSFramework.EF.OViewContext ctx = new CMSFramework.EF.OViewContext(ConfigInfo.DBConnName))
            {
                ctx.OViewLabelSensorLocs.Attach(_transLoc);
                ctx.Entry(_transLoc).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 根据机组ID获取机组部件实时状态列表
        /// </summary>
        /// <param name="_turID"></param>
        public static List<AlarmStatus_Component> GetCompRTAlarmStatusListByTurID(string _turID)
        {
            List<AlarmStatus_Component> alarmComponeList = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                alarmComponeList = ctx.AlarmStatus_Components.Where(item => item.WindTurbineID == _turID).ToList();
            }
            return alarmComponeList;
        }

        /// <summary>
        /// 获取机组下测量位置实时状态
        /// </summary>
        /// <param name="_turbineId"></param>
        public static List<AlarmStatus_MeasLocVib> GetMeasLocRTAlarmSatusListByTurID(string _turbineId)
        {
            List<AlarmStatus_MeasLocVib> stateList = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                stateList = ctx.AlarmStatus_MeasLocVibs.Where(item => item.WindTurbineID == _turbineId).ToList();
            }
            return stateList;
        }

        #region CMS相关接口
        /// <summary>
        /// 获取机组总貌图（带映射）
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <returns></returns>
        public static byte[] GetTurbineStyleFromMapping(string _turbineId)
        {
            byte[] byteImage = null;
            try
            {
                using (CMSFramework.EF.OViewContext ctx = new CMSFramework.EF.OViewContext(CMSFramework.BusinessEntity.ConfigInfo.DBConnName))
                {
                    var mapping = ctx.OViewTurbineMapping.FirstOrDefault(item => item.WindTurbineID == _turbineId);
                    if (mapping != null)
                    {
                        var _style = ctx.OViewTurbineStyles.FirstOrDefault(item => item.OViewGuid == mapping.OViewGuid);
                        if (_style != null)
                        {
                            byteImage = _style.StyleText;
                        }
                    }
                }
            }
            catch { return null; }
            return byteImage;
        }
        /// <summary>
        /// 获取机组总貌图实体（带映射）
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <returns></returns>
        public static CMSFramework.BusinessEntity.TurbineStyle GetTurbineStyle(string _turbineId)
        {
            CMSFramework.BusinessEntity.TurbineStyle _TurbineStyle = null;
            try
            {
                using (CMSFramework.EF.OViewContext ctx = new CMSFramework.EF.OViewContext(CMSFramework.BusinessEntity.ConfigInfo.DBConnName))
                {
                    var mapping = ctx.OViewTurbineMapping.FirstOrDefault(item => item.WindTurbineID == _turbineId);
                    if (mapping != null)
                    {
                        _TurbineStyle = ctx.OViewTurbineStyles.FirstOrDefault(item => item.OViewGuid == mapping.OViewGuid);
                    }
                }
            }
            catch { return null; }
            return _TurbineStyle;
        }

        #endregion
    }
}
