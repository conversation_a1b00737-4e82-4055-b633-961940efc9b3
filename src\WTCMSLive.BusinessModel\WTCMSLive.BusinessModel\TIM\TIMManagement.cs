﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AppFramework.IDUtility;
using AppFramework.Utility;
using CMSFramework.BusinessEntity;
using CMSFramework.DAUEntities;
using Microsoft.EntityFrameworkCore;

namespace WTCMSLive.BusinessModel.TIM
{
    public class TIMManagement
    {
 
        public static List<ModbusUnit> GetModbusunitList(string turbineID)
        {
            
            List<ModbusUnit> list = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                list = ctx.ModbusUnits.Where(item => item.WindTurbineID == turbineID).ToList();
            }
            return list;
        }

        public static ModbusUnit GetModbusunitByID(string turbineID,int modbusDeviceID)
        {

            ModbusUnit modbus = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                modbus = ctx.ModbusUnits.FirstOrDefault(item => item.WindTurbineID == turbineID && item.ModbusDeviceID == modbusDeviceID);
            }
            return modbus;
        }

        public static ModbusChannel GetModbusChannelByID(string turbineID, int modbusDeviceID)
        {

            ModbusChannel modbus = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                modbus = ctx.ModbusChannelList.FirstOrDefault(item => item.WindTurbineID == turbineID && item.ModbusDeviceID == modbusDeviceID);
            }
            return modbus;
        }

        public static ModbusChannel GetModbusChannelByMeasLocID(string turbineID, string measlocID)
        {

            ModbusChannel modbus = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                modbus = ctx.ModbusChannelList.FirstOrDefault(item => item.WindTurbineID == turbineID && item.MeasLocationID == measlocID);
            }
            return modbus;
        }

        public static void AddModbusunit(ModbusUnit modbusUnit)
        {
            try
            {
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    ctx.ModbusUnits.Add(modbusUnit);
                    ctx.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("AddModbusunit", ex);
            }
        }




        public static List<TimCalibration> GetTimUnit(string turbineID)
        {
            List<TimCalibration> res = new List<TimCalibration>();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                res = ctx.TimCalibrations.Where(item => item.WindTurbineID == turbineID ).ToList();
            }
            return res;
        }

        public static void AddTimUnit(TimCalibration timunit)
        {
            try
            {
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    ctx.TimCalibrations.Add(timunit);
                    ctx.SaveChanges();
                }
            }catch(Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("AddModbusunit", ex);
            }
        }


        public static SerialServer GetSerialServer(string turbineID, string modbusID)
        {
            SerialServer res = new SerialServer();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                res = ctx.SerialServers.FirstOrDefault(item => item.WindTurbineID == turbineID && item.ModbusUnitID == modbusID);
            }
            return res;
        }

        public static List<SerialServer> GetSerialServer(string turbineID)
        {
            List<SerialServer> res = new List<SerialServer>();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                res = ctx.SerialServers.Where(item => item.WindTurbineID == turbineID).ToList();
            }
            return res;
        }

        public static void AddSerialServer(SerialServer serialserver)
        {
            try
            {
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    ctx.SerialServers.Add(serialserver);
                    ctx.SaveChanges();
                }
            }catch(Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("AddSerialServer", ex);
            }
        }


        public static MeasDefinition_Ex GetMeasDefinitioinEx(string turbineID,string MeasDefinitionID)
        {
            MeasDefinition_Ex res = new MeasDefinition_Ex();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                res = ctx.MeasDefinitions_Exs.FirstOrDefault(item => item.WindTurbineID == turbineID && item.MeasDefinitionID == MeasDefinitionID);
            }
            return res;
        }

        public static ModbusDef GetModbusDef(string turbineID,string MeasDefinitionID,string ModbusUnitID)
        {
            ModbusDef res = new ModbusDef();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                res = ctx.ModbusDefs.FirstOrDefault(item => item.WindTurbineID == turbineID && item.MeasDefinitionID == MeasDefinitionID && item.ModbusUnitID == ModbusUnitID);
            }
            return res;
        }

        public static List<ModbusDef> GetModbusDefList(string turbineID, string MeasDefinitionID)
        {
            List<ModbusDef> res = new List<ModbusDef>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                res = ctx.ModbusDefs.Where(item => item.WindTurbineID == turbineID && item.MeasDefinitionID == MeasDefinitionID).ToList();
            }
            return res;
        }

        public static List<ModbusDef> GetModbusDefListBydeviceID(string turbineID, int deviceID)
        {
            List<ModbusDef> res = new List<ModbusDef>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                res = ctx.ModbusDefs.Where(item => item.WindTurbineID == turbineID && item.ModbusDeviceID == deviceID).ToList();
            }
            return res;
        }

        public static List<ModbusDef> GetModbusDef(string turbineID)
        {
            List<ModbusDef> res = new List<ModbusDef>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                res = ctx.ModbusDefs.Where(item => item.WindTurbineID == turbineID).ToList();
            }
            return res;
        }


        public static void AddModbusDef(List<ModbusDef> modbusDef)
        {
            try
            {
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    ctx.ModbusDefs.AddRange(modbusDef);
                    ctx.SaveChanges();
                }
            }catch(Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("AddMeaDefitionEX", ex);
            }
        }
        

        public static void AddMeaDefitionEX(List<MeasDefinition_Ex> measDefEXresult)
        {
            try
            {
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    ctx.MeasDefinitions_Exs.AddRange(measDefEXresult);
                    ctx.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("AddMeaDefitionEX", ex);
            }
        }


        public static void AddModbusDevice(
            string turbineID, string dauID, string ModbusID, 
            string modbusType, string comType, string comIP, int? comPort,
            string PortName, int? BaudRate,int? DataBit, short? Parity, short? StopBit)
        {
            try
            {
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    ctx.ModbusUnits.Add(new ModbusUnit()
                    {
                        WindTurbineID = turbineID,
                        DauID = dauID,
                        ModbusUnitID = ModbusID,
                        CommunicationMode = (EnumCommunicationMode)Convert.ToInt32(comType),
                        ModbusDevType = (EnumModbusDevType)Convert.ToInt32(modbusType),
                    });
                    ctx.SaveChanges();

                    if(comType == "0")
                    {

                        ctx.SerialServers.Add(new SerialServer()
                        {
                            ModbusUnitID = ModbusID,
                            SerialServerIP = comIP,
                            SerialServerPort = (int)comPort,
                            WindTurbineID = turbineID
                        });

                    }else if(comType == "1")
                    {
                        ctx.SerialPorts.Add(new SerialPortParam()
                        {
                            WindTurbineID = turbineID,
                            ModbusUnitID = ModbusID,
                            PortName = PortName,
                            BaudRate = BaudRate,
                            DataBit = DataBit,
                            StopBit = StopBit,
                            Parity = Parity,
                        });
                    }

                    ctx.SaveChanges();

                    // 处理各类型差异
                    switch (modbusType)
                    {
                        case "0"://倾角仪添加
                        case "1": //静态晃度仪
                        case "2": //动态晃度仪
                            // 添加svmonitor
                            //生产晃动测量位置信息
                            List<WindTurbineComponent> list = DevTreeManagement.GetComListByTurbineId(turbineID);
                            WindTurbineComponent myComponent = list.Find(i => i.ComponentName == "塔筒");
                            if (myComponent == null)
                            {
                                myComponent = new WindTurbineComponent();
                                myComponent.WindTurbineID = turbineID;
                                myComponent.ComponentName = "塔筒";
                                myComponent.ComponentID = $"{turbineID}TOW";
                                myComponent.CompManufacturer = "未知";
                                DevTreeManagement.AddDevComp(myComponent);
                            }


                            SVMManagement.AddSVM(new SVMUnit()
                            {
                                ComponentID = myComponent.ComponentID,
                                AssocWindTurbineID = turbineID,
                                SVMName = ModbusID,
                                SVMID = ModbusID,
                                ModbusAddress = ModbusID,
                            });

                            //添加timcalibration 标定使用
                            if (modbusType == "0")
                            {
                                TIMManagement.AddTimUnit(new TimCalibration()
                                {
                                    WindTurbineID = turbineID,
                                    TimUnitID = ModbusID,
                                    CalibAngleUpdateTime = DateTime.Now,
                                    CalibrationAngleX = 0,
                                    CalibrationAngleY = 0,

                                });
                            }

                            break;
                        case "3": //油液配置
                       
                            break;


                    }

                    ctx.SaveChanges();
                }
                
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("AddTimConf", ex);
            }

        }



        public static void AddTimConf(string turbineID, string dauID, string ModbusID,string modbusType,string comType, string comIP, int comPort,string section, string measdID, double timeLength, float freq,string measLoc,string oilNum,string timSec,string evList)
        {
            try
            {
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    ctx.ModbusUnits.Add(new ModbusUnit()
                    {
                        WindTurbineID = turbineID,
                        DauID = dauID,
                        ModbusUnitID = ModbusID,
                        CommunicationMode = (EnumCommunicationMode)Convert.ToInt32(comType),
                        ModbusDevType = (EnumModbusDevType)Convert.ToInt32(modbusType),
                    });
                    ctx.SaveChanges();
                  
                    if (comIP != null && comIP != "" && comPort != 0 )
                    {
                        ctx.SerialServers.Add(new SerialServer()
                        {
                            ModbusUnitID = ModbusID,
                            SerialServerIP = comIP,
                            SerialServerPort = comPort,
                            WindTurbineID = turbineID
                        });  
                    };
                    ctx.SaveChanges();

                    switch (modbusType)
                    {
                        case "0"://倾角仪添加
                        case "1": //静态晃度仪
                        case "2": //动态晃度仪
                            // 添加svmonitor
                            //生产晃动测量位置信息
                            List<WindTurbineComponent> list = DevTreeManagement.GetComListByTurbineId(turbineID);
                            WindTurbineComponent myComponent = list.Find(i => i.ComponentName == "塔筒");
                            if (myComponent == null)
                            {  
                                myComponent = new WindTurbineComponent();
                                myComponent.WindTurbineID = turbineID;
                                myComponent.ComponentName = "塔筒";
                                myComponent.ComponentID = $"{turbineID}TOW";
                                myComponent.CompManufacturer = "未知";
                                DevTreeManagement.AddDevComp(myComponent);
                            }


                            SVMManagement.AddSVM(new SVMUnit()
                            {
                                ComponentID = myComponent.ComponentID,
                                AssocWindTurbineID = turbineID,
                                SVMName = ModbusID,
                                SVMID = ModbusID,
                                ModbusAddress = ModbusID,
                            });

                            //添加timcalibration 标定使用
                            if (modbusType == "0")
                            {
                                TIMManagement.AddTimUnit(new TimCalibration()
                                {
                                    WindTurbineID = turbineID,
                                    TimUnitID = ModbusID,
                                    CalibAngleUpdateTime = DateTime.Now,
                                    CalibrationAngleX = 0,
                                    CalibrationAngleY = 0,

                                });
                            }

                            if (!string.IsNullOrEmpty(measLoc))
                            {
                                // 添加晃度测量位置
                            
                                string[] measlocList = measLoc.Split(',');

                                string[] evLists = null;
                                List<MeasDef_Ev_Vib> evconf = new List<MeasDef_Ev_Vib>();
                                if (!string.IsNullOrEmpty(evList))
                                {
                                    evLists = evList.Split(',');
                                }
                              
                                if (measlocList.Length == 0)
                                    break;
                                List<MeasLoc_SVM> measloc_svmList = new List<MeasLoc_SVM>();
                                List<MeasLoc_SVM> _measloc_svmList = new List<MeasLoc_SVM>();
                                // step1 找出用户绑定，但是没有在测点表里面存在的位置
                                string[] _measlocList = SVMManagement.GetMeasurementLocName(turbineID, measlocList, section, ModbusID);


                                if (_measlocList.Length != 0) {

                                    foreach (string measloc in _measlocList)
                                    {
                                        MeasLoc_SVM measLoc_SVM = new MeasLoc_SVM();
                                        measLoc_SVM.ComponentID = myComponent.ComponentID;
                                        measLoc_SVM.MeasLocationID = IDProvide.GetSVMLocID(turbineID, "TOW", section, measloc);
                                        measLoc_SVM.MeasLocName = measloc;
                                        measLoc_SVM.SectionName = section == "FDA" ? "塔基" : "塔顶";
                                        measLoc_SVM.WindTurbineID = turbineID;
                                        measLoc_SVM.ParamType = SVMManagement.GetParamType(measloc);
                                        _measloc_svmList.Add(measLoc_SVM);
                                    }
                             

                                    SVMManagement.AddSVMMeasLoc(turbineID, _measloc_svmList);
                               


                                }


                                // step2 添加表中不存在的测点。
                        
                                    //数据库至少有或者数据库全有
                                    foreach (string measloc in measlocList)
                                    {
                                        MeasLoc_SVM measLoc_SVM = new MeasLoc_SVM();
                                        measLoc_SVM.ComponentID = myComponent.ComponentID;
                                        measLoc_SVM.MeasLocationID = IDProvide.GetSVMLocID(turbineID, "TOW", section, measloc);
                                        measLoc_SVM.MeasLocName = measloc;
                                        measLoc_SVM.SectionName = section == "FDA" ? "塔基" : "塔顶";
                                        measLoc_SVM.WindTurbineID = turbineID;
                                        measLoc_SVM.ParamType = SVMManagement.GetParamType(measloc);
                                        measloc_svmList.Add(measLoc_SVM);

                                        
                                }

                                //添加晃度仪寄存器
                                List<SVMRegister> RegisterList = new List<SVMRegister>();
                               //List<MeasLoc_SVM> measloc_svmList = SVMManagement.GetNotUsedMeasLoc_SVMList(turbineID);
                                
                                // step 3 根据用户选定的测量位置，添加寄存器。
                                List<WaveDef_SVM> svmWaveDeflist = new List<WaveDef_SVM>();
                                for(int i = 0; i < measlocList.Length; i++)
                                {
                                    // 晃度仪寄存器
                                    MeasLoc_SVM mySVM = measloc_svmList.FirstOrDefault(k => k.MeasLocName == measlocList[i]);
                                    if(mySVM != null)
                                    {
                                        SVMRegister register = new SVMRegister();
                                        register.AssocWindTurbineID = mySVM.WindTurbineID;
                                        register.SVMRegisterAdr = SVMManagement.GetRegisterAdr(mySVM);
                                        register.ComponentID = mySVM.ComponentID;
                                        register.SVMMeasLocId = mySVM.MeasLocationID;
                                        register.RegisterType = (int)mySVM.ParamType;
                                        register.SVMID = ModbusID;
                                        RegisterList.Add(register);

                                        // 晃度仪波形定义
                                        svmWaveDeflist.Add(new WaveDef_SVM()
                                        {
                                            MeasDefinitionID = measdID,
                                            MeasLocationID = mySVM.MeasLocationID,
                                            WindTurbineID =  turbineID,
                                            WaveDefinitionID = measdID,
                                            SampleLength = (int)timeLength,
                                            SampleRate = freq,
                                            ParamType = mySVM.ParamType,
                                            WaveDefinitionName = measlocList[i],

                                        });
                                        
                                    }
                                }
                                //添加晃度仪寄存器
                                SVMManagement.AddSVMRegister(RegisterList);
                                //添加晃度仪设备波形定义
                                SVMManagement.AddWaveDefSVM(svmWaveDeflist);
                                // 添加特征值
                                
                                if (svmWaveDeflist.Count > 0)
                                {
                                    // 晃度仪特征值
                                    if (evLists != null)
                                    {
                                        foreach(var svmWave in svmWaveDeflist)
                                        {
                                            foreach (var ev in evLists)
                                            {
                                                evconf.Add(new MeasDef_Ev_Vib()
                                                {
                                                    MeasDefinitionID = svmWave.MeasDefinitionID,
                                                    MeasLocationID = svmWave.MeasLocationID,
                                                    WindTurbineID = turbineID,
                                                    WaveDefinitionID = svmWave.WaveDefinitionID,
                                                    EvType = EnumEigenValueGroupType.GeneralEV,
                                                    Type = (EnumEigenvalueName)Convert.ToInt32(ev),
                                                });
                                            }
                                        }
                                    }
                                }
                                EigenValueManage.AddMdfTimedomainEvConf(evconf);
                                UpdateMeasDefVersion(turbineID);
                            }
                            break;
                        case "3": //油液配置
                            if (!string.IsNullOrEmpty(measLoc))
                            {
                                string[] arrList = measLoc.Split(',');
                                ctx.OilUnits.Add(new OilUnit()
                                {
                                    OilUnitID = ModbusID,
                                    WindTurbineID = turbineID,
                                    ViscositySensorEnabled = arrList.Contains("ViscositySensorEnabled"),
                                    WaterSensorEnabled = arrList.Contains("WaterSensorEnabled"),
                                    WearParticleSensorEnabled = arrList.Contains("WearParticleSensorEnabled"),

                                });

                                ctx.SaveChanges();

                                UpdateMeasDefVersion(turbineID);
                            }
                            break;
                   

                    }

                    ctx.SaveChanges();
                }
                // 测量定义添加
                if(measdID!= null)
                {
                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {

                        ctx.ModbusDefs.Add(new ModbusDef()
                        {
                            WindTurbineID = turbineID,
                            ModbusUnitID = ModbusID,
                            MeasDefinitionID = measdID,
                            SampleTime = (int)timeLength,
                            SampleFrequency = freq,
                        });

                        //ctx.MeasDefinitions_Exs.Add(new MeasDefinition_Ex()
                        //{
                        //    DaqInterval = timeLength,
                        //    DauID = dauID,
                        //    MeasDefinitionID = measdID,
                        //    WindTurbineID = turbineID
                        //});
                        ctx.SaveChanges();
                    }
                }
            }
            catch(Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("AddTimConf", ex);
            }
           
        }
        public static bool UpdateMeasDefVersion(string TurbineID)
        {
            int count = 0;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                List<WindDAU> DAUList = ctx.DAUnits.Where(obj => obj.WindTurbineID == TurbineID).ToList();
                for (int i = 0; i < DAUList.Count; i++)
                {
                    if (DAUList[i] != null)
                    {
                        DAUList[i].MeasDefVersion += 1;
                        ctx.Entry(DAUList[i]).State = EntityState.Modified;
                        count += ctx.SaveChanges();
                    }
                }
            }
            return count > 0;
        }

        

        private static EnumSVMParamType GetEnumByID(string ID)
        {
            if (ID.Contains("TOWPi"))
            {
                return EnumSVMParamType.Pitch;
            }else if (ID.Contains("TOWRo"))
            {
                return EnumSVMParamType.Roll;
            }else if (ID.Contains("TOWVe"))
            {
                return EnumSVMParamType.Vertical;
            }else if (ID.Contains("TOWHo"))
            {
                return EnumSVMParamType.Horizontal;
            }else if (ID.Contains("TOWAx"))
            {
                return EnumSVMParamType.Axisl;
            }else if (ID.Contains("SVMTmp"))
            {
                return EnumSVMParamType.Temperature;
            }
            return EnumSVMParamType.Axisl;
        }

        private static string GetNameByID(string ID)
        {
            if (ID.EndsWith("Pi"))
            {
                return "俯仰角";
            }
            else if (ID.EndsWith("Ro"))
            {
                return "横滚角";
            }
            else if (ID.EndsWith("Ve"))
            {
                return "垂直加速度";
            }
            else if (ID.EndsWith("Ho"))
            {
                return "水平加速度";
            }
            else if (ID.EndsWith("Ax"))
            {
                return "轴向加速度";
            }
            else if (ID.EndsWith("T"))
            {
                return "SVM温度";
            }
            return "俯仰角";
        }

        public static void DelTimConf(string turbineID, string modbusID, string measdID, string modbusType)
        {
            try
            {
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    // 删除modbusunit
                    ModbusUnit modbusunit = ctx.ModbusUnits.FirstOrDefault(p => p.WindTurbineID == turbineID && p.ModbusUnitID == modbusID);
                    if (modbusunit != null)
                    {
                        ctx.ModbusUnits.Remove(modbusunit);
                    }
                    TimCalibration timCalibration = ctx.TimCalibrations.FirstOrDefault(p => p.WindTurbineID == turbineID && p.TimUnitID == modbusID);
                    if (timCalibration!=null)
                    {
                        ctx.TimCalibrations.Remove(timCalibration);
                    }
                    // 删除serialserver
                    SerialServer serialserver = ctx.SerialServers.FirstOrDefault(p => p.WindTurbineID == turbineID && p.ModbusUnitID == modbusID);
                    if(serialserver != null)
                    {
                        ctx.SerialServers.Remove(serialserver);
                    }

                    switch (modbusType)
                    {
                        case "0"://倾角仪
                        case "1": //静态晃度仪
                        case "2": //动态晃度仪
                                  // 删除测量定义
                                  // 删除svmmeaslocation
                            SVMManagement.DeleteSVMdefin(turbineID, measdID, modbusID);

                            if (measdID != null)
                            {
                                using (CMSFramework.EF.MeasDef.MDFContext _ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                                {
                                    var mdf = _ctx.ModbusDefs.FirstOrDefault(item => item.WindTurbineID == turbineID && item.ModbusUnitID == modbusID && item.MeasDefinitionID == measdID);
                                    if (mdf != null)
                                    {
                                        _ctx.ModbusDefs.Remove(mdf);
                                    }

                                    //var mdf_ex = ctx.MeasDefinitions_Exs.FirstOrDefault(p=>p.WindTurbineID == turbineID && p.MeasDefinitionID == measdID);
                                    //if (mdf_ex != null)
                                    //{
                                    //    ctx.MeasDefinitions_Exs.Remove(mdf_ex);
                                    //}
                                    _ctx.SaveChanges();
                                }
                            }
                       
                            // 删除svmonitor
                            // 删除晃度仪并删除绑定机组下的晃度仪测量位置
                            SVMManagement.DeleteSVMByID(turbineID, modbusID);
                            
                            UpdateMeasDefVersion(turbineID);
 
                            break;
                        case "3": //油液配置
                            // 删除油液
                            OilUnit oilunit = ctx.OilUnits.FirstOrDefault(k => k.WindTurbineID == turbineID && k.OilUnitID == modbusID);
                            if (oilunit != null)
                            {
                                ctx.OilUnits.Remove(oilunit);
                            }
                            using (CMSFramework.EF.MeasDef.MDFContext ctxx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName)) {
                                ModbusDef modbusDefData = ctxx.ModbusDefs.FirstOrDefault(m => m.WindTurbineID == turbineID && m.ModbusUnitID == modbusID);
                                if (modbusDefData != null) {
                                    ctxx.ModbusDefs.Remove(modbusDefData);
                                    ctxx.SaveChanges();
                                }                                
                            }

                            // 删除油液所有的报警定义
                            using (CMSFramework.EF.MonContext ctx2 = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
                            {
                                //var oilloca = new string[] { "ViscositySensor", "WaterSensor", "WearParticleSensor" };
                                //var alarm = ctx2.AlarmDefLists.Where(t => t.WindTurbineID == turbineID && oilloca.Any(oil => t.MeasLocationID.Contains(oil))).ToList();
                                var oillocal = new string[] { "ViscositySensor", "WaterSensor", "WearParticleSensor" };
                                var alarm = ctx2.AlarmDefLists
                                    .Where(t => t.WindTurbineID == turbineID)
                                    .ToList() // 将数据加载到内存中,避免数据库差异
                                    .Where(t => oillocal.Any(oil => t.MeasLocationID.Contains(oil)))
                                    .ToList();
                                if (alarm != null && alarm.Count > 0)
                                {
                                    foreach (var al in alarm)
                                    {
                                        ctx2.AlarmDefThresholdGroupLists.RemoveRange(ctx2.AlarmDefThresholdGroupLists.Where(item => item.ThresholdGroup == al.ThresholdGroup));
                                    }
                                    ctx2.AlarmDefLists.RemoveRange(alarm);
                                }
                                ctx2.SaveChanges();
                            }
                            UpdateMeasDefVersion(turbineID);
                            break;
                    }
                    ctx.SaveChanges();
                }

             
            }catch(Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("DelTimConf", ex);
            }
        }

        public static List<OilUnit> GetOilconfigByturID(string turbineID)
        {
            try
            {
                List<OilUnit> oilunit = new List<OilUnit>();
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    oilunit = ctx.OilUnits.Where(k => k.WindTurbineID == turbineID).ToList();
                }
                return oilunit;
            }catch(Exception ex)
            {
                return new List<OilUnit>();
            }
        }

        public static void AddOilconfig(List<OilUnit> oilunit)
        {
            try
            {
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    ctx.OilUnits.AddRange(oilunit);
                    ctx.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("AddOilconfigByturID", ex);
            }
        }
    }
}
