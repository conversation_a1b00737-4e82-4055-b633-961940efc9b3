﻿using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Management;

namespace WTCMSLive.WebSite.Core.Models
{
    public static class MetricsManager
    {
        private static readonly bool IsWindows = RuntimeInformation.IsOSPlatform(OSPlatform.Windows);

        #region CPU
        private static readonly object CpuLock = new();
        private static PerformanceCounter _cpuCounter;
        private static DateTime _lastCpuSampleTime = DateTime.MinValue;
        private static double _lastCpuPercent = 0;

        public static double GetCpuUsage()
        {
            lock (CpuLock)
            {
                try
                {
                    if (IsWindows)
                    {
                        return GetWindowsCpuUsage();
                    }
                    else
                    {
                        return GetLinuxCpuUsage();
                    }
                }
                catch (Exception)
                {
                    return _lastCpuPercent;
                }
            }
        }

        private static double GetWindowsCpuUsage()
        {
            try
            {
                // 初始化CPU性能计数器
                if (_cpuCounter == null)
                {
                    _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                    _cpuCounter.NextValue(); // 第一次调用返回0
                    _lastCpuSampleTime = DateTime.UtcNow;
                    return 0;
                }

                var now = DateTime.UtcNow;
                var elapsed = (now - _lastCpuSampleTime).TotalMilliseconds;

                // 至少间隔100ms才采样
                if (elapsed < 100)
                {
                    return _lastCpuPercent;
                }

                var cpuUsage = _cpuCounter.NextValue();
                _lastCpuPercent = Math.Round(Math.Max(0, Math.Min(100, cpuUsage)), 2);
                _lastCpuSampleTime = now;

                return _lastCpuPercent;
            }
            catch
            {
                return 0;
            }
        }

        private static double GetLinuxCpuUsage()
        {
            try
            {
                var statPath = "/proc/stat";
                if (!File.Exists(statPath))
                    return 0;

                var lines = File.ReadAllLines(statPath);
                var cpuLine = lines.FirstOrDefault(l => l.StartsWith("cpu "));
                if (string.IsNullOrEmpty(cpuLine))
                    return 0;

                var parts = cpuLine.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length < 8)
                    return 0;

                // CPU时间字段：user, nice, system, idle, iowait, irq, softirq, steal
                var user = long.Parse(parts[1]);
                var nice = long.Parse(parts[2]);
                var system = long.Parse(parts[3]);
                var idle = long.Parse(parts[4]);
                var iowait = long.Parse(parts[5]);
                var irq = long.Parse(parts[6]);
                var softirq = long.Parse(parts[7]);

                var totalTime = user + nice + system + idle + iowait + irq + softirq;
                var idleTime = idle + iowait;
                var activeTime = totalTime - idleTime;

                // 使用静态变量存储上次的值
                if (!_linuxCpuStats.HasValue)
                {
                    _linuxCpuStats = (totalTime, activeTime);
                    return 0;
                }

                var (lastTotal, lastActive) = _linuxCpuStats.Value;
                var totalDiff = totalTime - lastTotal;
                var activeDiff = activeTime - lastActive;

                _linuxCpuStats = (totalTime, activeTime);

                if (totalDiff <= 0)
                    return _lastCpuPercent;

                var cpuPercent = (double)activeDiff / totalDiff * 100.0;
                _lastCpuPercent = Math.Round(Math.Max(0, Math.Min(100, cpuPercent)), 2);

                return _lastCpuPercent;
            }
            catch
            {
                return 0;
            }
        }

        private static (long total, long active)? _linuxCpuStats = null;

        // 清理资源的方法
        public static void Dispose()
        {
            _cpuCounter?.Dispose();
        }
        #endregion

        #region Memory
        public static double GetMemoryUsagePercent()
        {
            try
            {
                if (IsWindows)
                {
                    return GetWindowsMemoryUsage();
                }
                else
                {
                    return GetLinuxMemoryUsage();
                }
            }
            catch (Exception)
            {
                return 0;
            }
        }

        /// <summary>
        /// 获取系统内存使用率（百分比）
        /// 注意：此方法现在返回内存使用率百分比，而不是MB数值
        /// </summary>
        /// <returns>内存使用率百分比 (0-100)</returns>
        public static double GetMemoryMB()
        {
            try
            {
                if (IsWindows)
                {
                    return GetWindowsMemoryUsage();
                }
                else
                {
                    return GetLinuxMemoryUsage();
                }
            }
            catch (Exception)
            {
                return 0;
            }
        }

        private static double GetWindowsMemoryUsage()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var totalMemory = Convert.ToDouble(obj["TotalVisibleMemorySize"]) * 1024; // KB to Bytes
                        var freeMemory = Convert.ToDouble(obj["FreePhysicalMemory"]) * 1024; // KB to Bytes
                        var usedMemory = totalMemory - freeMemory;

                        return Math.Round((usedMemory / totalMemory) * 100, 2);
                    }
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }



        private static double GetLinuxMemoryUsage()
        {
            try
            {
                var meminfoPath = "/proc/meminfo";
                if (!File.Exists(meminfoPath))
                    return 0;

                var lines = File.ReadAllLines(meminfoPath);
                long totalMemory = 0, freeMemory = 0, buffers = 0, cached = 0;

                foreach (var line in lines)
                {
                    if (line.StartsWith("MemTotal:"))
                        long.TryParse(line.Split(':')[1].Trim().Split(' ')[0], out totalMemory);
                    else if (line.StartsWith("MemFree:"))
                        long.TryParse(line.Split(':')[1].Trim().Split(' ')[0], out freeMemory);
                    else if (line.StartsWith("Buffers:"))
                        long.TryParse(line.Split(':')[1].Trim().Split(' ')[0], out buffers);
                    else if (line.StartsWith("Cached:"))
                        long.TryParse(line.Split(':')[1].Trim().Split(' ')[0], out cached);
                }

                if (totalMemory <= 0)
                    return 0;

                // 可用内存 = 空闲内存 + 缓冲区 + 缓存
                var availableMemory = freeMemory + buffers + cached;
                var usedMemory = totalMemory - availableMemory;

                return Math.Round((double)usedMemory / totalMemory * 100, 2);
            }
            catch
            {
                return 0;
            }
        }


        #endregion

        #region Disk IO
        private static readonly object DiskLock = new();
        private static long _lastReadBytes;
        private static long _lastWriteBytes;
        private static DateTime _lastDiskTime = DateTime.UtcNow;
        private static readonly Dictionary<string, (long read, long write)> _lastDiskStats = new();

        public static (double readMBs, double writeMBs) GetDiskIoRate()
        {
            lock (DiskLock)
            {
                try
                {
                    long totalReadBytes = 0, totalWriteBytes = 0;

                    if (IsWindows)
                    {
                        // Windows: 使用性能计数器获取系统总磁盘IO
                        totalReadBytes = GetWindowsDiskReadBytes();
                        totalWriteBytes = GetWindowsDiskWriteBytes();
                    }
                    else
                    {
                        // Linux: 读取 /proc/diskstats 获取系统总磁盘IO
                        (totalReadBytes, totalWriteBytes) = GetLinuxDiskIoBytes();
                    }

                    var now = DateTime.UtcNow;
                    var elapsed = (now - _lastDiskTime).TotalSeconds;

                    // 第一次采样，仅做初始化
                    if (_lastDiskTime == DateTime.UtcNow || elapsed <= 0)
                    {
                        _lastReadBytes = totalReadBytes;
                        _lastWriteBytes = totalWriteBytes;
                        _lastDiskTime = now;
                        return (0, 0);
                    }

                    // 转换为MB/s (1MB = 1024*1024 bytes)
                    var readRate = Math.Round((totalReadBytes - _lastReadBytes) / (1024.0 * 1024.0) / elapsed, 2);
                    var writeRate = Math.Round((totalWriteBytes - _lastWriteBytes) / (1024.0 * 1024.0) / elapsed, 2);

                    _lastReadBytes = totalReadBytes;
                    _lastWriteBytes = totalWriteBytes;
                    _lastDiskTime = now;

                    return (Math.Max(0, readRate), Math.Max(0, writeRate));
                }
                catch (Exception)
                {
                    // 发生异常时返回0，避免程序崩溃
                    return (0, 0);
                }
            }
        }

        private static long GetWindowsDiskReadBytes()
        {
            try
            {
                long totalReadBytes = 0;

                // 使用PerformanceCounter获取磁盘读取字节数
                using (var pc = new PerformanceCounter("PhysicalDisk", "Disk Read Bytes/sec", "_Total"))
                {
                    pc.NextValue(); // 第一次调用返回0，需要调用两次
                    Thread.Sleep(100); // 短暂等待
                    var bytesPerSec = pc.NextValue();
                    totalReadBytes = (long)(bytesPerSec * 0.1); // 转换为累计字节数的近似值
                }

                // 如果PerformanceCounter不可用，尝试使用WMI
                if (totalReadBytes == 0)
                {
                    totalReadBytes = GetWindowsDiskBytesViaWMI(true);
                }

                return totalReadBytes;
            }
            catch
            {
                return 0;
            }
        }

        private static long GetWindowsDiskWriteBytes()
        {
            try
            {
                long totalWriteBytes = 0;

                // 使用PerformanceCounter获取磁盘写入字节数
                using (var pc = new PerformanceCounter("PhysicalDisk", "Disk Write Bytes/sec", "_Total"))
                {
                    pc.NextValue(); // 第一次调用返回0，需要调用两次
                    Thread.Sleep(100); // 短暂等待
                    var bytesPerSec = pc.NextValue();
                    totalWriteBytes = (long)(bytesPerSec * 0.1); // 转换为累计字节数的近似值
                }

                // 如果PerformanceCounter不可用，尝试使用WMI
                if (totalWriteBytes == 0)
                {
                    totalWriteBytes = GetWindowsDiskBytesViaWMI(false);
                }

                return totalWriteBytes;
            }
            catch
            {
                return 0;
            }
        }

        private static long GetWindowsDiskBytesViaWMI(bool isRead)
        {
            try
            {
                if (!IsWindows) return 0;

                long totalBytes = 0;
                var propertyName = isRead ? "DiskReadBytesPersec" : "DiskWriteBytesPersec";

                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PerfRawData_PerfDisk_PhysicalDisk WHERE Name='_Total'"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var value = obj[propertyName];
                        if (value != null && long.TryParse(value.ToString(), out var bytes))
                        {
                            totalBytes += bytes;
                        }
                    }
                }

                return totalBytes;
            }
            catch
            {
                return 0;
            }
        }

        private static (long readBytes, long writeBytes) GetLinuxDiskIoBytes()
        {
            try
            {
                long totalReadBytes = 0, totalWriteBytes = 0;

                // 读取 /proc/diskstats 文件
                var diskStatsPath = "/proc/diskstats";
                if (!File.Exists(diskStatsPath))
                    return (0, 0);

                var lines = File.ReadAllLines(diskStatsPath);
                foreach (var line in lines)
                {
                    var parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length >= 14)
                    {
                        var deviceName = parts[2];

                        // 只统计物理磁盘，跳过分区和虚拟设备
                        if (IsPhysicalDisk(deviceName))
                        {
                            // 字段说明：
                            // parts[5] = 读取扇区数
                            // parts[9] = 写入扇区数
                            // 每个扇区通常是512字节
                            if (long.TryParse(parts[5], out var readSectors) &&
                                long.TryParse(parts[9], out var writeSectors))
                            {
                                totalReadBytes += readSectors * 512;
                                totalWriteBytes += writeSectors * 512;
                            }
                        }
                    }
                }

                return (totalReadBytes, totalWriteBytes);
            }
            catch
            {
                return (0, 0);
            }
        }

        private static bool IsPhysicalDisk(string deviceName)
        {
            // 判断是否为物理磁盘设备
            // 通常物理磁盘名称如：sda, sdb, nvme0n1, hda, vda等
            // 跳过分区（如sda1, sdb2）和虚拟设备（如loop, ram等）

            if (string.IsNullOrEmpty(deviceName))
                return false;

            // 跳过明显的虚拟设备
            var virtualDevices = new[] { "loop", "ram", "dm-", "md", "sr", "fd" };
            if (virtualDevices.Any(vd => deviceName.StartsWith(vd)))
                return false;

            // 跳过分区（包含数字的通常是分区）
            if (deviceName.Any(char.IsDigit))
            {
                // 但是nvme设备例外，如nvme0n1是物理设备，nvme0n1p1才是分区
                if (deviceName.StartsWith("nvme") && deviceName.Contains("n") && !deviceName.Contains("p"))
                    return true;
                return false;
            }

            return true;
        }
        #endregion
    }
}
