﻿namespace WTCMSLive.WebSite.Models
{
    public class OilAnalyzeConfig
    {
        public string WindTurbineID { get; set; }
        public string SensorType { get; set; }
        public string SettingName { get; set; }
        public string SettingID { get; set; }
        public int TimeInterval { get; set; }
        public string Content { get; set; }
        public float AlarmNum { get; set; }
        public float AlarmNumRate { get; set; }
        public float WarnNum { get; set; }
        public float WarnNumRate { get; set; }
        public string GroupID { get; set; }
    }


    public class Oilparticledata
    {
        public string WindTurbineID { get; set; }
        public string OilUnitID { get; set; }
        public DateTime OilAcquisitionTime { get; set; }
        public int Ferromagnetic1 { get; set; }
        public int Ferromagnetic2 { get; set; }
        public int Ferromagnetic3 { get; set; }
        public int Ferromagnetic4 { get; set; }
        public int Ferromagnetic5 { get; set; }
        public int Nonferromagnetic1 { get; set; }
        public int Nonferromagnetic2 { get; set; }
        public int Nonferromagnetic3 { get; set; }
        public int Nonferromagnetic4 { get; set; }
        public int Nonferromagnetic5 { get; set; }
    }
    public class Oilwaterdata
    {
        public string WindTurbineID { get; set; }
        public string OilUnitID { get; set; }
        public DateTime OilAcquisitionTime { get; set; }
        public int WaterContent { get; set; }
        public int WaterActivity { get; set; }
        public int OilTempFromWaterSensor { get; set; }
    }
    public class Oilviscositydata
    {
        public string WindTurbineID { get; set; }
        public string OilUnitID { get; set; }
        public DateTime OilAcquisitionTime { get; set; }
        public int DynamicViscosity { get; set; }
        public int KinematicViscosity { get; set; }
        public int Density { get; set; }
        public int Permittivity { get; set; }
        public int OilTempFromViscositySensor { get; set; }
    }
}