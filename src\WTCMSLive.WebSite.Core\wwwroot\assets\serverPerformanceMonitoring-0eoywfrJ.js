import{r as m,y as z,w as E,dE as F,f as g,d as i,z as _,u as $,o as s,i as r,c as u,F as A,e as R,g as d,t as h,b as o,s as V,m as W,aR as j}from"./index-BjOW8S1L.js";import{W as X,c as y}from"./table-RP3jLHlo.js";import{u as q}from"./serverManager-CDkfMT4F.js";import{s as b}from"./useWebSocket-Br1zZvLi.js";import{L as v}from"./index-BXWgJpPi.js";import{_ as J}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as K}from"./tools-zTE6InS0.js";import{B as Q}from"./index-7iPMz_Qy.js";import"./index-CzSbT6op.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";import"./styleChecker-CFtINSLw.js";import"./initDefaultProps-P4j1rGDC.js";import"./index-DTxROkTj.js";import"./shallowequal-gCpTBdTi.js";import"./index-BJEkaghg.js";import"./ActionButton-C_grUmdF.js";import"./index-kP-mINcM.js";const Z={class:"header"},tt={class:"baseInfo"},et={class:"content"},at={class:"list"},nt={class:"monitorBtns"},rt={key:0},st={class:"chartbox"},ot={class:"chartbox"},it={class:"chartbox"},B=100,lt={__name:"serverPerformanceMonitoring",setup(ct){const C=q(),U=$();j("noHeaderBorder",!0);const p={showToolbox:!1,noShowlegend:!0,grid:{left:20,bottom:15,top:30,right:60,containLabel:!0},areaStyle:{opacity:.1}},k=t=>[{name:"cpu利用率",data:t.cpuUsage?`${t.cpuUsage.usagePercentage}%`:""},{name:"cpu核数",data:t.cpuUsage?t.cpuUsage.processorCount:""},{name:"memory利用率",data:t.memoryUsage?`${t.memoryUsage.usagePercentage}%`:""},{name:"memory总大小(GB)",data:t.memoryUsage?t.memoryUsage.totalMemoryMB:""}],M=t=>[{name:"时间",data:t.timestamp?_(t.timestamp).format("YYYY-MM-DD HH:mm:ss"):""},{name:"服务器系统类型",data:t.operatingSystem?t.operatingSystem:""}],f=m(!1),S=m(k({})),x=m(M({})),I=m([]),l=m(!1),e=z({chartInformation1:{title:"memory利用率",legendArr:["memory利用率(%)"]},chartInformation2:{title:"cpu利用率",legendArr:["cpu利用率(%)"]},chartInformation3:{title:"disk读写速度",legendArr:["disk读速度(MB)","disk写速度(MB)"]},chartDatas1:{time:[],lineData:[{line:[]}]},chartDatas2:{time:[],lineData:[{line:[]}]},chartDatas3:{time:[],lineData:[{line:[]},{line:[]}]}}),Y=async()=>{f.value=!0;let t=await C.fetchGetServerPerformance();S.value=k(t||{}),x.value=M(t||{}),t&&(I.value=t.diskUsage||[]),f.value=!1},L=async()=>{P()},w=async()=>{l.value&&(await b.stopConnection(),l.value=!1,e.chartDatas1={time:[],lineData:[{line:[]}]},e.chartDatas2={time:[],lineData:[{line:[]}]},e.chartDatas3={time:[],lineData:[{line:[]},{line:[]}]})},P=async()=>{const t=await b.startConnection("/Hubs/ServerPerformanceHub");l.value=t,t?b.onReceiveMessage("ReceiveMetrics",a=>{O(a)}):W.error("连接失败")},O=t=>{if(!t)return;let a=_(t.timestamp).format("YYYY-MM-DD HH:mm:ss");e.chartDatas1.time.length>=B&&(e.chartDatas1.time.shift(),e.chartDatas1.lineData[0].line.shift()),e.chartDatas1.time.push(a),e.chartDatas1.lineData[0].line.push(t.memory),e.chartDatas2.time.length>=B&&(e.chartDatas2.time.shift(),e.chartDatas2.lineData[0].line.shift()),e.chartDatas2.time.push(a),e.chartDatas2.lineData[0].line.push(t.cpu),e.chartDatas3.time.length>=B&&(e.chartDatas3.time.shift(),e.chartDatas3.lineData[0].line.shift(),e.chartDatas3.lineData[1].line.shift()),e.chartDatas3.time.push(a),e.chartDatas3.lineData[0].line.push(t.diskRead),e.chartDatas3.lineData[1].line.push(t.diskWrite)};E(()=>U.params.id,()=>{Y()},{immediate:!0}),F(()=>{w()});const G=async()=>{let c="\uFEFF"+await C.fetchServerPerformanceDownload();const D=new Blob([c],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a"),T=URL.createObjectURL(D);n.setAttribute("href",T);let N=_(new Date).format("YYYY-MM-DD");n.setAttribute("download",`近一个月服务器性能数据_${N}.csv`),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)},H=[{title:"盘符",dataIndex:"driveName",align:"center"},{title:"使用率",dataIndex:"usagePercentage",align:"center",customRender:({record:t})=>t.usagePercentage?`${t.usagePercentage}%`:""},{title:"总大小(GB)",dataIndex:"totalSizeGB",align:"center"},{title:"已使用(GB)",dataIndex:"usedSizeGB",align:"center"},{title:"未使用(GB)",dataIndex:"freeSizeGB",align:"center"}];return(t,a)=>{const c=Q,D=K;return s(),g(D,{spinning:f.value,size:"large"},{default:i(()=>[r("h3",Z,[a[1]||(a[1]=r("b",{class:"title"},"服务器性能",-1)),r("div",tt,[(s(!0),u(A,null,R(x.value,n=>(s(),u("span",{key:n.name},[d(h(n.name)+": ",1),r("b",null,h(n.data),1)]))),128))])]),r("div",et,[r("ul",at,[(s(!0),u(A,null,R(S.value,n=>(s(),u("li",{key:n.name},[d(h(n.name)+" ",1),r("b",null,h(n.data),1)]))),128))]),r("div",nt,[l.value?(s(),g(c,{key:1,onClick:w,type:"primary"},{default:i(()=>a[3]||(a[3]=[d("关闭性能监控",-1)])),_:1,__:[3]})):(s(),g(c,{key:0,onClick:L},{default:i(()=>a[2]||(a[2]=[d("开启性能监控",-1)])),_:1,__:[2]})),o(c,{type:"primary",onClick:a[0]||(a[0]=n=>G()),title:"下载近一个月服务器性能数据"},{default:i(()=>a[4]||(a[4]=[d(" 下载 ",-1)])),_:1,__:[4]})]),o(X,{tableTitle:"disk使用情况","table-key":"0","table-columns":H,"table-datas":I.value,noBatchApply:!0,actionCloumnProps:{width:170,align:"center"}},null,8,["table-datas"]),l.value?(s(),u("div",rt,[o(y,{tableTitle:"memory利用率",defaultCollapse:!0,batchApply:!1},{content:i(()=>[r("div",st,[o(v,{boxId:"chart1",chartOptions:{...p,unit:["time","利用率(%)"]},informations:e.chartInformation1,noResetChart:!0,chartData:e.chartDatas1},null,8,["chartOptions","informations","chartData"])])]),_:1}),o(y,{tableTitle:"cpu利用率",defaultCollapse:!0,batchApply:!1},{content:i(()=>[r("div",ot,[o(v,{boxId:"chart2",chartOptions:{...p,unit:["time","利用率(%)"]},informations:e.chartInformation2,noResetChart:!0,chartData:e.chartDatas2},null,8,["chartOptions","informations","chartData"])])]),_:1}),o(y,{tableTitle:"disk读写速度",defaultCollapse:!0,batchApply:!1},{content:i(()=>[r("div",it,[o(v,{boxId:"chart3",chartOptions:{...p,unit:["time","读写速度(MB)"]},informations:e.chartInformation3,noResetChart:!0,chartData:e.chartDatas3},null,8,["chartOptions","informations","chartData"])])]),_:1})])):V("",!0)])]),_:1},8,["spinning"])}}},It=J(lt,[["__scopeId","data-v-f5331808"]]);export{It as default};
