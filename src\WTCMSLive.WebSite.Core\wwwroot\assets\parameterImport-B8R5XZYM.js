import{W as R}from"./table-RP3jLHlo.js";import{O as J}from"./index-CzSbT6op.js";import{C as V,Q as Z,R as $,S as P,T as A,r as i,y as K,w as Q,f as T,d as u,u as j,o as C,i as a,b as l,g as v,x as I,c as G,m as p}from"./index-BjOW8S1L.js";import{_ as H}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as X}from"./tools-zTE6InS0.js";import{_ as Y}from"./index-9RFCYCf2.js";import{B as ee}from"./index-7iPMz_Qy.js";import{U}from"./UploadOutlined-BLiqGWw7.js";import{M as te}from"./index-BnSFuLp6.js";import"./ActionButton-C_grUmdF.js";import"./styleChecker-CFtINSLw.js";import"./initDefaultProps-P4j1rGDC.js";import"./shallowequal-gCpTBdTi.js";import"./index-DTxROkTj.js";import"./index-kP-mINcM.js";import"./index-BJEkaghg.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";import"./useRefs-CX1uwt0r.js";const oe=V("parameterImport",{state:()=>({modelList:[],modelOptions:[]}),actions:{reset(){this.$reset()},async fetchCopyTurbineBoltBD(n){try{return await A(n)}catch(t){throw console.error("获取失败:",t),t}},async fetchBoltBDFileUpdate(n){try{return await P(n)}catch(t){throw console.error("操作失败:",t),t}},async fetchBoltJZFileUpdate(n){try{return await $(n)}catch(t){throw console.error("操作失败:",t),t}},async fetchEditBoltBD(n){try{return await Z(n)}catch(t){throw console.error("操作失败:",t),t}}}}),ae={class:"uploadFileBox"},le={class:"uploadBox"},se={class:"uploadItem"},re={class:"uploadItem"},ne={key:1},ie={__name:"parameterImport",setup(n){const t=oe(),k=j(),_=i(""),f=i(""),m=i({}),b=i([]),B=i(!1),y=i(!1),d=i([]),c=i([]),w=K({tableData:[]}),h=async()=>{B.value=!0,w.tableData=await t.fetchCopyTurbineBoltBD(),B.value=!1};Q(()=>k.params.id,async o=>{h()},{immediate:!0});const L=o=>(d.value=[o],!1),S=o=>(c.value=[o],!1),W=o=>{(!o.fileList||o.fileList.length===0)&&(d.value=[])},M=o=>{(!o.fileList||o.fileList.length===0)&&(c.value=[])},q=async o=>{const{rowData:e,tableKey:s,operateType:r,title:x}=o;f.value=r,m.value={...e},_.value="编辑偏移量",b.value=[F[4]],z()},E=async o=>{const e=await t.fetchEditBoltBD({...m.value,offsetCoeff:o.offsetCoeff});e&&e.code===1?(p.success("编辑成功"),h(),g()):p.error("编辑失败"+e.msg)},D=async o=>{let e=[];if(o==="1"?e=[...d.value]:e=[...c.value],e.length===0){p.error("请选择文件");return}const s=new FormData;s.append("file",e[0]);let r={};o==="1"?r=await t.fetchBoltJZFileUpdate(s):r=await t.fetchBoltBDFileUpdate(s),r.code===1?(o==="1"?(p.success("导入螺栓基准成功"),d.value=[]):(p.success("导入螺栓标定成功"),c.value=[]),h()):p.error("上传失败！"+r.msg)},F=[{title:"厂站",dataIndex:"windParkName",columnWidth:80},{title:"螺栓型号",dataIndex:"boltModel",columnWidth:100},{title:"预紧力系数",dataIndex:"preloadCalCoeffs",columnWidth:70},{title:"温度系数",dataIndex:"tempCalibCoeff",columnWidth:110},{title:"偏移量",dataIndex:"offsetCoeff",columnWidth:100}],z=()=>{y.value=!0},g=o=>{y.value=!1,b.value=[],m.value={},_.value="",f.value=""};return(o,e)=>{const s=ee,r=Y,x=te,N=X;return C(),T(N,{spinning:B.value,size:"large"},{default:u(()=>[a("div",null,[a("div",ae,[a("ul",le,[a("li",null,[e[4]||(e[4]=a("span",null,"上传: ",-1)),a("div",se,[l(r,{"file-list":d.value,"before-upload":L,accept:".zip",onChange:W},{default:u(()=>[l(s,null,{default:u(()=>[l(I(U)),e[2]||(e[2]=v(" 导入螺栓基准 ",-1))]),_:1,__:[2]})]),_:1},8,["file-list"])]),l(s,{type:"primary",class:"uploadFileBtn",onClick:e[0]||(e[0]=O=>D("1"))},{default:u(()=>e[3]||(e[3]=[v("确定",-1)])),_:1,__:[3]})]),a("li",null,[e[7]||(e[7]=a("span",null,"上传: ",-1)),a("div",re,[l(r,{"file-list":c.value,"before-upload":S,accept:".zip",onChange:M},{default:u(()=>[l(s,null,{default:u(()=>[l(I(U)),e[5]||(e[5]=v(" 导入螺栓标定 ",-1))]),_:1,__:[5]})]),_:1},8,["file-list"])]),l(s,{type:"primary",class:"uploadFileBtn",onClick:e[1]||(e[1]=O=>D("2"))},{default:u(()=>e[6]||(e[6]=[v("确定",-1)])),_:1,__:[6]})])])]),a("div",null,[l(R,{ref:"table",size:"default","table-key":"0","table-title":"螺栓标定批量配置列表","table-columns":F,"table-operate":["edit"],"record-key":"boltModel","table-datas":w.tableData,noBatchApply:!0,onEditRow:q},null,8,["table-datas"])]),l(x,{maskClosable:!1,width:"600px",open:y.value,title:_.value,footer:"",onCancel:g},{default:u(()=>[f.value==="add"||f.value==="edit"?(C(),T(J,{key:0,titleCol:b.value,initFormData:m.value,onSubmit:E},null,8,["titleCol","initFormData"])):(C(),G("div",ne))]),_:1},8,["open","title"])])]),_:1},8,["spinning"])}}},Ie=H(ie,[["__scopeId","data-v-49066881"]]);export{Ie as default};
