import{O as D}from"./index-CzSbT6op.js";import{W as T}from"./table-RP3jLHlo.js";import{z as o,r as l,y as v,w as M,f as p,d as _,A as F,u as x,o as u,b as C,aR as R}from"./index-BjOW8S1L.js";import{u as S}from"./serverManager-CDkfMT4F.js";import{S as I}from"./tools-zTE6InS0.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";import"./styleChecker-CFtINSLw.js";import"./index-7iPMz_Qy.js";import"./initDefaultProps-P4j1rGDC.js";import"./index-DTxROkTj.js";import"./shallowequal-gCpTBdTi.js";import"./index-BJEkaghg.js";import"./ActionButton-C_grUmdF.js";import"./index-kP-mINcM.js";const J={__name:"log",setup(k){const m=S(),d=x(),f=()=>[{title:"用户名",dataIndex:"operator"},{title:"内容",dataIndex:"logContent"},{title:"操作",dataIndex:"logTitle"},{title:"时间",dataIndex:"eventTime",headerOperations:{sorter:!0,date:!0},customRender:({record:t})=>t.eventTime?F("span",{},o(t.eventTime).format("YYYY-MM-DD HH:mm:ss")):""}];R("noHeaderBorder",!0);const g=[{title:"选择时间",dataIndex:"timeRange",inputType:"rangePicker",timeFormat:["YYYY-MM-DD","YYYY-MM-DD"],noMinLabelWidth:!0,isrequired:!0},{title:"日志筛选",dataIndex:"logType",inputType:"select",formItemWidth:"200",selectOptions:[]}];let c=[o().subtract(3,"month"),o()];const r=l({timeRange:c}),n=l(!1),i=l(!1),a=v({formlist:g,tableDatas:[],tableColumns:f()}),s=async t=>{n.value=!0;let e={beginTime:o(t.timeRange[0]).format("YYYY-MM-DD"),endTime:o(t.timeRange[1]).format("YYYY-MM-DD")};t.logType&&(e={...e,logType:t.logType}),a.tableDatas=await m.fetchHomeSystemLog(e),n.value=!1},y=async()=>{let t=await m.fetchGetSystemLogType();if(t&&t.length){let e=[...a.formlist];e[1].selectOptions=[...t],a.formlist=e}};M(()=>d.params.id,()=>{y(),s(r.value)},{immediate:!0});const Y=async t=>{s(t)},h=()=>{r.value={timeRange:c},s(r.value),i.value=!i.value};return(t,e)=>{const b=I;return u(),p(b,{spinning:n.value,size:"large"},{default:_(()=>[(u(),p(D,{key:i.value,titleCol:a.formlist,initFormData:r.value,formlayout:"inline",actions:["cancel"],onCancelForm:h,onSubmit:Y},null,8,["titleCol","initFormData"])),C(T,{"table-key":"0","table-columns":a.tableColumns,noheader:!0,noBatchApply:!0,"record-key":"guid","table-datas":a.tableDatas},null,8,["table-columns","table-datas"])]),_:1},8,["spinning"])}}};export{J as default};
