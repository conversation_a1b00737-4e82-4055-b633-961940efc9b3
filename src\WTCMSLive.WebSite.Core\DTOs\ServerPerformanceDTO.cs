namespace WTCMSLive.WebSite.Core.DTOs
{
    /// <summary>
    /// 服务器性能监控DTO
    /// </summary>
    public class ServerPerformanceDTO
    {
        /// <summary>
        /// CPU使用率信息
        /// </summary>
        public CpuUsageDTO CpuUsage { get; set; }

        /// <summary>
        /// 内存使用率信息
        /// </summary>
        public MemoryUsageDTO MemoryUsage { get; set; }

        /// <summary>
        /// 磁盘使用率信息列表
        /// </summary>
        public List<DiskUsageDTO> DiskUsage { get; set; } = new List<DiskUsageDTO>();

        /// <summary>
        /// 数据采集时间
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 服务器名称
        /// </summary>
        public string ServerName { get; set; }

        /// <summary>
        /// 操作系统信息
        /// </summary>
        public string OperatingSystem { get; set; }
    }

    /// <summary>
    /// CPU使用率DTO
    /// </summary>
    public class CpuUsageDTO
    {
        /// <summary>
        /// CPU使用率百分比
        /// </summary>
        public double UsagePercentage { get; set; }

        /// <summary>
        /// 处理器核心数
        /// </summary>
        public int ProcessorCount { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 内存使用率DTO
    /// </summary>
    public class MemoryUsageDTO
    {
        /// <summary>
        /// 内存使用率百分比
        /// </summary>
        public double UsagePercentage { get; set; }

        /// <summary>
        /// 总内存大小（MB）
        /// </summary>
        public double TotalMemoryMB { get; set; }

        /// <summary>
        /// 已使用内存大小（MB）
        /// </summary>
        public double UsedMemoryMB { get; set; }

        /// <summary>
        /// 可用内存大小（MB）
        /// </summary>
        public double AvailableMemoryMB { get; set; }

        /// <summary>
        /// 当前进程工作集内存（MB）
        /// </summary>
        public double ProcessWorkingSetMB { get; set; }

        /// <summary>
        /// GC管理的内存（MB）
        /// </summary>
        public double GCMemoryMB { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 磁盘使用率DTO
    /// </summary>
    public class DiskUsageDTO
    {
        /// <summary>
        /// 驱动器名称（如 C:\, /dev/sda1）
        /// </summary>
        public string DriveName { get; set; }

        /// <summary>
        /// 驱动器类型
        /// </summary>
        public string DriveType { get; set; }

        /// <summary>
        /// 文件系统类型
        /// </summary>
        public string FileSystem { get; set; }

        /// <summary>
        /// 磁盘使用率百分比
        /// </summary>
        public double UsagePercentage { get; set; }

        /// <summary>
        /// 总容量（GB）
        /// </summary>
        public double TotalSizeGB { get; set; }

        /// <summary>
        /// 已使用容量（GB）
        /// </summary>
        public double UsedSizeGB { get; set; }

        /// <summary>
        /// 可用容量（GB）
        /// </summary>
        public double FreeSizeGB { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
