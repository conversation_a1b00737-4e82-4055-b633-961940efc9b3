﻿using CMSFramework.BusinessEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace WTCMSLive.BusinessModel
{
    public static class DiagnosisReportManagement
    {
        /// <summary>
        /// 添加机组维修报告
        /// </summary>
        /// <param name="_maintainReport"></param>
        public static void AddTurMaintainReport(TurbineMaintainReport _maintainReport)
        {
            using (CMSFramework.EF.DiagContext ctx = new CMSFramework.EF.DiagContext(ConfigInfo.DBConnName))
            {
                ctx.TurbineReportMaintainRecords.Add(_maintainReport);
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 根据机组ID获取诊断报告列表
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <returns></returns>
        public static List<TurbineDiagnosisReport> GetDiagnosisReportListByTurId(string _turbineId)
        {
            List<TurbineDiagnosisReport> list = null;
            using (CMSFramework.EF.DiagContext ctx = new CMSFramework.EF.DiagContext(ConfigInfo.DBConnName))
            {
                list = ctx.TurbineReportDiagnosisRecords.Where(item => item.WindTurbineID == _turbineId).OrderBy(item => item.DiagnosisTime).ToList();
                ctx.SaveChanges();
            }
            return list;
        }

        /// <summary>
        /// 获取机组下的诊断信息
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <returns></returns>
        public static TurbineDiagnosisReport GetLastDiagnosisReport(string _turbineId)
        {
            TurbineDiagnosisReport diagnosisReport = null;
            using (CMSFramework.EF.DiagContext ctx = new CMSFramework.EF.DiagContext(ConfigInfo.DBConnName))
            {
                diagnosisReport = ctx.TurbineReportDiagnosisRecords.Where(item => item.WindTurbineID == _turbineId).OrderByDescending(item => item.DiagnosisTime).FirstOrDefault();
                ctx.SaveChanges();
            }
            return diagnosisReport;
        }


        /// <summary>
        /// 根据机组ID获取维修报告列表
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <returns></returns>
        public static List<TurbineMaintainReport> GetMaintainReportListByTurId(string _turbineId)
        {
            List<TurbineMaintainReport> maintainReportList = null;
            using (CMSFramework.EF.DiagContext ctx = new CMSFramework.EF.DiagContext(ConfigInfo.DBConnName))
            {
                maintainReportList = ctx.TurbineReportMaintainRecords.Where(item => item.WindTurbineID == _turbineId).OrderBy(item => item.HandleTime).ToList();
                ctx.SaveChanges();
            }
            return maintainReportList;
        }

        /// <summary>
        /// 获取机组下的维修报告信息
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <returns></returns>
        public static TurbineMaintainReport GetLastMaintainReport(string _turbineId)
        {
            TurbineMaintainReport maintainReport = null;
            using (CMSFramework.EF.DiagContext ctx = new CMSFramework.EF.DiagContext(ConfigInfo.DBConnName))
            {
                maintainReport = ctx.TurbineReportMaintainRecords.Where(item => item.WindTurbineID == _turbineId).OrderByDescending(item => item.HandleTime).FirstOrDefault();
                ctx.SaveChanges();
            }
            return maintainReport;
        }
    }
}
