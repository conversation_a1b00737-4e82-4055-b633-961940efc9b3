﻿using System.Runtime.InteropServices;

namespace DataSecurity
{
    unsafe internal class DataEncryption
    {
        [DllImport("DatEnDe.dll", EntryPoint = "DataEnCrypt", CallingConvention = CallingConvention.Cdecl)]
        private static extern void DataEnCrypt(IntPtr pbyteArray, int length, ushort* pErrorCode);

        [DllImport("DatEnDe.dll", EntryPoint = "DataDeCrypt", CallingConvention = CallingConvention.Cdecl)]
        private static extern void DataDeCrypt(IntPtr pbyteArray, int length, ushort* pErrorCode);

        /// <summary>
        /// 数据加密
        /// </summary>
        /// <param name="_bytes"></param>
        //internal static void EncryptData(byte[] _srcData)
        //{
        //    IntPtr pSrc = Marshal.UnsafeAddrOfPinnedArrayElement(_srcData, 0);
        //    ushort errorCode = 0;
        //    ushort* pCode = &errorCode;
        //    DataEnCrypt(pSrc, _srcData.Length, pCode);
        //    if (errorCode != 0)
        //    {
        //        throw new Exception($"Encrypt Data error. Error code is { errorCode }");
        //    }
        //}


        public static byte[] EncryptData(byte[] srcData)
        {
            var bakArray = new byte[srcData.Length];
            Buffer.BlockCopy(srcData, 0, bakArray, 0, srcData.Length);
            IntPtr pSrc = Marshal.UnsafeAddrOfPinnedArrayElement(bakArray, 0);
            ushort errorCode = 0;
            ushort* pCode = &errorCode;
            DataEnCrypt(pSrc, bakArray.Length, pCode);

            if (errorCode != 0)
            {
                throw new Exception($"Encrypt data error. Error code is {errorCode}");
            }
            return bakArray;
        }


        /// <summary>
        /// 数据解密
        /// </summary>
        /// <param name="_bytes"></param>
        //internal static void DecryptData(byte[] _srcData)
        //{
        //    IntPtr pSrc = Marshal.UnsafeAddrOfPinnedArrayElement(_srcData, 0);
        //    ushort errorCode = 0;
        //    ushort* pCode = &errorCode;
        //    DataDeCrypt(pSrc, _srcData.Length, pCode);
        //    if (errorCode != 0)
        //    {
        //        throw new Exception($"Decrypt Data error. Error code is { errorCode }");
        //    }
        //}

        public static byte[] DecryptData(byte[] srcData)
        {
            var bakArray = new byte[srcData.Length];
            Buffer.BlockCopy(srcData, 0, bakArray, 0, srcData.Length);
            IntPtr pSrc = Marshal.UnsafeAddrOfPinnedArrayElement(bakArray, 0);
            ushort errorCode = 0;
            ushort* pCode = &errorCode;
            DataDeCrypt(pSrc, bakArray.Length, pCode);
            if (errorCode != 0)
            {
                throw new Exception($"Decrypt data error. Error code is {errorCode}");
            }
            return bakArray;
        }
    }
}
