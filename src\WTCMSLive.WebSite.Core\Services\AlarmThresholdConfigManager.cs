using WTCMSLive.WebSite.Core.Models;
using CMSFramework.BusinessEntity;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 报警阈值配置管理器
    /// </summary>
    public static class AlarmThresholdConfigManager
    {
        /// <summary>
        /// 根据部件名称和特征值代码获取振动报警阈值
        /// </summary>
        /// <param name="componentName">部件名称</param>
        /// <param name="eigenValueCode">特征值代码</param>
        /// <param name="sectionName">截面名称（可选）</param>
        /// <param name="orientation">方向（可选）</param>
        /// <returns></returns>
        public static ThresholdValues GetVibrationThreshold(string componentName, string eigenValueCode, 
            string sectionName = "", string orientation = "")
        {
            try
            {
                var configFile = AlarmThreshold_ConfigFile.GetAlarmThresholdConfigFile();
                var thresholdList = configFile.VibrationAlarmThreshold?.ThresholdList;
                
                if (thresholdList == null || !thresholdList.Any())
                {
                    return GetDefaultVibrationThreshold();
                }

                // 查找匹配的配置项
                var matchedItem = thresholdList.FirstOrDefault(item =>
                    item.ComponentName.Equals(componentName, StringComparison.OrdinalIgnoreCase) &&
                    item.EigenValueCode.Equals(eigenValueCode, StringComparison.OrdinalIgnoreCase) &&
                    (string.IsNullOrWhiteSpace(item.SectionName) || item.SectionName.Equals(sectionName, StringComparison.OrdinalIgnoreCase)) &&
                    (string.IsNullOrWhiteSpace(item.Orientation) || item.Orientation.Equals(orientation, StringComparison.OrdinalIgnoreCase))
                );

                if (matchedItem != null)
                {
                    return new ThresholdValues
                    {
                        ForwardWarning = matchedItem.ForwardWarning,
                        ForwardAlarm = matchedItem.ForwardAlarm,
                        ReverseWarning = matchedItem.ReverseWarning,
                        ReverseAlarm = matchedItem.ReverseAlarm,
                        WorkCondition = matchedItem.WorkCondition
                    };
                }

                CMSFramework.Logger.Logger.LogInfoMessage($"未找到匹配的振动报警阈值配置: {componentName}-{eigenValueCode}，使用默认值");
                return GetDefaultVibrationThreshold();
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"获取振动报警阈值配置失败: {componentName}-{eigenValueCode}", ex);
                return GetDefaultVibrationThreshold();
            }
        }

        /// <summary>
        /// 根据特征值代码获取晃度报警阈值
        /// </summary>
        /// <param name="eigenValueCode">特征值代码</param>
        /// <param name="componentName">部件名称（可选）</param>
        /// <returns></returns>
        public static ThresholdValues GetSVMThreshold(string eigenValueCode, string componentName = "")
        {
            try
            {
                var configFile = AlarmThreshold_ConfigFile.GetAlarmThresholdConfigFile();
                var thresholdList = configFile.SVMAlarmThreshold?.ThresholdList;
                
                if (thresholdList == null || !thresholdList.Any())
                {
                    return GetDefaultSVMThreshold();
                }

                // 查找匹配的配置项
                var matchedItem = thresholdList.FirstOrDefault(item =>
                    item.EigenValueCode.Equals(eigenValueCode, StringComparison.OrdinalIgnoreCase) &&
                    (string.IsNullOrWhiteSpace(componentName) || item.ComponentName.Equals(componentName, StringComparison.OrdinalIgnoreCase))
                );

                if (matchedItem != null)
                {
                    return new ThresholdValues
                    {
                        ForwardWarning = matchedItem.ForwardWarning,
                        ForwardAlarm = matchedItem.ForwardAlarm,
                        ReverseWarning = matchedItem.ReverseWarning,
                        ReverseAlarm = matchedItem.ReverseAlarm,
                        WorkCondition = matchedItem.WorkCondition
                    };
                }

                CMSFramework.Logger.Logger.LogInfoMessage($"未找到匹配的晃度报警阈值配置: {eigenValueCode}，使用默认值");
                return GetDefaultSVMThreshold();
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"获取晃度报警阈值配置失败: {eigenValueCode}", ex);
                return GetDefaultSVMThreshold();
            }
        }

        /// <summary>
        /// 根据特征值代码获取油液报警阈值
        /// </summary>
        /// <param name="eigenValueCode">特征值代码</param>
        /// <param name="componentName">部件名称（可选）</param>
        /// <returns></returns>
        public static ThresholdValues GetOilThreshold(string eigenValueCode, string componentName = "")
        {
            try
            {
                var configFile = AlarmThreshold_ConfigFile.GetAlarmThresholdConfigFile();
                var thresholdList = configFile.OilAlarmThreshold?.ThresholdList;
                
                if (thresholdList == null || !thresholdList.Any())
                {
                    return GetDefaultOilThreshold();
                }

                // 查找匹配的配置项
                var matchedItem = thresholdList.FirstOrDefault(item =>
                    item.EigenValueCode.Equals(eigenValueCode, StringComparison.OrdinalIgnoreCase) &&
                    (string.IsNullOrWhiteSpace(componentName) || item.ComponentName.Equals(componentName, StringComparison.OrdinalIgnoreCase))
                );

                if (matchedItem != null)
                {
                    return new ThresholdValues
                    {
                        ForwardWarning = matchedItem.ForwardWarning,
                        ForwardAlarm = matchedItem.ForwardAlarm,
                        ReverseWarning = matchedItem.ReverseWarning,
                        ReverseAlarm = matchedItem.ReverseAlarm,
                        WorkCondition = matchedItem.WorkCondition
                    };
                }

                CMSFramework.Logger.Logger.LogInfoMessage($"未找到匹配的油液报警阈值配置: {eigenValueCode}，使用默认值");
                return GetDefaultOilThreshold();
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"获取油液报警阈值配置失败: {eigenValueCode}", ex);
                return GetDefaultOilThreshold();
            }
        }

        /// <summary>
        /// 解析工况参数
        /// </summary>
        /// <param name="workConditionStr">工况参数字符串</param>
        /// <returns></returns>
        public static EnumWorkCondition_ParamType ParseWorkCondition(string workConditionStr)
        {
            if (string.IsNullOrWhiteSpace(workConditionStr))
                return EnumWorkCondition_ParamType.WCPT_NOWORKCONDTION;

            switch (workConditionStr.ToUpper())
            {
                case "WCPT_POWER":
                    return EnumWorkCondition_ParamType.WCPT_Power;
                case "WCPT_ROTSPEED":
                    return EnumWorkCondition_ParamType.WCPT_RotSpeed;
                case "WCPT_BLADE_01TEMP":
                    return EnumWorkCondition_ParamType.WCPT_Blade_01Temp;
                case "WCPT_BLADE_02TEMP":
                    return EnumWorkCondition_ParamType.WCPT_Blade_02Temp;
                case "WCPT_BLADE_03TEMP":
                    return EnumWorkCondition_ParamType.WCPT_Blade_03Temp;
                case "WCPT_NOWORKCONDTION":
                default:
                    return EnumWorkCondition_ParamType.WCPT_NOWORKCONDTION;
            }
        }

        /// <summary>
        /// 获取默认振动报警阈值
        /// </summary>
        /// <returns></returns>
        private static ThresholdValues GetDefaultVibrationThreshold()
        {
            return new ThresholdValues
            {
                ForwardWarning = 2.0,
                ForwardAlarm = 4.0,
                ReverseWarning = null,
                ReverseAlarm = null,
                WorkCondition = "WCPT_NOWORKCONDTION"
            };
        }

        /// <summary>
        /// 获取默认晃度报警阈值
        /// </summary>
        /// <returns></returns>
        private static ThresholdValues GetDefaultSVMThreshold()
        {
            return new ThresholdValues
            {
                ForwardWarning = 0.5,
                ForwardAlarm = 1.0,
                ReverseWarning = null,
                ReverseAlarm = null,
                WorkCondition = "WCPT_NOWORKCONDTION"
            };
        }

        /// <summary>
        /// 获取默认油液报警阈值
        /// </summary>
        /// <returns></returns>
        private static ThresholdValues GetDefaultOilThreshold()
        {
            return new ThresholdValues
            {
                ForwardWarning = 50.0,
                ForwardAlarm = 100.0,
                ReverseWarning = null,
                ReverseAlarm = null,
                WorkCondition = "WCPT_NOWORKCONDTION"
            };
        }
    }
}
