import{f as n,d as o,a as u,o as c,b as p,g as i,t as m}from"./index-BjOW8S1L.js";import{R as _}from"./index-DgvSlLW6.js";import{B as l}from"./index-7iPMz_Qy.js";import"./initDefaultProps-P4j1rGDC.js";const x={__name:"404",setup(f){const e=u(),s=()=>{e.push("/")};return(t,b)=>{const a=l,r=_;return c(),n(r,{status:"404",title:"404","sub-title":t.$t("tip.404")},{extra:o(()=>[p(a,{type:"primary",onClick:s},{default:o(()=>[i(m(t.$t("button.backHome")),1)]),_:1})]),_:1},8,["sub-title"])}}};export{x as default};
