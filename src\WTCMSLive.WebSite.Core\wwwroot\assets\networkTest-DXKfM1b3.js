import{W as Ye}from"./table-RP3jLHlo.js";import{af as H,ae as He,a4 as re,r as F,j as z,b as g,aP as le,aE as Pe,a0 as k,au as q,a5 as Le,h as ke,w as ae,ag as _t,aR as st,aa as ne,ar as ye,aS as me,ao as St,Z as Ct,_ as Tt,a1 as ct,aL as wt,a2 as dt,aQ as It,aI as Pt,aT as kt,a7 as Bt,az as Lt,aB as Dt,ap as Te,at as At,aq as Rt,C as Et,aU as Nt,aV as Mt,aW as Ot,aX as Wt,aY as Ht,u as zt,y as Gt,f as Xt,d as j,o as Vt,i as ue,g as we,m as $e}from"./index-BjOW8S1L.js";import{S as jt,h as Ft}from"./tools-zTE6InS0.js";import{_ as Ut}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{b as Ke,w as xe,u as M,d as We,B as qt}from"./index-7iPMz_Qy.js";import{K as J,i as Ze}from"./shallowequal-gCpTBdTi.js";import{u as Yt,E as Kt,D as Zt,M as Qt,_ as Jt}from"./ActionButton-C_grUmdF.js";import{i as ea,R as Qe,a as ta,o as aa}from"./styleChecker-CFtINSLw.js";import{u as na}from"./useRefs-CX1uwt0r.js";import{c as ut,t as ia,a as oa,d as la,h as ra,f as sa}from"./index-CpBSPak5.js";import{i as ca,u as Je}from"./index-CzSbT6op.js";import{i as vt}from"./initDefaultProps-P4j1rGDC.js";import"./index-DTxROkTj.js";import"./index-kP-mINcM.js";import"./index-BJEkaghg.js";import"./index-D3J_dKbJ.js";function da(e,t,a,o){if(!Ke(e))return e;t=ut(t,e);for(var n=-1,l=t.length,i=l-1,v=e;v!=null&&++n<l;){var b=ia(t[n]),h=a;if(b==="__proto__"||b==="constructor"||b==="prototype")return e;if(n!=i){var $=v[b];h=void 0,h===void 0&&(h=Ke($)?$:ea(t[n+1])?[]:{})}oa(v,b,h),v=v[b]}return e}function ua(e,t,a){for(var o=-1,n=t.length,l={};++o<n;){var i=t[o],v=la(e,i);a(v,i)&&da(l,ut(i,e),v)}return l}function va(e,t){return ua(e,t,function(a,o){return ra(e,o)})}var pt=sa(function(e,t){return e==null?{}:va(e,t)});function pa(e){const t=H(),a=H(!1);function o(){for(var n=arguments.length,l=new Array(n),i=0;i<n;i++)l[i]=arguments[i];a.value||(xe.cancel(t.value),t.value=xe(()=>{e(...l)}))}return He(()=>{a.value=!0,xe.cancel(t.value)}),o}function fa(e){const t=H([]),a=H(typeof e=="function"?e():e),o=pa(()=>{let l=a.value;t.value.forEach(i=>{l=i(l)}),t.value=[],a.value=l});function n(l){t.value.push(l),o()}return[a,n]}const ba=re({compatConfig:{MODE:3},name:"TabNode",props:{id:{type:String},prefixCls:{type:String},tab:{type:Object},active:{type:Boolean},closable:{type:Boolean},editable:{type:Object},onClick:{type:Function},onResize:{type:Function},renderWrapper:{type:Function},removeAriaLabel:{type:String},onFocus:{type:Function}},emits:["click","resize","remove","focus"],setup(e,t){let{expose:a,attrs:o}=t;const n=F();function l(b){var h;!((h=e.tab)===null||h===void 0)&&h.disabled||e.onClick(b)}a({domRef:n});function i(b){var h;b.preventDefault(),b.stopPropagation(),e.editable.onEdit("remove",{key:(h=e.tab)===null||h===void 0?void 0:h.key,event:b})}const v=z(()=>{var b;return e.editable&&e.closable!==!1&&!(!((b=e.tab)===null||b===void 0)&&b.disabled)});return()=>{var b;const{prefixCls:h,id:$,active:w,tab:{key:x,tab:p,disabled:T,closeIcon:s},renderWrapper:c,removeAriaLabel:f,editable:C,onFocus:y}=e,I=`${h}-tab`,r=g("div",{key:x,ref:n,class:le(I,{[`${I}-with-remove`]:v.value,[`${I}-active`]:w,[`${I}-disabled`]:T}),style:o.style,onClick:l},[g("div",{role:"tab","aria-selected":w,id:$&&`${$}-tab-${x}`,class:`${I}-btn`,"aria-controls":$&&`${$}-panel-${x}`,"aria-disabled":T,tabindex:T?null:0,onClick:d=>{d.stopPropagation(),l(d)},onKeydown:d=>{[J.SPACE,J.ENTER].includes(d.which)&&(d.preventDefault(),l(d))},onFocus:y},[typeof p=="function"?p():p]),v.value&&g("button",{type:"button","aria-label":f||"remove",tabindex:0,class:`${I}-remove`,onClick:d=>{d.stopPropagation(),i(d)}},[(s==null?void 0:s())||((b=C.removeIcon)===null||b===void 0?void 0:b.call(C))||"×"])]);return c?c(r):r}}}),et={width:0,height:0,left:0,top:0};function ha(e,t){const a=F(new Map);return Pe(()=>{var o,n;const l=new Map,i=e.value,v=t.value.get((o=i[0])===null||o===void 0?void 0:o.key)||et,b=v.left+v.width;for(let h=0;h<i.length;h+=1){const{key:$}=i[h];let w=t.value.get($);w||(w=t.value.get((n=i[h-1])===null||n===void 0?void 0:n.key)||et);const x=l.get($)||k({},w);x.right=b-x.left-x.width,l.set($,x)}a.value=new Map(l)}),a}const ft=re({compatConfig:{MODE:3},name:"AddButton",inheritAttrs:!1,props:{prefixCls:String,editable:{type:Object},locale:{type:Object,default:void 0}},setup(e,t){let{expose:a,attrs:o}=t;const n=F();return a({domRef:n}),()=>{const{prefixCls:l,editable:i,locale:v}=e;return!i||i.showAdd===!1?null:g("button",{ref:n,type:"button",class:`${l}-nav-add`,style:o.style,"aria-label":(v==null?void 0:v.addAriaLabel)||"Add tab",onClick:b=>{i.onEdit("add",{event:b})}},[i.addIcon?i.addIcon():"+"])}}}),ga={prefixCls:{type:String},id:{type:String},tabs:{type:Object},rtl:{type:Boolean},tabBarGutter:{type:Number},activeKey:{type:[String,Number]},mobile:{type:Boolean},moreIcon:Le.any,moreTransitionName:{type:String},editable:{type:Object},locale:{type:Object,default:void 0},removeAriaLabel:String,onTabClick:{type:Function},popupClassName:String,getPopupContainer:q()},ma=re({compatConfig:{MODE:3},name:"OperationNode",inheritAttrs:!1,props:ga,emits:["tabClick"],slots:Object,setup(e,t){let{attrs:a,slots:o}=t;const[n,l]=M(!1),[i,v]=M(null),b=p=>{const T=e.tabs.filter(f=>!f.disabled);let s=T.findIndex(f=>f.key===i.value)||0;const c=T.length;for(let f=0;f<c;f+=1){s=(s+p+c)%c;const C=T[s];if(!C.disabled){v(C.key);return}}},h=p=>{const{which:T}=p;if(!n.value){[J.DOWN,J.SPACE,J.ENTER].includes(T)&&(l(!0),p.preventDefault());return}switch(T){case J.UP:b(-1),p.preventDefault();break;case J.DOWN:b(1),p.preventDefault();break;case J.ESC:l(!1);break;case J.SPACE:case J.ENTER:i.value!==null&&e.onTabClick(i.value,p);break}},$=z(()=>`${e.id}-more-popup`),w=z(()=>i.value!==null?`${$.value}-${i.value}`:null),x=(p,T)=>{p.preventDefault(),p.stopPropagation(),e.editable.onEdit("remove",{key:T,event:p})};return ke(()=>{ae(i,()=>{const p=document.getElementById(w.value);p&&p.scrollIntoView&&p.scrollIntoView(!1)},{flush:"post",immediate:!0})}),ae(n,()=>{n.value||v(null)}),Yt({}),()=>{var p;const{prefixCls:T,id:s,tabs:c,locale:f,mobile:C,moreIcon:y=((p=o.moreIcon)===null||p===void 0?void 0:p.call(o))||g(Kt,null,null),moreTransitionName:I,editable:r,tabBarGutter:d,rtl:u,onTabClick:S,popupClassName:A}=e;if(!c.length)return null;const L=`${T}-dropdown`,X=f==null?void 0:f.dropdownAriaLabel,se={[u?"marginRight":"marginLeft"]:d};c.length||(se.visibility="hidden",se.order=1);const ce=le({[`${L}-rtl`]:u,[`${A}`]:!0}),ve=C?null:g(Zt,{prefixCls:L,trigger:["hover"],visible:n.value,transitionName:I,onVisibleChange:l,overlayClassName:ce,mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:e.getPopupContainer},{overlay:()=>g(Qt,{onClick:D=>{let{key:ee,domEvent:O}=D;S(ee,O),l(!1)},id:$.value,tabindex:-1,role:"listbox","aria-activedescendant":w.value,selectedKeys:[i.value],"aria-label":X!==void 0?X:"expanded dropdown"},{default:()=>[c.map(D=>{var ee,O;const Y=r&&D.closable!==!1&&!D.disabled;return g(Jt,{key:D.key,id:`${$.value}-${D.key}`,role:"option","aria-controls":s&&`${s}-panel-${D.key}`,disabled:D.disabled},{default:()=>[g("span",null,[typeof D.tab=="function"?D.tab():D.tab]),Y&&g("button",{type:"button","aria-label":e.removeAriaLabel||"remove",tabindex:0,class:`${L}-menu-item-remove`,onClick:K=>{K.stopPropagation(),x(K,D.key)}},[((ee=D.closeIcon)===null||ee===void 0?void 0:ee.call(D))||((O=r.removeIcon)===null||O===void 0?void 0:O.call(r))||"×"])]})})]}),default:()=>g("button",{type:"button",class:`${T}-nav-more`,style:se,tabindex:-1,"aria-hidden":"true","aria-haspopup":"listbox","aria-controls":$.value,id:`${s}-more`,"aria-expanded":n.value,onKeydown:h},[y])});return g("div",{class:le(`${T}-nav-operations`,a.class),style:a.style},[ve,g(ft,{prefixCls:T,locale:f,editable:r},null)])}}}),bt=Symbol("tabsContextKey"),$a=e=>{st(bt,e)},ht=()=>_t(bt,{tabs:F([]),prefixCls:F()}),ya=.1,tt=.01,Ie=20,at=Math.pow(.995,Ie);function xa(e,t){const[a,o]=M(),[n,l]=M(0),[i,v]=M(0),[b,h]=M(),$=F();function w(r){const{screenX:d,screenY:u}=r.touches[0];o({x:d,y:u}),clearInterval($.value)}function x(r){if(!a.value)return;r.preventDefault();const{screenX:d,screenY:u}=r.touches[0],S=d-a.value.x,A=u-a.value.y;t(S,A),o({x:d,y:u});const L=Date.now();v(L-n.value),l(L),h({x:S,y:A})}function p(){if(!a.value)return;const r=b.value;if(o(null),h(null),r){const d=r.x/i.value,u=r.y/i.value,S=Math.abs(d),A=Math.abs(u);if(Math.max(S,A)<ya)return;let L=d,X=u;$.value=setInterval(()=>{if(Math.abs(L)<tt&&Math.abs(X)<tt){clearInterval($.value);return}L*=at,X*=at,t(L*Ie,X*Ie)},Ie)}}const T=F();function s(r){const{deltaX:d,deltaY:u}=r;let S=0;const A=Math.abs(d),L=Math.abs(u);A===L?S=T.value==="x"?d:u:A>L?(S=d,T.value="x"):(S=u,T.value="y"),t(-S,-S)&&r.preventDefault()}const c=F({onTouchStart:w,onTouchMove:x,onTouchEnd:p,onWheel:s});function f(r){c.value.onTouchStart(r)}function C(r){c.value.onTouchMove(r)}function y(r){c.value.onTouchEnd(r)}function I(r){c.value.onWheel(r)}ke(()=>{var r,d;document.addEventListener("touchmove",C,{passive:!1}),document.addEventListener("touchend",y,{passive:!1}),(r=e.value)===null||r===void 0||r.addEventListener("touchstart",f,{passive:!1}),(d=e.value)===null||d===void 0||d.addEventListener("wheel",I,{passive:!1})}),He(()=>{document.removeEventListener("touchmove",C),document.removeEventListener("touchend",y)})}function nt(e,t){const a=F(e);function o(n){const l=typeof n=="function"?n(a.value):n;l!==a.value&&t(l,a.value),a.value=l}return[a,o]}const it={width:0,height:0,left:0,top:0,right:0},_a=()=>({id:{type:String},tabPosition:{type:String},activeKey:{type:[String,Number]},rtl:{type:Boolean},animated:ye(),editable:ye(),moreIcon:Le.any,moreTransitionName:{type:String},mobile:{type:Boolean},tabBarGutter:{type:Number},renderTabBar:{type:Function},locale:ye(),popupClassName:String,getPopupContainer:q(),onTabClick:{type:Function},onTabScroll:{type:Function}}),Sa=(e,t)=>{const{offsetWidth:a,offsetHeight:o,offsetTop:n,offsetLeft:l}=e,{width:i,height:v,x:b,y:h}=e.getBoundingClientRect();return Math.abs(i-a)<1?[i,v,b-t.x,h-t.y]:[a,o,l,n]},ot=re({compatConfig:{MODE:3},name:"TabNavList",inheritAttrs:!1,props:_a(),slots:Object,emits:["tabClick","tabScroll"],setup(e,t){let{attrs:a,slots:o}=t;const{tabs:n,prefixCls:l}=ht(),i=H(),v=H(),b=H(),h=H(),[$,w]=na(),x=z(()=>e.tabPosition==="top"||e.tabPosition==="bottom"),[p,T]=nt(0,(_,m)=>{x.value&&e.onTabScroll&&e.onTabScroll({direction:_>m?"left":"right"})}),[s,c]=nt(0,(_,m)=>{!x.value&&e.onTabScroll&&e.onTabScroll({direction:_>m?"top":"bottom"})}),[f,C]=M(0),[y,I]=M(0),[r,d]=M(null),[u,S]=M(null),[A,L]=M(0),[X,se]=M(0),[ce,ve]=fa(new Map),D=ha(n,ce),ee=z(()=>`${l.value}-nav-operations-hidden`),O=H(0),Y=H(0);Pe(()=>{x.value?e.rtl?(O.value=0,Y.value=Math.max(0,f.value-r.value)):(O.value=Math.min(0,r.value-f.value),Y.value=0):(O.value=Math.min(0,u.value-y.value),Y.value=0)});const K=_=>_<O.value?O.value:_>Y.value?Y.value:_,pe=H(),[G,fe]=M(),be=()=>{fe(Date.now())},he=()=>{clearTimeout(pe.value)},Se=(_,m)=>{_(P=>K(P+m))};xa(i,(_,m)=>{if(x.value){if(r.value>=f.value)return!1;Se(T,_)}else{if(u.value>=y.value)return!1;Se(c,m)}return he(),be(),!0}),ae(G,()=>{he(),G.value&&(pe.value=setTimeout(()=>{fe(0)},100))});const de=function(){let _=arguments.length>0&&arguments[0]!==void 0?arguments[0]:e.activeKey;const m=D.value.get(_)||{width:0,height:0,left:0,right:0,top:0};if(x.value){let P=p.value;e.rtl?m.right<p.value?P=m.right:m.right+m.width>p.value+r.value&&(P=m.right+m.width-r.value):m.left<-p.value?P=-m.left:m.left+m.width>-p.value+r.value&&(P=-(m.left+m.width-r.value)),c(0),T(K(P))}else{let P=s.value;m.top<-s.value?P=-m.top:m.top+m.height>-s.value+u.value&&(P=-(m.top+m.height-u.value)),T(0),c(K(P))}},De=H(0),Ae=H(0);Pe(()=>{let _,m,P,B,E,R;const Z=D.value;["top","bottom"].includes(e.tabPosition)?(_="width",B=r.value,E=f.value,R=A.value,m=e.rtl?"right":"left",P=Math.abs(p.value)):(_="height",B=u.value,E=f.value,R=X.value,m="top",P=-s.value);let W=B;E+R>B&&E<B&&(W=B-R);const U=n.value;if(!U.length)return[De.value,Ae.value]=[0,0];const Q=U.length;let oe=Q;for(let V=0;V<Q;V+=1){const te=Z.get(U[V].key)||it;if(te[m]+te[_]>P+W){oe=V-1;break}}let N=0;for(let V=Q-1;V>=0;V-=1)if((Z.get(U[V].key)||it)[m]<P){N=V+1;break}return[De.value,Ae.value]=[N,oe]});const Ge=()=>{ve(()=>{var _;const m=new Map,P=(_=v.value)===null||_===void 0?void 0:_.getBoundingClientRect();return n.value.forEach(B=>{let{key:E}=B;const R=w.value.get(E),Z=(R==null?void 0:R.$el)||R;if(Z){const[W,U,Q,oe]=Sa(Z,P);m.set(E,{width:W,height:U,left:Q,top:oe})}}),m})};ae(()=>n.value.map(_=>_.key).join("%%"),()=>{Ge()},{flush:"post"});const Re=()=>{var _,m,P,B,E;const R=((_=i.value)===null||_===void 0?void 0:_.offsetWidth)||0,Z=((m=i.value)===null||m===void 0?void 0:m.offsetHeight)||0,W=((P=h.value)===null||P===void 0?void 0:P.$el)||{},U=W.offsetWidth||0,Q=W.offsetHeight||0;d(R),S(Z),L(U),se(Q);const oe=(((B=v.value)===null||B===void 0?void 0:B.offsetWidth)||0)-U,N=(((E=v.value)===null||E===void 0?void 0:E.offsetHeight)||0)-Q;C(oe),I(N),Ge()},Xe=z(()=>[...n.value.slice(0,De.value),...n.value.slice(Ae.value+1)]),[mt,$t]=M(),ie=z(()=>D.value.get(e.activeKey)),Ve=H(),je=()=>{xe.cancel(Ve.value)};ae([ie,x,()=>e.rtl],()=>{const _={};ie.value&&(x.value?(e.rtl?_.right=me(ie.value.right):_.left=me(ie.value.left),_.width=me(ie.value.width)):(_.top=me(ie.value.top),_.height=me(ie.value.height))),je(),Ve.value=xe(()=>{$t(_)})}),ae([()=>e.activeKey,ie,D,x],()=>{de()},{flush:"post"}),ae([()=>e.rtl,()=>e.tabBarGutter,()=>e.activeKey,()=>n.value],()=>{Re()},{flush:"post"});const Ee=_=>{let{position:m,prefixCls:P,extra:B}=_;if(!B)return null;const E=B==null?void 0:B({position:m});return E?g("div",{class:`${P}-extra-content`},[E]):null};return He(()=>{he(),je()}),()=>{const{id:_,animated:m,activeKey:P,rtl:B,editable:E,locale:R,tabPosition:Z,tabBarGutter:W,onTabClick:U}=e,{class:Q,style:oe}=a,N=l.value,V=!!Xe.value.length,te=`${N}-nav-wrap`;let Ne,Me,Fe,Ue;x.value?B?(Me=p.value>0,Ne=p.value+r.value<f.value):(Ne=p.value<0,Me=-p.value+r.value<f.value):(Fe=s.value<0,Ue=-s.value+u.value<y.value);const Ce={};Z==="top"||Z==="bottom"?Ce[B?"marginRight":"marginLeft"]=typeof W=="number"?`${W}px`:W:Ce.marginTop=typeof W=="number"?`${W}px`:W;const qe=n.value.map((Oe,yt)=>{const{key:ge}=Oe;return g(ba,{id:_,prefixCls:N,key:ge,tab:Oe,style:yt===0?void 0:Ce,closable:Oe.closable,editable:E,active:ge===P,removeAriaLabel:R==null?void 0:R.removeAriaLabel,ref:$(ge),onClick:xt=>{U(ge,xt)},onFocus:()=>{de(ge),be(),i.value&&(B||(i.value.scrollLeft=0),i.value.scrollTop=0)}},o)});return g("div",{role:"tablist",class:le(`${N}-nav`,Q),style:oe,onKeydown:()=>{be()}},[g(Ee,{position:"left",prefixCls:N,extra:o.leftExtra},null),g(Qe,{onResize:Re},{default:()=>[g("div",{class:le(te,{[`${te}-ping-left`]:Ne,[`${te}-ping-right`]:Me,[`${te}-ping-top`]:Fe,[`${te}-ping-bottom`]:Ue}),ref:i},[g(Qe,{onResize:Re},{default:()=>[g("div",{ref:v,class:`${N}-nav-list`,style:{transform:`translate(${p.value}px, ${s.value}px)`,transition:G.value?"none":void 0}},[qe,g(ft,{ref:h,prefixCls:N,locale:R,editable:E,style:k(k({},qe.length===0?void 0:Ce),{visibility:V?"hidden":null})},null),g("div",{class:le(`${N}-ink-bar`,{[`${N}-ink-bar-animated`]:m.inkBar}),style:mt.value},null)])]})])]}),g(ma,ne(ne({},e),{},{removeAriaLabel:R==null?void 0:R.removeAriaLabel,ref:b,prefixCls:N,tabs:Xe.value,class:!V&&ee.value}),pt(o,["moreIcon"])),g(Ee,{position:"right",prefixCls:N,extra:o.rightExtra},null),g(Ee,{position:"right",prefixCls:N,extra:o.tabBarExtraContent},null)])}}}),Ca=re({compatConfig:{MODE:3},name:"TabPanelList",inheritAttrs:!1,props:{activeKey:{type:[String,Number]},id:{type:String},rtl:{type:Boolean},animated:{type:Object,default:void 0},tabPosition:{type:String},destroyInactiveTabPane:{type:Boolean}},setup(e){const{tabs:t,prefixCls:a}=ht();return()=>{const{id:o,activeKey:n,animated:l,tabPosition:i,rtl:v,destroyInactiveTabPane:b}=e,h=l.tabPane,$=a.value,w=t.value.findIndex(x=>x.key===n);return g("div",{class:`${$}-content-holder`},[g("div",{class:[`${$}-content`,`${$}-content-${i}`,{[`${$}-content-animated`]:h}],style:w&&h?{[v?"marginRight":"marginLeft"]:`-${w}00%`}:null},[t.value.map(x=>ta(x.node,{key:x.key,prefixCls:$,tabKey:x.key,id:o,animated:h,active:x.key===n,destroyInactiveTabPane:b}))])])}}});var Ta={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"};function lt(e){for(var t=1;t<arguments.length;t++){var a=arguments[t]!=null?Object(arguments[t]):{},o=Object.keys(a);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(a).filter(function(n){return Object.getOwnPropertyDescriptor(a,n).enumerable}))),o.forEach(function(n){wa(e,n,a[n])})}return e}function wa(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var ze=function(t,a){var o=lt({},t,a.attrs);return g(St,lt({},o,{icon:Ta}),null)};ze.displayName="PlusOutlined";ze.inheritAttrs=!1;const Ia=e=>{const{componentCls:t,motionDurationSlow:a}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${a}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${a}`}}}}},[Ze(e,"slide-up"),Ze(e,"slide-down")]]},Pa=e=>{const{componentCls:t,tabsCardHorizontalPadding:a,tabsCardHeadBackground:o,tabsCardGutter:n,colorSplit:l}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:a,background:o,border:`${e.lineWidth}px ${e.lineType} ${l}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:e.colorPrimary,background:e.colorBgContainer},[`${t}-ink-bar`]:{visibility:"hidden"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:`${n}px`}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:`${n}px`}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${e.borderRadiusLG}px 0 0 ${e.borderRadiusLG}px`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},ka=e=>{const{componentCls:t,tabsHoverColor:a,dropdownEdgeChildVerticalPadding:o}=e;return{[`${t}-dropdown`]:k(k({},ct(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${o}px 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":k(k({},wt),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${e.paddingXXS}px ${e.paddingSM}px`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:a}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},Ba=e=>{const{componentCls:t,margin:a,colorSplit:o}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:`0 0 ${a}px 0`,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${e.lineWidth}px ${e.lineType} ${o}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},
            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,
        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:`${a}px`,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:e.controlHeight*1.25,[`${t}-tab`]:{padding:`${e.paddingXS}px ${e.paddingLG}px`,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:`${e.margin}px 0 0 0`},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:`-${e.lineWidth}px`},borderLeft:{_skip_check_:!0,value:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:-e.lineWidth},borderRight:{_skip_check_:!0,value:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},La=e=>{const{componentCls:t,padding:a}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXS}px 0`,fontSize:e.fontSize}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${a}px 0`,fontSize:e.fontSizeLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXXS*1.5}px ${a}px`}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${e.borderRadius}px ${e.borderRadius}px`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${e.borderRadius}px ${e.borderRadius}px 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${e.borderRadius}px ${e.borderRadius}px 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${e.borderRadius}px 0 0 ${e.borderRadius}px`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXS}px ${a}px ${e.paddingXXS*1.5}px`}}}}}},Da=e=>{const{componentCls:t,tabsActiveColor:a,tabsHoverColor:o,iconCls:n,tabsHorizontalGutter:l}=e,i=`${t}-tab`;return{[i]:{position:"relative",display:"inline-flex",alignItems:"center",padding:`${e.paddingSM}px 0`,fontSize:`${e.fontSize}px`,background:"transparent",border:0,outline:"none",cursor:"pointer","&-btn, &-remove":k({"&:focus:not(:focus-visible), &:active":{color:a}},dt(e)),"&-btn":{outline:"none",transition:"all 0.3s"},"&-remove":{flex:"none",marginRight:{_skip_check_:!0,value:-e.marginXXS},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},"&:hover":{color:o},[`&${i}-active ${i}-btn`]:{color:e.colorPrimary,textShadow:e.tabsActiveTextShadow},[`&${i}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${i}-disabled ${i}-btn, &${i}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${i}-remove ${n}`]:{margin:0},[n]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${i} + ${i}`]:{margin:{_skip_check_:!0,value:`0 0 0 ${l}px`}}}},Aa=e=>{const{componentCls:t,tabsHorizontalGutter:a,iconCls:o,tabsCardGutter:n}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:`0 0 0 ${a}px`},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[o]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:`${e.marginSM}px`}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:`${e.marginXS}px`},marginLeft:{_skip_check_:!0,value:`-${e.marginXXS}px`},[o]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:`${n}px`},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},Ra=e=>{const{componentCls:t,tabsCardHorizontalPadding:a,tabsCardHeight:o,tabsCardGutter:n,tabsHoverColor:l,tabsActiveColor:i,colorSplit:v}=e;return{[t]:k(k(k(k({},ct(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:a,background:"transparent",border:0,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.controlHeightLG/8,transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:k({minWidth:`${o}px`,marginLeft:{_skip_check_:!0,value:`${n}px`},padding:`0 ${e.paddingXS}px`,background:"transparent",border:`${e.lineWidth}px ${e.lineType} ${v}`,borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:l},"&:active, &:focus:not(:focus-visible)":{color:i}},dt(e))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.colorPrimary,pointerEvents:"none"}}),Da(e)),{[`${t}-content`]:{position:"relative",display:"flex",width:"100%","&-animated":{transition:"margin 0.3s"}},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:{outline:"none",flex:"none",width:"100%"}}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping'])`]:{justifyContent:"center"}}}}}},Ea=Ct("Tabs",e=>{const t=e.controlHeightLG,a=Tt(e,{tabsHoverColor:e.colorPrimaryHover,tabsActiveColor:e.colorPrimaryActive,tabsCardHorizontalPadding:`${(t-Math.round(e.fontSize*e.lineHeight))/2-e.lineWidth}px ${e.padding}px`,tabsCardHeight:t,tabsCardGutter:e.marginXXS/2,tabsHorizontalGutter:32,tabsCardHeadBackground:e.colorFillAlter,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120});return[La(a),Aa(a),Ba(a),ka(a),Pa(a),Ra(a),Ia(a)]},e=>({zIndexPopup:e.zIndexPopupBase+50}));let rt=0;const gt=()=>({prefixCls:{type:String},id:{type:String},popupClassName:String,getPopupContainer:q(),activeKey:{type:[String,Number]},defaultActiveKey:{type:[String,Number]},direction:Te(),animated:Rt([Boolean,Object]),renderTabBar:q(),tabBarGutter:{type:Number},tabBarStyle:ye(),tabPosition:Te(),destroyInactiveTabPane:At(),hideAdd:Boolean,type:Te(),size:Te(),centered:Boolean,onEdit:q(),onChange:q(),onTabClick:q(),onTabScroll:q(),"onUpdate:activeKey":q(),locale:ye(),onPrevClick:q(),onNextClick:q(),tabBarExtraContent:Le.any});function Na(e){return e.map(t=>{if(Pt(t)){const a=k({},t.props||{});for(const[x,p]of Object.entries(a))delete a[x],a[kt(x)]=p;const o=t.children||{},n=t.key!==void 0?t.key:void 0,{tab:l=o.tab,disabled:i,forceRender:v,closable:b,animated:h,active:$,destroyInactiveTabPane:w}=a;return k(k({key:n},a),{node:t,closeIcon:o.closeIcon,tab:l,disabled:i===""||i,forceRender:v===""||v,closable:b===""||b,animated:h===""||h,active:$===""||$,destroyInactiveTabPane:w===""||w})}return null}).filter(t=>t)}const Ma=re({compatConfig:{MODE:3},name:"InternalTabs",inheritAttrs:!1,props:k(k({},vt(gt(),{tabPosition:"top",animated:{inkBar:!0,tabPane:!1}})),{tabs:Dt()}),slots:Object,setup(e,t){let{attrs:a,slots:o}=t;We(e.onPrevClick===void 0&&e.onNextClick===void 0,"Tabs","`onPrevClick / @prevClick` and `onNextClick / @nextClick` has been removed. Please use `onTabScroll / @tabScroll` instead."),We(e.tabBarExtraContent===void 0,"Tabs","`tabBarExtraContent` prop has been removed. Please use `rightExtra` slot instead."),We(o.tabBarExtraContent===void 0,"Tabs","`tabBarExtraContent` slot is deprecated. Please use `rightExtra` slot instead.");const{prefixCls:n,direction:l,size:i,rootPrefixCls:v,getPopupContainer:b}=Bt("tabs",e),[h,$]=Ea(n),w=z(()=>l.value==="rtl"),x=z(()=>{const{animated:u,tabPosition:S}=e;return u===!1||["left","right"].includes(S)?{inkBar:!1,tabPane:!1}:u===!0?{inkBar:!0,tabPane:!0}:k({inkBar:!0,tabPane:!1},typeof u=="object"?u:{})}),[p,T]=M(!1);ke(()=>{T(ca())});const[s,c]=Je(()=>{var u;return(u=e.tabs[0])===null||u===void 0?void 0:u.key},{value:z(()=>e.activeKey),defaultValue:e.defaultActiveKey}),[f,C]=M(()=>e.tabs.findIndex(u=>u.key===s.value));Pe(()=>{var u;let S=e.tabs.findIndex(A=>A.key===s.value);S===-1&&(S=Math.max(0,Math.min(f.value,e.tabs.length-1)),c((u=e.tabs[S])===null||u===void 0?void 0:u.key)),C(S)});const[y,I]=Je(null,{value:z(()=>e.id)}),r=z(()=>p.value&&!["left","right"].includes(e.tabPosition)?"top":e.tabPosition);ke(()=>{e.id||(I(`rc-tabs-${rt}`),rt+=1)});const d=(u,S)=>{var A,L;(A=e.onTabClick)===null||A===void 0||A.call(e,u,S);const X=u!==s.value;c(u),X&&((L=e.onChange)===null||L===void 0||L.call(e,u))};return $a({tabs:z(()=>e.tabs),prefixCls:n}),()=>{const{id:u,type:S,tabBarGutter:A,tabBarStyle:L,locale:X,destroyInactiveTabPane:se,renderTabBar:ce=o.renderTabBar,onTabScroll:ve,hideAdd:D,centered:ee}=e,O={id:y.value,activeKey:s.value,animated:x.value,tabPosition:r.value,rtl:w.value,mobile:p.value};let Y;S==="editable-card"&&(Y={onEdit:(fe,be)=>{let{key:he,event:Se}=be;var de;(de=e.onEdit)===null||de===void 0||de.call(e,fe==="add"?Se:he,fe)},removeIcon:()=>g(Lt,null,null),addIcon:o.addIcon?o.addIcon:()=>g(ze,null,null),showAdd:D!==!0});let K;const pe=k(k({},O),{moreTransitionName:`${v.value}-slide-up`,editable:Y,locale:X,tabBarGutter:A,onTabClick:d,onTabScroll:ve,style:L,getPopupContainer:b.value,popupClassName:le(e.popupClassName,$.value)});ce?K=ce(k(k({},pe),{DefaultTabBar:ot})):K=g(ot,pe,pt(o,["moreIcon","leftExtra","rightExtra","tabBarExtraContent"]));const G=n.value;return h(g("div",ne(ne({},a),{},{id:u,class:le(G,`${G}-${r.value}`,{[$.value]:!0,[`${G}-${i.value}`]:i.value,[`${G}-card`]:["card","editable-card"].includes(S),[`${G}-editable-card`]:S==="editable-card",[`${G}-centered`]:ee,[`${G}-mobile`]:p.value,[`${G}-editable`]:S==="editable-card",[`${G}-rtl`]:w.value},a.class)}),[K,g(Ca,ne(ne({destroyInactiveTabPane:se},O),{},{animated:x.value}),null)]))}}}),_e=re({compatConfig:{MODE:3},name:"ATabs",inheritAttrs:!1,props:vt(gt(),{tabPosition:"top",animated:{inkBar:!0,tabPane:!1}}),slots:Object,setup(e,t){let{attrs:a,slots:o,emit:n}=t;const l=i=>{n("update:activeKey",i),n("change",i)};return()=>{var i;const v=Na(It((i=o.default)===null||i===void 0?void 0:i.call(o)));return g(Ma,ne(ne(ne({},aa(e,["onUpdate:activeKey"])),a),{},{onChange:l,tabs:v}),o)}}}),Oa=()=>({tab:Le.any,disabled:{type:Boolean},forceRender:{type:Boolean},closable:{type:Boolean},animated:{type:Boolean},active:{type:Boolean},destroyInactiveTabPane:{type:Boolean},prefixCls:{type:String},tabKey:{type:[String,Number]},id:{type:String}}),Be=re({compatConfig:{MODE:3},name:"ATabPane",inheritAttrs:!1,__ANT_TAB_PANE:!0,props:Oa(),slots:Object,setup(e,t){let{attrs:a,slots:o}=t;const n=F(e.forceRender);ae([()=>e.active,()=>e.destroyInactiveTabPane],()=>{e.active?n.value=!0:e.destroyInactiveTabPane&&(n.value=!1)},{immediate:!0});const l=z(()=>e.active?{}:e.animated?{visibility:"hidden",height:0,overflowY:"hidden"}:{display:"none"});return()=>{var i;const{prefixCls:v,forceRender:b,id:h,active:$,tabKey:w}=e;return g("div",{id:h&&`${h}-panel-${w}`,role:"tabpanel",tabindex:$?0:-1,"aria-labelledby":h&&`${h}-tab-${w}`,"aria-hidden":!$,style:[l.value,a.style],class:[`${v}-tabpane`,$&&`${v}-tabpane-active`,a.class]},[($||n.value||b)&&((i=o.default)===null||i===void 0?void 0:i.call(o))])}}});_e.TabPane=Be;_e.install=function(e){return e.component(_e.name,_e),e.component(Be.name,Be),e};const Wa=Et("networkTest",{state:()=>({mCSDeviceList:[],dAUDeviceList:[]}),actions:{reset(){this.$reset()},async fetchGetMCSDeviceList(e){try{const t=await Ht(e);return this.mCSDeviceList=t,t}catch(t){throw console.error("获取失败:",t),t}},async fetchGetDAUDeviceList(e){try{let t=await Wt(e);return this.dAUDeviceList=t,t}catch(t){throw console.error("获取失败:",t),t}},async fetchSinglePingTest(e){try{return await Ot(e)}catch(t){throw console.error("获取失败:",t),t}},async fetchSingleTelnetTest(e){try{return await Mt(e)}catch(t){throw console.error(t),t}},async fetchBatchNetworkTest(e){try{return await Nt(e)}catch(t){throw console.error(t),t}}}}),Ha=["onClick"],za=["onClick"],Ga=["onClick"],Xa=["onClick"],Va={__name:"networkTest",setup(e){const t=Wa(),a=zt(),o=F(a.params.id),n=Gt({table1:[],table2:[]}),l=F(!1);st("noHeaderBorder",!0);const i=[{title:"设备名称",dataIndex:"deviceName"},{title:"IP地址",dataIndex:"ipAddress"},{title:"端口号",dataIndex:"port"},{title:"子网掩码",dataIndex:"subnetMask"},{title:"网关地址",dataIndex:"gateway"},{title:"MAC地址",dataIndex:"macAddress"},{title:"ping延迟(ms)",dataIndex:"averageLatency"},{title:"ping丢包率",dataIndex:"packetLoss",width:100,customRender:({record:s})=>s&&(s.packetLoss||s.packetLoss==0)?`${s.packetLoss}%`:""},{title:"ping结果",dataIndex:"pingIsSuccess"},{title:"telnet结果",dataIndex:"telnetIsSuccess"},{title:"网络测试",dataIndex:"otherColumn",columnHidden:!0,width:160}],v=[{title:"设备名称",dataIndex:"deviceName"},{title:"IP地址",dataIndex:"ipAddress"},{title:"端口号",dataIndex:"port"},{title:"子网掩码",dataIndex:"subnetMask"},{title:"网关地址",dataIndex:"gateway"},{title:"MAC地址",dataIndex:"macAddress"},{title:"ping延迟(ms)",dataIndex:"averageLatency"},{title:"ping丢包率",dataIndex:"packetLoss",width:100,customRender:({record:s})=>s&&(s.packetLoss||s.packetLoss==0)?`${s.packetLoss}%`:""},{title:"ping测试结果",dataIndex:"pingIsSuccess"},{title:"telnet测试结果",dataIndex:"telnetIsSuccess"},{title:"网络测试",dataIndex:"otherColumn",columnHidden:!0,width:160}],b=async()=>{if(o.value){l.value=!0;const s=await t.fetchGetDAUDeviceList({windParkId:o.value});n.table1=s}l.value=!1},h=async()=>{if(o.value){const s=await t.fetchGetMCSDeviceList({windParkId:o.value});n.table2=s}};ae(()=>a.params.id,s=>{s&&(o.value=s,b())},{immediate:!0});const $=s=>{s=="1"?b():s=="2"&&h()},w=async(s,c)=>{l.value=!0;const f=await t.fetchSinglePingTest({ipAddress:c.ipAddress});if(f&&f.code===1){$e.success("测试完成");let C=f.data;s=="1"?n.table1=n.table1.map(y=>y.deviceId==c.deviceId?{...y,...C,pingIsSuccess:C.isSuccess?"成功":"失败"}:y):s=="2"&&(n.table2=n.table2.map(y=>y.deviceId==c.deviceId?{...y,...C,pingIsSuccess:C.isSuccess?"成功":"失败"}:y))}l.value=!1},x=async(s,c)=>{l.value=!0;const f=await t.fetchSingleTelnetTest({ipAddress:c.ipAddress,port:c.port});if(f&&f.code===1){$e.success("测试完成");let C=f.data;s=="1"?n.table1=n.table1.map(y=>y.deviceId==c.deviceId?{...y,...C,telnetIsSuccess:C.isSuccess?"成功":"失败"}:y):s=="2"&&(n.table2=n.table2.map(y=>y.deviceId==c.deviceId?{...y,...C,telnetIsSuccess:C.isSuccess?"成功":"失败"}:y))}l.value=!1},p=async(s,c)=>{if(l.value=!0,!c||!c.length){$e.warning("请选择要测试的设备");return}let f=[];s=="1"?f=t.dAUDeviceList.filter(I=>c.includes(I.deviceId)):s=="2"&&(f=t.mCSDeviceList.filter(I=>c.includes(I.deviceId)));let C={testType:"both",devices:f};const y=await t.fetchBatchNetworkTest(C);if(y&&y.code===1){$e.success("测试完成");let I=y.data,r=[];s=="1"?r=n.table1:s=="2"&&(r=n.table2),r=r.map(d=>{if(c.includes(d.deviceId)){let u=I.pingResults.find(S=>S.deviceId==d.deviceId);return{...d,...u,pingIsSuccess:u.isSuccess?"成功":"失败"}}return d}),r=r.map(d=>{if(c.includes(d.deviceId)){let u=I.telnetResults.find(S=>S.deviceId==d.deviceId);return{...d,...u,telnetIsSuccess:u.isSuccess?"成功":"失败"}}return d}),s=="1"?n.table1=[...r]:s=="2"&&(n.table2=[...r])}l.value=!1},T=(s,c)=>{if(!c||!c.length){$e.warning("请选择要测试的设备");return}let f=[],C="",y=[];s=="1"?(C="DAU测试结果.csv",y=[...i],f=n.table1.filter(I=>c.includes(I.deviceId))):s=="2"&&(C="主控测试结果.csv",y=[...v],f=n.table2.filter(I=>c.includes(I.deviceId))),y.pop(),Ft(y,f,C)};return(s,c)=>{const f=qt,C=Be,y=_e,I=jt;return Vt(),Xt(I,{spinning:l.value,size:"large"},{default:j(()=>[g(y,{onChange:$,destroyInactiveTabPane:!0},{default:j(()=>[g(C,{key:"1",tab:"DAU通讯测试"},{default:j(()=>[ue("div",null,[g(Ye,{ref:"table",size:"default","table-key":"0","table-columns":i,"table-operate":[],"record-key":"deviceId","table-datas":n.table1,noBatchApply:!0,stayPage:!0,selectedRows:!0},{rightButtons:j(({selectedRowKeys:r})=>[g(f,{type:"primary",class:"btnItem",onClick:d=>p("1",r),disabled:!r.length},{default:j(()=>c[0]||(c[0]=[we(" 批量测试 ",-1)])),_:2,__:[0]},1032,["onClick","disabled"]),g(f,{type:"primary",class:"btnItem",onClick:d=>T("1",r),disabled:!r.length},{default:j(()=>c[1]||(c[1]=[we(" 导出CSV ",-1)])),_:2,__:[1]},1032,["onClick","disabled"])]),otherColumn:j(({column:r,record:d,text:u})=>[ue("span",{onClick:S=>w("1",d),class:"editBtn"},"ping",8,Ha),ue("span",{onClick:S=>x("1",d),class:"editBtn"},"telnet",8,za)]),_:1},8,["table-datas"])])]),_:1}),g(C,{key:"2",tab:"主控通讯测试","force-render":""},{default:j(()=>[ue("div",null,[g(Ye,{ref:"table",size:"default","table-key":"0","table-columns":v,"table-operate":[],"record-key":"deviceId","table-datas":n.table2,noBatchApply:!0,stayPage:!0,selectedRows:!0},{rightButtons:j(({selectedRowKeys:r})=>[g(f,{type:"primary",class:"btnItem",onClick:d=>p("2",r),disabled:!r.length},{default:j(()=>c[2]||(c[2]=[we(" 批量测试 ",-1)])),_:2,__:[2]},1032,["onClick","disabled"]),g(f,{type:"primary",class:"btnItem",onClick:d=>T("2",r),disabled:!r.length},{default:j(()=>c[3]||(c[3]=[we(" 导出CSV ",-1)])),_:2,__:[3]},1032,["onClick","disabled"])]),otherColumn:j(({column:r,record:d,text:u})=>[ue("span",{onClick:S=>w("2",d),class:"editBtn"},"ping",8,Ga),ue("span",{onClick:S=>x("2",d),class:"editBtn"},"telnet",8,Xa)]),_:1},8,["table-datas"])])]),_:1})]),_:1})]),_:1},8,["spinning"])}}},sn=Ut(Va,[["__scopeId","data-v-ce5b3ff9"]]);export{sn as default};
