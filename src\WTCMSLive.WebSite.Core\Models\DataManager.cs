﻿using AppFramework.Utility;
using WTCMSLive.BusinessModel;
using CMSFramework.BusinessEntity;

namespace WTCMSLive.WebSite.Models
{
    public class DataManager
    {
        /// <summary>
        /// 取得各个风场的情报
        /// </summary>
        /// <returns></returns>
        public List<WindPark_Web> GetWindParkList()
        {
            List<WindPark_Web> dataList = new List<WindPark_Web>();

            //取得风场基本情报
            List<WindPark> windParkList = BusinessModel.DevTreeManagement.GetWindParkList();
            //诊断情况
            List<DiagnosisAssignment> diagList = AlrmRecordManagement.GetRegularDiagnosisList();
            List<WindTurbine> turbineListPark = DevTreeManagement.GetTurbinesList();
            List<RTAlarmStatus_DAU> statusDAUList = DAUConditionManagement.GetDAURTAlarmStatusList();
            for (int i = 0; i < windParkList.Count; i++)
            {
                WindPark_Web parkData = new WindPark_Web();
                //将风场情报整合进 WindPark_Web中，便于前台展示。
                parkData.WindParkName = windParkList[i].WindParkName;
                //parkData.WTTotalNumber = windParkList[i].WTTotalNumber;
                parkData.WindParkID = windParkList[i].WindParkID;
                //根据风场ID，取得风场诊断情报，采集单元情报。
                //To Do
                int waring = 0;
                int danger = 0;
                int diag_app = 0;
                int diagnosis_count = 0;
                int finishedDiag = 0;
                List<WindTurbine> turbineList = turbineListPark.FindAll(item => item.WindParkID == windParkList[i].WindParkID).OrderBy(item => item.WindTurbineName).ToList();

                parkData.WTTotalNumber = turbineList.Count;
                for (int j = 0; j < turbineList.Count; j++)
                {
                    DiagnosisAssignment turbineDia = diagList.Find(item => item.WindTurbineID.ToString() == turbineList[j].WindTurbineID);
                    if (turbineDia != null)
                    {
                        //判断机组诊断状态
                        if (turbineDia.HitchDegree == (int)EnumAlarmDegree.AlarmDeg_Warning)
                        {
                            ++waring;
                        }
                        if (turbineDia.HitchDegree == (int)EnumAlarmDegree.AlarmDeg_Alarm)
                        {
                            ++danger;
                        }
                        //申请诊断数
                        if (turbineDia.AssignmentStatus == EnumAssignmentStatus.Accepted || turbineDia.AssignmentStatus == EnumAssignmentStatus.Create || turbineDia.AssignmentStatus == EnumAssignmentStatus.Diagnosis)
                        {
                            ++diag_app;
                        }
                        if (turbineDia.AssignmentType == EnumAssignmentType.DiagnosticEngineer)
                        {
                            ++diagnosis_count;
                        }
                        if (turbineDia.AssignmentStatus==EnumAssignmentStatus.Finished)
                        {
                            ++finishedDiag;
                        }
                    }
                }
                parkData.diagnosis_Waring = waring.ToString();
                parkData.diagnosis_Danger = danger.ToString();
                parkData.diagnosis_app = diag_app.ToString();
                parkData.diagnosis_count = diagnosis_count.ToString();
                parkData.finishedDiag = finishedDiag.ToString();

                //根据风场，获取DAU列表
                List<RTAlarmStatus_DAU> statusDAUListWindPark =new List<RTAlarmStatus_DAU>();
                foreach (RTAlarmStatus_DAU item in statusDAUList)
                {
                    if (turbineList.Exists(c => c.WindTurbineID == item.WindTurbineID))
                    {
                        statusDAUListWindPark.Add(item);
                    }
                }
                int dauFault = 0;//通信异常
                int sensorFault = 0;//传感器故障
                int noData = 0;//无数据到达
                for (int dauid = 0; dauid < statusDAUListWindPark.Count; dauid++)
                {
                    switch (statusDAUListWindPark[dauid].AlarmState)
                    {
                        case EnumDAUStatus.CommunicationError:
                            ++dauFault;
                            break;
                        case EnumDAUStatus.SensorFault:
                            ++sensorFault;
                            break;
                        case EnumDAUStatus.NoDataArrive:
                            ++noData;
                            break;
                        default:
                            break;
                    }
                }
                parkData.DAU_Fault = dauFault.ToString();
                parkData.Sensor_Fault = sensorFault.ToString();
                parkData.NoData = noData.ToString();
                dataList.Add(parkData);
            }

            return dataList;
        }

        //获取集团级机组总览信息
        public BaseTableModel GetWindParkTable()
        {
            List<WindPark_Web> windParkList = GetWindParkList();
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "windParkList";
            List<Rows> rows = new List<Rows>();
            for (int j = 0; j < windParkList.Count; j++)
            {
                Rows cells = new Rows();
                cells.cells = GetWindParkListTableCell(windParkList[j]);
                rows.Add(cells);
            }
            tableModel.rows = rows.ToArray();

            return tableModel;
        }

        private Cell[] GetWindParkListTableCell(WindPark_Web windPark)
        {
            List<Cell> cellList = new List<Cell>();
            //风电场	
            Cell cell0 = new Cell();
            cell0.type = "link";
            cell0.displayValue = windPark.WindParkName;
            cell0.href = "/WindPark/Index/" + windPark.WindParkID;
            cellList.Add(cell0);
            //监测风机数	
            Cell cell1 = new Cell();
            cell1.displayValue = windPark.WTTotalNumber.ToString();
            cellList.Add(cell1);
            #region 隐藏未实现功能
            ////故障等级：警告	
            //Cell cell2 = new Cell();
            //cell2.displayValue = windPark.diagnosis_Waring;
            //cell2.color = "#f78c00";
            ////cellList.Add(cell2);
            ////故障等级：危险
            //Cell cell3 = new Cell();
            //cell3.displayValue = windPark.diagnosis_Danger;
            //cell3.color = "#d9544f";
            ////cellList.Add(cell3);
            ////定期诊断数
            //Cell cell4 = new Cell();
            //cell4.displayValue = windPark.diagnosis_count;
            ////cellList.Add(cell4);
            ////申请诊断   
            //Cell cell5 = new Cell();
            //cell5.displayValue = windPark.diagnosis_app;
            ////cellList.Add(cell5);
            ////已诊断
            //Cell cell9 = new Cell();
            //cell9.displayValue = windPark.finishedDiag;
            ////cellList.Add(cell9);
            #endregion
            //采集单元通信异常	
            Cell cell6 = new Cell();
            cell6.displayValue = windPark.DAU_Fault;
            cellList.Add(cell6);
            //传感器故障	
            Cell cell7 = new Cell();
            cell7.displayValue = windPark.Sensor_Fault;
            cellList.Add(cell7);
            //无数据
            Cell cell8 = new Cell();
            cell8.displayValue = windPark.NoData;
            cellList.Add(cell8);

            return cellList.ToArray();
        }

        public string GetTurbineListTable(string windParkID, string UserId)
        {
            //取得机组列表
            List<WindTurbine> turbineList = DevTreeManagement.GetTurbinesListByWindParkId(windParkID);
            List<AlarmStatus_Turbine> statusRTList = DevRTStateManagement.GetTurRTAlarmStatusList(windParkID);
            List<DiagnosisAssignment> diagList = AlrmRecordManagement.GetRegularDiagnosisList();
            List<RTAlarmStatus_DAU> statusDAUList = DAUConditionManagement.GetDAURTAlarmStatusByWindParkId(windParkID);
            List<AlarmStatus_Component> compList = DevRTStateManagement.GetTurComRTStateListWindPark(windParkID);
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "windTurbineList";
            List<Rows> rows = new List<Rows>();
            for (int j = 0; j < turbineList.Count; j++)
            {
                Rows cells = new Rows();
                string style = string.Empty;
                cells.cells = GetTurbineListTableCell(turbineList[j], out style, statusRTList, diagList, statusDAUList, compList, UserId);
                cells.style = style;
                rows.Add(cells);
            }
            tableModel.rows = rows.ToArray();
            return tableModel.ToJson();
        }

        private Cell[] GetTurbineListTableCell(WindTurbine windTurbine, out string style, List<AlarmStatus_Turbine> statusRTList, List<DiagnosisAssignment> diagList, List<RTAlarmStatus_DAU> dauList, List<AlarmStatus_Component> compList,string UserId)
        {
            style = string.Empty;
            List<Cell> cellList = new List<Cell>();
            cellList.Add(new Cell()
            {
                displayValue = "0",
                type = "hide"
            });
            //机组名
            Cell cell1 = new Cell();
            cell1.type = "link";
            cell1.displayValue = windTurbine.WindTurbineName;
            cell1.title = cell1.displayValue;
            cell1.href = "/WindTurbine/Index/" + windTurbine.WindParkID + "/" + windTurbine.WindTurbineID;
            cellList.Add(cell1);
            //机组状态
            DeviceRTAlarmStatus RTAlarm = statusRTList.Find(item=>item.DevSegmentID==windTurbine.WindTurbineID);
            Cell cell2 = new Cell();
            if (RTAlarm != null)
            {
                cell2.displayValue = AppFramework.Utility.EnumHelper.GetDescription(RTAlarm.AlarmDegree);
                cell2.title = cell2.displayValue;

                //style = Utility.GetStyleFromAlarmType(RTAlarm.AlarmType);
                cell2.color = Utility.GetColorFromAlarmType(RTAlarm.AlarmDegree);
            }
            cellList.Add(cell2);

            //部件实时状态
            List<AlarmStatus_Component> deviceList = compList.FindAll(item => item.WindTurbineID == windTurbine.WindTurbineID);
            Cell cell3 = new Cell();
            cell3.type = "p";
            string deviceStatus = string.Empty;
            //排序机组部件 wangy 2015年7月25日
            string[] arr = AppFramework.IDUtility.CodeProvide.GetComponentDic().Keys.ToList<string>().ToArray();
            int DevCount = 0;
            foreach (string DevSegmentName in arr)
            {
                if (DevCount == deviceList.Count()) break;
                DeviceRTAlarmStatus alarmStatus = deviceList.Find(i => i.DevSegmentName == DevSegmentName);
                if (alarmStatus != null)
                {
                    deviceStatus += alarmStatus.DevSegmentName + "_" + AppFramework.Utility.EnumHelper.GetDescription(alarmStatus.AlarmDegree) + "|";
                    DevCount++;
                }
            }
            cell3.displayValue = deviceStatus;
            cellList.Add(cell3);

            //机组状态更新时间
            Cell cell4 = new Cell();
            if (RTAlarm != null)
            {
                cell4.displayValue = RTAlarm.AlarmUpdateTime.ToString("yyyy-MM-dd HH:mm:ss");
            }
            cellList.Add(cell4);
            //诊断进程
            DiagnosisAssignment diag = diagList.Find(item => item.WindTurbineID == windTurbine.WindTurbineID);
            Cell cell5 = new Cell();
            if (diag != null)
            {
                cell5.displayValue = EnumHelper.GetDescription(diag.AssignmentStatus);
            }
            else
            {
                cell5.displayValue = string.Empty;
            }
            //诊断等级
            Cell cell6 = new Cell();
            //诊断时间
            Cell cell7 = new Cell();
            if (diag!=null)
            {
                cell6.displayValue = AppFramework.Utility.EnumHelper.GetDescription((EnumAlarmDegree)diag.HitchDegree);
                cell6.color = Utility.GetColorFromAlarmType((EnumAlarmDegree)diag.HitchDegree);
                cell7.displayValue = diag.DiagnosisTime?.ToString("yyyy-MM-dd HH:mm:ss");
            }
            else
            {
                cell6.displayValue = string.Empty;
                cell7.displayValue = "-";
            }
           // cellList.Add(cell5);
           // cellList.Add(cell6);
           // cellList.Add(cell7);

            //报警事件 获取机组未完成的报警事件
            /*Cell cell8 = new Cell();
            AlarmEvent alarmEvent = AlrmRecordManagement.GetNotFinishedAlarmEventByTurID(windTurbine.WindTurbineID);
            if (alarmEvent != null)
            {
                cell8.displayValue = EnumHelper.GetDescription((EnumAlarmEventHandleState)Convert.ToInt32(alarmEvent.AlarmEventState));
            }
            else {
                cell8.displayValue = "";
            }
            cellList.Add(cell8);*/
            if (RoleManagement.IsFunOperation(UserId, ModuleName.DAUManage, FunctionName.Show))
            {
                RTAlarmStatus_DAU dau = dauList.Find(item => item.WindTurbineID == windTurbine.WindTurbineID);
                Cell cell9 = new Cell();
                Cell cell10 = new Cell();
                //if (dau != null)
                //{
                //    //采集单元状态               
                //    cell9.displayValue = EnumHelper.GetDescription(dau.AlarmState);
                //    //为了不喧宾夺主，就把DAU状态的颜色去掉了 wangy 2015年8月12日
                //    cell9.color = Utility.GetColorFromAlarmType(dau.AlarmState);
                //    //DAU状态更新时间                
                //    cell10.displayValue = dau.StatusUpdateTime.ToString();
                //}
                //else
                //{
                //    cell9.displayValue = "";
                //    cell10.displayValue = "";
                //}

                //支持多采集单元 modified by sq 20190221
                List<RTAlarmStatus_DAU> daulists = dauList.FindAll(item => item.WindTurbineID == windTurbine.WindTurbineID);
                cell9.type = "p";
                string dauStatusTxt = string.Empty;
                cell9.displayValue = "";
                //cell10.displayValue = "";
                foreach (var item in daulists)
                {
                    WindDAU windDAU = DauManagement.GetDAUNameById(item.WindTurbineID,item.DauID);
                    dauStatusTxt += windDAU.DAUName +" : "+ EnumHelper.GetDescription(item.AlarmState) + "_" + Utility.GetColorFromAlarmType(item.AlarmState) + "_" + item.StatusUpdateTime.ToString() + "|";
                }
            /*    daulists.ForEach(item =>
                {
                    WindDAU windDAU = DauManagement.GetDAUById(item.WindTurbineID + item.DauID);
                    dauStatusTxt += windDAU.DAUName+EnumHelper.GetDescription(item.AlarmState) + "_" + Utility.GetColorFromAlarmType(item.AlarmState) + "_" + item.StatusUpdateTime.ToString() + "|";
                });*/
                cell9.displayValue = dauStatusTxt;
                cellList.Add(cell9);
                //cellList.Add(cell10);
            }
            return cellList.ToArray();
        }
    }
}