using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json;
using System.Diagnostics;
using System.Reflection;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Core.Attributes;
using WTCMSLive.WebSite.Core.DTOs;
using WTCMSLive.WebSite.Core.Models;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 批量操作服务实现
    /// </summary>
    public class BatchOperationService : IBatchOperationService
    {
        private readonly ILogger<BatchOperationService> _logger;

        public BatchOperationService(ILogger<BatchOperationService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 执行批量操作
        /// </summary>
        public async Task ExecuteBatchAsync(ActionExecutedContext context, BatchOperationAttribute attribute)
        {
            try
            {
                // 从请求中提取目标机组列表
                var targetTurbineIds = ExtractTargetTurbineIds(context, attribute.TargetIdsParameter);

                if (targetTurbineIds?.Any() != true)
                {
                    _logger.LogDebug("没有找到目标机组列表，跳过批量操作");
                    return; // 没有批量操作目标，直接返回
                }

                _logger.LogInformation("开始执行批量操作，目标机组数量: {Count}, 方法: {Method}", 
                    targetTurbineIds.Count, attribute.BatchMethodName);

                // 获取Controller实例
                var controller = context.Controller;

                // 通过反射找到批量操作方法
                var batchMethod = controller.GetType().GetMethod(attribute.BatchMethodName);
                if (batchMethod == null)
                {
                    throw new InvalidOperationException($"批量操作方法 {attribute.BatchMethodName} 未找到");
                }

                // 获取原始请求参数
                var originalRequest = ExtractRequestObject(context);

                // 执行批量操作
                var batchResults = await ExecuteBatchMethodAsync(
                    controller, batchMethod, targetTurbineIds, originalRequest, attribute);

                // 包装返回结果
                if (context.Result is OkObjectResult okResult)
                {
                    var wrappedResult = CreateApiResponseWithBatch(okResult.Value, batchResults);
                    context.Result = new OkObjectResult(wrappedResult);
                }

                _logger.LogInformation("批量操作完成，成功: {Success}, 失败: {Failed}",
                    batchResults.Count(r => r.Success), batchResults.Count(r => !r.Success));

                // 清理缓存的原始记录信息
                CleanupCachedData(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行批量操作时发生错误");
                // 不影响主操作的结果，只记录错误
            }
        }

        /// <summary>
        /// 从请求中提取目标机组ID列表
        /// </summary>
        private List<string> ExtractTargetTurbineIds(ActionExecutedContext context, string parameterName)
        {
            try
            {
                // 从ActionArguments中获取
                var actionExecutingContext = context.HttpContext.Items["ActionExecutingContext"] as ActionExecutingContext;

                if (actionExecutingContext?.ActionArguments != null)
                {
                    // 尝试从各种可能的参数中提取
                    foreach (var arg in actionExecutingContext.ActionArguments.Values)
                    {
                        if (arg != null)
                        {
                            var targetIds = ExtractTargetIdsFromObject(arg, parameterName);
                            if (targetIds?.Any() == true)
                            {
                                return targetIds;
                            }
                        }
                    }
                }

                return new List<string>();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "提取目标机组ID列表时发生错误");
                return new List<string>();
            }
        }

        /// <summary>
        /// 从对象中提取目标ID列表
        /// </summary>
        private List<string> ExtractTargetIdsFromObject(object obj, string parameterName)
        {
            try
            {
                var objType = obj.GetType();
                
                // 尝试通过属性获取
                var property = objType.GetProperty(parameterName, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                if (property != null && property.PropertyType == typeof(List<string>))
                {
                    return property.GetValue(obj) as List<string>;
                }

                // 尝试通过字段获取
                var field = objType.GetField(parameterName, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                if (field != null && field.FieldType == typeof(List<string>))
                {
                    return field.GetValue(obj) as List<string>;
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 提取原始请求对象
        /// </summary>
        private object ExtractRequestObject(ActionExecutedContext context)
        {
            var actionExecutingContext = context.HttpContext.Items["ActionExecutingContext"] as ActionExecutingContext;
            
            if (actionExecutingContext?.ActionArguments != null)
            {
                // 返回第一个非字符串类型的参数（通常是请求DTO）
                foreach (var arg in actionExecutingContext.ActionArguments.Values)
                {
                    if (arg != null && arg.GetType() != typeof(string))
                    {
                        return arg;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 执行批量操作方法
        /// </summary>
        private async Task<List<BatchItemResult>> ExecuteBatchMethodAsync(
            object controller, 
            MethodInfo batchMethod, 
            List<string> targetTurbineIds, 
            object originalRequest,
            BatchOperationAttribute attribute)
        {
            var results = new List<BatchItemResult>();
            var semaphore = new SemaphoreSlim(attribute.MaxConcurrency, attribute.MaxConcurrency);
            var tasks = new List<Task>();

            foreach (var turbineId in targetTurbineIds)
            {
                tasks.Add(ExecuteSingleBatchOperation(
                    controller, batchMethod, turbineId, originalRequest, 
                    results, semaphore, attribute.IgnoreErrors));
            }

            await Task.WhenAll(tasks);
            return results;
        }

        /// <summary>
        /// 执行单个批量操作
        /// </summary>
        private async Task ExecuteSingleBatchOperation(
            object controller,
            MethodInfo batchMethod,
            string turbineId,
            object originalRequest,
            List<BatchItemResult> results,
            SemaphoreSlim semaphore,
            bool ignoreErrors)
        {
            await semaphore.WaitAsync();
            
            try
            {
                var stopwatch = Stopwatch.StartNew();
                
                try
                {
                    // 调用批量操作方法
                    var result = batchMethod.Invoke(controller, new object[] { turbineId, originalRequest });
                    
                    // 处理异步方法
                    if (result is Task task)
                    {
                        await task;
                        
                        // 获取Task<T>的结果
                        if (task.GetType().IsGenericType)
                        {
                            var property = task.GetType().GetProperty("Result");
                            result = property?.GetValue(task);
                        }
                        else
                        {
                            result = "操作完成";
                        }
                    }

                    stopwatch.Stop();

                    lock (results)
                    {
                        // 查询机组名
                        var turbine = DevTreeManagement.GetWindTurbine(turbineId);
                        results.Add(new BatchItemResult
                        {
                            TurbineId = turbine?.WindTurbineName ?? turbineId,
                            Success = true,
                            Result = result,
                            ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                        });
                    }
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    
                    var errorMessage = ex.InnerException?.Message ?? ex.Message;
                    _logger.LogWarning(ex, "批量操作失败，机组ID: {TurbineId}, 错误: {Error}", turbineId, errorMessage);

                    lock (results)
                    {
                        // 查询机组名
                        var turbine = DevTreeManagement.GetWindTurbine(turbineId);
                        results.Add(new BatchItemResult
                        {
                            TurbineId = turbine?.WindTurbineName ?? turbineId,
                            Success = false,
                            ErrorMessage = errorMessage,
                            ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                        });
                    }

                    if (!ignoreErrors)
                    {
                        throw;
                    }
                }
            }
            finally
            {
                semaphore.Release();
            }
        }

        /// <summary>
        /// 创建包含批量操作结果的ApiResponse格式
        /// </summary>
        private object CreateApiResponseWithBatch(object mainResult, List<BatchItemResult> batchResults)
        {
            // 检查mainResult是否已经是ApiResponse格式
            if (mainResult != null)
            {
                var mainResultType = mainResult.GetType();

                // 如果是ApiResponse<T>格式
                if (mainResultType.IsGenericType &&
                    (mainResultType.GetGenericTypeDefinition().Name.Contains("ApiResponse") ||
                     mainResultType.Name.Contains("ApiResponse")))
                {
                    // 创建一个动态对象来包装结果
                    var codeProperty = mainResultType.GetProperty("Code");
                    var msgProperty = mainResultType.GetProperty("Msg");
                    var dataProperty = mainResultType.GetProperty("Data");

                    var code = codeProperty?.GetValue(mainResult) ?? 1;
                    var msg = msgProperty?.GetValue(mainResult) ?? "Success";
                    var data = dataProperty?.GetValue(mainResult);

                    return new
                    {
                        code = code,
                        msg = msg,
                        data = data,
                        batchResults = new
                        {
                            results = batchResults,
                            hasErrors = batchResults.Any(r => !r.Success),
                            successCount = batchResults.Count(r => r.Success),
                            failureCount = batchResults.Count(r => !r.Success),
                            totalCount = batchResults.Count
                        }
                    };
                }
            }

            // 如果不是ApiResponse格式，创建默认格式
            return new
            {
                code = 1,
                msg = "Success",
                data = mainResult,
                batchResults = new
                {
                    results = batchResults,
                    hasErrors = batchResults.Any(r => !r.Success),
                    successCount = batchResults.Count(r => r.Success),
                    failureCount = batchResults.Count(r => !r.Success),
                    totalCount = batchResults.Count
                }
            };
        }

        /// <summary>
        /// 清理缓存的数据
        /// </summary>
        private void CleanupCachedData(ActionExecutedContext context)
        {
            try
            {
                // 清理原始测量位置记录缓存
                context.HttpContext.Items.Remove("OriginalMeasLocRecords");
                context.HttpContext.Items.Remove("OriginalProcessMeasLocRecords");
                context.HttpContext.Items.Remove("OriginalWorkingConditionMeasLocRecords");
                context.HttpContext.Items.Remove("OriginalRotSpdLocRecords");
                context.HttpContext.Items.Remove("OriginalSvmMeasLocRecords");
                context.HttpContext.Items.Remove("OriginalModbusMeasLocRecords");
                context.HttpContext.Items.Remove("OriginalMCSRegisterRecords");

                // 测量方案
                context.HttpContext.Items.Remove("SolutionDelRecords");
                context.HttpContext.Items.Remove("SolutionEditRecords");

                // 波形定义
                context.HttpContext.Items.Remove("OriginalWaveDefRecords");
                context.HttpContext.Items.Remove("OriginalWaveDefDefRecords");

                context.HttpContext.Items.Remove("OriginalVCWaveDefRecords");
                context.HttpContext.Items.Remove("OriginalVCWaveDefDefRecords");

                context.HttpContext.Items.Remove("OriginalEnvWaveDefRecords");
                context.HttpContext.Items.Remove("OriginalEnvWaveDefDelRecords");

                _logger.LogDebug("已清理批量操作缓存数据");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "清理批量操作缓存数据时发生错误");
            }
        }
    }
}
