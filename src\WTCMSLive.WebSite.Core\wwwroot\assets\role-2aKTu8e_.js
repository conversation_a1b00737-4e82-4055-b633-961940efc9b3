import{W as F}from"./table-RP3jLHlo.js";import{O as G}from"./index-CzSbT6op.js";import{dk as q,r as i,y as A,w as B,f as D,d as f,u as E,o as g,i as x,b as _,m as a}from"./index-BjOW8S1L.js";import{u as J}from"./role-B1IC8Ilo.js";import{_ as L}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as V}from"./tools-zTE6InS0.js";import{M as W}from"./index-BnSFuLp6.js";import"./ActionButton-C_grUmdF.js";import"./styleChecker-CFtINSLw.js";import"./index-7iPMz_Qy.js";import"./initDefaultProps-P4j1rGDC.js";import"./shallowequal-gCpTBdTi.js";import"./index-DTxROkTj.js";import"./index-kP-mINcM.js";import"./index-BJEkaghg.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";const j={__name:"role",setup(z){const u=J(),k=q(),d=i(""),n=i(""),s=i({}),w=i([]),p=i(!1);let r=window.localStorage.getItem("role")?JSON.parse(window.localStorage.getItem("role")).userRole:{};const T=[{title:"角色名称",dataIndex:"roleName",width:150,align:"center"},{title:"描述",dataIndex:"roleDescription",inputType:"textarea",maxLength:100,width:300,align:"center"},{title:"页面权限",dataIndex:"modules",inputType:"",customRender:({text:e,record:t})=>t.modules&&t.modules.length?t.modules.map(l=>l.moduleName).join("，"):""}],y=[{title:"角色名称",dataIndex:"roleName",isrequired:!0},{title:"描述",dataIndex:"roleDescription",inputType:"textarea",maxLength:100,width:800},{title:"页面权限",slotName:"permissionTitle",hidden:!0},{title:"根级",dataIndex:"permission1",inputType:"checkboxGroup",width:800,isrequired:!0,selectOptions:[{label:"状态监测",value:"21"},{label:"采集单元监测",value:"22"},{label:"设备配置",value:"23"},{label:"机型管理",value:"24"},{label:"参数导入",value:"25"}]},{dataIndex:"permission2",inputType:"checkboxGroup",width:800,title:"厂站级",isrequired:!0,selectOptions:[{label:"状态监测",value:"31"},{label:"采集单元监测",value:"32"},{label:"设备配置",value:"33"},{label:"主控配置",value:"34"},{label:"采集单元配置",value:"35"},{label:"服务器软件配置",value:"36"},{label:"网络验证测试",value:"37"}]},{dataIndex:"permission3",inputType:"checkboxGroup",width:800,title:"设备级",isrequired:!0,selectOptions:[{label:"设备监测",value:"41"},{label:"特征值监测",value:"42"},{label:"采集单元监测",value:"43"},{label:"设备配置",value:"44"},{label:"主控配置",value:"45"},{label:"采集单元配置",value:"46"},{label:"Modbus配置",value:"47"},{label:"测量定义配置",value:"48"},{label:"报警定义配置",value:"49"},{label:"测量方案配置",value:"410"}]},{dataIndex:"permission4",inputType:"checkboxGroup",width:800,title:"系统设置",selectOptions:[{label:"密码设定",value:"11"},{label:"角色管理",value:"12"},{label:"用户管理",value:"13"},{label:"系统日志",value:"14"},{label:"模版管理",value:"15"},{label:"性能监控",value:"16"},{label:"服务器软件控制",value:"17"}]}],c=i(!1),S=E(),b=A({table:[],tableColumns:T}),m=async()=>{p.value=!0,b.table=await u.fetchGetrolelist(),p.value=!1};B(()=>S.params.id,()=>{m()},{immediate:!0});const I=()=>{c.value=!0},v=e=>{c.value=!1,w.value=[],s.value={},d.value="",n.value=""},C=e=>{d.value="添加角色",n.value="add",s.value={permission1:["21"],permission2:["31"],permission3:["41"]},I()},R=async e=>{const{record:t}=e;if(r&&r.roleID&&r.roleID===t.roleID){a.error("当前角色不能删除!当前用户正在使用！");return}let l=await u.fetchDeleterole({roleID:t.roleID});l&&l.code===1?(m(),a.success("提交成功")):a.error("提交失败:"+l.msg)},N=e=>{const{rowData:t}=e;d.value="编辑角色",n.value="edit";let l={permission1:[],permission2:[],permission3:[],permission4:[]};t.modules&&t.modules.length&&t.modules.map(o=>{switch(o.moduleID.split("")[0]){case"2":l.permission1.push(o.moduleID);break;case"3":l.permission2.push(o.moduleID);break;case"4":l.permission3.push(o.moduleID);break;case"1":l.permission4.push(o.moduleID);break}}),s.value={...t,...l},I()},O=async e=>{switch(n.value){case"add":let t=await u.fetchAddrole({...e,roleDescription:e.roleDescription||"",moduleIds:[...e.permission1,...e.permission2,...e.permission3,...e.permission4||[]]});t&&t.code===1?(m(),a.success("提交成功"),v()):a.error("提交失败:"+t.msg);break;case"edit":let l=[...e.permission1,...e.permission2,...e.permission3,...e.permission4||[]];l=[...new Set(l)];let o=await u.fetchEditrole({...e,roleDescription:e.roleDescription||"",roleID:s.value.roleID,moduleIds:l});if(o&&o.code===1){if(r&&r.roleID&&r.roleID===s.value.roleID){let h=window.localStorage.getItem("role")?JSON.parse(window.localStorage.getItem("role")).moduleIds:[];if(h.length!==l.length||h.some(M=>!l.includes(M))){a.info("当前角色权限已修改，请重新登录！"),k.logout();return}}m(),a.success("提交成功"),v()}else a.error("提交失败:"+o.msg);break}};return(e,t)=>{const l=W,o=V;return g(),D(o,{spinning:p.value,size:"large"},{default:f(()=>[x("div",null,[_(F,{tableTitle:"角色列表","table-key":"0","table-columns":b.tableColumns,noBatchApply:!0,"table-operate":["edit","delete","add"],"record-key":"ModbusUnitID","table-datas":b.table,onAddRow:C,onDeleteRow:R,onEditRow:N,actionCloumnProps:{width:120,align:"center"}},null,8,["table-columns","table-datas"])]),_(l,{maskClosable:!1,width:"1000px",open:c.value,title:d.value,footer:"",onCancel:v},{default:f(()=>[(g(),D(G,{titleCol:y,initFormData:s.value,key:n.value,currentColumns:w.value,onSubmit:O},{permissionTitle:f(()=>t[0]||(t[0]=[x("div",{class:"permissionTitle"}," 页面权限： ",-1)])),_:1},8,["initFormData","currentColumns"]))]),_:1},8,["open","title"])]),_:1},8,["spinning"])}}},ne=L(j,[["__scopeId","data-v-d6dce312"]]);export{ne as default};
