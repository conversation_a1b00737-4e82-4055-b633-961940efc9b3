﻿namespace WTCMSLive.WebSite
{
    public class UnitConfig
    {
        private static UnitList unitConfig;
        private static string FileName = "";

        private UnitConfig()
        {
            GetUnitConfig();
        }

        private static void GetUnitConfig()
        {
            try
            {
                // 获取文件路径
                string fileName = Path.Combine(AppContext.BaseDirectory, "Scripts", "UnitNameConfig.xml");

                if (File.Exists(fileName))
                {
                    unitConfig = AppFramework.Utility.XmlFileHelper<UnitList>.Load(fileName);
                }
                else
                {
                    unitConfig = new UnitList();
                    CMSFramework.Logger.Logger.LogInfoMessage("单位配置文件丢失，请检查文件夹 /Scripts/UnitNameConfig.xml");
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.GetExceptionMessage(ex);
            }
        }

        public static string GetUnitNameByWaveType(string wavetypename)
        {
            if (unitConfig == null)
            {
                GetUnitConfig();
            }
            if (unitConfig != null)
            {
                var unit = unitConfig.UnitConfigList.Find(item => item.UnitType == wavetypename);
                if (unit != null)
                {
                    string UnitName = unit.UnitName;
                    return UnitName;//.Replace("mv/", "");
                }
            }
            return "";
        }

        /// <summary>
        /// 对比客户设置的单位是否和配置文件相同，如果不同，更新配置文件信息
        /// </summary>
        /// <param name="wavetypename">波形类型</param>
        /// <param name="unitName">单位名称</param>
        public static void CheckUnit(string wavetypename, string unitName)
        {
            if (unitConfig == null)
            {
                GetUnitConfig();
            }
            var unit = unitConfig.UnitConfigList.Find(item => item.UnitType == wavetypename);
            if (unit != null)
            {
                if (unitName != unit.UnitName)
                {
                    unit.UnitName = unitName;
                    AppFramework.Utility.XmlFileHelper<UnitList>.Save(unitConfig, FileName);
                }
            }
            else
            {
                unitConfig.UnitConfigList.Add(new Unit()
                {
                    UnitType = wavetypename,
                    UnitName = unitName
                });
                AppFramework.Utility.XmlFileHelper<UnitList>.Save(unitConfig, FileName);
            }
        }

        internal static string GetUnitNameBySignalType(string signalType)
        {
            //string SignalTypeName = AppFramework.Utility.EnumHelper.GetDescription((WRD.AMS.BusinessEntities.EnumSensorType)int.Parse(signalType)).Replace("传感器", "");
            string SignalTypeName = signalType;
            return UnitConfig.GetUnitNameByWaveType(SignalTypeName);
        }
    }

    public class UnitList
    {
        public List<Unit> UnitConfigList { get; set; }
    }

    public class Unit
    {
        public string UnitType { get; set; }
        public string UnitName { get; set; }
    }
}