﻿using System.Collections.Generic;
using System.Linq;
using CMSFramework.BusinessEntity;
using Microsoft.EntityFrameworkCore;

namespace WTCMSLive.BusinessModel
{
    public static class DevTreeManagement
    {
        #region 风电场管理
        /// <summary>
        /// 获取风电场实体
        /// </summary>
        /// <param name="_windParkId"></param>
        public static WindPark GetWindPark(string _windParkId)
        {
            WindPark windpark = new WindPark();
            if (string.IsNullOrEmpty(_windParkId))
            {
                return windpark;
            }
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                windpark = ctx.DevWindParks.FirstOrDefault(obj=>obj.WindParkID == _windParkId);
                if (windpark != null)
                {
                    windpark.WindTurbineList = ctx.DevWindTurbines.Where(item => item.WindParkID == _windParkId).OrderBy(item => item.WindTurbineID).ToList();
                }
                
            }
            return windpark;
        }

        public static WindPark GetWindParkAndComp(string _windParkId)
        {
            WindPark windpark = new WindPark();
            if (string.IsNullOrEmpty(_windParkId))
            {
                return windpark;
            }
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                windpark = ctx.DevWindParks.FirstOrDefault(obj => obj.WindParkID == _windParkId);
                if (windpark != null)
                {
                    windpark.WindTurbineList = ctx.DevWindTurbines.Where(item => item.WindParkID == _windParkId).OrderBy(item => item.WindTurbineID).ToList();
                    foreach(var tur in windpark.WindTurbineList)
                    {
                        tur.TurComponentList = ctx.DevTurComponents.Where(t => t.WindTurbineID == tur.WindTurbineID).ToList();
                    }
                }

            }
            return windpark;
        }

        /// <summary>
        /// 添加风电场
        /// </summary>
        /// <param name="_windPark"></param>
        public static void AddWindPark(WindPark _windPark)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevWindParks.Add(_windPark);
                ctx.SaveChanges();
            }
        }
        /// <summary>
        /// 删除风场
        /// </summary>
        /// <param name="_windParkId"></param>
        public static void DelDevWindParkByWindParkID(string  _windParkId)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevWindParks.Remove(ctx.DevWindParks.Find(_windParkId));
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 获取风电场列表
        /// </summary>
        /// <returns></returns>
        public static List<WindPark> GetWindParkList()
        {
            List<WindPark> windparkList = new List<WindPark>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                windparkList = ctx.DevWindParks.ToList();
                List<WindTurbine> turbineList = ctx.DevWindTurbines.ToList();
                foreach (WindPark wp in windparkList)
                {
                    wp.WindTurbineList = turbineList.FindAll(item => item.WindParkID == wp.WindParkID);
                }
            }
            return windparkList;
        }

        /// <summary>
        /// 修改风电场实体
        /// </summary>
        /// <param name="_windPark"></param>
        public static void EditWindPark(WindPark _windPark)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevWindParks.Attach(_windPark);
                ctx.Entry(_windPark).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        /// <summary>
        ///  获取风电场实体
        /// </summary>
        /// <param name="_turID"></param>
        /// <returns></returns>
        public static WindPark GetWindParkByTurID(string _turID)
        {
            WindPark park = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                WindTurbine wt = ctx.DevWindTurbines.Find(_turID);
                if (wt != null)
                {
                   park = ctx.DevWindParks.Find(wt.WindParkID);
                }
            }
            return park;
        }

        public static WindPark GetWindParkByParkID(string _ParkID)
        {
            WindPark park = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
               park = ctx.DevWindParks.Find(_ParkID);
            }
            return park;
        }
        #endregion 风电场管理

        #region 机组管理

        public static void AddWindTurbine_Manager(WindTurbine _windTurbine)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevWindTurbines.Add(_windTurbine);
                ctx.SaveChanges();
                ctx.DevTurComponents.AddRange(_windTurbine.TurComponentList);
                if (_windTurbine.RotSpdMeasLoc != null)
                {
                    ctx.DevMeasLocRotSpds.Add(_windTurbine.RotSpdMeasLoc);
                }
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 修改机组实体
        /// </summary>
        /// <param name="_windTurbine"></param>
        public static void EditWindTurbine(WindTurbine _windTurbine)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevWindTurbines.Attach(_windTurbine);
                ctx.Entry(_windTurbine).State = EntityState.Modified;
                ctx.SaveChanges();
            }
            using (CMSFramework.EF.DauContext ctx1_ = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                WindDAU dau = ctx1_.DAUnits.FirstOrDefault(item => item.WindTurbineID == _windTurbine.WindTurbineID);
                if (dau != null)
                {
                    dau.DAUName = _windTurbine.WindTurbineName;
                    dau.MeasDefVersion += 1;
                    ctx1_.DAUnits.Attach(dau);
                    ctx1_.Entry(dau).State = EntityState.Modified;
                    ctx1_.SaveChanges();
                }
                SVMUnit svm = ctx1_.SVMUnits.FirstOrDefault(item => item.AssocWindTurbineID == _windTurbine.WindTurbineID);
                if (svm != null)
                {
                    svm.SVMName = _windTurbine.WindTurbineName;
                    ctx1_.SVMUnits.Attach(svm);
                    ctx1_.Entry(svm).State = EntityState.Modified;
                    ctx1_.SaveChanges();
                }
            }
        }

        /// <summary>
        /// 删除机组实体
        /// </summary>
        /// <param name="_windTurbineId"></param>
        public static void DeleteWindTurbine(string _windTurbineId)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevWindTurbines.Remove(ctx.DevWindTurbines.Find(_windTurbineId));
                ctx.SaveChanges();
                try
                {
                    // sqlite外键约束暂时无法开启，先手动删除
                    ctx.DevMeasLocRotSpds.RemoveRange(ctx.DevMeasLocRotSpds.Where(t => t.WindTurbineID == _windTurbineId));
                    ctx.DevMeasLocProcesses.RemoveRange(ctx.DevMeasLocProcesses.Where(t => t.WindTurbineID == _windTurbineId));
                    ctx.DevMeasLocSVMs.RemoveRange(ctx.DevMeasLocSVMs.Where(t => t.WindTurbineID == _windTurbineId));
                    ctx.DevMeasLocVibrations.RemoveRange(ctx.DevMeasLocVibrations.Where(t => t.WindTurbineID == _windTurbineId));
                    ctx.DevTurComponents.RemoveRange(ctx.DevTurComponents.Where(t => t.WindTurbineID == _windTurbineId));

                    ctx.MeasLoc_Modbus.RemoveRange(ctx.MeasLoc_Modbus.Where(t => t.WindTurbineID == _windTurbineId));

                    ctx.SaveChanges();
                }
                catch { }
            }
        }

        /// <summary>
        /// 根据风电场Id获取未分配DAU机组列表
        /// </summary>
        /// <param name="_windParkId"></param>
        public static List<WindTurbine> GetNotUsedDAUTurListByParkId(string _windParkId)
        {
            List<WindTurbine> list = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list = ctx.DevWindTurbines.Where(item => item.WindParkID == _windParkId).ToList();
            }
            List<WindDAU> dauList = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                dauList= ctx.DAUnits.Where(item => item.WindParkID == _windParkId).ToList();
            }
            if (dauList == null || dauList.Count == 0)
                return list;
            dauList.ForEach(item => {
                list.Remove(list.Find(t => t.WindTurbineID == item.WindTurbineID));
            });
            return list;
        }

        /// <summary>
        /// 根据风电场ID获取未绑定晃度仪的机组(并且存在机舱部件)
        /// </summary>
        /// 机组中增加塔筒筛选 wangy 2015年9月7日
        /// <param name="WindParkID"></param>
        /// <returns></returns>
        public static List<WindTurbine> GetNotSVMTurListByParkId(string _windParkId)
        {
            List<WindTurbine> turList = new List<WindTurbine>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                turList = ctx.DevWindTurbines.Where(item => item.WindParkID == _windParkId).ToList();
                //List<WindTurbineComponent> componentList = ctx.DevTurComponents.Where(item => item.WindTurbineID.Contains(_windParkId)).ToList();
                List<WindTurbineComponent> componentList = (from devComp in ctx.DevTurComponents
                                join t in turList.Select(t => t.WindTurbineID).ToList()
                                on devComp.WindTurbineID equals t
                                select devComp).ToList();
                turList.ForEach(item => {
                    WindTurbineComponent component = componentList.Find(c => c.WindTurbineID == item.WindTurbineID && (c.ComponentName == "机舱" || c.ComponentName == "塔筒"));
                    if (component == null)
                    {
                        turList.Remove(item);
                    }
                });
            }
            List<SVMUnit> svmList = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                //svmList = ctx.SVMUnits.Where(item => item.AssocWindTurbineID.Contains(_windParkId)).ToList();
                svmList = ctx.SVMUnits.ToList();
                svmList = svmList.Where(t => t.AssocWindTurbineID.Contains(_windParkId)).ToList();
            }
            if (svmList == null || svmList.Count == 0)
                return turList;
            svmList.ForEach(item =>
            {
                turList.Remove(turList.Find(t => t.WindTurbineID == item.AssocWindTurbineID));
            });
            return turList;
        }


        /// <summary>
        /// 获取所有的机组列表
        /// </summary>
        /// <returns></returns>
        public static List<WindTurbine> GetTurbinesList()
        {
            List<WindTurbine> turList = new List<WindTurbine>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                turList = ctx.DevWindTurbines.ToList().OrderBy(item => item.WindTurbineName).ToList();
                List<WindTurbineComponent> ComponentList = ctx.DevTurComponents.ToList();
                turList.ForEach(item => {
                    item.TurComponentList = ComponentList.FindAll(com => com.WindTurbineID == item.WindTurbineID);
                });
            }
            return turList;
        }

        public static List<WindTurbine> GetTurbinesList(string _windParkId)
        {
            List<WindTurbine> turList = new List<WindTurbine>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                turList = ctx.DevWindTurbines.Where(t=>t.WindParkID == _windParkId).OrderBy(item => item.WindTurbineName).ToList();
                List<WindTurbineComponent> ComponentList = ctx.DevTurComponents.ToList();
                turList.ForEach(item => {
                    item.TurComponentList = ComponentList.FindAll(com => com.WindTurbineID == item.WindTurbineID);
                });
            }
            return turList;
        }
        /// <summary>
        /// 根据风电场Id获取机组列表
        /// </summary>
        /// <param name="_windParkId"></param>
        /// <returns></returns>
        public static List<WindTurbine> GetTurbinesListByWindParkId(string _windParkId)
        {
            List<WindTurbine> turList = new List<WindTurbine>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                turList = ctx.DevWindTurbines.Where(item => item.WindParkID == _windParkId).OrderBy(item => item.WindTurbineName).ToList();
            }
            return turList;
        }
        
        /// <summary>
        /// 根据风场ID和机组型号获取机组列表
        /// </summary>
        /// <param name="_windParkID"></param>
        /// <param name="_turbineModel"></param>
        /// <returns></returns>
        public static List<WindTurbine> GetTurbinesByParkIdAndTurbineModel(string _windParkId, string _turbineModel)
        {
            List<WindTurbine> turList = new List<WindTurbine>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                turList = ctx.DevWindTurbines.Where(item => item.WindParkID == _windParkId && item.WindTurbineModel==_turbineModel).ToList();
            }
            return turList;
        }

        /// <summary>
        /// 获取机组型号列表
        /// </summary>
        /// <returns></returns>
        public static List<WindTurbineModel> GetWindTurbineModelList()
        {
            List<WindTurbineModel> list = new List<WindTurbineModel>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list= ctx.DevWTurbineModels.OrderByDescending(t=>t.CreatedAt).ToList();
            }
            return list;
        }

        /// <summary>
        /// 获取机组型号
        /// </summary>
        /// <returns></returns>
        public static WindTurbineModel GetWindTurbineModel(string TurbineModel)
        {
            WindTurbineModel turModel = new WindTurbineModel();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                turModel = ctx.DevWTurbineModels.Find(TurbineModel);
            }
            return turModel;
        }

        /// <summary>
        /// 更新机型参数
        /// </summary>
        /// <param name="modelToUpdate"></param>
        public static void UpdateWindTurbineModel(WindTurbineModel modelToUpdate)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                // 查找数据库中的机型
                var dbModel = ctx.DevWTurbineModels.FirstOrDefault(m => m.TurbineModel == modelToUpdate.TurbineModel);
                if (dbModel != null)
                {
                    // 更新数据库中的机型信息
                    dbModel.StructureType = modelToUpdate.StructureType;
                    dbModel.RatedPower = modelToUpdate.RatedPower;
                    dbModel.BladeNum = modelToUpdate.BladeNum;
                    dbModel.Manufactory = modelToUpdate.Manufactory;
                    dbModel.RatedGeneratorSpeed = modelToUpdate.RatedGeneratorSpeed;
                    dbModel.GridConnectedGeneratorSpeed = modelToUpdate.GridConnectedGeneratorSpeed;
                    dbModel.UpdatedAt = modelToUpdate.UpdatedAt;
                    dbModel.TowerHeight = modelToUpdate.TowerHeight;

                    ctx.SaveChanges(); // 提交更改
                }
            }
        }

        /// <summary>
        /// 获取机组实体
        /// </summary>
        /// <param name="_windTurbineId"></param>
        /// <returns></returns>
        public static WindTurbine GetWindTurbine(string _windTurbineId)
        {
            WindTurbine tur = new WindTurbine();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                tur = ctx.DevWindTurbines.Find(_windTurbineId);
                if (tur != null)
                {
                    tur.WTurbineModel = ctx.DevWTurbineModels.Find(tur.WindTurbineModel);
                    tur.DevWindPark = ctx.DevWindParks.Find(tur.WindParkID);
                }
            }
            return tur;
        }

        public static WindTurbine GetAllWindTurbine(string _windTurbineId)
        {
            WindTurbine tur = new WindTurbine();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                tur = ctx.DevWindTurbines.Find(_windTurbineId);
                if (tur != null)
                {
                    tur.WTurbineModel = ctx.DevWTurbineModels.Find(tur.WindTurbineModel);
                    tur.VibMeasLocList = ctx.DevMeasLocVibrations.Where(item => item.WindTurbineID == _windTurbineId).ToList();
                    tur.DevMeasLocRotSpds = ctx.DevMeasLocRotSpds.Where(item => item.WindTurbineID == _windTurbineId).ToList();
                    tur.ProcessMeasLocList = ctx.DevMeasLocProcesses.Where(item => item.WindTurbineID == _windTurbineId).ToList();
                    tur.WTurbineModel = ctx.DevWTurbineModels.Find(tur.WindTurbineModel);
                }
            }
            return tur;
        }

        /// <summary>
        /// 保存设备树
        /// </summary>
        /// <param name="_windTurbine"></param>
        public static void AddWindTurbine_ManagerOverLoad(WindTurbine _windTurbine)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevWindTurbines.Add(_windTurbine);
                // 外键约束
                ctx.SaveChanges();
                ctx.DevTurComponents.AddRange(_windTurbine.TurComponentList);
                ctx.SaveChanges();
                if (_windTurbine.RotSpdMeasLoc != null)
                {
                    ctx.DevMeasLocRotSpds.Add(_windTurbine.RotSpdMeasLoc);
                }
                
                ctx.DevMeasLocVibrations.AddRange(_windTurbine.VibMeasLocList);
                ctx.DevMeasLocSVMs.AddRange(_windTurbine.MeasLocSVMList);
                ctx.DevMeasLocProcesses.AddRange(_windTurbine.ProcessMeasLocList);
                ctx.DevMeasLocVoltageCurrents.AddRange(_windTurbine.VoltageCurrentMeasLocList);

                //ctx.MeasLoc_Modbus.AddRange(_windTurbine.DevMeasLocModbus);
                ctx.SaveChanges();
                
            }
        }
        /// <summary>
        /// 获取设备树
        /// </summary>
        /// <param name="_windTurbineId"></param>
        /// <returns></returns>
        public static WindTurbine GetAllWindTurbineOverLoad(string _windTurbineId)
        {
            WindTurbine tur = new WindTurbine();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                tur = ctx.DevWindTurbines.Find(_windTurbineId);
                if (tur != null)
                {
                    tur.WTurbineModel = ctx.DevWTurbineModels.Find(tur.WindTurbineModel);
                    tur.VibMeasLocList = ctx.DevMeasLocVibrations.Where(item => item.WindTurbineID == _windTurbineId).ToList();
                    tur.DevMeasLocRotSpds = ctx.DevMeasLocRotSpds.Where(item => item.WindTurbineID == _windTurbineId).ToList();
                    tur.ProcessMeasLocList = ctx.DevMeasLocProcesses.Where(item => item.WindTurbineID == _windTurbineId).ToList();
                    //tur.WTurbineModel = ctx.DevWTurbineModels.Find(tur.WindTurbineModel);
                    tur.TurComponentList = ctx.DevTurComponents.Where(item => item.WindTurbineID == _windTurbineId).ToList();
                    tur.MeasLocSVMList = ctx.DevMeasLocSVMs.Where(item => item.WindTurbineID == _windTurbineId).ToList();

                    tur.VoltageCurrentMeasLocList = ctx.DevMeasLocVoltageCurrents.Where(item => item.WindTurbineID == _windTurbineId).ToList();

                    tur.DevMeasLocModbus = ctx.MeasLoc_Modbus.Where(t => t.WindTurbineID == _windTurbineId).ToList();
                }
            }
            return tur;
        }

        #endregion 机组管理

        #region 部件管理

        public static List<WindTurbineComponent> GetAllComList()
        {
            List<WindTurbineComponent> turComList = new List<WindTurbineComponent>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                turComList = ctx.DevTurComponents.ToList();
            }

            return turComList;
        }

        /// <summary>
        /// 获取部件列表
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <returns></returns>
        public static List<WindTurbineComponent> GetComListByTurbineId(string _turbineId)
        {
            List<WindTurbineComponent> turComList = new List<WindTurbineComponent>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                turComList = ctx.DevTurComponents.Where(item => item.WindTurbineID == _turbineId).ToList();
            }
            return turComList;
        }
        /// <summary>
        /// 获取部件实体
        /// </summary>
        /// <param name="_turComponentid"></param>
        /// <returns></returns>
        public static WindTurbineComponent GetTurbComponent(string _turComponentid)
        {
            WindTurbineComponent turCom = new WindTurbineComponent();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                turCom = ctx.DevTurComponents.Find(_turComponentid);
            }
            return turCom;
        }

        public static int AddDevComp(WindTurbineComponent myComponent)
        {
            int result =0;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevTurComponents.Add(myComponent);
                result = ctx.SaveChanges();
            }
            return result;
        }
        #endregion

        #region 测量位置管理

        #region 振动测量位置
        /// <summary>
        /// 获取振动测量位置列表
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <param name="_componentId"></param>
        /// <returns></returns>
        public static List<MeasLoc_Vib> GetVibMeasLocationByComId(string _componentId)
        {
            List<MeasLoc_Vib> list = new List<MeasLoc_Vib>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list = ctx.DevMeasLocVibrations.Where(item => item.ComponentID == _componentId).ToList();
                WindTurbineComponent component = ctx.DevTurComponents.Find(_componentId);
                list.ForEach(item => {
                    item.DevTurComponent = component;
                });
            }
            return list;
        }

        public static List<MeasLoc_Vib> GetVibMeasLocationByParkID(string _parkID)
        {
            List<MeasLoc_Vib> list = new List<MeasLoc_Vib>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list = ctx.DevMeasLocVibrations.Where(item => item.WindTurbineID.Contains(_parkID)).ToList();
              
            }
            return list;
        }

        /// <summary>
        /// 获取振动测量位置实体
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <param name="_vibMeasLocID"></param>
        /// <returns></returns>
        public static MeasLoc_Vib GetVibMeasLocByID(string _vibMeasLocID)
        {
            MeasLoc_Vib measLocVib = new MeasLoc_Vib();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                measLocVib = ctx.DevMeasLocVibrations.Find(_vibMeasLocID);
                if(measLocVib!= null)
                {
                    measLocVib.DevTurComponent = ctx.DevTurComponents.Find(measLocVib.ComponentID);
                }
            }
            return measLocVib;
        }

        public static MeasLoc_VoltageCurrent GetVoltageCurrentMeasLocByID(string _vibMeasLocID)
        {
            MeasLoc_VoltageCurrent measLocVib = new MeasLoc_VoltageCurrent();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                measLocVib = ctx.DevMeasLocVoltageCurrents.Find(_vibMeasLocID);
                measLocVib.DevTurComponent = ctx.DevTurComponents.Find(measLocVib?.ComponentID);
            }
            return measLocVib;
        }

        /// <summary>
        /// 添加振动测量位置实体
        /// </summary>
        /// <param name="_vibMeasLoc"></param>
        /// <returns></returns>
        public static void AddVibMeasLoc(MeasLoc_Vib _vibMeasLoc)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocVibrations.Add(_vibMeasLoc);
                ctx.SaveChanges();
            }
        }

        public static void AddVibMeasLocRange(List<MeasLoc_Vib> _vibMeasLoc)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocVibrations.AddRange(_vibMeasLoc);
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 添加电流电压测量位置
        /// </summary>
        /// <param name="_vibMeasLoc"></param>
        public static void AddVoltageCurrentMeasLoc(MeasLoc_VoltageCurrent _vibMeasLoc)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocVoltageCurrents.Add(_vibMeasLoc);
                ctx.SaveChanges();
            }
        }
        /// <summary>
        /// 批量添加电流电压测量位置
        /// </summary>
        /// <param name="_vibMeasLoc"></param>
        public static void AddVoltageCurrentMeasLocRange(List<MeasLoc_VoltageCurrent> _vibMeasLoc)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocVoltageCurrents.AddRange(_vibMeasLoc);
                ctx.SaveChanges();
            }
        }
        /// <summary>
        /// 修改振动测量位置实体
        /// </summary>
        /// <param name="_vibMeasLoc"></param>
        /// <returns></returns>
        public static void EditVibMeasLoc(MeasLoc_Vib _vibMeasLoc)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocVibrations.Attach(_vibMeasLoc);
                ctx.Entry(_vibMeasLoc).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 电流电压过程量编辑
        /// </summary>
        /// <param name="_vibMeasLoc"></param>
        public static void EditVoltageCurrentMeasLoc(MeasLoc_VoltageCurrent _vibMeasLoc)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocVoltageCurrents.Attach(_vibMeasLoc);
                ctx.Entry(_vibMeasLoc).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        /// <summary>
        ///  删除振动测量位置
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <param name="_vibMeasLocID"></param>
        /// <returns></returns>
        public static bool DeleteVibMeasLoc(string _vibMeasLocID)
        {
            int count = 0;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocVibrations.Remove(ctx.DevMeasLocVibrations.Find(_vibMeasLocID));
                count = ctx.SaveChanges();
            }
            return count > 0;
        }

        /// <summary>
        /// 删除电压电流测量位置
        /// </summary>
        /// <param name="_vibMeasLocID"></param>
        /// <returns></returns>
        public static bool DeleteVoltageCurrentsMeasLoc(string _vibMeasLocID)
        {
            int count = 0;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocVoltageCurrents.Remove(ctx.DevMeasLocVoltageCurrents.Find(_vibMeasLocID));
                count = ctx.SaveChanges();
            }
            return count > 0;
        }

        /// <summary>
        /// 根据机组Id， 获取振动测量位置列表
        /// </summary>
        /// <param name="_windTurId"></param>
        /// <returns></returns>
        public static List<MeasLoc_Vib> GetVibMeasLocationByTurId(string _windTurId)
        {
            List<MeasLoc_Vib> list = new List<MeasLoc_Vib>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list = ctx.DevMeasLocVibrations.Where(item => item.WindTurbineID == _windTurId).ToList();
                List<WindTurbineComponent> component = ctx.DevTurComponents.Where(item => item.WindTurbineID == _windTurId).ToList();
                list.ForEach(item => {
                    item.DevTurComponent = component.Find(c => c.ComponentID == item.ComponentID);
                });
            }
            return list;
        }

        public static List<MeasLoc_VoltageCurrent> GetVoltageCurrentMeasLocationByTurId(string _windTurId)
        {
            List<MeasLoc_VoltageCurrent> list = new List<MeasLoc_VoltageCurrent>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list = ctx.DevMeasLocVoltageCurrents.Where(item => item.WindTurbineID == _windTurId).ToList();
                List<WindTurbineComponent> component = ctx.DevTurComponents.Where(item => item.WindTurbineID == _windTurId).ToList();
                list.ForEach(item => {
                    item.DevTurComponent = component.Find(c => c.ComponentID == item.ComponentID);
                });
            }
            return list;
        }

        /// <summary>
        /// 获取所有的测量位置列表
        /// </summary>
        /// <returns></returns>
        public static List<MeasLoc_Vib> GetAllMeasLocations()
        {
            List<MeasLoc_Vib> list = new List<MeasLoc_Vib>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list = ctx.DevMeasLocVibrations.ToList();
                var componentList = ctx.DevTurComponents.ToList();
                list.ForEach(item => {
                    item.DevTurComponent = componentList.Find(com => com.ComponentID == item.ComponentID);
                });
            }
            return list;
        }

        #endregion

        #region 转速测量位置

        /// <summary>
        /// 获取转速测量位置实体
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <param name="_rotSpdMeasLocid"></param>
        /// <returns></returns>
        public static MeasLoc_RotSpd GetRotSpdMeasLocation(string _rotSpdMeasLocid)
        {
            MeasLoc_RotSpd MeasLocRotSpd = new MeasLoc_RotSpd();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                MeasLocRotSpd = ctx.DevMeasLocRotSpds.Find(_rotSpdMeasLocid);
            }
            return MeasLocRotSpd;
        }
        
        /// <summary>
        /// 获取机组下所有转速测量位置
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <returns></returns>
        public static List<MeasLoc_RotSpd> GetRotSpdMeasLocListByTurId(string _turbineId)
        {
            List<MeasLoc_RotSpd> list = new List<MeasLoc_RotSpd>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list = ctx.DevMeasLocRotSpds.Where(item => item.WindTurbineID == _turbineId).ToList();
            }
            return list;
        }

        /// <summary>
        /// 获取配置波形定义的转速测量位置
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <returns></returns>
        public static List<MeasLoc_RotSpd> GetMDFRotSpdMeasLocListByTurId(string _turbineId)
        {
            List<MeasLoc_RotSpd> list = new List<MeasLoc_RotSpd>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list = ctx.DevMeasLocRotSpds.Where(item => item.WindTurbineID == _turbineId).ToList();
            }
            List<DAUChannel_RotSpeed> RotSpeedList = new List<DAUChannel_RotSpeed>();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                RotSpeedList = ctx.DAURotSpdChannels.Where(item => item.WindTurbineID == _turbineId).ToList();
            }
            //list.ForEach(item => {
            //    DAUChannel_RotSpeed channel = RotSpeedList.Find(rot => rot.MeasLocRotSpdID == item.MeasLocationID);
            //    if (channel == null)
            //    {
            //        list.Remove(item);
            //    }
            //});

            for (int i = list.Count - 1; i >= 0; i--)
            {
                MeasLoc_RotSpd item = list[i];
                DAUChannel_RotSpeed channel = RotSpeedList.Find(rot => rot.MeasLocRotSpdID == item.MeasLocationID);

                if (channel == null)
                {
                    list.RemoveAt(i);
                }
            }

            return list;
        }
        
        /// <summary>
        /// 转速测量位置是否被关联使用
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <param name="_rotSpdLocId"></param>
        /// <returns></returns>
        public static bool IsRotSpdMeasLocUsed(string _rotSpdLocId)
        {
            MeasLoc_RotSpd MeasLocRotSpd = new MeasLoc_RotSpd();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                MeasLocRotSpd = ctx.DevMeasLocRotSpds.Find(_rotSpdLocId);
            }
            return MeasLocRotSpd != null;
        }
        /// <summary>
        /// 修改转速测量位置实体
        /// </summary>
        /// <param name="_measLocation"></param>
        public static void EditRotSpdMeasLocation(MeasLoc_RotSpd _measLocation)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocRotSpds.Attach(_measLocation);
                ctx.Entry(_measLocation).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        #endregion 转速测量位置

        #region 工况测量位置
        /// <summary>
        /// 获取机组下 工况测量位置ID列表
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <returns></returns>
        public static List<MeasLoc_Process> GetWorkCondMeasLocByTurID(string _turbineID)
        {
            List<MeasLoc_Process> list = new List<MeasLoc_Process>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list = ctx.DevMeasLocProcesses.Where(item => item.WindTurbineID == _turbineID).ToList();
            }
            return list;
        }
        /// <summary>
        /// 获取工况测量位置实体
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <param name="_workCondMeasLocid"></param>
        /// <returns></returns>
        public static MeasLoc_Process GetWorkCondMeasLocation(string _workCondMeasLocid)
        {
            MeasLoc_Process measLocPro = new MeasLoc_Process();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                measLocPro = ctx.DevMeasLocProcesses.Find(_workCondMeasLocid);
            }
            return measLocPro;
        }

        /// <summary>
        /// 添加工况测量位置
        /// </summary>
        /// <param name="_measLocation"></param>
        public static void AddWorkCondMeasLocation(MeasLoc_Process _measLocation)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocProcesses.Add(_measLocation);
                ctx.SaveChanges();
            }
        }
        public static void AddWorkCondMeasLocationRange(List<MeasLoc_Process> _measLocation)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocProcesses.AddRange(_measLocation);
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 修改工况测量位置
        /// </summary>
        /// <param name="_measLocation"></param>
        public static void EditWorkCondMeasLocation(MeasLoc_Process _measLocation)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocProcesses.Attach(_measLocation);
                ctx.Entry(_measLocation).State= EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 删除工况测量位置
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <param name="_workCondMeasLocId"></param>
        public static void DeleteWorkCondMeasLocation(string _workCondMeasLocId)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocProcesses.Remove(ctx.DevMeasLocProcesses.Find(_workCondMeasLocId));
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 获取机组下未绑定寄存器的工况测量位置列表
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <returns></returns>
        public static List<MeasLoc_Process> GetNotUsedMCSChanLocProcessList(string _turbineID)
        {
            List<MeasLoc_Process> measLocProList = new List<MeasLoc_Process>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                measLocProList = ctx.DevMeasLocProcesses.Where(item => item.WindTurbineID == _turbineID).ToList();
            }
            List<MCSChannel> mcsChannelList = null;
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                mcsChannelList = ctx.MCSRegisters.Where(item => item.WindTurbineID == _turbineID).ToList();
            }
            if (mcsChannelList == null || mcsChannelList.Count == 0)
            {
                return measLocProList;
            }
            List<MeasLoc_Process> NotUseMCSLoc = new List<MeasLoc_Process>();
            measLocProList.ForEach(item => {
                MCSChannel channel = mcsChannelList.Find(mcs => mcs.MeasLocProcessID == item.MeasLocationID);
                if (channel == null)
                {
                    NotUseMCSLoc.Add(item);
                    //measLocProList.Remove(item);
                }
            });
            return NotUseMCSLoc;
        }

        /// <summary>
        /// 获取机组下工况测量位置列表（数据来源是DAU的）
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <returns></returns>
        public static List<MeasLoc_Process> GetMeaslocProcessListWithDAUByTurID(string _turbineID)
        {
            List<MeasLoc_Process> measLocProList = new List<MeasLoc_Process>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                measLocProList = ctx.DevMeasLocProcesses.Where(item => item.WindTurbineID == _turbineID && item.FieldBusType == EnumWorkConDataSource.WindDAU).ToList();
            }
            return measLocProList;
        }
        #endregion 工况测量位置

        #endregion 测量位置
        /// <summary>
        /// 获取机组下测量定义列表
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <returns></returns>
        public static List<MeasDefinition> GetMeasDefListByTurId(string _turbineId)
        {
            List<MeasDefinition> mdfModels = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                mdfModels = ctx.MeasDefinitions.Where(item => item.WindTurbineID == _turbineId).ToList();
            }
            return mdfModels;
        }

        /// <summary>
        ///  振动测量位置是否正在被使用
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <param name="_measLocId"></param>
        /// <returns></returns>
        public static bool IsVibMeasLocUsed(string _measLocId)
        {
            WaveDefinition waveDef = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                waveDef = ctx.MDFWaveDefinitions.Where(item => item.MeasLocationID == _measLocId).FirstOrDefault();
            }
            return waveDef != null;
        }


        public static bool IsVoltageCurrentMeasLocUsed(string _measLocId)
        {
            WaveDef_VoltageCurrent waveDef = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                waveDef = ctx.MdfWaveDefVoltageCurrents.Where(item => item.MeasLocationID == _measLocId).FirstOrDefault();
            }
            return waveDef != null;
        }
        /// <summary>
        /// 工况测量位置是否被使用
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_measLocId"></param>
        /// <returns></returns>
        public static bool IsWorkCondMeasLocUsed(string _measLocId)
        {
            MeasDef_Process ProcessDef = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ProcessDef = ctx.MDFWorkConditions.Where(item => item.MeasLocationID == _measLocId).FirstOrDefault();
            }
            return ProcessDef != null;
        }
        /// <summary>
        /// 通过测量位置名称判断工况测量位置是否被使用
        /// </summary>
        /// <param name="_turID">机组ID</param>
        /// <param name="_measLocName">测量位置名称</param>
        /// <returns></returns>
        public static bool IsWorkCondMeasLocUsedByLocName(string _turID, string _measLocName,string Datafrom)
        {
            //MeasDef_Process ProcessDef = null;
            MeasLoc_Process measLocProcess=null;

            EnumWorkConDataSource fb = EnumWorkConDataSource.WindDAU;
            if (Datafrom == "主控系统") { fb = EnumWorkConDataSource.ModbusOnTcp; }
            if (Datafrom == "Wind DAU") { fb = EnumWorkConDataSource.WindDAU; }

            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                measLocProcess = ctx.DevMeasLocProcesses.Where(item=>item.WindTurbineID == _turID && item.MeasLocName==_measLocName && item.FieldBusType == fb).FirstOrDefault();
            }
            //using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            //{
            //    if (measLocProcess != null)
            //    {
            //        ProcessDef = ctx.MDFWorkConditions.Where(item => item.MeasLocationID == measLocProcess.MeasLocationID).FirstOrDefault();
            //    }
            //}
            return measLocProcess != null;
        }
        
        ///wangy 2016年5月12日 ，修改总貌图接口
        /// <summary>
        /// 添加2.0机组总貌图
        /// </summary>
        /// <param name="_TurbineStyle"></param>
        public static void AddTurbineStyle(CMSFramework.BusinessEntity.TurbineStyle _TurbineStyle)
        {
            using (CMSFramework.EF.OViewContext ctx = new CMSFramework.EF.OViewContext(CMSFramework.BusinessEntity.ConfigInfo.DBConnName))
            {
                ctx.OViewTurbineStyles.Add(_TurbineStyle);
                ctx.SaveChanges();
            }
        }
        /// <summary>
        /// 添加总貌图映射关系表
        /// </summary>
        /// <param name="_TurbineStyleMapping"></param>
        public static void AddTurbineStyleMapping(CMSFramework.BusinessEntity.OverViewTurbineMapping _TurbineStyleMapping)
        {
            using (CMSFramework.EF.OViewContext ctx = new CMSFramework.EF.OViewContext(CMSFramework.BusinessEntity.ConfigInfo.DBConnName))
            {
                var list = ctx.OViewTurbineMapping.Where(item => item.WindTurbineID == _TurbineStyleMapping.WindTurbineID);
                if (list != null && list.Count() > 0)
                {
                    //添加映射之前，先删除已有的映射
                    ctx.OViewTurbineMapping.RemoveRange(list);
                }
                ctx.OViewTurbineMapping.Add(_TurbineStyleMapping);
                ctx.SaveChanges();
            }
        }
        /// <summary>
        /// 删除总貌图映射关系
        /// </summary>
        /// <param name="_TurbineStyleMapping"></param>
        public static void DeleteTurbineStyleMapping(OverViewTurbineMapping _TurbineStyleMapping)
        {
            using (CMSFramework.EF.OViewContext ctx = new CMSFramework.EF.OViewContext(CMSFramework.BusinessEntity.ConfigInfo.DBConnName))
            {
                ctx.OViewTurbineMapping.Attach(_TurbineStyleMapping);
                ctx.Entry(_TurbineStyleMapping).State = EntityState.Deleted;
                ctx.SaveChanges();
            }
        }

        #region CMSFramework.EF应用 接口
        /// <summary>
        /// 添加机型信息
        /// </summary>
        /// <param name="turbineModel"></param>
        /// <returns></returns>
        public static bool AddWindTurbineModel(WindTurbineModel turbineModel)
        {
            int count = 0;
            CMSFramework.BusinessEntity.WindTurbineModel turModel = new CMSFramework.BusinessEntity.WindTurbineModel();
            turModel.TurbineModel = turbineModel.TurbineModel;
            turModel.Manufactory = turbineModel.Manufactory;
            turModel.StructureType = (CMSFramework.BusinessEntity.EnumTurModelStructureType)turbineModel.StructureType;
            turModel.RatedPower = turbineModel.RatedPower;
            turModel.BladeNum = turbineModel.BladeNum;
            turModel.GridConnectedGeneratorSpeed = turbineModel.GridConnectedGeneratorSpeed;
            turModel.RatedGeneratorSpeed = turbineModel.RatedGeneratorSpeed;
            turModel.CreatedAt = turbineModel.CreatedAt;
            turModel.UpdatedAt = turbineModel.UpdatedAt;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(CMSFramework.BusinessEntity.ConfigInfo.DBConnName))
            {
                ctx.DevWTurbineModels.Add(turModel);
                count = ctx.SaveChanges();
            }
            return count > 0;
        }

        /// <summary>
        /// 获取机组总貌图
        /// </summary>
        /// <param name="turbindId"></param>
        /// <returns></returns>
        public static byte[] GetTurbineImageV2(string turbindId)
        {
            byte[] oViewStyleData = null;
            using (CMSFramework.EF.OViewContext ctx = new CMSFramework.EF.OViewContext(CMSFramework.BusinessEntity.ConfigInfo.DBConnName))
            {
                var mapping = ctx.OViewTurbineMapping.FirstOrDefault(item => item.WindTurbineID == turbindId);
                if (mapping != null)
                {
                    var oViewStyle = ctx.OViewTurbineStyles.FirstOrDefault(item => item.OViewGuid == mapping.OViewGuid);
                    if (oViewStyle != null)
                    {
                        oViewStyleData = oViewStyle.StyleText;
                    }
                }
            }
            return oViewStyleData;
        }

        #endregion
        public static byte[] GetTubineImageV2(string turbindId, string type = "bvm")
        {
            byte[] oViewStyleData = null;
            using (CMSFramework.EF.OViewContext ctx = new CMSFramework.EF.OViewContext(CMSFramework.BusinessEntity.ConfigInfo.DBConnName))
            {
                var mapping = ctx.OViewTurbineMapping.FirstOrDefault(item => item.WindTurbineID == turbindId);
                if (mapping != null)
                {
                    var oViewStyle = ctx.OViewTurbineStyles.FirstOrDefault(item => item.OViewGuid == mapping.OViewGuid);
                    if (oViewStyle != null)
                    {
                        oViewStyleData = oViewStyle.StyleText;
                    }
                }
            }
            return oViewStyleData;
        }
    }
}
