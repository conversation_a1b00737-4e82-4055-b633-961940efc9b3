﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <!--Log设定-->
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
  </configSections>

  <!--  App.Config 中增加以下配置信息
  <appSettings>
    <add key="log4net.Config" value="log4net.config"/>
  </appSettings> 
  -->

  <log4net>
    <appender name="ErrorLogger" type="log4net.Appender.RollingFileAppender">
      <!--支持多线程-->
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <file value="ErrorLog/"/>
      <preserveLogFileNameExtension value="true"/>
      <!--  -->
      <appendToFile value="true" />
      <maximumFileSize value="1024KB" />
      <!-- 最大size -->
      <maxSizeRollBackups value="50" />
      <staticLogFileName value="false" />
      <rollingStyle value="Composite" />
      <datePattern value="yyyyMM//yyyyMMdd'.log'" />
      <CountDirection value ="1"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %level (%file:%line) - %message%newline" />
      </layout>
    </appender>

    <appender name="DebugLogger" type="log4net.Appender.RollingFileAppender">
      <!--支持多线程-->
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <file value="DebugLog/"/>
      <preserveLogFileNameExtension value="true"/>
      <!--  -->
      <appendToFile value="true" />
      <maximumFileSize value="1024KB" />
      <!-- 最大size -->
      <maxSizeRollBackups value="50" />
      <staticLogFileName value="false" />
      <rollingStyle value="Composite" />
      <datePattern value="yyyyMM//yyyyMMdd'.log'" />
      <CountDirection value ="1"/>
      <!-- %logger -->
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %level (%file:%line) - %message%newline" />
      </layout>
    </appender>

    <logger additivity="true" name="DebugLog">
      <level value="DEBUG"/>
      <appender-ref ref="DebugLogger" />
    </logger>

    <logger additivity="true" name="ErrorLog">
      <level value="ERROR"/>
      <appender-ref ref="ErrorLogger" />
    </logger>

  </log4net>
</configuration>