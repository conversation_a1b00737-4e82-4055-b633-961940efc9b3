﻿using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Models;
using CMSFramework.BusinessEntity;
using WTCMSLive.WebSite.Common;
using WTCMSLive.BusinessModel.TIM;
using Newtonsoft.Json.Linq;
using System.Data.Entity.Validation;
using Microsoft.AspNetCore.Mvc;
using WTCMSLive.WebSite.Core.Models;
using Microsoft.EntityFrameworkCore;
using CMSFramework.MeasDefEntities;
using WTCMSLive.WebSite.Core.Attributes;
using Microsoft.AspNetCore.Authorization;
using System.Collections.Immutable;
using WTCMSLive.WebSite.Core.DTOs;

namespace WTCMSLive.WebSite.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class MeasdController : ControllerBase
    {
        // 王岩 2015年6月16日 16:44:06
        //测量定义配置相关方法

        #region 机组相关

        /// <summary>
        /// 获取机组型号列表
        /// </summary>
        /// <returns></returns>
        public string InitTurbineModelList()
        {
            List<WindTurbineModel> _listTurbineModel =
                    DevTreeManagement.GetWindTurbineModelList();
            return _listTurbineModel.ToJson();
        }

        #region 测量定义批量添加(不选机组的时候)

        /// <summary>
        /// 根据测量定义模板获取机组信息列表
        /// </summary>
        /// <param name="_windParkId"></param>
        /// <param name="_windTurbineModel"></param>
        /// <returns></returns>
        public string GetTurbinesByParkIdAndTurbineModel(string _windParkId, string _windTurbineModel)
        {
            return DevTreeManagement.GetTurbinesByParkIdAndTurbineModel(_windParkId, _windTurbineModel).ToJson();
        }


        #endregion

        #endregion

        #region 风机相关

        #region 测量定义

        /// <summary>
        /// 获取测量定义列表
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetMeasdList")]
        public IActionResult GetMeasdList(string turbineID)
        {
            List<MeasDefinition> list = MeasDefinitionManagement.GetMeasDefListByTurId(turbineID);
            List<WaveDef_RotSpd> WaveDef_RS = MeasDefinitionManagement.GetWaveDefListRotSpdByTrubineID(turbineID);
            List<MeasDef_Process> windTurbineDefProcess = MeasDefinitionManagement.GetMDFWorkCondLocListByTurbineId(turbineID);
            List<WindDAU> daus = DauManagement.GetDAUListByWindTurbineID(turbineID);
            WindTurbine turbine = DevTreeManagement.GetAllWindTurbine(turbineID);
            List<Meas_MeasDef> measList = new List<Meas_MeasDef>();
            List<CMSFramework.BusinessEntity.MeasDef_Ev_Process> MeasDef_Supervise_ProcessList = null;
            List<MeasLoc_Process> allProcessLocList = new List<MeasLoc_Process>();

            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                allProcessLocList = ctx.DevMeasLocProcesses.Where(itemss => itemss.WindTurbineID == turbineID).ToList();
            }

            List<ModbusUnit> modbusList = TIMManagement.GetModbusunitList(turbineID);

            //添加转速
            List<MeasLoc_RotSpd> allRotSpdList = new List<MeasLoc_RotSpd>();
            using (CMSFramework.EF.DevContext ctxx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                allRotSpdList = ctxx.DevMeasLocRotSpds.Where(item => item.WindTurbineID == turbineID).ToList();
            }

            list.ForEach(item =>
            {
                //MeasLoc_RotSpd MeasLocation_RotSpd = turbine.DevMeasLocRotSpds.Count > 0 ? turbine.DevMeasLocRotSpds[0] : new MeasLoc_RotSpd();
                WaveDef_RotSpd WaveDefRotSpd = WaveDef_RS.Find(rot => rot.MeasDefinitionID == item.MeasDefinitionID);
                //获取工况下的测量定义列表
                List<string> measDefProcess = windTurbineDefProcess.FindAll(mdfProcess => item.MeasDefinitionID == mdfProcess.MeasDefinitionID).Select(meas => meas.MeasLocationID).ToList();
                int waveCount = 0;
                if (WaveDefRotSpd != null)
                    waveCount = WaveDefRotSpd.LineCounts;
                Meas_MeasDef measDef = new Meas_MeasDef()
                {
                    IsAvailable = item.IsAvailable,
                    MeasDefinitionID = item.MeasDefinitionID,
                    MeasDefinitionName = item.MeasDefinitionName,
                    Mdf_Ex = item.Mdf_Ex,
                    MeasLocation_RotSpd = turbine.DevMeasLocRotSpds,
                    MeasLocList_Process = turbine.ProcessMeasLocList.FindAll(meas => measDefProcess.Contains(meas.MeasLocationID)),
                    WaveLineCounts = waveCount
                };
                //获取对应的DAU信息
                if (item.Mdf_Ex != null)
                {
                    WindDAU dau = daus.FirstOrDefault(obj => obj.DauID == item.Mdf_Ex.DauID);
                    if (dau != null)
                    {
                        measDef.DauName = dau.DAUName;
                        measDef.DauID = dau.DauID;
                    }
                }
                if (waveCount == 0)
                {
                    //用户没启用转速
                    measDef.MeasLocation_RotSpd = new List<MeasLoc_RotSpd>();
                }
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    //measDef.ProcessSuperviseDefList = ctx.ProcessEvConfs.Where(items => items.WindTurbineID == turbineID && items.MeasDefinitionID == item.MeasDefinitionID).ToList();
                    MeasDef_Supervise_ProcessList = ctx.ProcessEvConfs.Where(items => items.WindTurbineID == turbineID && items.MeasDefinitionID == item.MeasDefinitionID).ToList();


                    List<string> measName = new List<string>();
                    if (MeasDef_Supervise_ProcessList.Count > 0)
                    {

                        for (int j = 0; j < MeasDef_Supervise_ProcessList.Count; j++)
                        {
                            if (MeasDef_Supervise_ProcessList[j].MeasLocationID.Contains("SPD"))
                            {

                                var measDefProcesName = allRotSpdList.Find(t => t.MeasLocationID == MeasDef_Supervise_ProcessList[j].MeasLocationID);
                                if (measDefProcesName != null)
                                {
                                    measName.Add(measDefProcesName.MeasLocName);
                                }
                            }
                            else
                            {
                                var measDefProcesName = allProcessLocList.Find(t => t.MeasLocationID == MeasDef_Supervise_ProcessList[j].MeasLocationID);
                                if (measDefProcesName != null)
                                {
                                    measName.Add(measDefProcesName.MeasLocName);

                                }
                            }


                        }
                    }


                    measDef.ProcessSuperviseDefList = MeasDef_Supervise_ProcessList;
                    measDef.SuperviseEvName = string.Join(",", measName);
                }


                // 获取modbus设备信息
                var modbusdef = TIMManagement.GetModbusDefList(turbineID, measDef.MeasDefinitionID);
                if (modbusdef != null && modbusdef.Any())
                {
                    measDef.modbusUnits = new();
                    foreach (var modbus in modbusdef)
                    {
                        measDef.modbusUnits.AddRange(modbusList.Where(t => t.ModbusDeviceID == modbus.ModbusDeviceID).ToList());
                    }
                }

                measList.Add(measDef);
            });
            return Ok(measList);
        }

        //获取工况监视特征值回显
        public string GetMSPList(string windTurbineID, string MeasDefinitionID)
        {
            //获取测量定义id 
            List<MeasDefinition> list = MeasDefinitionManagement.GetMeasDefListByTurId(windTurbineID);

            List<Meas_MeasDef> measList = new List<Meas_MeasDef>();
            List<CMSFramework.BusinessEntity.MeasDef_Ev_Process> MeasDef_Supervise_ProcessList = null;
            var measDefProces = "";
            List<MeasLoc_Process> allProcessLocList = null;
            list.ForEach(item =>
            {
                if (item.MeasDefinitionID == MeasDefinitionID)
                {


                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        MeasDef_Supervise_ProcessList = ctx.ProcessEvConfs.Where(items => items.WindTurbineID == windTurbineID && items.MeasDefinitionID == MeasDefinitionID).ToList();

                    }
                    using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                    {
                        allProcessLocList = ctx.DevMeasLocProcesses.Where(itemss => itemss.WindTurbineID == windTurbineID).ToList();
                    }
                    if (MeasDef_Supervise_ProcessList.Count != 0)
                    {
                        using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                            for (int j = 0; j < MeasDef_Supervise_ProcessList.Count; j++)
                            {
                                var measDefProcesName = allProcessLocList.Find(itemss => itemss.MeasLocationID == MeasDef_Supervise_ProcessList[j].MeasLocationID);

                                if (measDefProcesName != null)
                                {
                                    measDefProces += measDefProcesName.MeasLocName + ",";

                                }
                                else
                                {
                                    List<MeasLoc_RotSpd> allRotSpdList = null;
                                    using (CMSFramework.EF.DevContext ctxx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                                    {
                                        allRotSpdList = ctxx.DevMeasLocRotSpds.Where(RotSpditem => RotSpditem.WindTurbineID == windTurbineID).ToList();
                                    }
                                    string MeasLocationRotSpdName = allRotSpdList[0].MeasLocName;
                                    measDefProces += MeasLocationRotSpdName + ",";
                                }
                            }
                        }
                    }
                }
            });
            return measDefProces.ToJson();
        }


        /// <summary>
        /// 获取测量定义列表中的工况检测特征值
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        public string GetworkconditionevconfName(string turbineID, string measDefName)
        {
            //获取测量定义id 
            List<MeasDefinition> list = MeasDefinitionManagement.GetMeasDefListByTurId(turbineID);

            List<Meas_MeasDef> measList = new List<Meas_MeasDef>();
            List<CMSFramework.BusinessEntity.MeasDef_Ev_Process> MeasDef_Supervise_ProcessList = null;
            var measDefProces = "";
            List<MeasLoc_Process> allProcessLocList = null;
            list.ForEach(item =>
            {
                if (item.MeasDefinitionName == measDefName)
                {


                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        MeasDef_Supervise_ProcessList = ctx.ProcessEvConfs.Where(items => items.WindTurbineID == turbineID && items.MeasDefinitionID == item.MeasDefinitionID).ToList();

                    }
                    using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                    {
                        allProcessLocList = ctx.DevMeasLocProcesses.Where(itemss => itemss.WindTurbineID == turbineID).ToList();
                    }
                    if (MeasDef_Supervise_ProcessList.Count != 0)
                    {
                        using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                            for (int j = 0; j < MeasDef_Supervise_ProcessList.Count; j++)
                            {
                                var measDefProcesName = allProcessLocList.Find(itemss => itemss.MeasLocationID == MeasDef_Supervise_ProcessList[j].MeasLocationID);

                                if (measDefProcesName != null)
                                {
                                    measDefProces += measDefProcesName.MeasLocName + ",";

                                }
                                else
                                {
                                    List<MeasLoc_RotSpd> allRotSpdList = null;
                                    using (CMSFramework.EF.DevContext ctxx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                                    {
                                        allRotSpdList = ctxx.DevMeasLocRotSpds.Where(RotSpditem => RotSpditem.WindTurbineID == turbineID).ToList();
                                    }
                                    string MeasLocationRotSpdName = allRotSpdList[0].MeasLocName;
                                    measDefProces += MeasLocationRotSpdName + ",";
                                }
                            }
                        }
                    }
                }
            });
            return measDefProces.ToJson();
        }

        /// <summary>
        /// 获取测量定义工况特征值
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        public string GetSuperviseEvName(string turbineID, string measDefName)
        {
            //获取测量定义id 
            List<MeasDefinition> list = MeasDefinitionManagement.GetMeasDefListByTurId(turbineID);

            List<Meas_MeasDef> measList = new List<Meas_MeasDef>();
            List<MeasDef_Ev_Process> MeasDef_Ev_ProcessList = null;
            var measDefProces = "";
            List<MeasLoc_Process> allProcessLocList = null;
            list.ForEach(item =>
            {
                if (item.MeasDefinitionName == measDefName)
                {

                    //查询测量定义工况表
                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        MeasDef_Ev_ProcessList = ctx.ProcessEvConfs.Where(items => items.WindTurbineID == turbineID && items.MeasDefinitionID == item.MeasDefinitionID).ToList();

                    }
                    using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                    {
                        allProcessLocList = ctx.DevMeasLocProcesses.Where(itemss => itemss.WindTurbineID == turbineID).ToList();
                    }
                    //查询测量定义转速
                    List<WaveDef_RotSpd> reslist = MeasDefinitionManagement.GetWaveDefListRotSpd(turbineID, item.MeasDefinitionID);

                    if (MeasDef_Ev_ProcessList.Count != 0)
                    {
                        using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                            for (int j = 0; j < MeasDef_Ev_ProcessList.Count; j++)
                            {
                                var measDefProcesName = allProcessLocList.Find(itemss => itemss.MeasLocationID == MeasDef_Ev_ProcessList[j].MeasLocationID);

                                if (measDefProcesName != null)
                                {
                                    measDefProces += measDefProcesName.MeasLocName + ",";

                                }
                                else
                                {
                                    List<MeasLoc_RotSpd> allRotSpdList = null;
                                    using (CMSFramework.EF.DevContext ctxx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                                    {
                                        allRotSpdList = ctxx.DevMeasLocRotSpds.Where(RotSpditem => RotSpditem.WindTurbineID == turbineID).ToList();
                                    }
                                    string MeasLocationRotSpdName = allRotSpdList[0].MeasLocName;
                                    measDefProces += MeasLocationRotSpdName + ",";
                                }
                            }
                        }
                    }
                    /*if (reslist.Count !=0 ) {
                        using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                                    List<MeasLoc_RotSpd> allRotSpdList = null;
                                    using (CMSFramework.EF.DevContext ctxx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                                    {
                                        allRotSpdList = ctxx.DevMeasLocRotSpds.Where(RotSpditem => RotSpditem.WindTurbineID == turbineID).ToList();
                                    }
                                    string MeasLocationRotSpdName = allRotSpdList[0].MeasLocName;
                                    measDefProces += MeasLocationRotSpdName + ",";
                        }
                    }*/
                }
            });
            return measDefProces.ToJson();
        }
        /// <summary>
        /// 获取测量定义列表中的工况检测特征值
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="measDefName"></param>
        /// <param name="ruleName"></param>
        /// <returns></returns>
        public string GetTriggerRuleInfo(string turbineID, string measDefName, string ruleName)
        {
            //根据机组id和测量定义名称查询测量定义id
            List<MeasDefinition> mdfModelsList = null;
            List<MeasTriggerProcess> measTriggerSupervisedVariableList = null;
            List<MeasTriggerRuleDef> measTriggerDefsList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                mdfModelsList = ctx.MeasDefinitions.Where(item => item.WindTurbineID == turbineID && item.MeasDefinitionName == measDefName).ToList();
            }

            //根据机组id加测量定义id查询匹配触发规则名称以及触发规则id(mdftriggerrule)
            string MeasDefID = mdfModelsList[0].MeasDefinitionID;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                measTriggerDefsList = ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == turbineID && item.MeasDefinitionID == MeasDefID && item.RuleName == ruleName).ToList();
            }

            //根据机组id加RuleID 获取触发时的测量定义id
            string TriggerRuleID = measTriggerDefsList[0].RuleID;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {

                measTriggerSupervisedVariableList = ctx.TriggerProcess.Where(item => item.WindTurbineID == turbineID && item.RuleID == TriggerRuleID).ToList();
            }
            for (int n = 0; n < measTriggerSupervisedVariableList.Count; n++)
            {
                //查询中文的测量位置名称（根据机组id和测量位置id在devmeaslocprocess表中查询），将测量位置名称从英文切换成中文
                List<MeasLoc_Process> measLocProcessList = new List<MeasLoc_Process>();
                List<MeasLoc_RotSpd> measLocRotSpdList = new List<MeasLoc_RotSpd>();
                using (CMSFramework.EF.DevContext measLocCtx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                {
                    string MLID = measTriggerSupervisedVariableList[n].MeasLocationID;
                    measLocProcessList = measLocCtx.DevMeasLocProcesses.Where(processesitem => processesitem.WindTurbineID == turbineID && processesitem.MeasLocationID == MLID).ToList();
                    if (measLocProcessList.Count != 0)
                    {
                        measTriggerSupervisedVariableList[n].VariableName = measLocProcessList[0].MeasLocName;
                    }
                    else
                    {
                        measLocRotSpdList = measLocCtx.DevMeasLocRotSpds.Where(item => item.WindTurbineID == turbineID).ToList();
                        measTriggerSupervisedVariableList[n].VariableName = measLocRotSpdList[0].MeasLocName;
                    }

                }
            }

            return measTriggerSupervisedVariableList.ToJson();
        }

        /// <summary>
        /// 回显时间
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="measDefName"></param>
        /// <param name="ruleName"></param>
        /// <returns></returns>
        public string GetTriggerTimeInfo(string turbineID, string measDefName, string ruleName)
        {
            //根据机组id和测量定义名称查询测量定义id
            List<MeasDefinition> mdfModelsList = null;
            List<MeasTriggerProcess> measTriggerSupervisedVariableList = null;
            List<MeasTriggerRuleDef> measTriggerDefsList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                mdfModelsList = ctx.MeasDefinitions.Where(item => item.WindTurbineID == turbineID && item.MeasDefinitionName == measDefName).ToList();
            }

            //根据机组id加测量定义id查询匹配触发规则名称以及触发规则id(mdftriggerrule)
            string MeasDefID = mdfModelsList[0].MeasDefinitionID;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                measTriggerDefsList = ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == turbineID && item.MeasDefinitionID == MeasDefID && item.RuleName == ruleName).ToList();
            }

            //根据机组id加RuleID 获取触发时的测量定义id
            string TriggerRuleID = measTriggerDefsList[0].RuleID;
            List<MeasTriggerTime> measTriggerTimeList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                measTriggerTimeList = ctx.TriggerTimes.Where(item => item.RuleID == TriggerRuleID).ToList();
            }
            return measTriggerTimeList.ToJson();
        }

        /// <summary>
        /// 获取测量定义列表中的工况检测特征值
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="measDefName"></param>
        /// <param name="ruleName"></param>
        /// <returns></returns>
        public string GetTriggerRuleIsAvailable(string turbineID, string measDefName, string ruleName)
        {
            //根据机组id和测量定义名称查询测量定义id
            List<MeasDefinition> mdfModelsList = null;
            List<MeasDef_Ev_Process> measTriggerSupervisedVariableList = null;
            List<MeasTriggerRuleDef> measTriggerDefsList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                mdfModelsList = ctx.MeasDefinitions.Where(item => item.WindTurbineID == turbineID && item.MeasDefinitionName == measDefName).ToList();
            }

            //根据机组id加测量定义id查询匹配触发规则名称以及触发规则id(mdftriggerrule)
            string MeasDefID = mdfModelsList[0].MeasDefinitionID;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                measTriggerDefsList = ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == turbineID && item.MeasDefinitionID == MeasDefID && item.RuleName == ruleName).ToList();
            }


            return measTriggerDefsList.ToJson();
        }

        /// <summary>
        /// 获取测量定义列表中的工况检测特征值
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="measDefName"></param>
        /// <param name="ruleName"></param>
        /// <returns></returns>
        public string AfterTriggerMesd(string turbineID, string measDefName, string ruleName)
        {
            //根据机组id和测量定义名称查询测量定义id
            List<MeasDefinition> mdfModelsList = null;
            List<MeasDef_Ev_Process> measTriggerSupervisedVariableList = null;
            List<MeasTriggerRuleDef> measTriggerDefsList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                mdfModelsList = ctx.MeasDefinitions.Where(item => item.WindTurbineID == turbineID && item.MeasDefinitionName == measDefName).ToList();
            }

            //根据机组id加测量定义id查询匹配触发规则名称以及触发规则id(mdftriggerrule)
            string MeasDefID = mdfModelsList[0].MeasDefinitionID;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                measTriggerDefsList = ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == turbineID && item.MeasDefinitionID == MeasDefID && item.RuleName == ruleName).ToList();
            }

            //根据机组id加RuleID 获取触发时的测量定义id
            string TriggerRuleID = measTriggerDefsList[0].RuleID;
            List<MeasTriggeredExecuteMdf> measTriggeredExecuteMdfList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                measTriggeredExecuteMdfList = ctx.ExecuteMdfs.Where(item => item.WindTurbineID == turbineID && item.RuleID == TriggerRuleID).ToList();

            }

            return measTriggeredExecuteMdfList.ToJson();
        }
        /// <summary>
        /// 获取触发规则
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        public string GetTriggerSuperviseEvName(string turbineID)
        {
            List<MeasDefinition> list = MeasDefinitionManagement.GetMeasDefListByTurId(turbineID);

            List<Meas_MeasDef> measList = new List<Meas_MeasDef>();
            List<CMSFramework.BusinessEntity.MeasDef_Ev_Process> MeasDef_Supervise_ProcessList = null;
            var measDefProces = "";
            List<MeasLoc_Process> allProcessLocList = null;
            list.ForEach(item =>
            {
                if (item.MeasDefinitionName == list[0].MeasDefinitionName)
                {


                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        MeasDef_Supervise_ProcessList = ctx.ProcessEvConfs.Where(items => items.WindTurbineID == turbineID && items.MeasDefinitionID == item.MeasDefinitionID).ToList();

                    }
                    using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                    {
                        allProcessLocList = ctx.DevMeasLocProcesses.Where(itemss => itemss.WindTurbineID == turbineID).ToList();
                    }
                    if (MeasDef_Supervise_ProcessList.Count != 0)
                    {
                        using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                            for (int j = 0; j < MeasDef_Supervise_ProcessList.Count; j++)
                            {
                                var measDefProcesName = allProcessLocList.Find(itemss => itemss.MeasLocationID == MeasDef_Supervise_ProcessList[j].MeasLocationID);
                                measDefProces += measDefProcesName.MeasLocName + ",";
                            }
                        }
                    }
                }
            });
            return measDefProces.ToJson();
        }



        /// <summary>
        /// 添加/编辑触发采集配置信息
        /// </summary>
        /// <param name="TurbineID"></param>
        /// <param name="TriggerRuleName"></param>
        /// <param name="IsAvailable"></param>
        /// <param name="TriggerMeasDefName"></param>
        /// <param name="ConditionMonitoringLocIds"></param>
        /// <param name="TriggerData"></param>
        /// <param name="isAddType"></param>
        /// <returns></returns>
        /// <summary>
        /// 批量添加触发采集
        /// </summary>
        /// <param name="request">批量添加请求</param>
        /// <returns></returns>
        [HttpPost("TirggerAcq")]
        [BatchOperation(nameof(BatchAddTriggerAcqFun))]
        public IActionResult BatchAddTriggerAcq([FromBody] BatchAddTriggerAcqRequest request)
        {
            if (request?.SourceData == null)
                return Ok(ApiResponse<string>.Error("参数不能为空"));

            // 执行原有的添加逻辑
            return ExecuteAddTriggerAcqOperation(request.SourceData);
        }

        /// <summary>
        /// 执行添加触发采集操作
        /// </summary>
        /// <param name="triggerAcquisitions">触发采集列表</param>
        /// <returns></returns>
        private IActionResult ExecuteAddTriggerAcqOperation(TriggerAcquisitionTOD triggerAcquisitions)
        {
            var results = new List<string>();
            int successCount = 0;
            int failCount = 0;

            try
            {
                var result = AddTriggerGatherDisposeSingle(triggerAcquisitions);
                if (result is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                {
                    if (apiResponse.Code == 1)
                    {
                        successCount++;
                        results.Add($"机组 {triggerAcquisitions.TurbineID}: 成功添加触发采集 {triggerAcquisitions.TriggerRuleName}");
                    }
                    else
                    {
                        failCount++;
                        results.Add($"机组 {triggerAcquisitions.TurbineID}: 添加触发采集失败 - {apiResponse.Msg}");
                    }
                }
            }
            catch (Exception ex)
            {
                failCount++;
                results.Add($"机组 {triggerAcquisitions.TurbineID}: 添加触发采集异常 - {ex.Message}");
            }

            var summary = $"批量添加完成：成功 {successCount} 个，失败 {failCount} 个";
            results.Insert(0, summary);

            return Ok(ApiResponse<List<string>>.Success(results));
        }

        ///
        [HttpPost("TirggerAcqV1")]
        public IActionResult AddTriggerGatherDispose(TriggerAcquisitionTOD triggerAcquisitionTOD)
        {
            return AddTriggerGatherDisposeSingle(triggerAcquisitionTOD);
        }

        /// <summary>
        /// 添加单个触发采集
        /// </summary>
        /// <param name="triggerAcquisitionTOD"></param>
        /// <returns></returns>
        private IActionResult AddTriggerGatherDisposeSingle(TriggerAcquisitionTOD triggerAcquisitionTOD)
        {

            try
            {
                bool IsAvailable = true;
                //根据机组id和测量定义名称查询测量定义id
                List<MeasDefinition> mdfModelsList = null;
                List<MeasTriggerRuleDef> measTriggerDefsList = null;

                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    mdfModelsList = ctx.MeasDefinitions.Where(item => item.WindTurbineID == triggerAcquisitionTOD.TurbineID && item.MeasDefinitionName == triggerAcquisitionTOD.TriggerMeasDefName).ToList();
                }
                //根据机组id加测量定义id查询匹配触发规则名称以及触发规则id(mdftriggerrule)
                string MeasDefID = mdfModelsList[0].MeasDefinitionID;
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    measTriggerDefsList = ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == triggerAcquisitionTOD.TurbineID && item.MeasDefinitionID == MeasDefID && item.RuleName == triggerAcquisitionTOD.TriggerRuleName).ToList();
                }
                if (measTriggerDefsList.Count != 0)
                {
                    string TriggerRuleID = measTriggerDefsList[0].RuleID;
                    if (triggerAcquisitionTOD.isAddType == "1")
                    {
                        return Ok(ApiResponse<string>.Error("触发规则已存在"));
                    }
                    //删除mdftriggersupervisedvariable表
                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        ctx.TriggerProcess.RemoveRange(ctx.TriggerProcess.Where(item => item.WindTurbineID == triggerAcquisitionTOD.TurbineID && item.RuleID == TriggerRuleID));
                        ctx.SaveChanges();
                    }

                    //删除mdftriggerrule表
                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        ctx.TriggerRuleDefs.RemoveRange(ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == triggerAcquisitionTOD.TurbineID && item.MeasDefinitionID == MeasDefID && item.RuleName == triggerAcquisitionTOD.TriggerRuleName));
                        ctx.SaveChanges();
                    }

                    //删除mdftriggeredexecutemdf表

                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        ctx.ExecuteMdfs.RemoveRange(ctx.ExecuteMdfs.Where(item => item.WindTurbineID == triggerAcquisitionTOD.TurbineID && item.RuleID == TriggerRuleID));
                        ctx.SaveChanges();
                    }
                    //删除mdftriggertime表
                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        ctx.TriggerTimes.RemoveRange(ctx.TriggerTimes.Where(item => item.RuleID == TriggerRuleID));
                        ctx.SaveChanges();
                    }
                }


                //添加触发测量定义表
                MeasDefinitionManagement.AddMdfTrigger(triggerAcquisitionTOD.TurbineID, triggerAcquisitionTOD.TriggerRuleName, triggerAcquisitionTOD.TriggerMeasDefName, IsAvailable);
                if (triggerAcquisitionTOD.triggertime == null || triggerAcquisitionTOD.triggertime == "")
                {
                    //添加触发监视变量测量定义
                    MeasDefinitionManagement.AddMdfMdfTriggerSupervisedVariable(triggerAcquisitionTOD.TurbineID, triggerAcquisitionTOD.TriggerRuleName, triggerAcquisitionTOD.TriggerMeasDefName, triggerAcquisitionTOD.ConditionMonitoringLocIds, triggerAcquisitionTOD.TriggerData);
                }
                //添加触发实施测量定义表
                MeasDefinitionManagement.AddMdfTriggeredExecuteMdf(triggerAcquisitionTOD.TurbineID, triggerAcquisitionTOD.ConditionMonitoringLocIds, triggerAcquisitionTOD.TriggerMeasDefName, triggerAcquisitionTOD.TriggerRuleName);
                //获取规则id
                List<MeasTriggerRuleDef> measTriggerList = null;
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    measTriggerList = ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == triggerAcquisitionTOD.TurbineID && item.MeasDefinitionID == MeasDefID && item.RuleName == triggerAcquisitionTOD.TriggerRuleName).ToList();
                }
                string ruleid = measTriggerList[0].RuleID;
                //添加触发时间表
                if (triggerAcquisitionTOD.triggertime != null && triggerAcquisitionTOD.triggertime != "")
                {
                    MeasDefinitionManagement.AddMdfTriggeredTime(ruleid, triggerAcquisitionTOD.triggertime);
                }

                //触发采集配置后测量定义版本号升级
                if (!string.IsNullOrEmpty(triggerAcquisitionTOD.dauid))
                {
                    DauManagement.UpdateMeasDefVersion(triggerAcquisitionTOD.TurbineID, triggerAcquisitionTOD.dauid);
                }

                return Ok(ApiResponse<string>.Success("OK"));
            }
            catch (DbEntityValidationException dbEx)
            {
                return Ok(ApiResponse<string>.Error(dbEx.Message));
            }
        }

        /// <summary>
        /// 获取触发配置数据信息
        /// </summary>
        /// <param name="TurbineID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetMeasdTriggerList")]
        public IActionResult GetMeasdTriggerList(string TurbineID, string MeasDefinitionID)
        {
            //根据机组id获取该机组下所有的触发规则和触发id，以及测量定义id，
            //根据触发id获取触发规则获取变量名，比较符，和触发数值
            //根据机组id和触发id获取被触发的测量定义id,根据测量定义id获取值测量定于名称
            //触发工况

            List<TriggerWebTableDataModel> triggerWebTableDataModelsList = new List<TriggerWebTableDataModel>();

            List<MeasTriggerRuleDef> measTriggerDefsList = null;

            List<MeasTriggerRuleDef> triggerDataList = new List<MeasTriggerRuleDef>();

            List<MeasTriggerProcess> measTriggerSupervisedVariableList = null;


            //根据机组id和测量定义名称查询测量定义id
            MeasDefinition mdfModelsList = null;

            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                mdfModelsList = ctx.MeasDefinitions.FirstOrDefault(item => item.WindTurbineID == TurbineID && item.MeasDefinitionID == MeasDefinitionID);
            }

            //根据机组id加测量定义id查询匹配触发规则名称以及触发规则id(mdftriggerrule)
            if (mdfModelsList != null)
            {
                string MeasDefID = mdfModelsList.MeasDefinitionID;

                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    measTriggerDefsList = ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == TurbineID && item.MeasDefinitionID == MeasDefID).ToList();
                }
                for (int i = 0; i < measTriggerDefsList.Count; i++)
                {

                    //添加规则名称
                    TriggerWebTableDataModel triggerWebTableDataModel = new TriggerWebTableDataModel();
                    triggerWebTableDataModel.RuleName = measTriggerDefsList[i].RuleName;
                    triggerWebTableDataModel.MeasdName = mdfModelsList.MeasDefinitionName;
                    triggerWebTableDataModel.RuleID = measTriggerDefsList[i].RuleID;
                    //根据机组id加RuleID 获取触发时的测量定义id
                    string TriggerRuleID = measTriggerDefsList[i].RuleID;
                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {

                        measTriggerSupervisedVariableList = ctx.TriggerProcess.Where(item => item.WindTurbineID == TurbineID && item.RuleID == TriggerRuleID).ToList();
                    }

                    string TriggerRuleData = "";
                    List<string> TriggerRuleDataList = new List<string>();
                    //mdftriggersupervisedvariable
                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        for (int n = 0; n < measTriggerSupervisedVariableList.Count; n++)
                        {
                            //查询中文的测量位置名称（根据机组id和测量位置id在devmeaslocprocess表中查询），将测量位置名称从英文切换成中文
                            List<MeasLoc_Process> measLocProcessList = new List<MeasLoc_Process>();
                            List<MeasLoc_RotSpd> measLocRotSpdList = new List<MeasLoc_RotSpd>();
                            using (CMSFramework.EF.DevContext measLocCtx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                            {
                                string MLID = measTriggerSupervisedVariableList[n].MeasLocationID;
                                measLocProcessList = measLocCtx.DevMeasLocProcesses.Where(processesitem => processesitem.WindTurbineID == TurbineID && processesitem.MeasLocationID == MLID).ToList();
                                if (measLocProcessList.Count != 0)
                                {
                                    measTriggerSupervisedVariableList[n].VariableName = measLocProcessList[0].MeasLocName;
                                }
                                else
                                {
                                    measLocRotSpdList = measLocCtx.DevMeasLocRotSpds.Where(item => item.WindTurbineID == TurbineID).ToList();
                                    measTriggerSupervisedVariableList[n].VariableName = measLocRotSpdList[0].MeasLocName;
                                }

                            }

                            if (measTriggerSupervisedVariableList[n].LogicCompare == 0)
                            {
                                TriggerRuleData += measTriggerSupervisedVariableList[n].VariableName + ">" + measTriggerSupervisedVariableList[n].Threshold + ",";
                                //TriggerRuleDataList.Add((new List<string>() { measTriggerSupervisedVariableList[n].VariableName ,">", measTriggerSupervisedVariableList[n].Threshold.ToString() }));
                                TriggerRuleDataList.Add(measTriggerSupervisedVariableList[n].VariableName + "," + ">" + "," + measTriggerSupervisedVariableList[n].Threshold);
                            }
                            else if (measTriggerSupervisedVariableList[n].LogicCompare == EnumLogicCompareType.Lesser)
                            {
                                TriggerRuleData += measTriggerSupervisedVariableList[n].VariableName + "<" + measTriggerSupervisedVariableList[n].Threshold + ",";
                                TriggerRuleDataList.Add(measTriggerSupervisedVariableList[n].VariableName + "," + "<" + "," + measTriggerSupervisedVariableList[n].Threshold);
                            }
                            else if (measTriggerSupervisedVariableList[n].LogicCompare == EnumLogicCompareType.GreaterAndEqual)
                            {
                                TriggerRuleData += measTriggerSupervisedVariableList[n].VariableName + ">=" + measTriggerSupervisedVariableList[n].Threshold + ",";
                                TriggerRuleDataList.Add(measTriggerSupervisedVariableList[n].VariableName + "," + ">=" + "," + measTriggerSupervisedVariableList[n].Threshold);
                            }
                            else if (measTriggerSupervisedVariableList[n].LogicCompare == EnumLogicCompareType.LesserAndEqual)
                            {
                                TriggerRuleData += measTriggerSupervisedVariableList[n].VariableName + "<=" + measTriggerSupervisedVariableList[n].Threshold + ",";
                                TriggerRuleDataList.Add(measTriggerSupervisedVariableList[n].VariableName + "," + "<=" + "," + measTriggerSupervisedVariableList[n].Threshold);
                            }
                            else if (measTriggerSupervisedVariableList[n].LogicCompare == EnumLogicCompareType.Equal)
                            {
                                TriggerRuleData += measTriggerSupervisedVariableList[n].VariableName + "=" + measTriggerSupervisedVariableList[n].Threshold + ",";
                                TriggerRuleDataList.Add(measTriggerSupervisedVariableList[n].VariableName + "," + "=" + "," + measTriggerSupervisedVariableList[n].Threshold);
                            }
                            else if (measTriggerSupervisedVariableList[n].LogicCompare == EnumLogicCompareType.NotEqual)
                            {
                                TriggerRuleData += measTriggerSupervisedVariableList[n].VariableName + "!=" + measTriggerSupervisedVariableList[n].Threshold + ",";
                                TriggerRuleDataList.Add(measTriggerSupervisedVariableList[n].VariableName + "," + "!=" + "," + measTriggerSupervisedVariableList[n].Threshold);
                            }

                        }

                    }
                    if (measTriggerSupervisedVariableList.Count == 0)
                    {
                        //触发时间
                        List<MeasTriggerTime> measTriggerTimeList = null;
                        using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                            measTriggerTimeList = ctx.TriggerTimes.Where(item => item.RuleID == TriggerRuleID).ToList();
                        }
                        if (measTriggerTimeList.Count != 0)
                        {
                            TriggerRuleData += "时间间隔" + measTriggerTimeList[0].TimeInterval + "分钟,";
                            TriggerRuleDataList.Add(measTriggerTimeList[0].TimeInterval.ToString());
                        }

                    }
                    if (TriggerRuleData != "")
                    {
                        TriggerRuleData = TriggerRuleData.Remove(TriggerRuleData.LastIndexOf(","), 1);
                    }

                    triggerWebTableDataModel.TriggerRule = TriggerRuleData;
                    triggerWebTableDataModel.TriggerRuleList = TriggerRuleDataList;
                    //根据机组id加RuleID 获取触发后的测量定义id
                    List<MeasTriggeredExecuteMdf> measTriggeredExecuteMdfList = null;
                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        measTriggeredExecuteMdfList = ctx.ExecuteMdfs.Where(item => item.RuleID == TriggerRuleID && item.WindTurbineID == TurbineID).ToList();
                    }
                    //根据机组id加测量定义id,获取触发后的测量定义名称
                    List<MeasDefinition> mdfTriggerModelsList = null;
                    string MdfTriggerName = "";
                    List<string> MdfTriggerMeasdefIDs = new List<string>();
                    for (int k = 0; k < measTriggeredExecuteMdfList.Count; k++)
                    {
                        string MesdID = measTriggeredExecuteMdfList[k].MeasDefinitionID;
                        using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                            mdfTriggerModelsList = ctx.MeasDefinitions.Where(item => item.WindTurbineID == TurbineID && item.MeasDefinitionID == MesdID).ToList();

                        }
                        if (mdfTriggerModelsList.Count != 0)
                        {
                            MdfTriggerName += mdfTriggerModelsList[0].MeasDefinitionName + ",";
                            MdfTriggerMeasdefIDs.Add(mdfTriggerModelsList[0].MeasDefinitionID.ToString());
                        }
                    }
                    if (measTriggerSupervisedVariableList.Count == 0)
                    {
                        triggerWebTableDataModel.TriggerRuleType = "时间";
                    }
                    else
                    {
                        triggerWebTableDataModel.TriggerRuleType = "工况";
                    }
                    if (MdfTriggerName != "")
                    {
                        MdfTriggerName = MdfTriggerName.Remove(MdfTriggerName.LastIndexOf(","), 1);
                    }

                    triggerWebTableDataModel.TriggerMeasdedName = MdfTriggerName;
                    triggerWebTableDataModel.TriggerMeasdId = MdfTriggerMeasdefIDs;
                    triggerWebTableDataModelsList.Add(triggerWebTableDataModel);
                }
            }
            return Ok(triggerWebTableDataModelsList);

        }
        /// <summary>
        /// 删除触发采集列表信息
        /// </summary>
        /// <param name="TurbineID">机组ID</param>
        /// <param name="MeasDefName">测量定义ID</param>
        /// <param name="RuleName">测量定义ID</param>
        /// <returns></returns>
        public string DeleteMeasdTriggerList(string TurbineID, string MeasDefName, string RuleName, string dauid)
        {
            string message = "state:{0},msg:'{1}'";
            try
            {
                //判断当前测量定义是否在其它测量定义中被触发


                /*                1.根据机组id和测量定义id和规则名称 查询规则id mdftriggerrule
                                  2.根据规则id和测量定义id 和机组id 删除mdftriggeredexecutemdf
                                  3.根据规则id和机组id删除mdftriggersupervisedvariable
                                  4.根据规则名称和机组id删除mdftriggerrule
                */
                //根据机组id和测量定义名称查询测量定义id
                List<MeasDefinition> mdfModelsList = null;
                List<MeasTriggerRuleDef> measTriggerDefsList = null;

                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    mdfModelsList = ctx.MeasDefinitions.Where(item => item.WindTurbineID == TurbineID && item.MeasDefinitionName == MeasDefName).ToList();
                }
                string MeasDefID = mdfModelsList[0].MeasDefinitionID;
                //根据机组id加测量定义id查询匹配触发规则名称以及触发规则id(mdftriggerrule)

                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    measTriggerDefsList = ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == TurbineID && item.MeasDefinitionID == MeasDefID && item.RuleName == RuleName).ToList();
                }

                string TriggerRuleID = measTriggerDefsList[0].RuleID;
                //查询mdftriggersupervisedvariable表如果没有数据，那就是时间触发删除时间
                List<MeasTriggerProcess> mtpList = null;
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    mtpList = ctx.TriggerProcess.Where(item => item.WindTurbineID == TurbineID && item.RuleID == TriggerRuleID).ToList();
                }
                if (mtpList.Count != 0)
                {
                    //删除mdftriggersupervisedvariable表
                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        ctx.TriggerProcess.RemoveRange(ctx.TriggerProcess.Where(item => item.WindTurbineID == TurbineID && item.RuleID == TriggerRuleID));
                        ctx.SaveChanges();
                    }
                }
                else
                {
                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        ctx.TriggerTimes.RemoveRange(ctx.TriggerTimes.Where(item => item.RuleID == TriggerRuleID));
                        ctx.SaveChanges();
                    }
                }

                //删除mdftriggersupervisedvariable表
                /*                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                                {
                                    ctx.TriggerProcess.RemoveRange(ctx.TriggerProcess.Where(item => item.WindTurbineID == TurbineID && item.RuleID == TriggerRuleID));
                                    ctx.SaveChanges();
                                }*/

                //删除mdftriggerrule表
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    ctx.TriggerRuleDefs.RemoveRange(ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == TurbineID && item.MeasDefinitionID == MeasDefID && item.RuleName == RuleName && item.RuleID == TriggerRuleID));
                    ctx.SaveChanges();
                }

                //删除mdftriggeredexecutemdf表
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    ctx.ExecuteMdfs.RemoveRange(ctx.ExecuteMdfs.Where(item => item.WindTurbineID == TurbineID && item.RuleID == TriggerRuleID));
                    ctx.SaveChanges();
                }
                // 提升DAU测量定义版本
                DauManagement.UpdateMeasDefVersion(TurbineID, dauid);

                message = string.Format(message, 1, "");
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditMeasDefinition]" + "删除失败 ", ex);
                message = string.Format(message, 0, "删除测量定义列表失败 :" + ex.Message);
            }
            return "{" + message + "}";
        }


        /// <summary>
        /// 根据机组名称获取采集单元列表。
        /// </summary>
        /// <param name="windTurbineId"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetDauLst")]
        public IActionResult GetDauLstByTurbineID(string turbineID)
        {
            var daulist = DAUSManageModel.GetDAUListById(turbineID);

            // 添加测量定义时，选择dau工况，需要dau工况名称
            List<MeasLoc_Process> allListbyTurId = WTCMSLive.BusinessModel.DevTreeManagement.GetWorkCondMeasLocByTurID(turbineID);
            foreach (var dau in daulist)
            {
                if (dau.ProcessChannelList != null)
                {
                    foreach (var _cur in dau.ProcessChannelList)
                    {
                        var measLoc = allListbyTurId.FirstOrDefault(t => t.MeasLocationID == _cur.MeasLoc_ProcessId);
                        if (measLoc != null)
                        {
                            _cur.WindTurbineID = measLoc.MeasLocName;
                        }
                    }
                }
            }

            return Ok(daulist);
        }
        /// <summary>
        /// 当测量定义是被动的时候，获取主动的测量定义。
        /// </summary>
        /// <param name="windTurbineId"></param>
        /// <param name="modelType"></param>
        /// <returns></returns>
        public string GetInitiativeMeasidList(string turbineID, string measDefinitionid)
        {
            //根据被动机组id 获取触发后的主动测量定义id
            List<MeasTriggeredExecuteMdf> measTriggeredExecuteMdfList = new List<MeasTriggeredExecuteMdf>();
            //获取主动的测量定义
            List<MeasDefinition> InitiativeMeasDefinitionsList = new List<MeasDefinition>();


            //查询触发规则id和测量定义关联表 mdftriggeredexecutemdf
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                measTriggeredExecuteMdfList = ctx.ExecuteMdfs.Where(item => item.WindTurbineID == turbineID && item.MeasDefinitionID == measDefinitionid).ToList();
            }
            if (measTriggeredExecuteMdfList.Count != 0)
            {

                //根据机组id加测量定义id查询匹配触发规则名称以及触发规则id(mdftriggerrule)
                for (int i = 0; i < measTriggeredExecuteMdfList.Count; i++)
                {
                    string ruleID = measTriggeredExecuteMdfList[i].RuleID;
                    //List<MeasTriggerRuleDef> measTriggerDefsList = null;
                    //根据机组id和触发规则id查询mdftriggerrule，
                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {

                        var measTriggerDefsList = ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == turbineID && item.RuleID == ruleID).GroupBy(t => t.MeasDefinitionID).ToList();

                        foreach (var item in measTriggerDefsList)
                        {
                            var _cur = InitiativeMeasDefinitionsList.FirstOrDefault(t => t.MeasDefinitionID == item.Key);

                            if (_cur == null)
                            {
                                MeasDefinition measDefinition = MeasDefinitionManagement.GetMeasdefinition(turbineID, item.Key);
                                InitiativeMeasDefinitionsList.Add(measDefinition);
                            }

                        }
                    }
                }
            }
            return InitiativeMeasDefinitionsList.ToJson();
        }

        /// <summary>
        /// 获取转速测量位置
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="MeasDefinitionID"></param>
        /// <returns></returns>
        public string GetRotSpdLocList(string turbineID)
        {
            List<MeasDefinition> list = MeasDefinitionManagement.GetMeasDefListByTurId(turbineID);
            List<WaveDef_RotSpd> Rotlist = new List<WaveDef_RotSpd>();
            list.ForEach(item =>
            {
                MeasDefinitionManagement.GetWaveDefListRotSpd(turbineID, item.MeasDefinitionID).ForEach(i =>
                {
                    Rotlist.Add(i);
                });
            });
            return Rotlist.ToJson();
        }

        /// <summary>
        /// 获取工况测量位置
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="MeasDefinitionID"></param>
        /// <returns></returns>
        public List<MeasLoc_Process> GetProcessMeasLocList(string turbineID, string MeasDefinitionID)
        {
            List<MeasLoc_Process> processMeasDefList = MeasDefinitionManagement.GetMeasLocWorkCondLocListByMeasDefId(turbineID, MeasDefinitionID);
            return processMeasDefList.OrderBy(item => item.OrderSeq).ToList();
        }

        public string GetProcessMeasLocListJson(string turbineID, string MeasDefinitionID)
        {
            return GetProcessMeasLocList(turbineID, MeasDefinitionID).ToJson();
        }
        /// <summary>
        /// 获取工况测量位置下的工况信息列表
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        public string GetWorkConListForMeasLoc(string turbineID)
        {
            List<MeasLoc_Process> allListbyTurId = WTCMSLive.BusinessModel.DevTreeManagement.GetWorkCondMeasLocByTurID(turbineID);
            List<MeasLoc_Process> notBoundList = new List<MeasLoc_Process>();
            WindDAU myChannel = DauManagement.GetDAUById(turbineID);
            List<DAUChannel_Process> List = new List<DAUChannel_Process>();
            if (myChannel != null) List = myChannel.ProcessChannelList;
            List<MCSChannel> mcsChannel = DAUMCS.GetMCSChannelList(turbineID);
            foreach (MeasLoc_Process loc_Pro in allListbyTurId)
            {
                //if (string.IsNullOrEmpty(loc_Pro.ParmaChannelNumber) == true)
                //{
                //    notBoundList.Add(loc_Pro);
                //}
                if (loc_Pro.FieldBusType == EnumWorkConDataSource.WindDAU)
                {
                    if (List.Find(item => item.MeasLoc_ProcessId == loc_Pro.MeasLocationID) == null)
                    {
                        notBoundList.Add(loc_Pro);
                    }
                }
                else
                {
                    if (mcsChannel.Find(item => item.MeasLocProcessID == loc_Pro.MeasLocationID) == null)
                    {
                        notBoundList.Add(loc_Pro);
                    }
                }
            }
            allListbyTurId.RemoveAll(
                  item =>
                  {
                      return (notBoundList.Find(notBount => notBount.MeasLocationID == item.MeasLocationID) != null);
                  });
            //油液磨粒的工况在一个测量定义中，只能添加一次
            var WCPT_Oil_Grit = DevTreeManagement.GetMeaslocProcessListWithDAUByTurID(turbineID).Find(item => item.Param_Type_Code == EnumWorkCondition_ParamType.WCPT_Oil_Debris && item.FieldBusType == EnumWorkConDataSource.WindDAU);
            if (WCPT_Oil_Grit != null)
            {
                var MdfOilGrit = MeasDefinitionManagement.GetMDFWorkCondLocListByTurbineId(turbineID).Find(item => item.MeasLocationID == WCPT_Oil_Grit.MeasLocationID);
                if (MdfOilGrit != null)
                {
                    allListbyTurId.RemoveAll(item => item.MeasLocationID == MdfOilGrit.MeasLocationID);
                }
            }
            allListbyTurId = allListbyTurId.OrderBy(item => item.OrderSeq).ToList();
            /*            List<MeasLoc_Process> DAUList = allListbyTurId.Where(item => item.FieldBusType == EnumWorkConDataSource.WindDAU).ToList();*/
            return allListbyTurId.Where(item => item.FieldBusType == EnumWorkConDataSource.WindDAU).ToJson() + "$$" + allListbyTurId.Where(item => item.FieldBusType == EnumWorkConDataSource.ModbusOnTcp).ToJson();
        }
        //Param_Type_Code
        public string GetWorkConListForMeasLocUpdate(string turbineID, string MeasDefId)
        {
            //return "123";
            string mark = "1";
            try
            {
                //string mark = "1";
                mark += "2";
                List<MeasLoc_Process> allListbyTurId = WTCMSLive.BusinessModel.DevTreeManagement.GetWorkCondMeasLocByTurID(turbineID);
                mark += "3";
                List<MeasLoc_Process> BoundList = MeasDefinitionManagement.GetMeasLocWorkCondLocListByMeasDefId(turbineID, MeasDefId);
                mark += "4";
                List<MeasDef_Process> WorkProcess = MeasDefinitionManagement.GetMDFWorkCondLocListByMeasDefId(turbineID, MeasDefId);
                mark += "5";
                List<MeasLoc_Process> notBoundList = new List<MeasLoc_Process>();

                WindDAU myChannel = DauManagement.GetDAUById(turbineID, MeasDefId);
                List<DAUChannel_Process> List = new List<DAUChannel_Process>();
                if (myChannel != null) List = myChannel.ProcessChannelList;
                List<MCSChannel> mcsChannel = DAUMCS.GetMCSChannelList(turbineID);
                List<MeasLoc_Process> temp_allListbyTurId = new List<MeasLoc_Process>();
                allListbyTurId.ForEach(item =>
                {
                    /*MeasLoc_Process measloc_proc = new MeasLoc_Process();
                    measloc_proc.Eu_type_code = item.Eu_type_code;
                    measloc_proc.FieldBusType = item.FieldBusType;
                    measloc_proc.MeasLocationID = item.MeasLocationID;
                    measloc_proc.MeasLocName = item.MeasLocName;
                    measloc_proc.OrderSeq = item.OrderSeq;
                    measloc_proc.Param_Type_Code = item.Param_Type_Code;
                    measloc_proc.Param_Type_Name = item.Param_Type_Name;
                    measloc_proc.ParmaChannelNumber = item.ParmaChannelNumber;
                    measloc_proc.ServerAddress = item.ServerAddress;
                    measloc_proc.WindTurbineID = item.WindTurbineID;*/

                    if (item.FieldBusType == EnumWorkConDataSource.WindDAU)
                    {
                        if (List.Find(dauProcess => dauProcess.MeasLoc_ProcessId == item.MeasLocationID) == null)
                        {
                            notBoundList.Add(item);
                        }
                    }
                    else
                    {
                        if (mcsChannel.Find(mcsProcess => mcsProcess.MeasLocProcessID == item.MeasLocationID) == null)
                        {
                            notBoundList.Add(item);
                        }
                    }
                    MeasDef_Process myProcess = WorkProcess.Find(i => i.MeasLocationID == item.MeasLocationID && item.FieldBusType == EnumWorkConDataSource.WindDAU);
                    if (myProcess != null)
                    {
                        item.ParmaChannelNumber = myProcess.UpperLimitFreqency.ToString();
                        item.Param_Type_Name = myProcess.SampleLength.ToString();
                    }
                    myProcess = WorkProcess.Find(i => i.MeasLocationID == item.MeasLocationID && item.FieldBusType == EnumWorkConDataSource.ModbusOnTcp);
                    if (myProcess != null)
                    {
                        //无意义，只是前台修改的一个标记
                        //@wangy 
                        //20210121现在，它有意义了。
                        //item.ParmaChannelNumber = "1";
                        item.ParmaChannelNumber = myProcess.UpperLimitFreqency.ToString();
                        item.Param_Type_Name = myProcess.SampleLength.ToString();
                    }
                    //temp_allListbyTurId.Add(measloc_proc);
                });
                //List<MeasLoc_Process> getbindList = new List<MeasLoc_Process>();
                //temp_allListbyTurId.ForEach(item =>
                //{
                //    if (notBoundList.Find(notBount => notBount.MeasLocationID == item.MeasLocationID) == null)
                //    {
                //        getbindList.Add(item);
                //    }
                //});
                //allListbyTurId = getbindList;
                //return getbindList.ToJson();
                allListbyTurId.RemoveAll(
                     item =>
                     {
                         return (notBoundList.Find(notBount => notBount.MeasLocationID == item.MeasLocationID) != null);
                     });
                var WCPT_Oil_Grit = DevTreeManagement.GetMeaslocProcessListWithDAUByTurID(turbineID).Find(item => item.Param_Type_Code == EnumWorkCondition_ParamType.WCPT_Oil_Debris && item.FieldBusType == EnumWorkConDataSource.WindDAU);
                if (WCPT_Oil_Grit != null)
                {
                    var MdfOilGrit = MeasDefinitionManagement.GetMDFWorkCondLocListByTurbineId(turbineID).Find(item => item.MeasLocationID == WCPT_Oil_Grit.MeasLocationID && item.MeasDefinitionID != MeasDefId);
                    if (MdfOilGrit != null)
                    {
                        allListbyTurId.RemoveAll(item => item.MeasLocationID == MdfOilGrit.MeasLocationID);
                    }
                }
                allListbyTurId = allListbyTurId.OrderBy(item => item.OrderSeq).ToList();
                //return allListbyTurId.ToJson();
                return allListbyTurId.Where(item => item.FieldBusType == EnumWorkConDataSource.WindDAU).ToJson() + "$$" + allListbyTurId.Where(item => item.FieldBusType == EnumWorkConDataSource.ModbusOnTcp).ToJson();
            }
            catch (Exception ex)
            {
                return mark + "\r\n" + ex.ToString();
            }
        }
        /// <summary>
        /// 获取测量定义id
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="MeasLocationNameStr"></param
        /// <returns></returns>
        public string GetMeasLocationID(string turbineID, string MeasDefId)
        {
            //by wjy
            //测量定义工况
            /*       List<string> measLocNameList = new List<string>();
                   if (!string.IsNullOrEmpty(MeasLocationNameStr))
                   {
                       measLocNameList = MeasLocationNameStr.Split(',').ToList();
                   }*/
            List<CMSFramework.BusinessEntity.MeasDef_Ev_Process> MeasDef_Ev_ProcessList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                MeasDef_Ev_ProcessList = ctx.ProcessEvConfs.Where(item => item.WindTurbineID == turbineID && item.MeasDefinitionID == MeasDefId).ToList();
            }
            List<MeasLoc_Process> allProcessLocList = null;
            List<string> measLocationInfo = new List<string>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                for (int i = 0; i < MeasDef_Ev_ProcessList.Count; i++)
                {
                    string measLocID = MeasDef_Ev_ProcessList[i].MeasLocationID;
                    allProcessLocList = ctx.DevMeasLocProcesses.Where(item => item.WindTurbineID == turbineID && item.MeasLocationID == measLocID).ToList();
                    if (allProcessLocList.Count != 0)
                    {
                        string MeasLocationIDandNameStr = allProcessLocList[0].MeasLocationID + "," + allProcessLocList[0].MeasLocName;
                        measLocationInfo.Add(MeasLocationIDandNameStr);
                    }
                }

            }
            return measLocationInfo.ToJson();
        }
        /// <summary>
        /// 添加/编辑测量定义列表信息
        /// </summary>
        /// <param name="MeasDefId"></param>
        /// <param name="MeasDefName"></param>
        /// <param name="WindTurbineID"></param>
        /// <param name="WindParkID"></param>
        /// <param name="measLocIds"></param>
        /// <param name="IsAvailable"></param>
        /// <param name="IsAcqRotSpd"></param>
        /// <param name="isAddType"></param>
        /// <param name="dauid">DAUid</param>
        /// <returns></returns>
        public string EditMeasDefinition(string MeasDefId, string MeasDefName, string WindTurbineID, string WindParkID, string measLocIds, string conditionMonitoringLocIds, string DAUmeasLocIds, bool IsAvailable, bool IsAcqRotSpd, bool IsAcqOil, int RotWaveLineCounts, string isAddType, string dauid, int daqInterval, int modelType = 0)
        {
            string message = "state:{0},msg:'{1}'";
            string actionName = "添加";
            try
            {
                // wjy 2021年6月30日 11:37
                //注释dau为空的校验判断
                /* var dau =  DauManagement.GetDAUById(WindTurbineID);
                 if (dau == null)
                 {
                     message = string.Format(message, 0, "请先添加机组DAU信息！");
                     return "{" + message + "}";
                 }*/
                //添加测量定义时，查询测量定义是否重复
                List<MeasDefinition> measDefinitionList = MeasDefinitionManagement.GetMeasDefListByTurId(WindTurbineID);
                var _exist = measDefinitionList.FirstOrDefault(t => t.MeasDefinitionName == MeasDefName && t.MeasDefinitionID != MeasDefId);
                if (_exist != null)
                {
                    message = string.Format(message, 4, "测量定义名称重复！");
                    return "{" + message + "}";
                }

                //测量定义修改
                List<string> conditionMonitoringLocIdList = new List<string>();
                if (!string.IsNullOrEmpty(conditionMonitoringLocIds))
                {
                    conditionMonitoringLocIdList = conditionMonitoringLocIds.Split(',').ToList();
                }
                List<MeasDef_Ev_Process> msplist = null;
                List<MeasTriggerRuleDef> mtrdlist = null;
                List<MeasTriggerProcess> mtsvlist = null;
                List<string> mlList = new List<string>();
                List<string> tlList = new List<string>();

                MeasDefinition measDef = new MeasDefinition();
                //把测量定义中的半角+变成全角＋
                MeasDefName = MeasDefName.Replace("+", "＋");
                measDef.MeasDefinitionName = MeasDefName.Trim();
                measDef.WindTurbineID = WindTurbineID;
                measDef.WindParkID = WindParkID;
                measDef.MeasDefinitionID = MeasDefId;
                measDef.IsAvailable = IsAvailable;

                measDef.Mdf_Ex = new MeasDefinition_Ex()
                {
                    DauID = dauid,
                    MeasDefinitionID = MeasDefId,
                    WindTurbineID = WindTurbineID,
                    DaqInterval = daqInterval,
                    ModelType = (EnumMeasDefModelType)modelType,
                };

                List<string> measLocIdList = new List<string>();
                if (!string.IsNullOrEmpty(measLocIds))
                    measLocIdList = measLocIds.Split(',').ToList();
                List<string> DAUmeasLocIdList = new List<string>();
                if (!string.IsNullOrEmpty(DAUmeasLocIds))
                    DAUmeasLocIdList = DAUmeasLocIds.Split(',').ToList();
                if (IsAcqOil)
                {
                    var oilGrit = DevTreeManagement.GetMeaslocProcessListWithDAUByTurID(WindTurbineID).Find(item => item.Param_Type_Code == EnumWorkCondition_ParamType.WCPT_Oil_Debris);
                    measLocIdList.Add(oilGrit.MeasLocationID);
                }
                //mdfworkcondition =>工况波形定义表添加

                if (isAddType == "1")
                {
                    MeasDefinitionManagement.AddMeasdefinition(measDef, measLocIdList, DAUmeasLocIdList, IsAcqRotSpd, RotWaveLineCounts, dauid);

                }
                else
                {

                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        // 当前工况特征值
                        var wkevlist = ctx.ProcessEvConfs.Where(item => item.WindTurbineID == WindTurbineID && item.MeasDefinitionID == MeasDefId).ToList();
                        var triggerlist = ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == WindTurbineID && item.MeasDefinitionID == MeasDefId).ToList();

                        if (triggerlist.Count > 0 && wkevlist.Count > 0)
                        {
                            // 被取消的工况特征值
                            wkevlist.RemoveAll(t => conditionMonitoringLocIdList.Contains(t.MeasLocationID));
                            if (wkevlist.Count > 0)
                            {
                                foreach (var t in triggerlist)
                                {
                                    var _triggerProcess = ctx.TriggerProcess.Where(item => item.WindTurbineID == WindTurbineID && item.RuleID == t.RuleID).Select(k => k.MeasLocationID).ToList();
                                    var offev = wkevlist.FirstOrDefault(e => _triggerProcess.Contains(e.MeasLocationID));

                                    if (offev != null)
                                    {
                                        message = string.Format(message, 3, "特征值正在被触发采集使用，请先修改触发配置！");
                                        return "{" + message + "}";
                                    }
                                }
                            }
                        }
                    }

                    MeasDefinitionManagement.EditMeasdefinition(measDef, measLocIdList, DAUmeasLocIdList, IsAcqRotSpd, RotWaveLineCounts, dauid, modelType);
                    actionName = "修改";
                }

                //工况监视特征值
                MeasDefinitionManagement.EditMeasDefSupervise2(WindTurbineID, measDef, conditionMonitoringLocIds, IsAcqRotSpd);


                message = string.Format(message, 1, measDef.MeasDefinitionID);
                #region
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = MeasDefId;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("{1}_测量定义({0})", MeasDefId, actionName);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditMeasDefinition]" + actionName + "测量定义失败 机组ID:" + WindTurbineID, ex);
                message = string.Format(message, 0, actionName + "测量定义失败 :" + ex.Message);
            }
            return "{" + message + "}";
        }

        /// <summary>
        /// 获取测量定义采集间隔单位
        /// </summary>
        /// <returns></returns>

        [HttpGet("GetDaqIntervalUnitType")]
        public IActionResult GetDaqIntervalUnitType()
        {
            List<KeyValuePair<int, string>> list = new List<KeyValuePair<int, string>>();
            foreach (EnumDaqIntervalUnit value in Enum.GetValues(typeof(EnumDaqIntervalUnit)))
            {
                list.Add(new KeyValuePair<int, string>((int)value, CommonUtility.GetDecscription(value)));
            }

            return Ok(list);
        }

        /// <summary>
        /// 添加测量定义
        /// </summary>
        /// <param name="measd"></param>
        /// <returns></returns>
        [HttpPost("AddMeasDefinition")]
        public IActionResult AddMeasDefinition(MeasDefinitionDTO measd)
        {
            string message = "state:{0},msg:'{1}'";
            string actionName = "添加";
            try
            {
                //添加测量定义时，查询测量定义是否重复
                List<MeasDefinition> measDefinitionList = MeasDefinitionManagement.GetMeasDefListByTurId(measd.WindTurbineID);
                var _exist = measDefinitionList.FirstOrDefault(t => t.MeasDefinitionName == measd.MeasDefinitionName && t.MeasDefinitionID != measd.MeasDefinitionID);
                if (_exist != null)
                {
                    message = string.Format(message, 4, "测量定义名称重复！");
                    return Ok(ApiResponse<List<string>>.Error("测量定义名称重复"));
                }
                List<string> measLocIdList = new List<string>();
                List<string> DAUmeasLocIdList = new List<string>();

                MeasDefinition measDef = new MeasDefinition();
                //把测量定义中的半角+变成全角＋
                measd.MeasDefinitionName = measd.MeasDefinitionName.Replace("+", "＋");
                measDef.MeasDefinitionName = measd.MeasDefinitionName.Trim();
                measDef.WindTurbineID = measd.WindTurbineID;
                measDef.WindParkID = measd.WindParkID;
                measDef.MeasDefinitionID = measd.MeasDefinitionID;
                measDef.IsAvailable = measd.IsAvailable;

                measDef.Mdf_Ex = new MeasDefinition_Ex()
                {
                    DauID = measd.DauID,
                    MeasDefinitionID = measd.MeasDefinitionID,
                    WindTurbineID = measd.WindTurbineID,
                    DaqInterval = measd.DaqInterval,
                    ModelType = (EnumMeasDefModelType)measd.ModelType,
                    DaqIntervalUnit = (EnumDaqIntervalUnit)measd.DaqIntervalUnit,
                };
                MeasDefinitionManagement.AddMeasdefinition(measDef, measLocIdList, DAUmeasLocIdList, false, 0, measd.DauID);

                // 添加modbus设备绑定
                if (!string.IsNullOrEmpty(measd.ModbusDeviceIDs))
                {
                    List<ModbusUnit> existingModbusUnits = TIMManagement.GetModbusunitList(measd.WindTurbineID);
                    string[] modbusids = measd.ModbusDeviceIDs.Split(",");
                    List<ModbusDef> modbusdefs = new();
                    for (int i = 0; i < modbusids.Length; i++)
                    {
                        // 通过modbusdeviceid查询modbus设备

                        var modbus = existingModbusUnits.FirstOrDefault(t => t.ModbusDeviceID == int.Parse(modbusids[i]));
                        if (modbus != null)
                        {
                            modbusdefs.Add(new ModbusDef()
                            {
                                WindTurbineID = measd.WindTurbineID,
                                ModbusUnitID = modbus.ModbusUnitID,
                                MeasDefinitionID = measDef.MeasDefinitionID,
                                ModbusDeviceID = modbus.ModbusDeviceID,
                                SampleFrequency = 0,
                                SampleTime = 0,
                            });
                        }
                    }

                    TIMManagement.AddModbusDef(modbusdefs);
                }

                message = string.Format(message, 1, measDef.MeasDefinitionID);
                #region
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = measd.MeasDefinitionID;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("{1}_测量定义({0})", measd.MeasDefinitionID, actionName);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditMeasDefinition]" + actionName + "测量定义失败 机组ID:" + measd.WindTurbineID, ex);
                message = string.Format(message, 0, actionName + "测量定义失败 :" + ex.Message);
                return Ok(ApiResponse<string>.Error("测量定义失败 :" + ex.Message));
            }
            return Ok(ApiResponse<string>.Success("OK"));
        }

        /// <summary>
        /// 添加测量定义
        /// </summary>
        /// <param name="measd"></param>
        /// <returns></returns>
        [HttpPost("UpdateMeasDefinition")]
        public IActionResult UpdateMeasDefinition(MeasDefinitionDTO measd)
        {
            string message = "state:{0},msg:'{1}'";
            string actionName = "添加";
            try
            {
                //添加测量定义时，查询测量定义是否重复
                List<MeasDefinition> measDefinitionList = MeasDefinitionManagement.GetMeasDefListByTurId(measd.WindTurbineID);
                var _exist = measDefinitionList.FirstOrDefault(t => t.MeasDefinitionName == measd.MeasDefinitionName && t.MeasDefinitionID != measd.MeasDefinitionID);
                if (_exist != null)
                {
                    message = string.Format(message, 4, "测量定义名称重复！");
                    return Ok(ApiResponse<List<string>>.Error("测量定义名称重复"));
                }
                List<string> measLocIdList = new List<string>();
                List<string> DAUmeasLocIdList = new List<string>();

                MeasDefinition measDef = new MeasDefinition();
                //把测量定义中的半角+变成全角＋
                measd.MeasDefinitionName = measd.MeasDefinitionName.Replace("+", "＋");
                measDef.MeasDefinitionName = measd.MeasDefinitionName.Trim();
                measDef.WindTurbineID = measd.WindTurbineID;
                measDef.WindParkID = measd.WindParkID;
                measDef.MeasDefinitionID = measd.MeasDefinitionID;
                measDef.IsAvailable = measd.IsAvailable;

                measDef.Mdf_Ex = new MeasDefinition_Ex()
                {
                    DauID = measd.DauID,
                    MeasDefinitionID = measd.MeasDefinitionID,
                    WindTurbineID = measd.WindTurbineID,
                    DaqInterval = measd.DaqInterval,
                    ModelType = (EnumMeasDefModelType)measd.ModelType,
                    DaqIntervalUnit = (EnumDaqIntervalUnit)measd.DaqIntervalUnit,
                };
                MeasDefinitionManagement.EditMeasdefinition(measDef, measLocIdList, DAUmeasLocIdList, false, 0, measd.DauID, measd.ModelType);
                message = string.Format(message, 1, measDef.MeasDefinitionID);
                #region
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = measd.MeasDefinitionID;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("{1}_测量定义({0})", measd.MeasDefinitionID, actionName);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditMeasDefinition]" + actionName + "测量定义失败 机组ID:" + measd.WindTurbineID, ex);
                message = string.Format(message, 0, actionName + "测量定义失败 :" + ex.Message);
                return Ok(ApiResponse<string>.Error("测量定义失败 :" + ex.Message));
            }
            return Ok(ApiResponse<string>.Success("OK"));
        }

        /// <summary>
        /// 删除测量定义列表信息
        /// </summary>
        /// <param name="WindTurbineID">机组ID</param>
        /// <param name="MeasDefId">测量定义ID</param>
        /// <returns></returns>
        /// 
        [HttpGet("DeleteMeasDef")]
        public IActionResult DeleteMeasChannel(string WindTurbineID, string MeasDefId)
        {
            string message = "state:{0},msg:'{1}'";
            try
            {
                MeasDefinition measDef = MeasDefinitionManagement.GetMeasdefinition(WindTurbineID, MeasDefId);
                if (measDef != null)
                {
                    //// 查询是否关联modbus设备，如果关联，则提示禁止删除。
                    //List<ModbusDef> modbusdeflist = TIMManagement.GetModbusDefList(WindTurbineID, MeasDefId);
                    //if (modbusdeflist.Count > 0)
                    //{
                    //    message = string.Format(message, 0, "删除失败，请先删除已绑定的Modbus设备！");
                    //    //return "{" + message + "}";
                    //    return Ok(ApiResponse<string>.Error("删除失败，请先删除已绑定的Modbus设备！"));
                    //}

                    //判断当前测量定义是否在其它测量定义中被触发
                    List<MeasTriggeredExecuteMdf> measTriggeredExecuteMdfList = new List<MeasTriggeredExecuteMdf>();
                    List<MeasDefinition> mdfModelsList = new List<MeasDefinition>();
                    List<string> triggerNameList = new List<string>();
                    string triggerRelationMeasName = "";
                    //查询触发规则id和测量定义关联表 mdftriggeredexecutemdf
                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        measTriggeredExecuteMdfList = ctx.ExecuteMdfs.Where(item => item.WindTurbineID == WindTurbineID && item.MeasDefinitionID == MeasDefId).ToList();
                    }
                    if (measTriggeredExecuteMdfList.Count != 0)
                    {

                        //根据机组id加测量定义id查询匹配触发规则名称以及触发规则id(mdftriggerrule)
                        for (int i = 0; i < measTriggeredExecuteMdfList.Count; i++)
                        {
                            string ruleID = measTriggeredExecuteMdfList[i].RuleID;
                            List<MeasTriggerRuleDef> triggerDefList = new List<MeasTriggerRuleDef>();
                            //根据机组id和触发规则id查询mdftriggerrule，
                            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                            {
                                triggerDefList = ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == WindTurbineID && item.RuleID == ruleID).ToList();
                            }
                            if (triggerDefList.Count != 0)
                            {
                                for (int j = 0; j < triggerDefList.Count; j++)
                                {
                                    string measid = triggerDefList[j].MeasDefinitionID;
                                    //获取应用到该触发的测量定义
                                    MeasDefinition measDefinition = MeasDefinitionManagement.GetMeasdefinition(WindTurbineID, measid);
                                    triggerNameList.Add(measDefinition.MeasDefinitionName);
                                }
                            }
                        }
                        List<string> replicateTriggerNameList = new List<string>();
                        for (int i = 0; i < triggerNameList.Count(); i++)
                        {
                            if (!replicateTriggerNameList.Contains(triggerNameList[i]))
                            {
                                replicateTriggerNameList.Add(triggerNameList[i]);
                            }
                        }
                        replicateTriggerNameList.ForEach(item =>
                        {
                            triggerRelationMeasName += item + ",";
                        });
                        triggerRelationMeasName = triggerRelationMeasName.Remove(triggerRelationMeasName.LastIndexOf(","), 1);
                        message = string.Format(message, 2, "当前测量定义已在" + triggerRelationMeasName + "的触发规则中使用，请先释放!");
                        return Ok(ApiResponse<string>.Error("当前测量定义已在" + triggerRelationMeasName + "的触发规则中使用，请先释放!"));
                    }

                    MeasDefinitionManagement.DeleteMeasdefinition(WindTurbineID, MeasDefId);
                    //删除指定测量定义下所有的触发采集数据
                    List<MeasTriggerRuleDef> measTriggerDefsList = null;
                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        measTriggerDefsList = ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == WindTurbineID && item.MeasDefinitionID == MeasDefId).ToList();
                    }

                    for (int i = 0; i < measTriggerDefsList.Count; i++)
                    {

                        string TriggerRuleID = measTriggerDefsList[i].RuleID;
                        //删除mdftriggersupervisedvariable表
                        /*                        using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                                                {
                                                    ctx.TriggerProcess.RemoveRange(ctx.TriggerProcess.Where(item => item.WindTurbineID == WindTurbineID && item.RuleID == TriggerRuleID));
                                                    ctx.SaveChanges();
                                                }*/
                        //查询mdftriggersupervisedvariable表如果没有数据，那就是时间触发删除时间
                        List<MeasTriggerProcess> mtpList = null;
                        using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                            mtpList = ctx.TriggerProcess.Where(item => item.WindTurbineID == WindTurbineID && item.RuleID == TriggerRuleID).ToList();
                        }
                        if (mtpList.Count != 0)
                        {
                            //删除mdftriggersupervisedvariable表
                            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                            {
                                ctx.TriggerProcess.RemoveRange(ctx.TriggerProcess.Where(item => item.WindTurbineID == WindTurbineID && item.RuleID == TriggerRuleID));
                                ctx.SaveChanges();
                            }
                        }
                        else
                        {
                            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                            {
                                ctx.TriggerTimes.RemoveRange(ctx.TriggerTimes.Where(item => item.RuleID == TriggerRuleID));
                                ctx.SaveChanges();
                            }
                        }

                        //删除mdftriggerrule表
                        using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                            ctx.TriggerRuleDefs.RemoveRange(ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == WindTurbineID && item.MeasDefinitionID == MeasDefId));
                            ctx.SaveChanges();
                        }

                        //删除mdftriggeredexecutemdf表
                        using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                            ctx.ExecuteMdfs.RemoveRange(ctx.ExecuteMdfs.Where(item => item.WindTurbineID == WindTurbineID && item.RuleID == TriggerRuleID));
                            ctx.SaveChanges();
                        }
                    }
                    //删除工况监视特征值

                    using (CMSFramework.EF.MeasDef.MDFContext ctxx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        List<CMSFramework.BusinessEntity.MeasDef_Ev_Process> MeasDef_Supervise_ProcessList = null;
                        MeasDef_Supervise_ProcessList = ctxx.ProcessEvConfs.Where(items => items.WindTurbineID == WindTurbineID).ToList();
                        foreach (var item in MeasDef_Supervise_ProcessList)
                        {
                            if (item.MeasDefinitionID == MeasDefId)
                            {
                                ctxx.ProcessEvConfs.Remove(item);
                            }

                        }
                        ctxx.SaveChanges();
                    }

                    // 删除modbus设备和测量定义绑定关系
                    using (CMSFramework.EF.MeasDef.MDFContext ctxx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        var modbusdef = ctxx.ModbusDefs.Where(items => items.WindTurbineID == WindTurbineID && items.MeasDefinitionID == MeasDefId);
                        if (modbusdef.Any())
                        {
                            ctxx.ModbusDefs.RemoveRange(modbusdef);
                            ctxx.SaveChanges();
                        }
                    }
                    //for (int i = 0; i < MeasDef_Supervise_ProcessList.Count; i++) {
                    //    string measlID = MeasDef_Supervise_ProcessList[i].MeasLocationID;
                    //    ctx.ProcessSupervises.RemoveRange(ctx.ProcessSupervises.Where(item => item.WindTurbineID == WindTurbineID && item.MeasDefinitionID == measDef.MeasDefinitionID && item.MeasLocationID== measlID));
                    //    ctx.SaveChanges();
                    //}
                    // ctx.SaveChanges();
                    //}

                }
                message = string.Format(message, 1, "");
                #region
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = MeasDefId;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("删除_测量定义列表({0})", MeasDefId);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditMeasDefinition]" + "删除测量定义列表失败 机组ID:" + WindTurbineID + " 测量定义ID：" + MeasDefId, ex);
                message = string.Format(message, 0, "删除测量定义列表失败 :" + ex.Message);
                return Ok(ApiResponse<string>.Error("删除测量定义列表失败"));

            }
            return Ok(ApiResponse<string>.Success("OK"));
        }

        #endregion

        #region 时域 波形定义

        /// <summary>
        /// 获取波形类型
        /// </summary>
        /// <returns></returns>
        /// 
        [HttpGet("GetWaveType")]
        public IActionResult GetWaveType()
        {
            List<KeyValuePair<int, string>> list = new List<KeyValuePair<int, string>>();
            foreach (EnumSignalType value in Enum.GetValues(typeof(EnumSignalType)))
            {
                list.Add(new KeyValuePair<int, string>((int)value, CommonUtility.GetDecscription(value)));
            }

            return Ok(list);
        }
        /// <summary>
        /// 获取机组ID获取时域波形定义
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <returns></returns>
        /// 
        [HttpGet("GetTimeWaveDefList")]
        public IActionResult GetTimeWaveDefListByTurbId(string turbineID, string measDefinitionID)
        {
            //List<MeasDefinition> Definlist = MeasDefinitionManagement.GetMeasDefListByTurId(turbineID);
            List<MeasLoc_Vib> LocVibList = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
            List<MeasDefinition> listDefin = new List<MeasDefinition>();
            List<WaveDef_Time> list = new List<WaveDef_Time>();
            List<WaveDef_Time> AllList = MeasDefinitionManagement.GetWaveDefByTurId_Time(turbineID);
            AllList.FindAll(item => item.MeasDefinitionID == measDefinitionID).ForEach(item =>
            {
                // 添加特征值配置
                item.VibEigenValueConf = EigenValueManage.GetMdfTimeDomainEvConf(turbineID, item.WaveDefinitionID, measDefinitionID, item.MeasLocationID);
                //WaveDefParamID 里面存储的SampleLength，因为SampleLength不是float型
                //wangy 2016年2月17日 11:33:32
                item.WaveDefParamID = Math.Round(Convert.ToDouble(item.SampleLength) / 1000, 3) + "";
                item.MeasLocationID = LocVibList.Find(vib => item.MeasLocationID == vib.MeasLocationID).MeasLocName;

                // 信号类型
                item.WindTurbineID = AppFramework.Utility.EnumHelper.GetDescription(item.SignalType);


                list.Add(item);
            });
            return Ok(list);
        }

        /// <summary>
        /// 获取电流电压波形定义列表
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="measDefinitionID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetVoltageCurrentWaveDefList")]
        public IActionResult GetVoltageCurrentWaveDefListByTurbId(string turbineID, string measDefinitionID)
        {
            //List<MeasDefinition> Definlist = MeasDefinitionManagement.GetMeasDefListByTurId(turbineID);
            List<MeasLoc_VoltageCurrent> LocVibList = DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(turbineID);
            List<MeasDefinition> listDefin = new List<MeasDefinition>();
            List<WaveDef_Time> list = new List<WaveDef_Time>();
            List<WaveDef_Time> AllList = MeasDefinitionManagement.GetWaveDefByTurId_VoltageCurrent(turbineID);
            AllList.FindAll(item => item.MeasDefinitionID == measDefinitionID).ForEach(item =>
            {
                // 添加特征值配置
                item.VibEigenValueConf = EigenValueManage.GetMdfTimeDomainEvConf(turbineID, item.WaveDefinitionID, measDefinitionID, item.MeasLocationID);

                item.WaveDefParamID = Math.Round(Convert.ToDouble(item.SampleLength) / 1000, 3) + "";
                item.MeasLocationID = LocVibList.Find(vib => item.MeasLocationID == vib.MeasLocationID).MeasLocName;

                // 信号类型
                item.WindTurbineID = AppFramework.Utility.EnumHelper.GetDescription(item.SignalType);

                list.Add(item);
            });
            return Ok(list);
        }

        /// <summary>
        /// 获取信号带宽
        /// </summary>
        /// <returns></returns>
        public string ShowUpperLimitFreqList()
        {
            return WebCommonRefData.GetUpperLimitFreqList().OrderBy(t => t.Value).ToJson();
        }
        /// <summary>
        /// 获取包络信号带宽
        /// </summary>
        /// <returns></returns>
        /// 
        [HttpGet("ShowParamUpperLimitFreqList")]
        public IActionResult ShowParamUpperLimitFreqList()
        {
            return Ok(WebCommonRefData.GetParamUpperLimitFreqList());
        }

        /// <summary>
        /// 获取采样长度
        /// </summary>
        /// <returns></returns>
        public string GetSampleTimeLengthList()
        {
            return WebCommonRefData.GetSampleTimeLengthList().ToJson();
        }

        /// <summary>
        /// 获取测量定义中未定义时域波形的 测量位置列表
        /// </summary>
        /// <param name="_measDefId"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetUnusedVibMeasLoc")]
        public IActionResult GetUnusedVibMeasLocByMeasDefId_Time(string turbineID, string MeasDefinitionID, string DAUID)
        {
            // 机组下的全部测量位置
            List<MeasLoc_Vib> vibLoc = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
            //List<DAUChannelV2> List = DauManagement.GetDAUVibChannelList(turbineID);
            List<DAUChannelV2> List = DAUSManageModel.GetDAUVibChannelList(turbineID, DAUID);
            //删除没绑定DAU通道的振动测量位置
            vibLoc.RemoveAll(item =>
            {
                return List.Find(wave => wave.MeasLocVibID == item.MeasLocationID) == null;
            });
            // 获取波形定义
            //List<WaveDef_Time> timeWave = MeasDefinitionManagement.GetWaveDefByMdfId_Time(turbineID, MeasDefinitionID);
            //vibLoc.RemoveAll(item =>
            //{
            //    return (timeWave.Find(wave => wave.MeasLocationID == item.MeasLocationID) != null);
            //});
            if (vibLoc.Count > 0)
                vibLoc = vibLoc.OrderBy(item => item.OrderSeq).ToList();
            return Ok(vibLoc);
        }

        /// <summary>
        /// 获取未使用的电流电压测量位置列表
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="MeasDefinitionID"></param>
        /// <param name="DAUID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetUnusedVoltageCurrentMeasLoc")]
        public IActionResult GetUnusedVoltageCurrentMeasLocByMeasDefId(string turbineID, string MeasDefinitionID, string DAUID)
        {
            // 机组下的全部测量位置
            List<MeasLoc_VoltageCurrent> vibLoc = DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(turbineID);
            //List<DAUChannelV2> List = DauManagement.GetDAUVibChannelList(turbineID);
            List<DAUChannel_VoltageCurrent> List = DAUSManageModel.GetDAUVoltageCurrentChannelList(turbineID, DAUID);
            //删除没绑定DAU通道的振动测量位置
            vibLoc.RemoveAll(item =>
            {
                return (List.Find(wave => wave.MeasLoc_ProcessId == item.MeasLocationID) == null);
            });
            // 获取波形定义
            //List<WaveDef_Time> timeWave = MeasDefinitionManagement.GetWaveDefByMdfId_Time(turbineID, MeasDefinitionID);
            //vibLoc.RemoveAll(item =>
            //{
            //    return (timeWave.Find(wave => wave.MeasLocationID == item.MeasLocationID) != null);
            //});
            if (vibLoc.Count > 0)
                vibLoc = vibLoc.OrderBy(item => item.OrderSeq).ToList();
            return Ok(vibLoc);
        }

        private List<MeasLoc_Vib> GetUnusedVibMeasLocByMeasDefId_TimeList(string turbineID, string MeasDefinitionID)
        {
            // 机组下的全部测量位置
            List<MeasLoc_Vib> vibLoc = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
            // 获取波形定义
            //移除波形定义配置限制 
            //List<WaveDef_Time> timeWave = MeasDefinitionManagement.GetWaveDefByMdfId_Time(turbineID, MeasDefinitionID);
            //vibLoc.RemoveAll(item =>
            //{
            //    return (timeWave.Find(wave => wave.MeasLocationID == item.MeasLocationID) != null);
            //});
            if (vibLoc.Count > 0)
                vibLoc = vibLoc.OrderBy(item => item.OrderSeq).ToList();
            return vibLoc;
        }

        private List<MeasLoc_VoltageCurrent> GetUnusedVibMeasLocByMeasDefId_VoltageCurrentList(string turbineID, string MeasDefinitionID)
        {
            // 机组下的全部测量位置
            List<MeasLoc_VoltageCurrent> vibLoc = DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(turbineID);
            // 获取波形定义
            //移除波形定义配置限制 
            //List<WaveDef_Time> timeWave = MeasDefinitionManagement.GetWaveDefByMdfId_Time(turbineID, MeasDefinitionID);
            //vibLoc.RemoveAll(item =>
            //{
            //    return (timeWave.Find(wave => wave.MeasLocationID == item.MeasLocationID) != null);
            //});
            if (vibLoc.Count > 0)
                vibLoc = vibLoc.OrderBy(item => item.OrderSeq).ToList();
            return vibLoc;
        }

        public List<MeasLoc_Vib> GetParameUnusedVibMeasLocByMeasDefId_TimeList(string turbineID, string MeasDefinitionID)
        {
            // 机组下的全部测量位置
            List<MeasLoc_Vib> vibLoc = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
            // 获取波形定义
            List<WaveDef_Time> timeWave = MeasDefinitionManagement.GetWaveDefByMdfId_Time(turbineID, MeasDefinitionID);
            vibLoc.RemoveAll(item =>
            {
                return (timeWave.Find(wave => wave.MeasLocationID == item.MeasLocationID) != null);
            });
            if (vibLoc.Count > 0)
                vibLoc = vibLoc.OrderBy(item => item.OrderSeq).ToList();
            return vibLoc;
        }

        /// <summary>
        /// 添加/修改时域波形定义
        /// </summary>
        /// <param name="WaveDefId">波形定义ID</param>
        /// <param name="WaveDefName">波形定义名称</param>
        /// <param name="MeasDefinitionID"></param>
        /// <param name="WindTurbineID"></param>
        /// <param name="WindParkID"></param>
        /// <param name="measLocIds">测量振动位置Id,添加用</param>
        /// <param name="TimeWdfUpFreq"></param>
        /// <param name="TimeWdfSampleLength"></param>
        /// <param name="isAddType"></param>
        /// <param name="messLocNames">测量振动位置Name,前台提示会用到</param>
        /// <returns></returns>
        public string EditWaveDefinition(string WaveDefId, string WaveDefName, string MeasDefinitionID, string WindTurbineID, string WindParkID, string measLocIds, float TimeWdfUpFreq, float TimeWdfSampleLength, string isAddType, string messLocNames, string dauid, int signalType, string GeneralEVList = "", string FBEList = "", bool isAll = false)
        {
            string message = "state:{0},msg:'{1}'";
            string actionName = "添加";
            try
            {
                List<string> measLocIdList = new List<string>();
                if (isAddType == "1")
                {
                    if (!string.IsNullOrEmpty(measLocIds))
                        measLocIdList = measLocIds.Split(',').ToList();
                    else
                    {
                        message = string.Format(message, 0, "没有振动测量位置信息！");
                        return "{" + message + "}";
                    }
                }
                WaveDef_Time wdf = isAddType == "1" ? new WaveDef_Time() : MeasDefinitionManagement.GetWaveDefById_Time(WindTurbineID, WaveDefId);
                wdf.WaveDefinitionName = WaveDefName.Trim();
                wdf.UpperLimitFreqency = TimeWdfUpFreq;
                wdf.SampleLength = Convert.ToInt32(TimeWdfSampleLength * 1000);
                wdf.SignalType = (EnumSignalType)signalType;
                //wdf.XmlEigenValueDef = MeasDefinitionManagement.EigenStringConverToJSON(GeneralEVList, FBEList);

                // 通带和频带特征值添加
                List<string> generalEvlist = new List<string>();
                if (!string.IsNullOrEmpty(GeneralEVList))
                {
                    generalEvlist = GeneralEVList.Split(',').ToList();
                }
                List<string> fbeEvlist = new List<string>();
                if (!string.IsNullOrEmpty(FBEList))
                {
                    fbeEvlist = FBEList.Split(',').ToList();
                }
                List<MeasDef_Ev_Vib> evconf = new List<MeasDef_Ev_Vib>();

                // 被添加的有效Id值，如果是添加操作，对应的是ID的串 ps: id是振动测量位置的ID
                string effectiveId = "";
                if (isAddType == "1")
                {
                    string ErrorMsg = "";
                    if (CheckAddTimeWdf(WindTurbineID, MeasDefinitionID, measLocIdList.Count(), out ErrorMsg) == false)
                    {
                        message = string.Format(message, 0, ErrorMsg);
                        return "{" + message + "}";
                    }
                    wdf.MeasDefinitionID = MeasDefinitionID;
                    wdf.WindTurbineID = WindTurbineID;
                    wdf.LowerLimitFreqency = 0;
                    wdf.WaveFormType = EnumWaveFormType.WDF_Time;
                    List<string> MeasLocationNames = messLocNames.Split(',').ToList();
                    string locNames = "";
                    string channelNames = string.Empty;
                    List<MeasLoc_Vib> vibLocList = GetUnusedVibMeasLocByMeasDefId_TimeList(WindTurbineID, MeasDefinitionID);
                    List<DAUChannelV2> channelList = WTCMSLive.BusinessModel.DauManagement.GetDAUVibChannelList(WindTurbineID);

                    // 修改其他波形定义
                    if (isAll)
                    {
                        // 当前测量定义下的所有波形定义
                        var _waveDefs = MeasDefinitionManagement.GetWaveDefByMdfId_Time(WindTurbineID, wdf.MeasDefinitionID);
                        if (_waveDefs.Count > 0)
                        {
                            foreach (var _curWave in _waveDefs)
                            {
                                evconf.AddRange(MakeTimeEvConfig(WindTurbineID, MeasDefinitionID, _curWave, GeneralEVList, FBEList));
                            }
                        }
                    }

                    // 遍历用户选择测量位置
                    for (int i = 0; i < measLocIdList.Count(); i++)
                    {
                        // 存在波形定义？
                        if (MeasDefinitionManagement.IsExsitsWaveDef(WindTurbineID, MeasDefinitionID, measLocIdList[i], EnumWaveFormType.WDF_Time) == false)
                        {
                            MeasLoc_Vib locVib = vibLocList.Find(item => item.MeasLocationID == measLocIdList[i]);
                            wdf.MeasLocationID = measLocIdList[i];
                            //存在对应的DAU通道?
                            DAUChannelV2 channel = channelList.Find(item => item.MeasLocVibID == measLocIdList[i]);
                            if (channel != null)
                            {
                                // 不存在波形定义并且有对应的通道，添加.
                                effectiveId = measLocIdList[i] + ",";
                                MeasDefinitionManagement.AddWaveDef_Time(wdf);

                                // 添加特征值
                                if (generalEvlist.Count > 0)
                                {
                                    foreach (var generalEv in generalEvlist)
                                    {
                                        var _curType = (EnumEigenvalueName)Convert.ToInt32(generalEv);
                                        var _name = EnumEigenvalueNameHelper.GetAlisa(_curType);
                                        if (_name == null)
                                        {
                                            _name = _curType.ToString().Replace("Enum_", "").ToUpper();
                                        }
                                        evconf.Add(new MeasDef_Ev_Vib()
                                        {
                                            WindTurbineID = WindTurbineID,
                                            MeasDefinitionID = MeasDefinitionID,
                                            MeasLocationID = measLocIdList[i],
                                            WaveDefinitionID = wdf.WaveDefinitionID,
                                            EvType = EnumEigenValueGroupType.GeneralEV,
                                            Type = _curType,
                                            Name = _name,
                                        });
                                    }
                                }

                                // 频域
                                if (fbeEvlist.Count > 0)
                                {
                                    foreach (var fbe in fbeEvlist)
                                    {
                                        List<string> _cur = fbe.Split('#').ToList();

                                        var _curType = EnumEigenvalueName.Enum_FBE;
                                        var _name = EnumEigenvalueNameHelper.GetAlisa(_curType);
                                        if (_name == null)
                                        {
                                            _name = _curType.ToString().Replace("Enum_", "").ToUpper();
                                        }

                                        evconf.Add(new MeasDef_Ev_Vib()
                                        {
                                            WindTurbineID = WindTurbineID,
                                            MeasDefinitionID = MeasDefinitionID,
                                            MeasLocationID = measLocIdList[i],
                                            WaveDefinitionID = wdf.WaveDefinitionID,
                                            EvType = EnumEigenValueGroupType.FBE,
                                            Type = _curType,
                                            Name = _name,
                                            UnderLimitValue = Convert.ToDouble(_cur[1]),
                                            UpperLimitValue = Convert.ToDouble(_cur[2]),
                                            UpperLimitUnit = "1",
                                            UnderLimitUnit = "1",
                                        });
                                    }

                                }

                            }
                            else
                            {
                                channelNames += MeasLocationNames[i] + ',';
                            }
                        }
                        else
                        {
                            // 存在，记录测量位置名称
                            locNames += MeasLocationNames[i] + "，";
                        }
                    }
                    // 测量位置已存在波形定义，提示。
                    if (locNames != "")
                    {
                        string strs = locNames.Substring(0, locNames.LastIndexOf("，"));
                        message = string.Format(message, 0, "振动测量位置（" + strs + "）已经存在时域波形定义。刷新查看添加结果。");
                        return "{" + message + "}";
                    }
                    if (!string.IsNullOrEmpty(channelNames))
                    {
                        string strs = channelNames.Substring(0, channelNames.LastIndexOf(","));
                        message = string.Format(message, 0, "振动测量位置（" + strs + "）没有和采集单元通道绑定。刷新查看添加结果。");
                        return "{" + message + "}";
                    }
                    if (locNames == "" && channelNames == "")
                    {
                        //如果是全部添加，则返回添加正常，前台自动刷新页面信息
                        // 如果是部分提交，那么前台需要手工刷新数据信息
                        message = string.Format(message, 1, "");
                    }
                }
                else
                {
                    // 是否应用当前测量一定下的全部
                    if (isAll)
                    {
                        // 当前测量定义下的所有波形定义
                        var _waveDefs = MeasDefinitionManagement.GetWaveDefByMdfId_Time(WindTurbineID, wdf.MeasDefinitionID);
                        if (_waveDefs.Count > 0)
                        {
                            foreach (var _curWave in _waveDefs)
                            {
                                evconf.AddRange(MakeTimeEvConfig(WindTurbineID, MeasDefinitionID, _curWave, GeneralEVList, FBEList));
                            }
                        }
                    }
                    else
                    {
                        evconf = MakeTimeEvConfig(WindTurbineID, MeasDefinitionID, wdf, GeneralEVList, FBEList);
                    }


                    effectiveId = wdf.MeasDefinitionID;
                    MeasDefinitionManagement.EditWaveDef_Time(wdf);
                    actionName = "修改";
                    message = string.Format(message, 1, "");



                    //// 删除特征值配置
                    //EigenValueManage.DelMdfTimedomainEvConf(WindTurbineID,wdf.WaveDefinitionID, MeasDefinitionID);
                    //// 添加特征值
                    //if (generalEvlist.Count > 0)
                    //{
                    //    foreach (var generalEv in generalEvlist)
                    //    {
                    //        var _curType = (EnumEigenvalueName)Convert.ToInt32(generalEv);
                    //        var _name = EnumEigenvalueNameHelper.GetAlisa(_curType);
                    //        if (_name == null)
                    //        {
                    //            _name = _curType.ToString().Replace("Enum_", "");
                    //        }
                    //        evconf.Add(new MeasDef_Ev_Vib()
                    //        {
                    //            WindTurbineID = WindTurbineID,
                    //            MeasDefinitionID = MeasDefinitionID,
                    //            MeasLocationID = wdf.MeasLocationID,
                    //            WaveDefinitionID = wdf.WaveDefinitionID,
                    //            EvType = EnumEigenValueGroupType.GeneralEV,
                    //            Type = _curType,
                    //            Name = _name,
                    //        });
                    //    }
                    //}

                    //// 频域
                    //if (fbeEvlist.Count > 0)
                    //{
                    //    foreach (var fbe in fbeEvlist)
                    //    {
                    //        List<string> _cur = fbe.Split('#').ToList();

                    //        var _curType = EnumEigenvalueName.Enum_FBE;
                    //        var _name = EnumEigenvalueNameHelper.GetAlisa(_curType);
                    //        if (_name == null)
                    //        {
                    //            _name = _curType.ToString().Replace("Enum_", "");
                    //        }

                    //        evconf.Add(new MeasDef_Ev_Vib()
                    //        {
                    //            WindTurbineID = WindTurbineID,
                    //            MeasDefinitionID = MeasDefinitionID,
                    //            MeasLocationID = wdf.MeasLocationID,
                    //            WaveDefinitionID = wdf.WaveDefinitionID,
                    //            EvType = EnumEigenValueGroupType.FBE,
                    //            Type = _curType,
                    //            Name = _name,
                    //            UnderLimitValue = Convert.ToDouble(_cur[1]),
                    //            UpperLimitValue = Convert.ToDouble(_cur[2]),
                    //            UpperLimitUnit = "1",
                    //            UnderLimitUnit = "1",
                    //        });
                    //    }

                    //}
                }

                EigenValueManage.AddMdfTimedomainEvConf(evconf);

                // 提升测量定义id
                //DauManagement.UpdateMeasDefVersion(WindTurbineID, dauid);

                #region
                if (!string.IsNullOrEmpty(effectiveId))
                {
                    LogEntity logEntity = new LogEntity();
                    logEntity.LogDB = ConstDefine.UserManagementLog;
                    logEntity.LogTime = DateTime.Now;
                    logEntity.NodeID = effectiveId.IndexOf(',') > -1 ? effectiveId.Substring(0, effectiveId.LastIndexOf(',')) : effectiveId;
                    logEntity.UserName = Request.Cookies["WindCMSUserName"];
                    logEntity.OperationDescription
                        = string.Format("{1}_测量定义({0})", logEntity.NodeID, actionName);
                    LogManagement.UserlogWrite(logEntity);
                }
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditWaveDefinition]" + actionName + "测量定义失败 机组ID:" + WindTurbineID, ex);
                message = string.Format(message, 0, actionName + "测量定义失败 :" + ex.Message);
            }
            return "{" + message + "}";
        }

        /// <summary>
        /// 获取所有的特征值类型
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetEigenValueType")]
        public IActionResult GetEigenValueType()
        {
            return Ok(EigenValueManager.GetGeneralEigenValueCodeList());
        }


        /// <summary>
        /// 添加/修改时域波形定义
        /// </summary>
        /// <param name="WaveDefId">波形定义ID</param>
        /// <param name="WaveDefName">波形定义名称</param>
        /// <param name="MeasDefinitionID"></param>
        /// <param name="WindTurbineID"></param>
        /// <param name="WindParkID"></param>
        /// <param name="measLocIds">测量振动位置Id,添加用</param>
        /// <param name="TimeWdfUpFreq"></param>
        /// <param name="TimeWdfSampleLength"></param>
        /// <param name="isAddType"></param>
        /// <param name="messLocNames">测量振动位置Name,前台提示会用到</param>
        /// <returns></returns>
        ///
        [HttpPost("MakeWaveDefinition")]
        [BatchOperation(nameof(BatchMakeWaveDefinition))]
        public IActionResult MakeWaveDefinition([FromBody] BatchMakeWaveDefinitionRequest request)
        {
            string message = "state:{0},msg:'{1}'";
            string actionName = "添加";
            try
            {
                // 参数验证
                if (request?.SourceData == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                var waveDefinitionDTO = request.SourceData;
                List<string> measLocIdList = new List<string>();
                if (waveDefinitionDTO.isAddType == "1")
                {
                    if (!string.IsNullOrEmpty(waveDefinitionDTO.measLocIds))
                        measLocIdList = waveDefinitionDTO.measLocIds.Split(',').ToList();
                    else
                    {
                        message = string.Format(message, 0, "没有振动测量位置信息！");
                        //return "{" + message + "}";
                        return Ok(ApiResponse<string>.Error(message));
                    }
                }
                WaveDef_Time wdf = waveDefinitionDTO.isAddType == "1" ? new WaveDef_Time() : MeasDefinitionManagement.GetWaveDefById_Time(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.WaveDefinitionID);
                wdf.WaveDefinitionName = waveDefinitionDTO.WaveDefinitionName.Trim();
                wdf.UpperLimitFreqency = waveDefinitionDTO.TimeWdfUpFreq;
                wdf.SampleLength = Convert.ToInt32(waveDefinitionDTO.TimeWdfSampleLength * 1000);
                wdf.SignalType = (EnumSignalType)waveDefinitionDTO.signalType;
                //wdf.XmlEigenValueDef = MeasDefinitionManagement.EigenStringConverToJSON(GeneralEVList, FBEList);

                // 通带和频带特征值添加
                List<string> generalEvlist = new List<string>();
                if (!string.IsNullOrEmpty(waveDefinitionDTO.GeneralEVList))
                {
                    generalEvlist = waveDefinitionDTO.GeneralEVList.Split(',').ToList();
                }
                List<string> fbeEvlist = new List<string>();
                if (!string.IsNullOrEmpty(waveDefinitionDTO.FBEList))
                {
                    fbeEvlist = waveDefinitionDTO.FBEList.Split(',').ToList();
                }
                List<MeasDef_Ev_Vib> evconf = new List<MeasDef_Ev_Vib>();

                // 被添加的有效Id值，如果是添加操作，对应的是ID的串 ps: id是振动测量位置的ID
                string effectiveId = "";
                if (waveDefinitionDTO.isAddType == "1")
                {
                    string ErrorMsg = "";
                    if (CheckAddTimeWdf(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.MeasDefinitionID, measLocIdList.Count(), out ErrorMsg) == false)
                    {
                        message = string.Format(message, 0, ErrorMsg);
                        //return "{" + message + "}";
                        return Ok(ApiResponse<string>.Error(message));
                    }
                    wdf.MeasDefinitionID = waveDefinitionDTO.MeasDefinitionID;
                    wdf.WindTurbineID = waveDefinitionDTO.WindTurbineID;
                    wdf.LowerLimitFreqency = 0;
                    wdf.WaveFormType = EnumWaveFormType.WDF_Time;
                    List<string> MeasLocationNames = waveDefinitionDTO.messLocNames.Split(',').ToList();
                    string locNames = "";
                    string channelNames = string.Empty;
                    List<MeasLoc_Vib> vibLocList = GetUnusedVibMeasLocByMeasDefId_TimeList(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.MeasDefinitionID);
                    List<DAUChannelV2> channelList = WTCMSLive.BusinessModel.DauManagement.GetDAUVibChannelList(waveDefinitionDTO.WindTurbineID);

                    // 修改其他波形定义
                    if (waveDefinitionDTO.isAll)
                    {
                        // 当前测量定义下的所有波形定义
                        var _waveDefs = MeasDefinitionManagement.GetWaveDefByMdfId_Time(waveDefinitionDTO.WindTurbineID, wdf.MeasDefinitionID);
                        if (_waveDefs.Count > 0)
                        {
                            foreach (var _curWave in _waveDefs)
                            {
                                evconf.AddRange(MakeTimeEvConfig(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.MeasDefinitionID, _curWave, waveDefinitionDTO.GeneralEVList, waveDefinitionDTO.FBEList));
                            }
                        }
                    }

                    // 遍历用户选择测量位置
                    for (int i = 0; i < measLocIdList.Count(); i++)
                    {
                        // 存在波形定义？
                        if (MeasDefinitionManagement.IsExsitsWaveDef(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.MeasDefinitionID, measLocIdList[i], EnumWaveFormType.WDF_Time) == false)
                        {
                            MeasLoc_Vib locVib = vibLocList.Find(item => item.MeasLocationID == measLocIdList[i]);
                            wdf.MeasLocationID = measLocIdList[i];
                            //存在对应的DAU通道?
                            DAUChannelV2 channel = channelList.Find(item => item.MeasLocVibID == measLocIdList[i]);
                            if (channel != null)
                            {
                                // 不存在波形定义并且有对应的通道，添加.
                                effectiveId = measLocIdList[i] + ",";
                                MeasDefinitionManagement.AddWaveDef_Time(wdf);

                                // 添加特征值
                                if (generalEvlist.Count > 0)
                                {
                                    foreach (var generalEv in generalEvlist)
                                    {
                                        var _curType = (EnumEigenvalueName)Convert.ToInt32(generalEv);
                                        var _name = EnumEigenvalueNameHelper.GetAlisa(_curType);
                                        if (_name == null)
                                        {
                                            _name = _curType.ToString().Replace("Enum_", "").ToUpper();
                                        }
                                        evconf.Add(new MeasDef_Ev_Vib()
                                        {
                                            WindTurbineID = waveDefinitionDTO.WindTurbineID,
                                            MeasDefinitionID = waveDefinitionDTO.MeasDefinitionID,
                                            MeasLocationID = measLocIdList[i],
                                            WaveDefinitionID = wdf.WaveDefinitionID,
                                            EvType = EnumEigenValueGroupType.GeneralEV,
                                            Type = _curType,
                                            Name = _name,
                                        });
                                    }
                                }

                                // 频域
                                if (fbeEvlist.Count > 0)
                                {
                                    foreach (var fbe in fbeEvlist)
                                    {
                                        List<string> _cur = fbe.Split('#').ToList();

                                        var _curType = EnumEigenvalueName.Enum_FBE;
                                        var _name = EnumEigenvalueNameHelper.GetAlisa(_curType);
                                        if (_name == null)
                                        {
                                            _name = _curType.ToString().Replace("Enum_", "").ToUpper();
                                        }

                                        evconf.Add(new MeasDef_Ev_Vib()
                                        {
                                            WindTurbineID = waveDefinitionDTO.WindTurbineID,
                                            MeasDefinitionID = waveDefinitionDTO.MeasDefinitionID,
                                            MeasLocationID = measLocIdList[i],
                                            WaveDefinitionID = wdf.WaveDefinitionID,
                                            EvType = EnumEigenValueGroupType.FBE,
                                            Type = _curType,
                                            Name = _name,
                                            UnderLimitValue = Convert.ToDouble(_cur[1]),
                                            UpperLimitValue = Convert.ToDouble(_cur[2]),
                                            UpperLimitUnit = "1",
                                            UnderLimitUnit = "1",
                                        });
                                    }
                                }

                            }
                            else
                            {
                                channelNames += MeasLocationNames[i] + ',';
                            }
                        }
                        else
                        {
                            // 存在，记录测量位置名称
                            locNames += MeasLocationNames[i] + "，";
                        }
                    }
                    // 测量位置已存在波形定义，提示。
                    if (locNames != "")
                    {
                        string strs = locNames.Substring(0, locNames.LastIndexOf("，"));
                        message = string.Format(message, 0, "振动测量位置（" + strs + "）已经存在时域波形定义。刷新查看添加结果。");
                        //return "{" + message + "}";
                        return Ok(ApiResponse<string>.Error(message));
                    }
                    if (!string.IsNullOrEmpty(channelNames))
                    {
                        string strs = channelNames.Substring(0, channelNames.LastIndexOf(","));
                        message = string.Format(message, 0, "振动测量位置（" + strs + "）没有和采集单元通道绑定。刷新查看添加结果。");
                        //return "{" + message + "}";
                        return Ok(ApiResponse<string>.Error(message));
                    }
                    if (locNames == "" && channelNames == "")
                    {
                        //如果是全部添加，则返回添加正常，前台自动刷新页面信息
                        // 如果是部分提交，那么前台需要手工刷新数据信息
                        message = string.Format(message, 1, "");
                    }
                }
                else
                {
                    // 缓存当前的波形定义
                    var curwave = MeasDefinitionManagement.GetWaveDefById_Time(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.WaveDefinitionID);
                    HttpContext.Items["OriginalWaveDefRecords"] = curwave;
                    // 是否应用当前测量一定下的全部
                    if (waveDefinitionDTO.isAll)
                    {
                        // 当前测量定义下的所有波形定义
                        var _waveDefs = MeasDefinitionManagement.GetWaveDefByMdfId_Time(waveDefinitionDTO.WindTurbineID, wdf.MeasDefinitionID);
                        if (_waveDefs.Count > 0)
                        {
                            foreach (var _curWave in _waveDefs)
                            {
                                evconf.AddRange(MakeTimeEvConfig(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.MeasDefinitionID, _curWave, waveDefinitionDTO.GeneralEVList, waveDefinitionDTO.FBEList));
                            }
                        }
                    }
                    else
                    {
                        evconf = MakeTimeEvConfig(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.MeasDefinitionID, wdf, waveDefinitionDTO.GeneralEVList, waveDefinitionDTO.FBEList);
                    }


                    effectiveId = wdf.MeasDefinitionID;
                    MeasDefinitionManagement.EditWaveDef_Time(wdf);
                    actionName = "修改";
                    message = string.Format(message, 1, "");

                }

                EigenValueManage.AddMdfTimedomainEvConf(evconf);

                #region
                if (!string.IsNullOrEmpty(effectiveId))
                {
                    LogEntity logEntity = new LogEntity();
                    logEntity.LogDB = ConstDefine.UserManagementLog;
                    logEntity.LogTime = DateTime.Now;
                    logEntity.NodeID = effectiveId.IndexOf(',') > -1 ? effectiveId.Substring(0, effectiveId.LastIndexOf(',')) : effectiveId;
                    logEntity.UserName = Request.Cookies["WindCMSUserName"];
                    logEntity.OperationDescription
                        = string.Format("{1}_测量定义({0})", logEntity.NodeID, actionName);
                    LogManagement.UserlogWrite(logEntity);
                }
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditWaveDefinition]" + actionName + "测量定义失败 机组ID:" + request.SourceData.WindTurbineID, ex);
                message = string.Format(message, 0, actionName + "测量定义失败 :" + ex.Message);
                return Ok(ApiResponse<string>.Error(message));
            }
            ///return "{" + message + "}";
            return Ok(ApiResponse<string>.Success(message));
        }

        /// <summary>
        /// 修改添加电流电压波形定义
        /// </summary>
        /// <param name="WaveDefId"></param>
        /// <param name="WaveDefName"></param>
        /// <param name="MeasDefinitionID"></param>
        /// <param name="WindTurbineID"></param>
        /// <param name="WindParkID"></param>
        /// <param name="measLocIds"></param>
        /// <param name="TimeWdfUpFreq"></param>
        /// <param name="TimeWdfSampleLength"></param>
        /// <param name="isAddType"></param>
        /// <param name="messLocNames"></param>
        /// <param name="dauid"></param>
        /// <param name="GeneralEVList"></param>
        /// <param name="FBEList"></param>
        /// <param name="isAll"></param>
        /// <returns></returns>
        public string EditWaveDefinitionVoltageCurrent(string WaveDefId, string WaveDefName, string MeasDefinitionID, string WindTurbineID, string WindParkID, string measLocIds, float TimeWdfUpFreq, float TimeWdfSampleLength, string isAddType, string messLocNames, string dauid, int signalType, string GeneralEVList = "", string FBEList = "", bool isAll = false)
        {
            string message = "state:{0},msg:'{1}'";
            string actionName = "添加";
            try
            {
                List<string> measLocIdList = new List<string>();
                if (isAddType == "1")
                {
                    if (!string.IsNullOrEmpty(measLocIds))
                        measLocIdList = measLocIds.Split(',').ToList();
                    else
                    {
                        message = string.Format(message, 0, "没有振动测量位置信息！");
                        return "{" + message + "}";
                    }
                }
                WaveDef_Time wdf = isAddType == "1" ? new WaveDef_Time() : MeasDefinitionManagement.GetWaveDefById_VoltageCurrent(WindTurbineID, WaveDefId);
                wdf.WaveDefinitionName = WaveDefName.Trim();
                wdf.UpperLimitFreqency = TimeWdfUpFreq;
                wdf.SampleLength = Convert.ToInt32(TimeWdfSampleLength * 1000);
                wdf.SignalType = (EnumSignalType)signalType;
                // 通带和频带特征值添加
                List<string> generalEvlist = new List<string>();
                if (!string.IsNullOrEmpty(GeneralEVList))
                {
                    generalEvlist = GeneralEVList.Split(',').ToList();
                }
                List<string> fbeEvlist = new List<string>();
                if (!string.IsNullOrEmpty(FBEList))
                {
                    fbeEvlist = FBEList.Split(',').ToList();
                }
                List<MeasDef_Ev_Vib> evconf = new List<MeasDef_Ev_Vib>();

                // 被添加的有效Id值，如果是添加操作，对应的是ID的串 ps: id是振动测量位置的ID
                string effectiveId = "";
                if (isAddType == "1")
                {
                    wdf.MeasDefinitionID = MeasDefinitionID;
                    wdf.WindTurbineID = WindTurbineID;
                    wdf.LowerLimitFreqency = 0;
                    wdf.WaveFormType = EnumWaveFormType.WDF_Time;

                    List<string> MeasLocationNames = messLocNames.Split(',').ToList();
                    string locNames = "";
                    string channelNames = string.Empty;
                    List<MeasLoc_VoltageCurrent> vibLocList = GetUnusedVibMeasLocByMeasDefId_VoltageCurrentList(WindTurbineID, MeasDefinitionID);
                    List<DAUChannel_VoltageCurrent> channelList = WTCMSLive.BusinessModel.DauManagement.GetDAUVoltageCurrentChannelList(WindTurbineID);

                    // 修改其他波形定义
                    if (isAll)
                    {
                        // 当前测量定义下的所有波形定义
                        var _waveDefs = MeasDefinitionManagement.GetWaveDefByMdfId_VoltageCurrent(WindTurbineID, wdf.MeasDefinitionID);
                        if (_waveDefs.Count > 0)
                        {
                            foreach (var _curWave in _waveDefs)
                            {
                                evconf.AddRange(MakeTimeEvConfig(WindTurbineID, MeasDefinitionID, _curWave, GeneralEVList, FBEList));
                            }
                        }
                    }

                    // 遍历用户选择测量位置
                    for (int i = 0; i < measLocIdList.Count(); i++)
                    {
                        // 存在波形定义？
                        if (MeasDefinitionManagement.IsExsitsWaveDef(WindTurbineID, MeasDefinitionID, measLocIdList[i], EnumWaveFormType.WDF_Time) == false)
                        {
                            MeasLoc_VoltageCurrent locVib = vibLocList.Find(item => item.MeasLocationID == measLocIdList[i]);
                            wdf.MeasLocationID = measLocIdList[i];
                            //存在对应的DAU通道?
                            DAUChannel_VoltageCurrent channel = channelList.Find(item => item.MeasLoc_ProcessId == measLocIdList[i]);
                            if (channel != null)
                            {
                                // 不存在波形定义并且有对应的通道，添加.
                                effectiveId = measLocIdList[i] + ",";
                                MeasDefinitionManagement.AddWaveDef_VoltageCurrent(wdf);

                                // 添加特征值
                                if (generalEvlist.Count > 0)
                                {
                                    foreach (var generalEv in generalEvlist)
                                    {
                                        var _curType = (EnumEigenvalueName)Convert.ToInt32(generalEv);
                                        var _name = EnumEigenvalueNameHelper.GetAlisa(_curType);
                                        if (_name == null)
                                        {
                                            _name = _curType.ToString().Replace("Enum_", "").ToUpper();
                                        }
                                        evconf.Add(new MeasDef_Ev_Vib()
                                        {
                                            WindTurbineID = WindTurbineID,
                                            MeasDefinitionID = MeasDefinitionID,
                                            MeasLocationID = measLocIdList[i],
                                            WaveDefinitionID = wdf.WaveDefinitionID,
                                            EvType = EnumEigenValueGroupType.GeneralEV,
                                            Type = _curType,
                                            Name = _name,
                                        });
                                    }
                                }

                                // 频域
                                if (fbeEvlist.Count > 0)
                                {
                                    foreach (var fbe in fbeEvlist)
                                    {
                                        List<string> _cur = fbe.Split('#').ToList();

                                        var _curType = EnumEigenvalueName.Enum_FBE;
                                        var _name = EnumEigenvalueNameHelper.GetAlisa(_curType);
                                        if (_name == null)
                                        {
                                            _name = _curType.ToString().Replace("Enum_", "").ToUpper();
                                        }

                                        evconf.Add(new MeasDef_Ev_Vib()
                                        {
                                            WindTurbineID = WindTurbineID,
                                            MeasDefinitionID = MeasDefinitionID,
                                            MeasLocationID = measLocIdList[i],
                                            WaveDefinitionID = wdf.WaveDefinitionID,
                                            EvType = EnumEigenValueGroupType.FBE,
                                            Type = _curType,
                                            Name = _name,
                                            UnderLimitValue = Convert.ToDouble(_cur[1]),
                                            UpperLimitValue = Convert.ToDouble(_cur[2]),
                                            UpperLimitUnit = "1",
                                            UnderLimitUnit = "1",
                                        });
                                    }

                                }

                            }
                            else
                            {
                                channelNames += MeasLocationNames[i] + ',';
                            }
                        }
                        else
                        {
                            // 存在，记录测量位置名称
                            locNames += MeasLocationNames[i] + "，";
                        }
                    }
                    // 测量位置已存在波形定义，提示。
                    if (locNames != "")
                    {
                        string strs = locNames.Substring(0, locNames.LastIndexOf("，"));
                        message = string.Format(message, 0, "电流电压测量位置（" + strs + "）已经存在时域波形定义。刷新查看添加结果。");
                        return "{" + message + "}";
                    }
                    if (!string.IsNullOrEmpty(channelNames))
                    {
                        string strs = channelNames.Substring(0, channelNames.LastIndexOf(","));
                        message = string.Format(message, 0, "电流电压测量位置（" + strs + "）没有和采集单元通道绑定。刷新查看添加结果。");
                        return "{" + message + "}";
                    }
                    if (locNames == "" && channelNames == "")
                    {
                        //如果是全部添加，则返回添加正常，前台自动刷新页面信息
                        // 如果是部分提交，那么前台需要手工刷新数据信息
                        message = string.Format(message, 1, "");
                    }
                }
                else
                {
                    // 是否应用当前测量一定下的全部
                    if (isAll)
                    {
                        // 当前测量定义下的所有波形定义
                        var _waveDefs = MeasDefinitionManagement.GetWaveDefByMdfId_VoltageCurrent(WindTurbineID, wdf.MeasDefinitionID);
                        if (_waveDefs.Count > 0)
                        {
                            foreach (var _curWave in _waveDefs)
                            {
                                evconf.AddRange(MakeTimeEvConfig(WindTurbineID, MeasDefinitionID, _curWave, GeneralEVList, FBEList));
                            }
                        }
                    }
                    else
                    {
                        evconf = MakeTimeEvConfig(WindTurbineID, MeasDefinitionID, wdf, GeneralEVList, FBEList);
                    }


                    effectiveId = wdf.MeasDefinitionID;
                    MeasDefinitionManagement.EditWaveDef_VoltageCurrent(wdf);
                    actionName = "修改";
                    message = string.Format(message, 1, "");
                }

                EigenValueManage.AddMdfTimedomainEvConf(evconf);

                // 提升测量定义id
                //DauManagement.UpdateMeasDefVersion(WindTurbineID, dauid);

                #region
                if (!string.IsNullOrEmpty(effectiveId))
                {
                    LogEntity logEntity = new LogEntity();
                    logEntity.LogDB = ConstDefine.UserManagementLog;
                    logEntity.LogTime = DateTime.Now;
                    logEntity.NodeID = effectiveId.IndexOf(',') > -1 ? effectiveId.Substring(0, effectiveId.LastIndexOf(',')) : effectiveId;
                    logEntity.UserName = Request.Cookies["WindCMSUserName"];
                    logEntity.OperationDescription
                        = string.Format("{1}_测量定义({0})", logEntity.NodeID, actionName);
                    LogManagement.UserlogWrite(logEntity);
                }
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditWaveDefinition]" + actionName + "测量定义失败 机组ID:" + WindTurbineID, ex);
                message = string.Format(message, 0, actionName + "测量定义失败 :" + ex.Message);
            }
            return "{" + message + "}";
        }

        [HttpPost("MakeWaveDefinitionVoltageCurrent")]
        [BatchOperation(nameof(BatchMakeWaveDefinitionVoltageCurrent))]
        public IActionResult MakeWaveDefinitionVoltageCurrent([FromBody] BatchMakeWaveDefinitionVoltageCurrentRequest request)
        {
            string message = "state:{0},msg:'{1}'";
            string actionName = "添加";
            try
            {
                // 参数验证
                if (request?.SourceData == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                var waveDefinitionDTO = request.SourceData;
                List<string> measLocIdList = new List<string>();
                if (waveDefinitionDTO.isAddType == "1")
                {
                    if (!string.IsNullOrEmpty(waveDefinitionDTO.measLocIds))
                        measLocIdList = waveDefinitionDTO.measLocIds.Split(',').ToList();
                    else
                    {
                        message = string.Format(message, 0, "没有振动测量位置信息！");
                        //return "{" + message + "}";
                        return Ok(ApiResponse<string>.Error(message));
                    }
                }
                WaveDef_Time wdf = waveDefinitionDTO.isAddType == "1" ? new WaveDef_Time() : MeasDefinitionManagement.GetWaveDefById_VoltageCurrent(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.WaveDefinitionID);
                wdf.WaveDefinitionName = waveDefinitionDTO.WaveDefinitionName.Trim();
                wdf.UpperLimitFreqency = waveDefinitionDTO.TimeWdfUpFreq;
                wdf.SampleLength = Convert.ToInt32(waveDefinitionDTO.TimeWdfSampleLength * 1000);
                wdf.SignalType = (EnumSignalType)waveDefinitionDTO.signalType;
                // 通带和频带特征值添加
                List<string> generalEvlist = new List<string>();
                if (!string.IsNullOrEmpty(waveDefinitionDTO.GeneralEVList))
                {
                    generalEvlist = waveDefinitionDTO.GeneralEVList.Split(',').ToList();
                }
                List<string> fbeEvlist = new List<string>();
                if (!string.IsNullOrEmpty(waveDefinitionDTO.FBEList))
                {
                    fbeEvlist = waveDefinitionDTO.FBEList.Split(',').ToList();
                }
                List<MeasDef_Ev_Vib> evconf = new List<MeasDef_Ev_Vib>();

                // 被添加的有效Id值，如果是添加操作，对应的是ID的串 ps: id是振动测量位置的ID
                string effectiveId = "";
                if (waveDefinitionDTO.isAddType == "1")
                {
                    wdf.MeasDefinitionID = waveDefinitionDTO.MeasDefinitionID;
                    wdf.WindTurbineID = waveDefinitionDTO.WindTurbineID;
                    wdf.LowerLimitFreqency = 0;
                    wdf.WaveFormType = EnumWaveFormType.WDF_Time;

                    List<string> MeasLocationNames = waveDefinitionDTO.messLocNames.Split(',').ToList();
                    string locNames = "";
                    string channelNames = string.Empty;
                    List<MeasLoc_VoltageCurrent> vibLocList = GetUnusedVibMeasLocByMeasDefId_VoltageCurrentList(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.MeasDefinitionID);
                    List<DAUChannel_VoltageCurrent> channelList = WTCMSLive.BusinessModel.DauManagement.GetDAUVoltageCurrentChannelList(waveDefinitionDTO.WindTurbineID);

                    // 修改其他波形定义
                    if (waveDefinitionDTO.isAll)
                    {
                        // 当前测量定义下的所有波形定义
                        var _waveDefs = MeasDefinitionManagement.GetWaveDefByMdfId_VoltageCurrent(waveDefinitionDTO.WindTurbineID, wdf.MeasDefinitionID);
                        if (_waveDefs.Count > 0)
                        {
                            foreach (var _curWave in _waveDefs)
                            {
                                evconf.AddRange(MakeTimeEvConfig(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.MeasDefinitionID, _curWave, waveDefinitionDTO.GeneralEVList, waveDefinitionDTO.FBEList));
                            }
                        }
                    }

                    // 遍历用户选择测量位置
                    for (int i = 0; i < measLocIdList.Count(); i++)
                    {
                        // 存在波形定义？
                        if (MeasDefinitionManagement.IsExsitsWaveDef(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.MeasDefinitionID, measLocIdList[i], EnumWaveFormType.WDF_Time) == false)
                        {
                            MeasLoc_VoltageCurrent locVib = vibLocList.Find(item => item.MeasLocationID == measLocIdList[i]);
                            wdf.MeasLocationID = measLocIdList[i];
                            //存在对应的DAU通道?
                            DAUChannel_VoltageCurrent channel = channelList.Find(item => item.MeasLoc_ProcessId == measLocIdList[i]);
                            if (channel != null)
                            {
                                // 不存在波形定义并且有对应的通道，添加.
                                effectiveId = measLocIdList[i] + ",";
                                MeasDefinitionManagement.AddWaveDef_VoltageCurrent(wdf);

                                // 添加特征值
                                if (generalEvlist.Count > 0)
                                {
                                    foreach (var generalEv in generalEvlist)
                                    {
                                        var _curType = (EnumEigenvalueName)Convert.ToInt32(generalEv);
                                        var _name = EnumEigenvalueNameHelper.GetAlisa(_curType);
                                        if (_name == null)
                                        {
                                            _name = _curType.ToString().Replace("Enum_", "").ToUpper();
                                        }
                                        evconf.Add(new MeasDef_Ev_Vib()
                                        {
                                            WindTurbineID = waveDefinitionDTO.WindTurbineID,
                                            MeasDefinitionID = waveDefinitionDTO.MeasDefinitionID,
                                            MeasLocationID = measLocIdList[i],
                                            WaveDefinitionID = wdf.WaveDefinitionID,
                                            EvType = EnumEigenValueGroupType.GeneralEV,
                                            Type = _curType,
                                            Name = _name,
                                        });
                                    }
                                }

                                // 频域
                                if (fbeEvlist.Count > 0)
                                {
                                    foreach (var fbe in fbeEvlist)
                                    {
                                        List<string> _cur = fbe.Split('#').ToList();

                                        var _curType = EnumEigenvalueName.Enum_FBE;
                                        var _name = EnumEigenvalueNameHelper.GetAlisa(_curType);
                                        if (_name == null)
                                        {
                                            _name = _curType.ToString().Replace("Enum_", "").ToUpper();
                                        }

                                        evconf.Add(new MeasDef_Ev_Vib()
                                        {
                                            WindTurbineID = waveDefinitionDTO.WindTurbineID,
                                            MeasDefinitionID = waveDefinitionDTO.MeasDefinitionID,
                                            MeasLocationID = measLocIdList[i],
                                            WaveDefinitionID = wdf.WaveDefinitionID,
                                            EvType = EnumEigenValueGroupType.FBE,
                                            Type = _curType,
                                            Name = _name,
                                            UnderLimitValue = Convert.ToDouble(_cur[1]),
                                            UpperLimitValue = Convert.ToDouble(_cur[2]),
                                            UpperLimitUnit = "1",
                                            UnderLimitUnit = "1",
                                        });
                                    }

                                }

                            }
                            else
                            {
                                channelNames += MeasLocationNames[i] + ',';
                            }
                        }
                        else
                        {
                            // 存在，记录测量位置名称
                            locNames += MeasLocationNames[i] + "，";
                        }
                    }
                    // 测量位置已存在波形定义，提示。
                    if (locNames != "")
                    {
                        string strs = locNames.Substring(0, locNames.LastIndexOf("，"));
                        message = string.Format(message, 0, "电流电压测量位置（" + strs + "）已经存在时域波形定义。刷新查看添加结果。");
                        //return "{" + message + "}";
                        return Ok(ApiResponse<string>.Error(message));
                    }
                    if (!string.IsNullOrEmpty(channelNames))
                    {
                        string strs = channelNames.Substring(0, channelNames.LastIndexOf(","));
                        message = string.Format(message, 0, "电流电压测量位置（" + strs + "）没有和采集单元通道绑定。刷新查看添加结果。");
                        //return "{" + message + "}";
                        return Ok(ApiResponse<string>.Error(message));
                    }
                    if (locNames == "" && channelNames == "")
                    {
                        //如果是全部添加，则返回添加正常，前台自动刷新页面信息
                        // 如果是部分提交，那么前台需要手工刷新数据信息
                        message = string.Format(message, 1, "");
                    }
                }
                else
                {
                    // 缓存当前的波形定义
                    var curwave = MeasDefinitionManagement.GetWaveDefById_VoltageCurrent(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.WaveDefinitionID);
                    HttpContext.Items["OriginalVCWaveDefRecords"] = curwave;
                    // 是否应用当前测量一定下的全部
                    if (waveDefinitionDTO.isAll)
                    {
                        // 当前测量定义下的所有波形定义
                        var _waveDefs = MeasDefinitionManagement.GetWaveDefByMdfId_VoltageCurrent(waveDefinitionDTO.WindTurbineID, wdf.MeasDefinitionID);
                        if (_waveDefs.Count > 0)
                        {
                            foreach (var _curWave in _waveDefs)
                            {
                                evconf.AddRange(MakeTimeEvConfig(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.MeasDefinitionID, _curWave, waveDefinitionDTO.GeneralEVList, waveDefinitionDTO.FBEList));
                            }
                        }
                    }
                    else
                    {
                        evconf = MakeTimeEvConfig(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.MeasDefinitionID, wdf, waveDefinitionDTO.GeneralEVList, waveDefinitionDTO.FBEList);
                    }


                    effectiveId = wdf.MeasDefinitionID;
                    MeasDefinitionManagement.EditWaveDef_VoltageCurrent(wdf);
                    actionName = "修改";
                    message = string.Format(message, 1, "");
                }

                EigenValueManage.AddMdfTimedomainEvConf(evconf);

                // 提升测量定义id
                //DauManagement.UpdateMeasDefVersion(WindTurbineID, dauid);

                #region
                if (!string.IsNullOrEmpty(effectiveId))
                {
                    LogEntity logEntity = new LogEntity();
                    logEntity.LogDB = ConstDefine.UserManagementLog;
                    logEntity.LogTime = DateTime.Now;
                    logEntity.NodeID = effectiveId.IndexOf(',') > -1 ? effectiveId.Substring(0, effectiveId.LastIndexOf(',')) : effectiveId;
                    logEntity.UserName = Request.Cookies["WindCMSUserName"];
                    logEntity.OperationDescription
                        = string.Format("{1}_测量定义({0})", logEntity.NodeID, actionName);
                    LogManagement.UserlogWrite(logEntity);
                }
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditWaveDefinition]" + actionName + "测量定义失败 机组ID:" + request.SourceData.WindTurbineID, ex);
                message = string.Format(message, 0, actionName + "测量定义失败 :" + ex.Message);
                return Ok(ApiResponse<string>.Error(message));
            }
            //return "{" + message + "}";
            return Ok(ApiResponse<string>.Success(message));
        }


        private List<MeasDef_Ev_Vib> MakeTimeEvConfig(string WindTurbineID, string MeasDefinitionID, WaveDef_Time wdf, string GeneralEVList, string FBEList)
        {
            List<MeasDef_Ev_Vib> evconf = new List<MeasDef_Ev_Vib>();

            List<string> generalEvlist = new List<string>();
            if (!string.IsNullOrEmpty(GeneralEVList))
            {
                generalEvlist = GeneralEVList.Split(',').ToList();
            }
            List<string> fbeEvlist = new List<string>();
            if (!string.IsNullOrEmpty(FBEList))
            {
                fbeEvlist = FBEList.Split(',').ToList();
            }


            // 删除特征值配置
            EigenValueManage.DelMdfTimedomainEvConf(WindTurbineID, wdf.WaveDefinitionID, MeasDefinitionID);
            // 添加特征值
            if (generalEvlist.Count > 0)
            {
                foreach (var generalEv in generalEvlist)
                {
                    var _curType = (EnumEigenvalueName)Convert.ToInt32(generalEv);
                    var _name = EnumEigenvalueNameHelper.GetAlisa(_curType);
                    if (_name == null)
                    {
                        _name = _curType.ToString().Replace("Enum_", "").ToUpper();
                    }
                    evconf.Add(new MeasDef_Ev_Vib()
                    {
                        WindTurbineID = WindTurbineID,
                        MeasDefinitionID = MeasDefinitionID,
                        MeasLocationID = wdf.MeasLocationID,
                        WaveDefinitionID = wdf.WaveDefinitionID,
                        EvType = EnumEigenValueGroupType.GeneralEV,
                        Type = _curType,
                        Name = _name,
                    });
                }
            }

            // 频域
            if (fbeEvlist.Count > 0)
            {
                foreach (var fbe in fbeEvlist)
                {
                    List<string> _cur = fbe.Split('#').ToList();

                    var _curType = EnumEigenvalueName.Enum_FBE;
                    var _name = EnumEigenvalueNameHelper.GetAlisa(_curType);
                    if (_name == null)
                    {
                        _name = _curType.ToString().Replace("Enum_", "").ToUpper();
                    }

                    evconf.Add(new MeasDef_Ev_Vib()
                    {
                        WindTurbineID = WindTurbineID,
                        MeasDefinitionID = MeasDefinitionID,
                        MeasLocationID = wdf.MeasLocationID,
                        WaveDefinitionID = wdf.WaveDefinitionID,
                        EvType = EnumEigenValueGroupType.FBE,
                        Type = _curType,
                        Name = _name,
                        UnderLimitValue = Convert.ToDouble(_cur[1]),
                        UpperLimitValue = Convert.ToDouble(_cur[2]),
                        UpperLimitUnit = "1",
                        UnderLimitUnit = "1",
                    });
                }

            }

            return evconf;
        }

        /// <summary>
        /// 验证添加时域波形定义时的数目问题
        /// </summary>
        /// <returns></returns>
        private bool CheckAddTimeWdf(string WindTurbineID, string MeasDefinitionID, int num, out string message)
        {
            message = "";
            //取消波形定义条目限制
            return true;
            //List<WaveDef_Time> list = MeasDefinitionManagement.GetWaveDefByMdfId_Time(WindTurbineID, MeasDefinitionID);
            //bool outPowerFromDAU = false;
            //// 判断功率采集是否为WindDAU
            //// 查找该测量定义下的工况测量位置
            //List<MeasLoc_Process> listProMeasLoc = MeasDefinitionManagement.GetWorkCondLocListByMeasDefId(WindTurbineID, MeasDefinitionID);
            //foreach (MeasLoc_Process item in listProMeasLoc)
            //{
            //    if (item.Param_Type_Code == WorkCondition_ParamType.OUTPOWER.Param_Type_Code && item.FieldBusType == EnumWorkConDataSource.WindDAU)
            //    {
            //        outPowerFromDAU = true;
            //        break;
            //    }
            //}
            //if (outPowerFromDAU == true)
            //{
            //    if (num > 9)
            //    {
            //        message = "波形数量最多9条！";
            //        return false;
            //    }
            //}
            //else
            //{
            //    if (num > 10)
            //    {
            //        message = "波形数量最多10条！";
            //        return false;
            //    }
            //}
            //return true;
        }

        /// <summary>
        /// 删除时域波形定义信息
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="WaveId"></param>
        /// <returns></returns>
        /// 
        [HttpPost("DeleteWaveChannel")]
        [BatchOperation(nameof(BatchDeleteWaveChannel))]
        public IActionResult DeleteWaveChannel([FromBody] BatchDeleteWaveChannelRequest request)
        {
            string message = "state:{0},msg:'{1}'";
            try
            {
                // 参数验证
                if (request?.SourceData == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                var deleteData = request.SourceData;
                string WindTurbineID = deleteData.WindTurbineID;
                string measDefId = deleteData.MeasDefId;
                string WaveId = deleteData.WaveId;

                //支持多波形删除
                List<string> wave = WaveId.Split(",").ToList();
                List<WaveDef_Time> Original = new();
                foreach (var waveItem in wave)
                {
                    WaveDef_Time _waveDef = WTCMSLive.BusinessModel.MeasDefinitionManagement.GetWaveDefById_Time(WindTurbineID, waveItem);
                    // 缓存用于批量
                    Original.Add(_waveDef);
                    if (_waveDef != null)
                    {
                        MeasDefinitionManagement.DeleteWaveDef(WindTurbineID, waveItem, measDefId);

                        // 删除特征值配置
                        EigenValueManage.DelMdfTimedomainEvConf(WindTurbineID, waveItem, measDefId);

                        // 提升测量定义id
                        //DauManagement.UpdateMeasDefVersion(WindTurbineID, dauid);
                        #region
                        LogEntity logEntity = new LogEntity();
                        logEntity.LogDB = ConstDefine.UserManagementLog;
                        logEntity.LogTime = DateTime.Now;
                        logEntity.NodeID = WaveId;
                        logEntity.UserName = Request.Cookies["WindCMSUserName"];
                        logEntity.OperationDescription
                            = string.Format("删除_时域波形定义列表({0})", WaveId);
                        LogManagement.UserlogWrite(logEntity);
                        #endregion
                    }
                }
                HttpContext.Items["OriginalWaveDefDefRecords"] = Original;
                message = string.Format(message, 1, "");
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteWaveChannel]删除时域波形定义列表失败 机组ID:" + request.SourceData.WindTurbineID + " 波形ID：" + request.SourceData.WaveId, ex);
                message = string.Format(message, 0, "删除时域波形定义列表失败 :" + ex.Message);
                return Ok(ApiResponse<string>.Error(message));
            }
            return Ok(ApiResponse<string>.Success(message));
        }

        /// <summary>
        /// 删除电流电压波形定义
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="measDefId"></param>
        /// <param name="WaveId"></param>
        /// <param name="dauid"></param>
        /// <returns></returns>
        /// 
        [HttpPost("DeleteWaveChannelVoltageCurren")]
        [BatchOperation(nameof(BatchDeleteWaveChannelVoltageCurrent))]
        public IActionResult DeleteWaveChannelVoltageCurren([FromBody] BatchDeleteWaveChannelVoltageCurrentRequest request)
        {
            string message = "state:{0},msg:'{1}'";
            try
            {
                // 参数验证
                if (request?.SourceData == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                var deleteData = request.SourceData;
                string WindTurbineID = deleteData.WindTurbineID;
                string measDefId = deleteData.MeasDefId;
                string WaveId = deleteData.WaveId;

                //支持多波形删除
                List<string> wave = WaveId.Split(",").ToList();
                List<WaveDef_Time> Original = new();
                foreach (var waveItem in wave)
                {
                    WaveDef_Time _waveDef
                = WTCMSLive.BusinessModel.MeasDefinitionManagement.GetWaveDefById_VoltageCurrent(WindTurbineID, waveItem);
                    Original.Add(_waveDef);
                    if (_waveDef != null)
                    {
                        MeasDefinitionManagement.DeleteWaveDefVoltageCurrent(WindTurbineID, waveItem, measDefId);

                        // 删除特征值配置
                        EigenValueManage.DelMdfTimedomainEvConf(WindTurbineID, waveItem, measDefId);

                        // 提升测量定义id
                        //DauManagement.UpdateMeasDefVersion(WindTurbineID, dauid);
                        #region
                        LogEntity logEntity = new LogEntity();
                        logEntity.LogDB = ConstDefine.UserManagementLog;
                        logEntity.LogTime = DateTime.Now;
                        logEntity.NodeID = WaveId;
                        logEntity.UserName = Request.Cookies["WindCMSUserName"];
                        logEntity.OperationDescription
                            = string.Format("删除_电流电压波形定义列表({0})", WaveId);
                        LogManagement.UserlogWrite(logEntity);
                        #endregion
                    }
                }
                HttpContext.Items["OriginalVCWaveDefDefRecords"] = Original;
                message = string.Format(message, 1, "");
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteWaveChannel]删除时域波形定义列表失败 机组ID:" + request.SourceData.WindTurbineID + " 波形ID：" + request.SourceData.WaveId, ex);
                message = string.Format(message, 0, "删除时域波形定义列表失败 :" + ex.Message);
                return Ok(ApiResponse<string>.Error(message));
            }
            return Ok(ApiResponse<string>.Success("OK"));
        }


        #region 高频包络波形定义

        /// <summary>
        /// 包络滤波器
        /// </summary>
        /// <returns></returns>
        /// 
        [HttpGet("ShowEnvelopeFilterList")]
        public IActionResult ShowEnvelopeFilterList()
        {
            return Ok(WebCommonRefData.GetEnvelopeFilterList());
        }

        /// <summary>
        /// 获取包络下未添加波形定义的测量位置
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="MeasDefinitionID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetParameUnusedVibMeasLoc")]
        public IActionResult GetParameUnusedVibMeasLocByMeasDefId_Time(string turbineID, string MeasDefinitionID, string DAUID)
        {
            // 机组下的全部测量位置
            List<MeasLoc_Vib> vibLoc = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
            //List<DAUChannelV2> List = DauManagement.GetDAUVibChannelList(turbineID);
            List<DAUChannelV2> List = DAUSManageModel.GetDAUVibChannelList(turbineID, DAUID);
            //删除没绑定DAU通道的振动测量位置
            vibLoc.RemoveAll(item =>
            {
                return (List.Find(wave => wave.MeasLocVibID == item.MeasLocationID && wave.DauID == DAUID) == null);
            });
            // 获取波形定义
            //List<WaveDefinition> timeWave = MeasDefinitionManagement.GetWaveDefByTurId(turbineID);
            //timeWave = timeWave.FindAll(item => item.MeasDefinitionID == MeasDefinitionID && item.WaveFormType == EnumWaveFormType.WDF_Envelope );
            //vibLoc.RemoveAll(item =>
            //{
            //    return (timeWave.Find(wave => wave.MeasLocationID == item.MeasLocationID) != null);
            //});
            if (vibLoc.Count > 0)
                vibLoc = vibLoc.OrderBy(item => item.OrderSeq).ToList();
            return Ok(vibLoc);
        }

        /// <summary>
        /// 获取机组ID获取高频包络波形定义
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <returns></returns>
        /// 
        [HttpGet("GetTimeParamEnvDefList")]
        public IActionResult GetTimeParamEnvDefListByTurbId(string turbineID, string measDefinitionID)
        {
            //List<MeasDefinition> Definlist = MeasDefinitionManagement.GetMeasDefListByTurId(turbineID);
            List<MeasLoc_Vib> LocVibList = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
            List<MeasDefinition> listDefin = new List<MeasDefinition>();
            List<WaveDef_Envlope> list = new List<WaveDef_Envlope>();
            List<WaveDef_Envlope> EnvlopeList = MeasDefinitionManagement.GetWaveDefByTruId_Envlope(turbineID);
            EnvlopeList.FindAll(item => item.MeasDefinitionID == measDefinitionID).ForEach(item =>
            {
                //WaveDefParamID 里面存储的SampleLength，因为SampleLength不是float型
                //wangy 2016年2月17日 11:33:32
                item.WaveDefParamID = Math.Round(Convert.ToDouble(item.SampleLength) / 1000, 3) + "";
                item.MeasLocationID = LocVibList.Find(vib => vib.MeasLocationID == item.MeasLocationID).MeasLocName;

                list.Add(item);
            });
            return Ok(list);
        }

        /// <summary>
        /// 添加高频包络
        /// </summary>
        /// <param name="WaveDefId"></param>
        /// <param name="WaveDefName"></param>
        /// <param name="MeasDefinitionID"></param>
        /// <param name="WindTurbineID"></param>
        /// <param name="WindParkID"></param>
        /// <param name="measLocIds"></param>
        /// <param name="CycleNumber"></param>
        /// <param name="EnvBandWidth"></param>
        /// <param name="EnvFiterFreq"></param>
        /// <param name="isAddType"></param>
        /// <param name="messLocNames"></param>
        /// <returns></returns>
        public string EditParamEnvDefinition(string WaveDefId, string WaveDefName, string MeasDefinitionID, string WindTurbineID, string WindParkID, string measLocIds, float SampleLength, int EnvBandWidth, float EnvFiterFreq, string isAddType, string messLocNames)
        {
            string message = "state:{0},msg:'{1}'";
            string actionName = "添加";
            try
            {
                List<string> measLocIdList = new List<string>();
                if (isAddType == "1")
                {
                    if (!string.IsNullOrEmpty(measLocIds))
                        measLocIdList = measLocIds.Split(',').ToList();
                    else
                    {
                        message = string.Format(message, 0, "没有振动测量位置信息！");
                        return "{" + message + "}";
                    }
                }
                WaveDef_Envlope wdf = isAddType == "1" ? new WaveDef_Envlope() : MeasDefinitionManagement.GetWaveDefById_Envlope(WindTurbineID, WaveDefId);
                wdf.WaveDefinitionName = WaveDefName.Trim();
                wdf.SampleLength = Convert.ToInt32(SampleLength * 1000);
                wdf.EnvBandWidth = EnvBandWidth;
                wdf.EnvFiterFreq = EnvFiterFreq;
                wdf.WaveFormType = EnumWaveFormType.WDF_Envelope;
                // 被添加的有效Id值，如果是添加操作，对应的是ID的串 ps: id是振动测量位置的ID
                string effectiveId = "";
                if (isAddType == "1")
                {
                    string ErrorMsg = "";
                    if (CheckAddTimeWdf(WindTurbineID, MeasDefinitionID, measLocIdList.Count(), out ErrorMsg) == false)
                    {
                        message = string.Format(message, 0, ErrorMsg);
                        return "{" + message + "}";
                    }
                    wdf.MeasDefinitionID = MeasDefinitionID;
                    wdf.WindTurbineID = WindTurbineID;
                    List<string> MeasLocationNames = messLocNames.Split(',').ToList();
                    string locNames = "";
                    string channelNames = string.Empty;
                    List<MeasLoc_Vib> vibLocList = GetParameUnusedVibMeasLocByMeasDefId_TimeList(WindTurbineID, MeasDefinitionID);
                    List<DAUChannelV2> channelList = WTCMSLive.BusinessModel.DauManagement.GetDAUVibChannelList(WindTurbineID);
                    // 遍历用户选择测量位置
                    for (int i = 0; i < measLocIdList.Count(); i++)
                    {
                        // 存在波形定义？
                        if (MeasDefinitionManagement.IsExsitsWaveDef(WindTurbineID, MeasDefinitionID, measLocIdList[i], EnumWaveFormType.WDF_Envelope) == false)
                        {
                            MeasLoc_Vib locVib = vibLocList.Find(item => item.MeasLocationID == measLocIdList[i]);
                            //if (locVib != null)
                            //{
                            wdf.MeasLocationID = measLocIdList[i];
                            effectiveId = measLocIdList[i] + ",";
                            MeasDefinitionManagement.AddWaveDef_Envelope(wdf);
                            //}
                        }
                        else
                        {
                            // 存在，记录测量位置名称
                            locNames += MeasLocationNames[i] + "，";
                        }
                    }
                    // 测量位置已存在波形定义，提示。
                    if (locNames != "" && locNames.IndexOf("，") > -1)
                    {
                        string strs = locNames.Substring(0, locNames.LastIndexOf("，"));
                        message = string.Format(message, 0, "振动测量位置（" + strs + "）已经存在阶次包络波形定义。刷新查看添加结果。");
                        //return "{" + message + "}";
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(effectiveId))
                        {
                            message = string.Format(message, 1, "");
                        }
                    }
                }
                else
                {
                    effectiveId = wdf.MeasDefinitionID;
                    MeasDefinitionManagement.EditWaveDef_Envlope(wdf);
                    actionName = "修改";
                    message = string.Format(message, 1, "");
                }
                try
                {
                    #region 添加日志
                    if (!string.IsNullOrEmpty(effectiveId))
                    {
                        LogEntity logEntity = new LogEntity();
                        logEntity.LogDB = ConstDefine.UserManagementLog;
                        logEntity.LogTime = DateTime.Now;
                        logEntity.NodeID = effectiveId.IndexOf(',') > -1 ? effectiveId.Substring(0, effectiveId.LastIndexOf(',')) : effectiveId;
                        logEntity.UserName = Request.Cookies["WindCMSUserName"];
                        logEntity.OperationDescription
                            = string.Format("{1}_高频包络波形定义({0})", effectiveId.IndexOf(',') > -1 ? effectiveId.Substring(0, effectiveId.LastIndexOf(',')) : effectiveId, actionName);
                        LogManagement.UserlogWrite(logEntity);
                    }
                    #endregion
                }
                catch
                {

                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditParamEnvDefinition]" + actionName + "高频包络波形定义失败 机组ID:" + WindTurbineID, ex);
                message = string.Format(message, 0, actionName + "高频包络波形定义失败 :" + ex.Message);
            }
            return "{" + message + "}";
        }

        /// <summary>
        /// 添加高频包络波形定义
        /// </summary>
        /// <param name="waveDefinitionDTO"></param>
        /// <returns></returns>
        [HttpPost("MakeParamEnvDefinition")]
        [BatchOperation(nameof(BatchMakeParamEnvDefinition))]
        public IActionResult MakeParamEnvDefinition([FromBody] BatchMakeParamEnvDefinitionRequest request)
        {
            string message = "state:{0},msg:'{1}'";
            string actionName = "添加";
            try
            {
                // 参数验证
                if (request?.SourceData == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                var waveDefinitionDTO = request.SourceData;
                List<string> measLocIdList = new List<string>();
                if (waveDefinitionDTO.isAddType == "1")
                {
                    if (!string.IsNullOrEmpty(waveDefinitionDTO.measLocIds))
                        measLocIdList = waveDefinitionDTO.measLocIds.Split(',').ToList();
                    else
                    {
                        message = string.Format(message, 0, "没有振动测量位置信息！");
                        //return "{" + message + "}";
                        return Ok(ApiResponse<string>.Error(message));
                    }
                }
                WaveDef_Envlope wdf = waveDefinitionDTO.isAddType == "1" ? new WaveDef_Envlope() : MeasDefinitionManagement.GetWaveDefById_Envlope(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.WaveDefinitionID);

                // 缓存当前的波形定义
                HttpContext.Items["OriginalEnvWaveDefRecords"] = wdf;

                wdf.WaveDefinitionName = waveDefinitionDTO.WaveDefinitionName.Trim();
                wdf.SampleLength = Convert.ToInt32(waveDefinitionDTO.SampleLength * 1000);
                wdf.EnvBandWidth = waveDefinitionDTO.EnvBandWidth;
                wdf.EnvFiterFreq = waveDefinitionDTO.EnvFiterFreq;
                wdf.WaveFormType = EnumWaveFormType.WDF_Envelope;
                // 被添加的有效Id值，如果是添加操作，对应的是ID的串 ps: id是振动测量位置的ID
                string effectiveId = "";
                if (waveDefinitionDTO.isAddType == "1")
                {
                    string ErrorMsg = "";
                    if (CheckAddTimeWdf(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.MeasDefinitionID, measLocIdList.Count(), out ErrorMsg) == false)
                    {
                        message = string.Format(message, 0, ErrorMsg);
                        //return "{" + message + "}";
                        return Ok(ApiResponse<string>.Error(message));
                    }
                    wdf.MeasDefinitionID = waveDefinitionDTO.MeasDefinitionID;
                    wdf.WindTurbineID = waveDefinitionDTO.WindTurbineID;
                    List<string> MeasLocationNames = waveDefinitionDTO.messLocNames.Split(',').ToList();
                    string locNames = "";
                    string channelNames = string.Empty;
                    List<MeasLoc_Vib> vibLocList = GetParameUnusedVibMeasLocByMeasDefId_TimeList(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.MeasDefinitionID);
                    List<DAUChannelV2> channelList = WTCMSLive.BusinessModel.DauManagement.GetDAUVibChannelList(waveDefinitionDTO.WindTurbineID);
                    // 遍历用户选择测量位置
                    for (int i = 0; i < measLocIdList.Count(); i++)
                    {
                        // 存在波形定义？
                        if (MeasDefinitionManagement.IsExsitsWaveDef(waveDefinitionDTO.WindTurbineID, waveDefinitionDTO.MeasDefinitionID, measLocIdList[i], EnumWaveFormType.WDF_Envelope) == false)
                        {
                            MeasLoc_Vib locVib = vibLocList.Find(item => item.MeasLocationID == measLocIdList[i]);
                            //if (locVib != null)
                            //{
                            wdf.MeasLocationID = measLocIdList[i];
                            effectiveId = measLocIdList[i] + ",";
                            MeasDefinitionManagement.AddWaveDef_Envelope(wdf);
                            //}
                        }
                        else
                        {
                            // 存在，记录测量位置名称
                            locNames += MeasLocationNames[i] + "，";
                        }
                    }
                    // 测量位置已存在波形定义，提示。
                    if (locNames != "" && locNames.IndexOf("，") > -1)
                    {
                        string strs = locNames.Substring(0, locNames.LastIndexOf("，"));
                        message = string.Format(message, 0, "振动测量位置（" + strs + "）已经存在阶次包络波形定义。刷新查看添加结果。");
                        //return "{" + message + "}";
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(effectiveId))
                        {
                            message = string.Format(message, 1, "");
                        }
                    }
                }
                else
                {
                    effectiveId = wdf.MeasDefinitionID;
                    MeasDefinitionManagement.EditWaveDef_Envlope(wdf);
                    actionName = "修改";
                    message = string.Format(message, 1, "");
                }
                try
                {
                    #region 添加日志
                    if (!string.IsNullOrEmpty(effectiveId))
                    {
                        LogEntity logEntity = new LogEntity();
                        logEntity.LogDB = ConstDefine.UserManagementLog;
                        logEntity.LogTime = DateTime.Now;
                        logEntity.NodeID = effectiveId.IndexOf(',') > -1 ? effectiveId.Substring(0, effectiveId.LastIndexOf(',')) : effectiveId;
                        logEntity.UserName = Request.Cookies["WindCMSUserName"];
                        logEntity.OperationDescription
                            = string.Format("{1}_高频包络波形定义({0})", effectiveId.IndexOf(',') > -1 ? effectiveId.Substring(0, effectiveId.LastIndexOf(',')) : effectiveId, actionName);
                        LogManagement.UserlogWrite(logEntity);
                    }
                    #endregion
                }
                catch
                {

                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditParamEnvDefinition]" + actionName + "高频包络波形定义失败 机组ID:" + request.SourceData.WindTurbineID, ex);
                message = string.Format(message, 0, actionName + "高频包络波形定义失败 :" + ex.Message);
                return Ok(ApiResponse<string>.Error(message));
            }
            //return "{" + message + "}";
            return Ok(ApiResponse<string>.Success("OK"));
        }
        /// <summary>
        /// 删除高频包络
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="WaveId"></param>
        /// <returns></returns>
        /// 
        [HttpPost("DeleteParamEnvChannel")]
        [BatchOperation(nameof(BatchDeleteParamEnvChannel))]
        public IActionResult DeleteParamEnvChannel([FromBody] BatchDeleteParamEnvChannelRequest request)
        {
            string message = "state:{0},msg:'{1}'";
            try
            {
                // 参数验证
                if (request?.SourceData == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                var deleteData = request.SourceData;
                string WindTurbineID = deleteData.WindTurbineID;
                string WaveId = deleteData.WaveId;

                List<string> wave = WaveId.Split(",").ToList();
                List<WaveDef_Envlope> Original = new();
                foreach (var waveItem in wave)
                {
                    //高频包络实体
                    //WDFParamEnvlope
                    WaveDef_Envlope paramEnvlope = MeasDefinitionManagement.GetWaveDefById_Envlope(WindTurbineID, waveItem);
                    if (paramEnvlope != null)
                    {
                        Original.Add(paramEnvlope);
                        WTCMSLive.BusinessModel.MeasDefinitionManagement.DeleteWaveDef(WindTurbineID, waveItem, paramEnvlope.MeasDefinitionID);
                        #region
                        LogEntity logEntity = new LogEntity();
                        logEntity.LogDB = ConstDefine.UserManagementLog;
                        logEntity.LogTime = DateTime.Now;
                        logEntity.NodeID = waveItem;
                        logEntity.UserName = Request.Cookies["WindCMSUserName"];
                        logEntity.OperationDescription
                            = string.Format("删除_时域波形定义列表({0})", waveItem);
                        LogManagement.UserlogWrite(logEntity);
                        #endregion
                    }
                }
                HttpContext.Items["OriginalEnvWaveDefDelRecords"] = Original;
                message = string.Format(message, 1, "");
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteParamEnvChannel]删除时域波形定义列表失败 波形ID:" + request.SourceData.WaveId, ex);
                message = string.Format(message, 0, "删除时域波形定义列表失败 :" + ex.Message);
                return Ok(ApiResponse<string>.Error(message));
            }
            //return "{" + message + "}";
            return Ok(ApiResponse<string>.Success(message));
        }

        #endregion

        #region  晃度波形定义列表
        /// <summary>
        /// 返回测量定义的列表信息（数据精简版）
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        public string GetMeasDefinitionList(string turbineID)
        {
            List<MeasDefinition> Definlist = MeasDefinitionManagement.GetMeasDefListByTurId(turbineID);
            //返回到客户端的测量定义列表信息，上面的信息太多，没实际意义 wangyan
            List<MeasDefinition> list = new List<MeasDefinition>();
            Definlist.ForEach(item =>
            {
                list.Add(new MeasDefinition()
                {
                    MeasDefinitionID = item.MeasDefinitionID,
                    MeasDefinitionName = item.MeasDefinitionName
                });
            });
            return list.ToJson();
        }

        /// <summary>
        /// 根据机组获取晃度波形定义列表
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        public string GetSVMTimeWaveDefListByTurbId(string turbineID)
        {
            List<MeasDefinition> Definlist = MeasDefinitionManagement.GetMeasDefListByTurId(turbineID);
            List<MeasDefinition> listDefinShort = new List<MeasDefinition>();
            Definlist.ForEach(item =>
            {
                listDefinShort.Add(new MeasDefinition()
                {
                    MeasDefinitionID = item.MeasDefinitionID,
                    MeasDefinitionName = item.MeasDefinitionName
                });
            });
            List<WaveDef_SVM> Templist = new List<WaveDef_SVM>();
            foreach (MeasDefinition defin in listDefinShort)
            {
                List<WaveDef_SVM> list = new List<WaveDef_SVM>();
                WaveDefinitionManagement.GetSVMWaveDefListByMdfId(turbineID, defin.MeasDefinitionID).ForEach(item =>
                {
                    list.Add(item);
                });
                if (list.Count > 0)
                {
                    // string[] arr = new string[] { "水平加速度", "轴向加速度", "垂直加速度", "横滚角", "俯仰角", "SVM温度" };
                    List<WaveDef_SVM> listByOrder = new List<WaveDef_SVM>();
                    foreach (EnumSVMParamType data in Enum.GetValues(typeof(EnumSVMParamType)))
                    {
                        WaveDef_SVM svmDef = list.Find(i => i.WaveDefinitionName == AppFramework.Utility.EnumHelper.GetDescription(data));
                        if (svmDef != null)
                        {
                            listByOrder.Add(svmDef);
                        }
                    }
                    list = listByOrder;
                }
                Templist.AddRange(list);
            }
            return "[" + listDefinShort.ToJson() + "," + Templist.ToJson() + "]";
        }

        public string GetSVMUnusedVibMeasLocByMeasDefId_Time(string turbineID, string MeasDefinitionID)
        {
            List<MeasLoc_SVM> vibLoc = new List<MeasLoc_SVM>();
            MeasDefinition measDef = MeasDefinitionManagement.GetMeasdefinition(turbineID, MeasDefinitionID);
            // 机组下全部晃度仪测量位置
            vibLoc = SVMManagement.GetMeasLoc_SVMListByTurID(measDef.WindTurbineID);
            // 获取SVM波形定义列表
            List<WaveDef_SVM> svmWave = WaveDefinitionManagement.GetSVMWaveDefListByMdfId(turbineID, MeasDefinitionID);
            // 移除定义了角度的
            vibLoc.RemoveAll(item =>
            {
                return (svmWave.Find(wave => wave.MeasLocationID == item.MeasLocationID) != null);
            });
            //没添加寄存器的，删除掉
            vibLoc.RemoveAll(item =>
            {
                return !SVMManagement.SVMIsRegister(turbineID, item.MeasLocationID);
            });
            if (vibLoc.Count > 0)
            {
                List<MeasLoc_SVM> listTemp = new List<MeasLoc_SVM>();
                // string[] arr = new string[] { "水平加速度", "轴向加速度", "垂直加速度", "横滚角", "俯仰角", "SVM温度" };
                foreach (EnumSVMParamType data in Enum.GetValues(typeof(EnumSVMParamType)))
                {
                    MeasLoc_SVM svm = vibLoc.Find(i => i.MeasLocName == AppFramework.Utility.EnumHelper.GetDescription(data));
                    if (svm != null)
                    {
                        listTemp.Add(svm);
                    }
                }
                vibLoc = listTemp;
            }
            return vibLoc.ToJson();
        }

        public string EditSVMWaveDefinition(string MeasDefinitionID, string WindTurbineID, string WindParkID, string measLocIds, string isAddType, string messLocNames, float SampleRate, short SampleLength)
        {
            string message = "state:{0},msg:'{1}'";
            string actionName = "添加";
            string effectiveId = "";
            try
            {
                List<string> measLocIdList = new List<string>();
                if (isAddType == "1")
                {
                    if (!string.IsNullOrEmpty(measLocIds))
                        measLocIdList = measLocIds.Split(',').ToList();
                    else
                    {
                        message = string.Format(message, 0, "没有晃度测量位置信息！");
                        return "{" + message + "}";
                    }
                }
                else
                {
                    //修改晃度波形定义
                    List<WaveDef_SVM> list = WaveDefinitionManagement.GetSVMWaveDefListByMdfId(WindTurbineID, MeasDefinitionID);
                    SVMManagement.UpdateDefin(list, SampleRate, SampleLength);
                    DAUManager.UpdateMeasDefVersion(WindTurbineID);
                    message = string.Format(message, 1, "");
                    return "{" + message + "}";
                }
                string locNames = "";
                List<string> measLocNameList = messLocNames.Split(',').ToList();
                // 遍历用户选择测量位置
                List<WaveDef_SVM> AddWaveSVMList = new List<WaveDef_SVM>();
                for (int i = 0; i < measLocIdList.Count(); i++)
                {
                    // 存在波形定义？
                    if (IsExsitsWaveDef(WindTurbineID, MeasDefinitionID, measLocIdList[i]) == false)
                    {
                        WaveDef_SVM wdf = new WaveDef_SVM();
                        wdf.MeasDefinitionID = MeasDefinitionID;
                        wdf.MeasLocationID = measLocIdList[i];
                        wdf.WaveDefinitionName = measLocNameList[i];
                        wdf.WindTurbineID = WindTurbineID;
                        wdf.SampleLength = SampleLength;
                        wdf.SampleRate = SampleRate;
                        wdf.ParamType = GetEnumByName(measLocNameList[i]);
                        AddWaveSVMList.Add(wdf);
                        //List<WaveDef_SVM> list = WaveDefinitionManagement.GetSVMWaveDefListByMdfId(WindTurbineID, MeasDefinitionID);
                        //SVMManagement.AddWaveDefSVM(wdf);
                        //统一采样频率和长度
                        //if (list != null && list.Count > 0)
                        //    SVMManagement.UpdateDefin(list, SampleRate, SampleLength);
                        effectiveId += measLocIdList[i] + "，";
                    }
                    else
                    {
                        // 存在，记录测量位置名称
                        locNames += measLocNameList[i] + "，";
                    }
                }
                SVMManagement.AddWaveDefSVM(AddWaveSVMList);
                DAUManager.UpdateMeasDefVersion(WindTurbineID);
                // 测量位置已存在波形定义，提示。
                if (locNames != "")
                {
                    message = string.Format(message, 0, "振动测量位置（" + locNames + "）已经存在时域波形定义。刷新查看添加结果。");
                }
                else
                {
                    message = string.Format(message, 1, "");
                    #region
                    LogEntity logEntity = new LogEntity();
                    logEntity.LogDB = ConstDefine.UserManagementLog;
                    logEntity.LogTime = DateTime.Now;
                    logEntity.NodeID = effectiveId.IndexOf(',') > -1 ? effectiveId.Substring(0, effectiveId.LastIndexOf(',')) : effectiveId;
                    logEntity.UserName = Request.Cookies["WindCMSUserName"];
                    logEntity.OperationDescription
                        = string.Format("{1}_测量定义({0})", (effectiveId.IndexOf(',') > -1 ? effectiveId.Substring(0, effectiveId.LastIndexOf(',')) : effectiveId), actionName);
                    LogManagement.UserlogWrite(logEntity);
                    #endregion
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditSVMWaveDefinition]" + actionName + "测量定义失败 机组ID：" + WindTurbineID, ex);
            }
            return "{" + message + "}";
        }

        private EnumSVMParamType GetEnumByName(string Name)
        {
            switch (Name)
            {
                case "俯仰角":
                    return EnumSVMParamType.Pitch;
                case "横滚角":
                    return EnumSVMParamType.Roll;
                case "垂直加速度":
                    return EnumSVMParamType.Vertical;
                case "水平加速度":
                    return EnumSVMParamType.Horizontal;
                case "轴向加速度":
                    return EnumSVMParamType.Axisl;
                case "SVM温度":
                    return EnumSVMParamType.Temperature;
            }
            return EnumSVMParamType.Axisl;
        }

        public bool IsExsitsWaveDef(string _turID, string _measId, string _locId)
        {
            return WaveDefinitionManagement.IsExistSVMWaveDef(_turID, _measId, _locId);
        }

        public string DeleteSVMDefList(string turbineID, string WaveDefinitionID)
        {
            string message = "state:{0},msg:'{1}'";
            try
            {
                WaveDef_SVM SVMWave = WaveDefinitionManagement.GetSVMWaveDefByID(turbineID, WaveDefinitionID);
                if (SVMWave != null)
                {
                    WaveDefinitionManagement.DeleteSVMWaveDef(SVMWave);
                    DAUManager.UpdateMeasDefVersion(turbineID);
                    #region ---添加日志---
                    LogEntity logEntity = new LogEntity();
                    logEntity.LogDB = ConstDefine.DevTreeManagementLog;
                    logEntity.LogTime = DateTime.Now;
                    logEntity.NodeID = SVMWave.WaveDefinitionID;
                    logEntity.UserName = Request.Cookies["WindCMSUserName"];
                    logEntity.OperationDescription = string.Format("删 除晃度仪波形定义({0}_{1})",
                        turbineID, WaveDefinitionID);
                    WTCMSLive.BusinessModel.LogManagement.DevTreelogWrite(logEntity);
                    #endregion
                }
                message = string.Format(message, 1, "");

            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteSVMDefList]晃度波形删除失败 机组ID：" + turbineID, ex);
                message = string.Format(message, 0, "晃度波形删除失败 :" + ex.Message);
            }
            return "{" + message + "}";
        }

        #endregion

        #endregion

        #endregion

        #region 工况测量定义
        /// <summary>
        /// 添加修改工况测量定义
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="DauID"></param>
        /// <param name="MeasDefinitionID"></param>
        /// <param name="measLocId"></param>
        /// <param name="UpperLimitFreqency"></param>
        /// <param name="SampleLength"></param>
        /// <returns></returns>

        public string SetWorkCondMeasd(string WindTurbineID, string DauID, string MeasDefinitionID, string measLocId, int UpperLimitFreqency, int SampleLength, bool EvAvg = false)
        {
            string message = "state:{0},msg:'{1}'";
            // 添加波形定义
            MeasDefinitionManagement.SetWorkCondMeasdefinition(WindTurbineID, MeasDefinitionID, measLocId, UpperLimitFreqency, SampleLength);
            // 设置XmlEigenValueDef
            //MeasLoc_Process workCondition = DevTreeManagement.GetWorkCondMeasLocation(measLocId);
            //MeasDefinitionManagement.GetWorkCondEigenValue(WindTurbineID, MeasDefinitionID, DauID, workCondition.Param_Type_Code.ToString(), (int)workCondition.Param_Type_Code);

            // 设置特征值
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                if (EvAvg)
                {
                    var wkev = ctx.ProcessEvConfs.FirstOrDefault(t => t.WindTurbineID == WindTurbineID && t.MeasDefinitionID == MeasDefinitionID && t.MeasLocationID == measLocId);
                    if (wkev == null)
                    {
                        int maxIndex = 0;
                        try
                        {
                            maxIndex = ctx.ProcessEvConfs.Max(t => t.EvId);
                        }
                        catch { }
                        // 添加
                        ctx.ProcessEvConfs.Add(new MeasDef_Ev_Process()
                        {
                            WindTurbineID = WindTurbineID,
                            MeasDefinitionID = MeasDefinitionID,
                            MeasLocationID = measLocId,
                            Type = EnumEigenvalueName.Enum_WorkEnv,
                            Name = EnumEigenvalueName.Enum_WorkEnv.ToString(),
                            EvId = maxIndex + 1,
                        });
                    }
                }
                else
                {
                    var ev = ctx.ProcessEvConfs.FirstOrDefault(t => t.WindTurbineID == WindTurbineID && t.MeasDefinitionID == MeasDefinitionID && t.MeasLocationID == measLocId);
                    if (ev != null)
                    {
                        ctx.ProcessEvConfs.Remove(ev);
                    }
                }

                ctx.SaveChanges();
            }
            DauManagement.UpdateMeasDefVersion(WindTurbineID, DauID);

            message = string.Format(message, 1, "OK");
            return "{" + message + "}";
        }


        /// <summary>
        /// 删除工况配置
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="DauID"></param>
        /// <param name="MeasDefinitionID"></param>
        /// <param name="measLocId"></param>
        /// <returns></returns>
        public string DeleteWorkCondMeasd(string WindTurbineID, string DauID, string MeasDefinitionID, string measLocId)
        {
            string message = "state:{0},msg:'{1}'";
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ctx.MDFWorkConditions.RemoveRange(ctx.MDFWorkConditions.Where(item => item.WindTurbineID == WindTurbineID && item.MeasDefinitionID == MeasDefinitionID && item.MeasLocationID == measLocId));
                ctx.SaveChanges();
            }
            MeasLoc_Process workCondition = DevTreeManagement.GetWorkCondMeasLocation(measLocId);
            //MeasDefinitionManagement.DeleteWorkCondEigenValue(WindTurbineID, MeasDefinitionID, DauID, workCondition.Param_Type_Code.ToString(), (int)workCondition.Param_Type_Code);

            // 删除特征值配置
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {

                var ev = ctx.ProcessEvConfs.FirstOrDefault(t => t.WindTurbineID == WindTurbineID && t.MeasDefinitionID == MeasDefinitionID && t.MeasLocationID == measLocId);
                if (ev != null)
                {
                    ctx.ProcessEvConfs.Remove(ev);
                    ctx.SaveChanges();
                }
            }

            DauManagement.UpdateMeasDefVersion(WindTurbineID, DauID);
            message = string.Format(message, 1, "OK");
            return "{" + message + "}";
        }

        /// <summary>
        /// 转速波形定义列表
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="MeasDefinitionID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetWorkCondSpdWave")]
        public IActionResult GetWorkCondSpdMeasdTableData(string turbineID, string measDefinitionID)
        {
            List<MeasLoc_RotSpd_EV> rotSpdLocList = new List<MeasLoc_RotSpd_EV>();
            var res = MeasDefinitionManagement.GetWaveDefListRotSpd(turbineID, measDefinitionID);
            var evlist = EigenValueManage.GetEigenValueProcess(turbineID, measDefinitionID);
            if (res != null)
            {
                res.ForEach(item =>
                {
                    var _cur = DevTreeManagement.GetRotSpdMeasLocation(item.MeasLoc_RotSpdID);
                    var spd = new MeasLoc_RotSpd_EV()
                    {
                        WindTurbineID = item.WindTurbineID,
                        MeasLocName = _cur.MeasLocName,
                        LineCounts = item.LineCounts,
                        MeasLocationID = item.MeasLoc_RotSpdID,
                        //GearRatio = _cur.LineCounts,
                    };


                    var _ev = evlist.FirstOrDefault(t => t.MeasLocationID == item.MeasLoc_RotSpdID);
                    if (_ev != null)
                    {
                        spd.EvAvg = true;
                    }
                    else
                    {
                        spd.EvAvg = false;
                    }
                    rotSpdLocList.Add(spd);

                });
            }
            return Ok(rotSpdLocList);
        }
        /// <summary>
        /// 转速波形下拉菜单
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="MeasDefinitionID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetWorkCondSpdMeasdOption")]
        public IActionResult GetWorkCondSpdMeasdOption(string WindTurbineID, string MeasDefinitionID)
        {
            List<MeasLoc_RotSpd> rotSpdLocList = new List<MeasLoc_RotSpd>();
            List<WaveDef_RotSpd> res = MeasDefinitionManagement.GetWaveDefListRotSpd(WindTurbineID, MeasDefinitionID);

            List<MeasLoc_RotSpd> data = DevTreeManagement.GetRotSpdMeasLocListByTurId(WindTurbineID);
            data.RemoveAll(
                     item =>
                     {
                         return (res.Find(t => t.MeasLoc_RotSpdID == item.MeasLocationID) != null);
                     });
            return Ok(data);
        }
        /// <summary>
        /// 新增修改转速波形定义
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="DauID"></param>
        /// <param name="MeasDefinitionID"></param>
        /// <param name="measLocId"></param>
        /// <param name="WaveLineCounts"></param>
        /// <returns></returns>
        public string SetWorkCondSpdMeasd(string WindTurbineID, string DauID, string MeasDefinitionID, string measLocId, int WaveLineCounts, bool EvAvg = false)
        {
            string message = "state:{0},msg:'{1}'";
            List<MeasLoc_RotSpd> rotSpdLocList = DevTreeManagement.GetRotSpdMeasLocListByTurId(WindTurbineID);

            List<WaveDef_RotSpd> WaveDefListRotSpd = MeasDefinitionManagement.GetWaveDefListRotSpd(WindTurbineID, MeasDefinitionID);
            if (rotSpdLocList.Count > 0)
            {
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    var wdf = ctx.MDFWaveDefRotSpds.FirstOrDefault(item => item.WindTurbineID == WindTurbineID && item.MeasDefinitionID == MeasDefinitionID);
                    if (wdf == null)
                    {
                        WaveDef_RotSpd waveDefRotSpd = new WaveDef_RotSpd()
                        {
                            WindTurbineID = WindTurbineID,
                            MeasDefinitionID = MeasDefinitionID,
                            MeasLoc_RotSpdID = rotSpdLocList[0].MeasLocationID,
                            LineCounts = WaveLineCounts
                        };
                        ctx.MDFWaveDefRotSpds.Add(waveDefRotSpd);
                    }
                    else
                    {
                        wdf.LineCounts = WaveLineCounts;
                    }

                    // 特征值配置，平均值
                    if (EvAvg)
                    {
                        var wkev = ctx.ProcessEvConfs.FirstOrDefault(t => t.WindTurbineID == WindTurbineID && t.MeasDefinitionID == MeasDefinitionID && t.MeasLocationID == measLocId);
                        if (wkev == null)
                        {
                            // 添加
                            ctx.ProcessEvConfs.Add(new MeasDef_Ev_Process()
                            {
                                WindTurbineID = WindTurbineID,
                                MeasDefinitionID = MeasDefinitionID,
                                MeasLocationID = measLocId,
                                Type = EnumEigenvalueName.Enum_SpdMean,
                                Name = EnumEigenvalueName.Enum_SpdMean.ToString(),
                            });
                        }
                    }
                    else
                    {
                        var ev = ctx.ProcessEvConfs.FirstOrDefault(t => t.WindTurbineID == WindTurbineID && t.MeasDefinitionID == MeasDefinitionID && t.MeasLocationID == measLocId);
                        if (ev != null)
                        {
                            ctx.ProcessEvConfs.Remove(ev);
                        }
                    }


                    ctx.SaveChanges();
                }
                // 提升DAU测量定义版本
                DauManagement.UpdateMeasDefVersion(WindTurbineID.ToString(), DauID);
                message = string.Format(message, 1, "OK");
            }
            else
            {
                message = string.Format(message, 0, "转速测点未定义");
            }


            return "{" + message + "}";
        }

        public string DeleteWorkCondSpdMeasd(string WindTurbineID, string DauID, string MeasDefinitionID, string measLocId)
        {
            string message = "state:{0},msg:'{1}'";

            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ctx.MDFWaveDefRotSpds.Remove(ctx.MDFWaveDefRotSpds.FirstOrDefault(t => t.WindTurbineID == WindTurbineID && t.MeasDefinitionID == MeasDefinitionID && t.MeasLoc_RotSpdID == measLocId));

                // 删除转速 平均值特征值
                var evAvg = ctx.ProcessEvConfs.FirstOrDefault(t => t.WindTurbineID == WindTurbineID && t.MeasDefinitionID == MeasDefinitionID && t.MeasLocationID == measLocId);

                if (evAvg != null)
                {
                    ctx.ProcessEvConfs.Remove(evAvg);
                }

                ctx.SaveChanges();
            }
            // 提升DAU测量定义版本
            DauManagement.UpdateMeasDefVersion(WindTurbineID.ToString(), DauID);
            message = string.Format(message, 1, "OK");
            return "{" + message + "}";
        }


        /// <summary>
        /// 工况option
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="MeasDefId"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetWorkConListForMeasLocOption")]
        public IActionResult GetWorkConListForMeasLocOption(string turbineID, string MeasDefId)
        {
            //return "123";
            string mark = "1";
            try
            {
                //string mark = "1";
                mark += "2";
                List<MeasLoc_Process> allListbyTurId = WTCMSLive.BusinessModel.DevTreeManagement.GetWorkCondMeasLocByTurID(turbineID);
                mark += "3";
                List<MeasLoc_Process> BoundList = MeasDefinitionManagement.GetMeasLocWorkCondLocListByMeasDefId(turbineID, MeasDefId);
                mark += "4";
                List<MeasDef_Process> WorkProcess = MeasDefinitionManagement.GetMDFWorkCondLocListByMeasDefId(turbineID, MeasDefId);
                mark += "5";
                List<MeasLoc_Process> notBoundList = new List<MeasLoc_Process>();

                WindDAU myChannel = DauManagement.GetDAUById(turbineID, MeasDefId);
                List<DAUChannel_Process> List = new List<DAUChannel_Process>();
                if (myChannel != null) List = myChannel.ProcessChannelList;
                List<MCSChannel> mcsChannel = DAUMCS.GetMCSChannelList(turbineID);
                List<MeasLoc_Process> temp_allListbyTurId = new List<MeasLoc_Process>();
                allListbyTurId.ForEach(item =>
                {
                    /*MeasLoc_Process measloc_proc = new MeasLoc_Process();
                    measloc_proc.Eu_type_code = item.Eu_type_code;
                    measloc_proc.FieldBusType = item.FieldBusType;
                    measloc_proc.MeasLocationID = item.MeasLocationID;
                    measloc_proc.MeasLocName = item.MeasLocName;
                    measloc_proc.OrderSeq = item.OrderSeq;
                    measloc_proc.Param_Type_Code = item.Param_Type_Code;
                    measloc_proc.Param_Type_Name = item.Param_Type_Name;
                    measloc_proc.ParmaChannelNumber = item.ParmaChannelNumber;
                    measloc_proc.ServerAddress = item.ServerAddress;
                    measloc_proc.WindTurbineID = item.WindTurbineID;*/

                    if (item.FieldBusType == EnumWorkConDataSource.WindDAU)
                    {
                        if (List.Find(dauProcess => dauProcess.MeasLoc_ProcessId == item.MeasLocationID) == null)
                        {
                            notBoundList.Add(item);
                        }
                    }
                    else
                    {
                        if (mcsChannel.Find(mcsProcess => mcsProcess.MeasLocProcessID == item.MeasLocationID) == null)
                        {
                            notBoundList.Add(item);
                        }
                    }
                    MeasDef_Process myProcess = WorkProcess.Find(i => i.MeasLocationID == item.MeasLocationID && item.FieldBusType == EnumWorkConDataSource.WindDAU);
                    if (myProcess != null)
                    {
                        item.ParmaChannelNumber = myProcess.UpperLimitFreqency.ToString();
                        item.Param_Type_Name = myProcess.SampleLength.ToString();
                    }
                    myProcess = WorkProcess.Find(i => i.MeasLocationID == item.MeasLocationID && item.FieldBusType == EnumWorkConDataSource.ModbusOnTcp);
                    if (myProcess != null)
                    {
                        //无意义，只是前台修改的一个标记
                        //@wangy 
                        //20210121现在，它有意义了。
                        //item.ParmaChannelNumber = "1";
                        item.ParmaChannelNumber = myProcess.UpperLimitFreqency.ToString();
                        item.Param_Type_Name = myProcess.SampleLength.ToString();
                    }
                    //temp_allListbyTurId.Add(measloc_proc);
                });
                //List<MeasLoc_Process> getbindList = new List<MeasLoc_Process>();
                //temp_allListbyTurId.ForEach(item =>
                //{
                //    if (notBoundList.Find(notBount => notBount.MeasLocationID == item.MeasLocationID) == null)
                //    {
                //        getbindList.Add(item);
                //    }
                //});
                //allListbyTurId = getbindList;
                //return getbindList.ToJson();
                allListbyTurId.RemoveAll(
                     item =>
                     {
                         return (notBoundList.Find(notBount => notBount.MeasLocationID == item.MeasLocationID) != null);
                     });

                allListbyTurId.RemoveAll(
                     item =>
                     {
                         return (BoundList.Find(Bount => Bount.MeasLocationID == item.MeasLocationID) != null);
                     });

                var WCPT_Oil_Grit = DevTreeManagement.GetMeaslocProcessListWithDAUByTurID(turbineID).Find(item => item.Param_Type_Code == EnumWorkCondition_ParamType.WCPT_Oil_Debris && item.FieldBusType == EnumWorkConDataSource.WindDAU);
                if (WCPT_Oil_Grit != null)
                {
                    var MdfOilGrit = MeasDefinitionManagement.GetMDFWorkCondLocListByTurbineId(turbineID).Find(item => item.MeasLocationID == WCPT_Oil_Grit.MeasLocationID && item.MeasDefinitionID != MeasDefId);
                    if (MdfOilGrit != null)
                    {
                        allListbyTurId.RemoveAll(item => item.MeasLocationID == MdfOilGrit.MeasLocationID);
                    }
                }
                allListbyTurId = allListbyTurId.OrderBy(item => item.OrderSeq).ToList();
                //return allListbyTurId.ToJson();
                return Ok(allListbyTurId);
            }
            catch (Exception ex)
            {
                //return mark + "\r\n" + ex.ToString();
                return Ok(new List<MeasLoc_Process>());
            }
        }

        /// <summary>
        /// 工况table
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="MeasDefId"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetWorkConWaveList")]
        public IActionResult GetWorkConListForMeasLocTableData(string turbineID, string MeasDefinitionID)
        {
            List<MeasDef_Process> wdfWorkConValues = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                wdfWorkConValues = ctx.MDFWorkConditions.Where(item => item.WindTurbineID == turbineID && item.MeasDefinitionID == MeasDefinitionID).ToList();
            }

            List<MeasLoc_Process> allListbyTurId = WTCMSLive.BusinessModel.DevTreeManagement.GetWorkCondMeasLocByTurID(turbineID);

            // 获取当前测量定义下的所有工况特征值
            var processEvlist = EigenValueManage.GetEigenValueProcess(turbineID, MeasDefinitionID);

            List<MeasLoc_Process_EV> res = new List<MeasLoc_Process_EV>();

            if (wdfWorkConValues != null)
            {
                wdfWorkConValues.ForEach(item =>
                {
                    var _cur = allListbyTurId.FirstOrDefault(t => t.MeasLocationID == item.MeasLocationID);
                    if (_cur != null)
                    {
                        var _mp = new MeasLoc_Process_EV()
                        {
                            DevWindTurbine = _cur.DevWindTurbine,
                            ParmaChannelNumber = item.UpperLimitFreqency.ToString(),
                            Param_Type_Name = item.SampleLength.ToString(),
                            MeasLocName = _cur.MeasLocName,
                            MeasLocationID = _cur.MeasLocationID,
                            Param_Type_Code = _cur.Param_Type_Code,
                            OrderSeq = _cur.OrderSeq,
                        };

                        // 特征值平均值
                        var avgEv = processEvlist.FirstOrDefault(t => t.MeasLocationID == item.MeasLocationID);
                        if (avgEv != null)
                        {
                            _mp.EvAvg = true;
                        }
                        else
                        {
                            _mp.EvAvg = false;
                        }
                        res.Add(_mp);
                    }

                });
            }

            return Ok(res);
        }
        #endregion


        #region 测量方案
        /// <summary>
        /// 测量方案编码
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="MeasSolutionName"></param>
        /// <param name="MeasSolutionType"></param>
        /// <param name="MeasDefinitionIDList"></param>
        /// <param name="DaqInterval"></param>
        /// <param name="WaveInterval"></param>
        /// <param name="EigenInterval"></param>
        /// <param name="MeasSolutionID"></param>
        /// <param name="TriggeringCondition"></param>
        /// <param name="TriggeringOperator"></param>
        /// <param name="TriggeringNum"></param>
        /// <returns></returns>

        public string EditMeasSolution(string WindTurbineID, string MeasSolutionName, string MeasSolutionType, string MeasDefinitionIDList, int DaqInterval, int WaveInterval, int EigenInterval, int? MeasSolutionID = null, string TriggeringCondition = "", string TriggeringOperator = "", int TriggeringNum = 0)
        {
            string message = "state:{0},msg:'{1}'";

            if (!string.IsNullOrEmpty(MeasDefinitionIDList))
            {
                var measlist = MeasDefinitionIDList.Split(',');

                List<MeasSolution> meassolution = new List<MeasSolution>();


                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    // 根据MeasSolutionID判断是否新增和修改

                    int newid;
                    if (MeasSolutionID != null)
                    {
                        //修改
                        ctx.MeasSolutions.RemoveRange(ctx.MeasSolutions.Where(t => t.MeasSolutionID == MeasSolutionID && t.WindTurbineID == WindTurbineID));
                        newid = (int)MeasSolutionID;
                    }
                    else
                    {
                        if (ctx.MeasSolutions.Count() > 0)
                        {
                            newid = ctx.MeasSolutions.Max(t => t.MeasSolutionID) + 1;
                        }
                        else
                        {
                            newid = 1;
                        }
                    }


                    for (int i = 0; i < measlist.Length; i++)
                    {
                        var _cur = new MeasSolution()
                        {
                            MeasSolutionID = newid,
                            MeasSolutionName = MeasSolutionName,
                            MeasSolutionType = MeasSolutionType,
                            WindTurbineID = WindTurbineID,
                            MeasDefinitionID = measlist[i],
                            DaqInterval = DaqInterval,
                            EigenInterval = EigenInterval,
                            WaveInterval = WaveInterval,
                            //TriggeringCondition = TriggeringCondition == "" ? null : EnumWorkCondition_ParamType.WCPT_RotSpeed,
                            //TriggeringNum = TriggeringNum,
                            //TriggeringOperator = TriggeringOperator,
                        };
                        if (!string.IsNullOrEmpty(TriggeringCondition))
                        {
                            _cur.TriggeringCondition = EnumWorkCondition_ParamType.WCPT_RotSpeed;
                            _cur.TriggeringNum = TriggeringNum;
                            _cur.TriggeringOperator = TriggeringOperator;
                        }
                        meassolution.Add(_cur);
                    }
                    ctx.MeasSolutions.AddRange(meassolution);
                    ctx.SaveChanges();

                    message = string.Format(message, 1, "OK");
                }
            }
            else
            {
                message = string.Format(message, 0, "请选择测量定义！");
            }

            return "{" + message + "}";

        }

        /// <summary>
        /// 测量方案删除
        /// </summary>
        /// <param name="MeasSolutionID"></param>
        /// <returns></returns>
        public bool DeleteMeasSolution(string windturbine, int MeasSolutionID)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ctx.MeasSolutions.RemoveRange(ctx.MeasSolutions.Where(t => t.WindTurbineID.Equals(windturbine) && t.MeasSolutionID == MeasSolutionID));
                ctx.SaveChanges();

                return true;
            }

        }

        /// <summary>
        /// 测量方案获取
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetMeasSolution")]
        public string GetMeasSolution(string WindTurbineID)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                // 测量方案
                var measSolutionlist = ctx.MeasSolutions.Where(t => t.WindTurbineID == WindTurbineID).ToList();
                // 测量定义
                var measlist = ctx.MeasDefinitions.Where(t => t.WindTurbineID == WindTurbineID).ToList();


                JObject res = new JObject();
                foreach (var item in measSolutionlist.GroupBy(t => t.MeasSolutionType))
                {
                    JArray measitem = new JArray();
                    foreach (var _p in item.AsEnumerable().GroupBy(k => k.MeasSolutionID))
                    {
                        var _curSolution = JObject.FromObject(_p.AsEnumerable().First());
                        //_curSolution["MeasDefinitionID"] = JArray.FromObject(item.AsEnumerable().Select(t => t.MeasDefinitionID));

                        JObject selector(MeasDefinition t) { return new JObject() { { "key", t.MeasDefinitionName }, { "value", t.MeasDefinitionID } }; }
                        _curSolution["MeasDefinitionID"] = JArray.FromObject(measlist.Where(t => _p.AsEnumerable().Select(s => s.MeasDefinitionID.ToString()).Contains(t.MeasDefinitionID)).Select(selector));

                        measitem.Add(_curSolution);
                    }

                    res.Add(item.Key, measitem);
                }
                return res.ToString();
            }
        }

        /// <summary>
        /// 根据机组加测量方案获取
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="MeasDefinitionID"></param>
        /// <returns></returns>
        public string GetMeasSolutionByID(string WindTurbineID, string MeasDefinitionID)
        {
            List<MeasSolution> result = new List<MeasSolution>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                List<MeasSolution> MeasSolutionsList = ctx.MeasSolutions.Where(t => t.WindTurbineID == WindTurbineID && t.MeasDefinitionID == MeasDefinitionID).ToList();
                if (MeasSolutionsList.Count != 0)
                {
                    result = MeasSolutionsList.GroupBy(item => item.MeasSolutionID).Select(item => item.First()).ToList();
                }
                return result.ToJson();
            }
        }

        /// <summary>
        /// 批量删除测量方案（原子性操作）
        /// </summary>
        /// <param name="request">批量删除测量方案请求</param>
        /// <returns></returns>
        [HttpPost("BatchDeleteMeasSolutions")]
        [BatchOperation(nameof(BatchDeleteMeasSolutionsFun))]
        public IActionResult BatchDeleteMeasSolutions([FromBody] BatchDeleteMeasSolutionsRequest request)
        {
            if (request?.SourceData == null || !request.SourceData.Any())
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            // 删除前先存储被删除的测量定义
            List<MeasSolution> originalRecords = new List<MeasSolution>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                foreach (var item in request.SourceData)
                {
                    var solution = ctx.MeasSolutions.FirstOrDefault(t => t.WindTurbineID == item.WindTurbineID && t.MeasSolutionID == item.MeasSolutionID);
                    if (solution != null)
                    {
                        originalRecords.Add(solution);
                    }

                }
            }
            HttpContext.Items["SolutionDelRecords"] = originalRecords;

            try
            {
                int successCount = 0;
                int failureCount = 0;
                var details = new List<string>();

                // 遍历每个删除请求
                foreach (var solutionId in request.SourceData)
                {
                    try
                    {
                        // 执行删除（原子性操作）
                        MeasDefinitionManagement.BatchDeleteMeasSolutions(solutionId.WindTurbineID, new List<int> { solutionId.MeasSolutionID });

                        successCount++;
                        details.Add($"删除成功: 测量方案ID {solutionId}");
                    }
                    catch (Exception ex)
                    {
                        failureCount++;
                        details.Add($"删除失败: 测量方案ID {solutionId} - {ex.Message}");
                        CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteMeasSolutions]删除测量方案失败: ID {solutionId}", ex);
                    }
                }

                var message = failureCount == 0 ? "批量删除成功" : $"批量删除完成，成功 {successCount} 个，失败 {failureCount} 个";
                return Ok(ApiResponse<string>.Success(message));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchDeleteMeasSolutions]批量删除测量方案失败", ex);
                return Ok(ApiResponse<string>.Error("批量删除失败: " + ex.Message));
            }
        }

        /// <summary>
        /// 批量删除测量方案到单个机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量删除测量方案请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteMeasSolutionsFun(string targetTurbineId, BatchDeleteMeasSolutionsRequest request)
        {
            var result = new List<string>();

            try
            {
                var originalRecords = HttpContext.Items["SolutionDelRecords"] as List<MeasSolution>;
                var mappedData = new List<int>();

                using (var ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    foreach (var sourceSolutionId in request.SourceData)
                    {
                        // 查找源测量方案,源方案已被删除，无法找到，从缓存中读取
                        var sourceSolution = originalRecords.FirstOrDefault(s => s.MeasSolutionID == sourceSolutionId.MeasSolutionID && s.WindTurbineID == sourceSolutionId.WindTurbineID);
                        if (sourceSolution == null)
                        {
                            CMSFramework.Logger.Logger.LogErrorMessage($"[MapDeleteMeasSolutionToTargetTurbine]未找到源测量方案: ID {sourceSolutionId}", new Exception("未找到源测量方案"));
                            continue;
                        }

                        // 在目标机组中查找同名的测量方案
                        var targetSolution = ctx.MeasSolutions.FirstOrDefault(s =>
                            s.WindTurbineID == targetTurbineId &&
                            s.MeasSolutionName == sourceSolution.MeasSolutionName &&
                            s.MeasSolutionType == sourceSolution.MeasSolutionType);

                        if (targetSolution != null)
                        {
                            mappedData.Add(targetSolution.MeasSolutionID);
                        }
                        else
                        {
                            result.Add($"删除失败: 测量方案 {sourceSolution.MeasSolutionName} 未找到");
                            CMSFramework.Logger.Logger.LogErrorMessage($"[MapDeleteMeasSolutionToTargetTurbine]在目标机组中未找到对应的测量方案: {sourceSolution.MeasSolutionName}", new Exception("未找到对应的测量方案"));
                        }
                    }
                }


                // 执行批量删除
                foreach (var solutionId in mappedData)
                {
                    var itemResult = new BatchItemResult
                    {
                        TurbineId = targetTurbineId,
                        ExecutedAt = DateTime.Now
                    };

                    try
                    {
                        // 执行删除（原子性操作）
                        MeasDefinitionManagement.BatchDeleteMeasSolutions(targetTurbineId, new List<int> { solutionId });

                        itemResult.Success = true;
                        itemResult.ErrorMessage = $"删除成功: 测量方案ID {solutionId}";
                        result.Add($"删除成功: 测量方案ID {solutionId}");
                    }
                    catch (Exception ex)
                    {
                        itemResult.Success = false;
                        itemResult.ErrorMessage = $"删除失败: 测量方案ID {solutionId} - {ex.Message}";
                        CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteMeasSolutionsBatch]删除测量方案失败: ID {solutionId}", ex);
                        result.Add($"删除失败: 测量方案ID {solutionId} - {ex.Message}");
                    }

                }
            }
            catch (Exception ex)
            {
                result.Add($"批量删除异常: {ex.Message}");
                CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteMeasSolutionsBatch]批量删除测量方案异常", ex);
            }

            return result;
        }

        /// <summary>
        /// 批量添加测量方案（原子性操作）
        /// </summary>
        /// <param name="request">批量添加测量方案请求</param>
        /// <returns></returns>
        [HttpPost("BatchAddMeasSolutions")]
        [BatchOperation(nameof(BatchAddMeasSolutionsFun))]
        public IActionResult BatchAddMeasSolutions([FromBody] BatchAddMeasSolutionsRequest request)
        {
            if (request?.SourceData == null || !request.SourceData.Any())
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            try
            {
                int successCount = 0;
                int failureCount = 0;
                var details = new List<string>();

                // 遍历每个添加请求
                foreach (var solutionDto in request.SourceData)
                {
                    try
                    {
                        if (solutionDto.MeasDefinitionIDList == null || solutionDto.MeasDefinitionIDList.Count == 0)
                        {
                            failureCount++;
                            details.Add($"添加失败: {solutionDto.MeasSolutionName} - 测量定义列表不能为空");
                            continue;
                        }

                        List<MeasSolution> solutions = new List<MeasSolution>();
                        int solutionId = MeasDefinitionManagement.GetNextMeasSolutionID();

                        // 为每个测量定义创建一个MeasSolution记录
                        foreach (var measDefId in solutionDto.MeasDefinitionIDList)
                        {
                            var solution = new MeasSolution
                            {
                                MeasSolutionID = solutionId,
                                MeasSolutionName = solutionDto.MeasSolutionName,
                                MeasSolutionType = solutionDto.MeasSolutionType,
                                WindTurbineID = solutionDto.WindTurbineID,
                                MeasDefinitionID = measDefId,
                                DaqInterval = solutionDto.DaqInterval,
                                EigenInterval = solutionDto.EigenInterval,
                                WaveInterval = solutionDto.WaveInterval
                            };

                            //if (!string.IsNullOrEmpty(solutionDto.TriggeringCondition))
                            //{
                            //    solution.TriggeringCondition = EnumWorkCondition_ParamType.WCPT_RotSpeed;
                            //    solution.TriggeringNum = solutionDto.TriggeringNum;
                            //    solution.TriggeringOperator = solutionDto.TriggeringOperator;
                            //}

                            solutions.Add(solution);
                        }

                        // 执行添加（原子性操作）
                        MeasDefinitionManagement.BatchAddMeasSolutions(solutions);

                        successCount++;
                        details.Add($"添加成功: {solutionDto.MeasSolutionName}");
                    }
                    catch (Exception ex)
                    {
                        failureCount++;
                        details.Add($"添加失败: {solutionDto.MeasSolutionName} - {ex.Message}");
                        CMSFramework.Logger.Logger.LogErrorMessage($"[BatchAddMeasSolutions]添加测量方案失败: {solutionDto.MeasSolutionName}", ex);
                    }
                }

                var message = failureCount == 0 ? "批量添加成功" : $"批量添加完成，成功 {successCount} 个，失败 {failureCount} 个";
                return Ok(ApiResponse<string>.Success(message));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchAddMeasSolutions]批量添加测量方案失败", ex);
                return Ok(ApiResponse<string>.Error("批量添加失败: " + ex.Message));
            }
        }

        /// <summary>
        /// 批量添加测量方案到单个机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量添加测量方案请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddMeasSolutionsFun(string targetTurbineId, BatchAddMeasSolutionsRequest request)
        {
            var result = new List<string>();
            try
            {
                var mappedData = new List<MeasSolutionAddItem>();

                foreach (var sourceDto in request.SourceData)
                {
                    List<string> tarmeasdID = new List<string>();
                    foreach (var measd in sourceDto.MeasDefinitionIDList)
                    {
                        var curmeasdlist = MeasDefinitionManagement.GetMeasdefinition(sourceDto.WindTurbineID, measd);
                        if (curmeasdlist == null)
                        {
                            continue;
                        }
                        var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasdlist.MeasDefinitionName);
                        if (tarmeasd != null)
                        {
                            tarmeasdID.Add(tarmeasd.MeasDefinitionID);
                        }
                        else
                        {
                            result.Add($"添加失败：测量定义: {curmeasdlist.MeasDefinitionName} 没有找到 ");
                        }

                    }

                    if (tarmeasdID.Count != sourceDto.MeasDefinitionIDList.Count)
                    {
                        continue;
                    }

                    var mappedDto = new MeasSolutionAddItem
                    {
                        MeasSolutionName = sourceDto.MeasSolutionName,
                        MeasSolutionType = sourceDto.MeasSolutionType,
                        MeasDefinitionIDList = tarmeasdID,
                        DaqInterval = sourceDto.DaqInterval,
                        WaveInterval = sourceDto.WaveInterval,
                        EigenInterval = sourceDto.EigenInterval
                    };

                    mappedData.Add(mappedDto);
                }


                // 执行批量添加
                foreach (var solutionDto in mappedData)
                {
                    try
                    {
                        if (solutionDto.MeasDefinitionIDList == null || solutionDto.MeasDefinitionIDList.Count == 0)
                        {
                            result.Add($"添加失败: {solutionDto.MeasSolutionName} - 测量定义列表不能为空");
                        }
                        else
                        {
                            List<MeasSolution> solutions = new List<MeasSolution>();
                            int solutionId = MeasDefinitionManagement.GetNextMeasSolutionID();

                            // 为每个测量定义创建一个MeasSolution记录
                            foreach (var measDefId in solutionDto.MeasDefinitionIDList)
                            {
                                var solution = new MeasSolution
                                {
                                    MeasSolutionID = solutionId,
                                    MeasSolutionName = solutionDto.MeasSolutionName,
                                    MeasSolutionType = solutionDto.MeasSolutionType,
                                    WindTurbineID = targetTurbineId,
                                    MeasDefinitionID = measDefId,
                                    DaqInterval = solutionDto.DaqInterval,
                                    EigenInterval = solutionDto.EigenInterval,
                                    WaveInterval = solutionDto.WaveInterval
                                };

                                solutions.Add(solution);
                            }

                            // 执行添加（原子性操作）
                            MeasDefinitionManagement.BatchAddMeasSolutions(solutions);
                            result.Add($"添加成功: {solutionDto.MeasSolutionName}");
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Add($"添加失败: {solutionDto.MeasSolutionName} - {ex.Message}");
                        CMSFramework.Logger.Logger.LogErrorMessage($"[BatchAddMeasSolutionsBatch]添加测量方案失败: {solutionDto.MeasSolutionName}", ex);
                    }

                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[BatchAddMeasSolutionsBatch]批量添加测量方案异常", ex);
            }

            return result;
        }

        /// <summary>
        /// 编辑单个测量方案
        /// </summary>
        /// <param name="request">批量编辑测量方案请求</param>
        /// <returns></returns>
        [HttpPost("EditMeasSolution")]
        [BatchOperation(nameof(BatchEditMeasSolutionFun))]
        public IActionResult EditMeasSolution([FromBody] BatchEditMeasSolutionRequest request)
        {
            if (request?.SourceData == null)
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            // 修改前先存储被修改的测量方案
            MeasSolution originalRecords = new MeasSolution();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                var solution = ctx.MeasSolutions.FirstOrDefault(t => t.WindTurbineID == request.SourceData.WindTurbineID && t.MeasSolutionID == request.SourceData.MeasSolutionID);
                HttpContext.Items["SolutionEditRecords"] = solution;

            }
            try
            {
                int successCount = 0;
                int failureCount = 0;
                var details = new List<string>();


                try
                {
                    if (request.SourceData.MeasDefinitionIDList == null || request.SourceData.MeasDefinitionIDList.Count == 0)
                    {
                        failureCount++;
                        details.Add($"编辑失败: {request.SourceData.MeasSolutionName} - 请选择测量定义！");

                    }

                    List<MeasSolution> solutions = new List<MeasSolution>();

                    // 为每个测量定义创建一个MeasSolution记录
                    foreach (var measDefId in request.SourceData.MeasDefinitionIDList)
                    {
                        var solution = new MeasSolution
                        {
                            MeasSolutionID = request.SourceData.MeasSolutionID,
                            MeasSolutionName = request.SourceData.MeasSolutionName,
                            MeasSolutionType = request.SourceData.MeasSolutionType,
                            WindTurbineID = request.SourceData.WindTurbineID,
                            MeasDefinitionID = measDefId,
                            DaqInterval = request.SourceData.DaqInterval,
                            EigenInterval = request.SourceData.EigenInterval,
                            WaveInterval = request.SourceData.WaveInterval
                        };

                        //if (!string.IsNullOrEmpty(dto.TriggeringCondition))
                        //{
                        //    solution.TriggeringCondition = EnumWorkCondition_ParamType.WCPT_RotSpeed;
                        //    solution.TriggeringNum = dto.TriggeringNum;
                        //    solution.TriggeringOperator = dto.TriggeringOperator;
                        //}

                        solutions.Add(solution);
                    }

                    // 执行编辑（原子性操作）
                    MeasDefinitionManagement.EditMeasSolution(request.SourceData.WindTurbineID, request.SourceData.MeasSolutionID, solutions);

                    successCount++;
                    details.Add($"编辑成功: {request.SourceData.MeasSolutionName}");
                }
                catch (Exception ex)
                {
                    failureCount++;
                    details.Add($"编辑失败: {request.SourceData.MeasSolutionName} - {ex.Message}");
                    CMSFramework.Logger.Logger.LogErrorMessage($"[EditMeasSolution]编辑测量方案失败: {request.SourceData.MeasSolutionName}", ex);
                }


                var message = failureCount == 0 ? "批量编辑成功" : $"批量编辑完成，成功 {successCount} 个，失败 {failureCount} 个";
                return Ok(ApiResponse<string>.Success(message));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditMeasSolution]批量编辑测量方案失败", ex);
                return Ok(ApiResponse<string>.Error("批量编辑失败: " + ex.Message));
            }
        }

        /// <summary>
        /// 批量编辑测量方案到单个机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量编辑测量方案请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchEditMeasSolutionFun(string targetTurbineId, BatchEditMeasSolutionRequest request)
        {
            var result = new List<string>();
            var sourceDto = request.SourceData;
            try
            {
                var originalRecords = HttpContext.Items["SolutionEditRecords"] as MeasSolution;

                // 映射测量定义ID
                var mappedMeasDefIds = MapMeasDefinitionIds(sourceDto.MeasDefinitionIDList.Select(int.Parse).ToList(), sourceDto.WindTurbineID, targetTurbineId);
                if (mappedMeasDefIds == null || !mappedMeasDefIds.Any())
                {
                    CMSFramework.Logger.Logger.LogErrorMessage($"[MapEditMeasSolutionToTargetTurbine]测量定义映射失败: {sourceDto.MeasSolutionName}", new Exception("测量定义映射失败"));
                    result.Add($"编辑失败: 测量定义缺失！");
                    return result;
                }
                var mappedDto = new EditMeasSolutionDTO
                {
                    WindTurbineID = targetTurbineId,
                    MeasSolutionName = sourceDto.MeasSolutionName,
                    MeasSolutionType = sourceDto.MeasSolutionType,
                    MeasDefinitionIDList = mappedMeasDefIds.Select(id => id.ToString()).ToList(),
                    DaqInterval = sourceDto.DaqInterval,
                    WaveInterval = sourceDto.WaveInterval,
                    EigenInterval = sourceDto.EigenInterval
                };

                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    var targetSolution = ctx.MeasSolutions.FirstOrDefault(t => t.WindTurbineID == targetTurbineId && t.MeasSolutionName == originalRecords.MeasSolutionName);
                    if (targetSolution == null)
                    {
                        result.Add($"编辑失败: 无法找到测量方案{originalRecords.MeasSolutionName}！");
                        return result;
                    }

                    mappedDto.MeasSolutionID = targetSolution.MeasSolutionID;
                }

                // 执行批量编辑
                try
                {
                    List<MeasSolution> solutions = new List<MeasSolution>();

                    foreach (var measDefId in mappedMeasDefIds)
                    {
                        var solution = new MeasSolution
                        {
                            MeasSolutionID = mappedDto.MeasSolutionID,
                            MeasSolutionName = mappedDto.MeasSolutionName,
                            MeasSolutionType = mappedDto.MeasSolutionType,
                            WindTurbineID = mappedDto.WindTurbineID,
                            MeasDefinitionID = measDefId.ToString(),
                            DaqInterval = mappedDto.DaqInterval,
                            EigenInterval = mappedDto.EigenInterval,
                            WaveInterval = mappedDto.WaveInterval
                        };

                        solutions.Add(solution);
                    }

                    // 执行编辑（原子性操作）
                    MeasDefinitionManagement.EditMeasSolution(mappedDto.WindTurbineID, mappedDto.MeasSolutionID, solutions);

                    result.Add($"编辑成功: {mappedDto.MeasSolutionName}");
                }
                catch (Exception ex)
                {
                    result.Add($"编辑失败: {mappedDto.MeasSolutionName} - {ex.Message}");
                    CMSFramework.Logger.Logger.LogErrorMessage($"[BatchEditMeasSolutionBatch]编辑测量方案失败: {mappedDto.MeasSolutionName}", ex);
                }
            }
            catch (Exception ex)
            {

                result.Add($"批量编辑异常: {ex.Message}");
                CMSFramework.Logger.Logger.LogErrorMessage($"[BatchEditMeasSolutionBatch]批量编辑测量方案异常", ex);
            }

            return result;
        }

        /// <summary>
        /// 获取测量方案（不按类型分组）
        /// </summary>
        /// <param name="WindTurbineID">机组ID</param>
        /// <returns></returns>
        [HttpGet("GetMeasSolutionList")]
        public IActionResult GetMeasSolutionList(string WindTurbineID)
        {
            try
            {
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    // 测量方案
                    var measSolutionlist = ctx.MeasSolutions.Where(t => t.WindTurbineID == WindTurbineID).ToList();
                    // 测量定义
                    var measlist = ctx.MeasDefinitions.Where(t => t.WindTurbineID == WindTurbineID).ToList();

                    var result = new List<MeasSolutionResponseDTO>();

                    foreach (var solutionGroup in measSolutionlist.GroupBy(k => k.MeasSolutionID))
                    {
                        var firstSolution = solutionGroup.First();
                        var responseDto = new WTCMSLive.WebSite.Core.DTOs.MeasSolutionResponseDTO
                        {
                            MeasSolutionID = firstSolution.MeasSolutionID,
                            MeasSolutionName = firstSolution.MeasSolutionName,
                            MeasSolutionType = firstSolution.MeasSolutionType,
                            WindTurbineID = firstSolution.WindTurbineID,
                            DaqInterval = firstSolution.DaqInterval,
                            EigenInterval = firstSolution.EigenInterval,
                            WaveInterval = firstSolution.WaveInterval,
                            //TriggeringCondition = firstSolution.TriggeringCondition?.ToString(),
                            //TriggeringOperator = firstSolution.TriggeringOperator,
                            //TriggeringNum = firstSolution.TriggeringNum ?? 0,
                            MeasDefinitions = measlist.Where(t => solutionGroup.Select(s => s.MeasDefinitionID).Contains(t.MeasDefinitionID))
                                .Select(t => new WTCMSLive.WebSite.Core.DTOs.MeasDefinitionInfo
                                {
                                    Key = t.MeasDefinitionName,
                                    Value = t.MeasDefinitionID
                                }).ToList()
                        };

                        result.Add(responseDto);
                    }

                    return Ok(result);
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetMeasSolutionList]获取测量方案列表失败", ex);
                return Ok(ApiResponse<string>.Error("获取测量方案列表失败: " + ex.Message));
            }
        }

        #endregion

        #region 工况测量定义

        /// <summary>
        /// 批量添加工况测量定义
        /// </summary>
        /// <param name="request">批量添加请求</param>
        /// <returns></returns>
        [HttpPost("BatchAddWorkCondMeasd")]
        [BatchOperation(nameof(BatchAddWorkCondMeasdFun))]
        public IActionResult BatchAddWorkCondMeasd([FromBody] BatchAddWorkCondMeasdRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));

            // 执行原有的添加逻辑
            return ExecuteWorkCondMeasdOperation(request.SourceData, "add");
        }

        /// <summary>
        /// 批量编辑工况测量定义
        /// </summary>
        /// <param name="request">批量编辑请求</param>
        /// <returns></returns>
        [HttpPost("BatchEditWorkCondMeasd")]
        [BatchOperation(nameof(BatchEditWorkCondMeasdFun))]
        public IActionResult BatchEditWorkCondMeasd([FromBody] BatchEditWorkCondMeasdRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));

            // 执行原有的编辑逻辑
            return ExecuteWorkCondMeasdOperation(request.SourceData, "edit");
        }

        /// <summary>
        /// 批量删除工况测量定义
        /// </summary>
        /// <param name="request">批量删除请求</param>
        /// <returns></returns>
        [HttpPost("BatchDeleteWorkCondMeasd")]
        [BatchOperation(nameof(BatchDeleteWorkCondMeasdFun))]
        public IActionResult BatchDeleteWorkCondMeasd([FromBody] BatchDeleteWorkCondMeasdRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));

            // 转换删除DTO为工况测量定义DTO
            var dtos = request.SourceData.Select(d => new WorkCondMeasdWaveDTO
            {
                WindTurbineID = d.WindTurbineID,
                DauID = d.DauID,
                MeasDefinitionID = d.MeasDefinitionID,
                measLocId = d.measLocId
            }).ToList();

            return ExecuteWorkCondMeasdOperation(dtos, "delete");
        }

        /// <summary>
        /// 批量添加/编辑/删除工况测量定义，操作原子性，全部成功或全部失败
        /// </summary>
        /// <param name="dtos">批量工况测量定义DTO</param>
        /// <param name="operation">操作类型：add/edit/delete</param>
        /// <returns></returns>
        [HttpPost("BatchWorkCondMeasd")]
        [Obsolete("请使用BatchAddWorkCondMeasd、BatchEditWorkCondMeasd或BatchDeleteWorkCondMeasd接口")]
        public IActionResult BatchWorkCondMeasd([FromBody] List<WorkCondMeasdWaveDTO> dtos, [FromQuery] string operation)
        {
            return ExecuteWorkCondMeasdOperation(dtos, operation);
        }

        /// <summary>
        /// 执行工况测量定义操作的内部方法
        /// </summary>
        /// <param name="dtos">工况测量定义DTO列表</param>
        /// <param name="operation">操作类型</param>
        /// <returns></returns>
        private IActionResult ExecuteWorkCondMeasdOperation(List<WorkCondMeasdWaveDTO> dtos, string operation)
        {
            if (dtos == null || dtos.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            // 事务保证原子性
            using (var scope = new System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, new System.Transactions.TransactionOptions { IsolationLevel = System.Transactions.IsolationLevel.ReadCommitted }, System.Transactions.TransactionScopeAsyncFlowOption.Enabled))
            {
                try
                {
                    foreach (var dto in dtos)
                    {
                        if (operation == "add" || operation == "edit")
                        {
                            // 添加或编辑逻辑一致，原方法已兼容
                            MeasDefinitionManagement.SetWorkCondMeasdefinition(dto.WindTurbineID, dto.MeasDefinitionID, dto.measLocId, (int)dto.UpperLimitFreqency, (int)dto.SampleLength);
                            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                            {
                                if (dto.EvAvg == true)
                                {
                                    var wkev = ctx.ProcessEvConfs.FirstOrDefault(t => t.WindTurbineID == dto.WindTurbineID && t.MeasDefinitionID == dto.MeasDefinitionID && t.MeasLocationID == dto.measLocId);
                                    if (wkev == null)
                                    {
                                        int maxIndex = 0;
                                        try { maxIndex = ctx.ProcessEvConfs.Max(t => t.EvId); } catch { }
                                        ctx.ProcessEvConfs.Add(new MeasDef_Ev_Process()
                                        {
                                            WindTurbineID = dto.WindTurbineID,
                                            MeasDefinitionID = dto.MeasDefinitionID,
                                            MeasLocationID = dto.measLocId,
                                            Type = EnumEigenvalueName.Enum_WorkEnv,
                                            Name = EnumEigenvalueName.Enum_WorkEnv.ToString(),
                                            EvId = maxIndex + 1,
                                        });
                                    }
                                }
                                else
                                {
                                    var ev = ctx.ProcessEvConfs.FirstOrDefault(t => t.WindTurbineID == dto.WindTurbineID && t.MeasDefinitionID == dto.MeasDefinitionID && t.MeasLocationID == dto.measLocId);
                                    if (ev != null)
                                    {
                                        ctx.ProcessEvConfs.Remove(ev);
                                    }
                                }
                                ctx.SaveChanges();
                            }
                            DauManagement.UpdateMeasDefVersion(dto.WindTurbineID, dto.DauID);
                        }
                        else if (operation == "delete")
                        {
                            // 删除工况配置
                            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                            {
                                ctx.MDFWorkConditions.RemoveRange(ctx.MDFWorkConditions.Where(item => item.WindTurbineID == dto.WindTurbineID && item.MeasDefinitionID == dto.MeasDefinitionID && item.MeasLocationID == dto.measLocId));
                                ctx.SaveChanges();
                            }
                            // 删除特征值配置
                            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                            {
                                var ev = ctx.ProcessEvConfs.FirstOrDefault(t => t.WindTurbineID == dto.WindTurbineID && t.MeasDefinitionID == dto.MeasDefinitionID && t.MeasLocationID == dto.measLocId);
                                if (ev != null)
                                {
                                    ctx.ProcessEvConfs.Remove(ev);
                                    ctx.SaveChanges();
                                }
                            }
                            DauManagement.UpdateMeasDefVersion(dto.WindTurbineID, dto.DauID);
                        }
                        else
                        {
                            return Ok(ApiResponse<string>.Error("不支持的操作类型" + operation));
                        }
                    }
                    scope.Complete();
                    return Ok(ApiResponse<string>.Success("OK"));
                }
                catch (Exception ex)
                {
                    // 事务回滚
                    //return Ok(string.Format(message, 0, ex.Message));
                    return Ok(ApiResponse<string>.Error(ex.Message));
                }
            }
        }

        /// <summary>
        /// 批量添加工况测量定义的批量操作实现
        /// </summary>
        /// <param name="request">批量添加请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddWorkCondMeasdFun(string targetTurbineId, BatchAddWorkCondMeasdRequest request)
        {
            var result = new List<string>();

            foreach (var sourceData in request.SourceData)
            {
                try
                {
                    var curmeasdlist = MeasDefinitionManagement.GetMeasdefinition(sourceData.WindTurbineID, sourceData.MeasDefinitionID);
                    if (curmeasdlist == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量定义");
                        continue;
                    }
                    var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasdlist.MeasDefinitionName);
                    if (tarmeasd == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量定义{curmeasdlist.MeasDefinitionName}");
                        continue;
                    }

                    // 创建目标机组的工况测量定义
                    //var targetData = await MapWorkCondMeasdToTargetTurbine(sourceData, targetTurbineId);
                    var sourceMeasLoc = DevTreeManagement.GetWorkCondMeasLocation(sourceData.measLocId);
                    if (sourceMeasLoc == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量位置{sourceData.measLocId}");
                        continue;
                    }

                    // 在目标机组中查找同名的测量位置
                    var targetMeasLocList = DevTreeManagement.GetWorkCondMeasLocByTurID(targetTurbineId);
                    var targetMeasLoc = targetMeasLocList.FirstOrDefault(m => m.MeasLocName == sourceMeasLoc.MeasLocName);

                    if (targetMeasLoc == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量位置{sourceMeasLoc.MeasLocName}");
                        continue;
                    }

                    // 创建目标数据
                    var targetData = new WorkCondMeasdWaveDTO
                    {
                        WindTurbineID = targetTurbineId,
                        DauID = sourceData.DauID,
                        MeasDefinitionID = tarmeasd.MeasDefinitionID,
                        measLocId = targetMeasLoc.MeasLocationID,
                        UpperLimitFreqency = sourceData.UpperLimitFreqency,
                        SampleLength = sourceData.SampleLength,
                        EvAvg = sourceData.EvAvg
                    };


                    if (targetData != null)
                    {
                        var addResult = ExecuteWorkCondMeasdOperation(new List<WorkCondMeasdWaveDTO> { targetData }, "add");
                        if (addResult is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                        {
                            if (apiResponse.Code == 1)
                            {
                                result.Add($"机组 {targetTurbineId}: 成功添加工况测量定义 {sourceData.MeasDefinitionID}");
                            }
                            else
                            {
                                result.Add($"机组 {targetTurbineId}: 添加工况测量定义失败 - {apiResponse.Msg}");
                            }
                        }
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 无法映射工况测量定义 {sourceData.MeasDefinitionID} - 测量位置不匹配");
                    }
                }
                catch (Exception ex)
                {
                    result.Add($"机组 {targetTurbineId}: 添加工况测量定义失败 - {ex.Message}");
                    CMSFramework.Logger.Logger.LogErrorMessage($"[BatchAddWorkCondMeasdBatch]添加工况测量定义失败 机组ID:{targetTurbineId}", ex);
                }
            }
            return result;
        }

        /// <summary>
        /// 批量编辑工况测量定义的批量操作实现
        /// </summary>
        /// <param name="request">批量编辑请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchEditWorkCondMeasdFun(string targetTurbineId, BatchEditWorkCondMeasdRequest request)
        {
            var result = new List<string>();

            foreach (var sourceData in request.SourceData)
            {
                try
                {
                    var curmeasdlist = MeasDefinitionManagement.GetMeasdefinition(sourceData.WindTurbineID, sourceData.MeasDefinitionID);
                    if (curmeasdlist == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量定义");
                        continue;
                    }
                    var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasdlist.MeasDefinitionName);
                    if (tarmeasd == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量定义{curmeasdlist.MeasDefinitionName}");
                        continue;
                    }

                    // 创建目标机组的工况测量定义
                    //var targetData = await MapWorkCondMeasdToTargetTurbine(sourceData, targetTurbineId);
                    var sourceMeasLoc = DevTreeManagement.GetWorkCondMeasLocation(sourceData.measLocId);
                    if (sourceMeasLoc == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量位置{sourceData.measLocId}");
                        continue;
                    }

                    // 在目标机组中查找同名的测量位置
                    var targetMeasLocList = DevTreeManagement.GetWorkCondMeasLocByTurID(targetTurbineId);
                    var targetMeasLoc = targetMeasLocList.FirstOrDefault(m => m.MeasLocName == sourceMeasLoc.MeasLocName);

                    if (targetMeasLoc == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量位置{sourceMeasLoc.MeasLocName}");
                        continue;
                    }

                    // 创建目标数据
                    var targetData = new WorkCondMeasdWaveDTO
                    {
                        WindTurbineID = targetTurbineId,
                        DauID = sourceData.DauID,
                        MeasDefinitionID = tarmeasd.MeasDefinitionID,
                        measLocId = targetMeasLoc.MeasLocationID,
                        UpperLimitFreqency = sourceData.UpperLimitFreqency,
                        SampleLength = sourceData.SampleLength,
                        EvAvg = sourceData.EvAvg
                    };

                    if (targetData != null)
                    {
                        var editResult = ExecuteWorkCondMeasdOperation(new List<WorkCondMeasdWaveDTO> { targetData }, "edit");
                        if (editResult is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                        {
                            if (apiResponse.Code == 1)
                            {
                                result.Add($"机组 {targetTurbineId}: 成功编辑工况测量定义 {sourceData.MeasDefinitionID}");
                            }
                            else
                            {
                                result.Add($"机组 {targetTurbineId}: 编辑工况测量定义失败 - {apiResponse.Msg}");
                            }
                        }
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 无法映射工况测量定义 {sourceData.MeasDefinitionID} - 测量位置不匹配");
                    }
                }
                catch (Exception ex)
                {
                    result.Add($"机组 {targetTurbineId}: 编辑工况测量定义失败 - {ex.Message}");
                    CMSFramework.Logger.Logger.LogErrorMessage($"[BatchEditWorkCondMeasdBatch]编辑工况测量定义失败 机组ID:{targetTurbineId}", ex);
                }
            }

            return result;
        }

        /// <summary>
        /// 批量删除工况测量定义的批量操作实现
        /// </summary>
        /// <param name="request">批量删除请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteWorkCondMeasdFun(string targetTurbineId, BatchDeleteWorkCondMeasdRequest request)
        {
            var result = new List<string>();

            foreach (var sourceDeleteData in request.SourceData)
            {
                try
                {
                    var curmeasdlist = MeasDefinitionManagement.GetMeasdefinition(sourceDeleteData.WindTurbineID, sourceDeleteData.MeasDefinitionID);
                    if (curmeasdlist == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量定义");
                        continue;
                    }
                    var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasdlist.MeasDefinitionName);
                    if (tarmeasd == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量定义{curmeasdlist.MeasDefinitionName}");
                        continue;
                    }

                    // 创建目标机组的工况测量定义
                    //var targetData = await MapWorkCondMeasdToTargetTurbine(sourceData, targetTurbineId);
                    var sourceMeasLoc = DevTreeManagement.GetWorkCondMeasLocation(sourceDeleteData.measLocId);
                    if (sourceMeasLoc == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量位置{sourceDeleteData.measLocId}");
                        continue;
                    }

                    // 在目标机组中查找同名的测量位置
                    var targetMeasLocList = DevTreeManagement.GetWorkCondMeasLocByTurID(targetTurbineId);
                    var targetMeasLoc = targetMeasLocList.FirstOrDefault(m => m.MeasLocName == sourceMeasLoc.MeasLocName);

                    if (targetMeasLoc == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量位置{sourceMeasLoc.MeasLocName}");
                        continue;
                    }


                    // 创建删除用的DTO
                    var deleteDto = new WorkCondMeasdWaveDTO
                    {
                        WindTurbineID = targetTurbineId,
                        DauID = sourceDeleteData.DauID,
                        MeasDefinitionID = tarmeasd.MeasDefinitionID,
                        measLocId = targetMeasLoc.MeasLocationID,
                    };

                    if (!string.IsNullOrEmpty(deleteDto.measLocId))
                    {
                        var deleteResult = ExecuteWorkCondMeasdOperation(new List<WorkCondMeasdWaveDTO> { deleteDto }, "delete");
                        if (deleteResult is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                        {
                            if (apiResponse.Code == 1)
                            {
                                result.Add($"机组 {targetTurbineId}: 成功删除工况测量定义 {sourceDeleteData.MeasDefinitionID}");
                            }
                            else
                            {
                                result.Add($"机组 {targetTurbineId}: 删除工况测量定义失败 - {apiResponse.Msg}");
                            }
                        }
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 无法映射测量位置 {sourceDeleteData.measLocId}");
                    }
                }
                catch (Exception ex)
                {
                    result.Add($"机组 {targetTurbineId}: 删除工况测量定义失败 - {ex.Message}");
                    CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteWorkCondMeasdBatch]删除工况测量定义失败 机组ID:{targetTurbineId}", ex);
                }
            }
            return result;
        }

        #endregion

        #region 转速波形定于

        /// <summary>
        /// 批量添加转速波形定义
        /// </summary>
        /// <param name="request">批量添加请求</param>
        /// <returns></returns>
        [HttpPost("BatchAddWorkCondSpdMeasd")]
        [BatchOperation(nameof(BatchAddWorkCondSpdMeasdFun))]
        public IActionResult BatchAddWorkCondSpdMeasd([FromBody] BatchAddWorkCondSpdMeasdRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));

            // 执行原有的添加逻辑
            return ExecuteWorkCondSpdMeasdOperation(request.SourceData, "add");
        }

        /// <summary>
        /// 批量编辑转速波形定义
        /// </summary>
        /// <param name="request">批量编辑请求</param>
        /// <returns></returns>
        [HttpPost("BatchEditWorkCondSpdMeasd")]
        [BatchOperation(nameof(BatchEditWorkCondSpdMeasdFun))]
        public IActionResult BatchEditWorkCondSpdMeasd([FromBody] BatchEditWorkCondSpdMeasdRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));

            // 执行原有的编辑逻辑
            return ExecuteWorkCondSpdMeasdOperation(request.SourceData, "edit");
        }

        /// <summary>
        /// 批量删除转速波形定义
        /// </summary>
        /// <param name="request">批量删除请求</param>
        /// <returns></returns>
        [HttpPost("BatchDeleteWorkCondSpdMeasd")]
        [BatchOperation(nameof(BatchDeleteWorkCondSpdMeasdFun))]
        public IActionResult BatchDeleteWorkCondSpdMeasd([FromBody] BatchDeleteWorkCondSpdMeasdRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));

            // 转换删除DTO为转速波形定义DTO
            var dtos = request.SourceData.Select(d => new WorkCondMeasdWaveDTO
            {
                WindTurbineID = d.WindTurbineID,
                DauID = d.DauID,
                MeasDefinitionID = d.MeasDefinitionID,
                measLocId = d.measLocId
            }).ToList();

            return ExecuteWorkCondSpdMeasdOperation(dtos, "delete");
        }

        /// <summary>
        /// 批量新增/编辑/删除转速波形定义，操作原子性，全部成功或全部失败
        /// </summary>
        /// <param name="dtos">批量DTO</param>
        /// <param name="operation">add/edit/delete</param>
        /// <returns></returns>
        [HttpPost("BatchWorkCondSpdMeasd")]
        [Obsolete("请使用BatchAddWorkCondSpdMeasd、BatchEditWorkCondSpdMeasd或BatchDeleteWorkCondSpdMeasd接口")]
        public IActionResult BatchWorkCondSpdMeasd([FromBody] List<WorkCondMeasdWaveDTO> dtos, [FromQuery] string operation)
        {
            return ExecuteWorkCondSpdMeasdOperation(dtos, operation);
        }

        /// <summary>
        /// 执行转速波形定义操作的内部方法
        /// </summary>
        /// <param name="dtos">转速波形定义DTO列表</param>
        /// <param name="operation">操作类型</param>
        /// <returns></returns>
        private IActionResult ExecuteWorkCondSpdMeasdOperation(List<WorkCondMeasdWaveDTO> dtos, string operation)
        {
            if (dtos == null || dtos.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            using (var scope = new System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, new System.Transactions.TransactionOptions { IsolationLevel = System.Transactions.IsolationLevel.ReadCommitted }, System.Transactions.TransactionScopeAsyncFlowOption.Enabled))
            {
                try
                {
                    foreach (var dto in dtos)
                    {
                        if (operation == "add" || operation == "edit")
                        {
                            var rotSpdLocList = DevTreeManagement.GetRotSpdMeasLocListByTurId(dto.WindTurbineID);
                            if (rotSpdLocList.Count > 0)
                            {
                                using (var ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                                {
                                    var wdf = ctx.MDFWaveDefRotSpds.FirstOrDefault(item => item.WindTurbineID == dto.WindTurbineID && item.MeasDefinitionID == dto.MeasDefinitionID);
                                    if (wdf == null)
                                    {
                                        WaveDef_RotSpd waveDefRotSpd = new WaveDef_RotSpd()
                                        {
                                            WindTurbineID = dto.WindTurbineID,
                                            MeasDefinitionID = dto.MeasDefinitionID,
                                            MeasLoc_RotSpdID = rotSpdLocList[0].MeasLocationID,
                                            LineCounts = (int)dto.UpperLimitFreqency // WaveLineCounts
                                        };
                                        ctx.MDFWaveDefRotSpds.Add(waveDefRotSpd);
                                    }
                                    else
                                    {
                                        wdf.LineCounts = (int)dto.UpperLimitFreqency; // WaveLineCounts
                                    }

                                    // 特征值配置，平均值
                                    if (dto.EvAvg != null && dto.EvAvg == true)
                                    {
                                        var wkev = ctx.ProcessEvConfs.FirstOrDefault(t => t.WindTurbineID == dto.WindTurbineID && t.MeasDefinitionID == dto.MeasDefinitionID && t.MeasLocationID == dto.measLocId);
                                        if (wkev == null)
                                        {
                                            ctx.ProcessEvConfs.Add(new MeasDef_Ev_Process()
                                            {
                                                WindTurbineID = dto.WindTurbineID,
                                                MeasDefinitionID = dto.MeasDefinitionID,
                                                MeasLocationID = dto.measLocId,
                                                Type = EnumEigenvalueName.Enum_SpdMean,
                                                Name = EnumEigenvalueName.Enum_SpdMean.ToString(),
                                            });
                                        }
                                    }
                                    else
                                    {
                                        var ev = ctx.ProcessEvConfs.FirstOrDefault(t => t.WindTurbineID == dto.WindTurbineID && t.MeasDefinitionID == dto.MeasDefinitionID && t.MeasLocationID == dto.measLocId);
                                        if (ev != null)
                                        {
                                            ctx.ProcessEvConfs.Remove(ev);
                                        }
                                    }
                                    ctx.SaveChanges();
                                }
                                DauManagement.UpdateMeasDefVersion(dto.WindTurbineID.ToString(), dto.DauID);
                            }
                            else
                            {
                                throw new Exception("转速测点未定义");
                            }
                        }
                        else if (operation == "delete")
                        {
                            using (var ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                            {
                                ctx.MDFWaveDefRotSpds.Remove(ctx.MDFWaveDefRotSpds.FirstOrDefault(t => t.WindTurbineID == dto.WindTurbineID && t.MeasDefinitionID == dto.MeasDefinitionID && t.MeasLoc_RotSpdID == dto.measLocId));
                                // 删除转速 平均值特征值
                                var evAvg = ctx.ProcessEvConfs.FirstOrDefault(t => t.WindTurbineID == dto.WindTurbineID && t.MeasDefinitionID == dto.MeasDefinitionID && t.MeasLocationID == dto.measLocId);
                                if (evAvg != null)
                                {
                                    ctx.ProcessEvConfs.Remove(evAvg);
                                }
                                ctx.SaveChanges();
                            }
                            DauManagement.UpdateMeasDefVersion(dto.WindTurbineID.ToString(), dto.DauID);
                        }
                        else
                        {
                            return Ok(ApiResponse<string>.Error("不支持的操作类型" + operation));
                        }
                    }
                    scope.Complete();
                    return Ok(ApiResponse<string>.Success("OK"));
                }
                catch (Exception ex)
                {
                    return Ok(ApiResponse<string>.Error(ex.Message));
                }
            }
        }

        /// <summary>
        /// 批量添加转速波形定义的批量操作实现
        /// </summary>
        /// <param name="request">批量添加请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddWorkCondSpdMeasdFun(string targetTurbineId, BatchAddWorkCondSpdMeasdRequest request)
        {
            var result = new List<string>();

            foreach (var sourceData in request.SourceData)
            {
                try
                {
                    var curmeasdlist = MeasDefinitionManagement.GetMeasdefinition(sourceData.WindTurbineID, sourceData.MeasDefinitionID);
                    if (curmeasdlist == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量定义");
                        continue;
                    }
                    var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasdlist.MeasDefinitionName);
                    if (tarmeasd == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量定义{curmeasdlist.MeasDefinitionName}");
                        continue;
                    }

                    // 创建目标机组的工况测量定义
                    var targetMeasLocID = sourceData.measLocId.Replace(sourceData.WindTurbineID, targetTurbineId);
                    var targetMeasLoc = DevTreeManagement.GetRotSpdMeasLocation(targetMeasLocID);
                    if (targetMeasLoc == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量位置{targetMeasLocID}");
                        continue;
                    }

                    // 在目标机组中查找同名的测量位置
                    //var targetMeasLocList = DevTreeManagement.GetWorkCondMeasLocByTurID(targetTurbineId);
                    //var targetMeasLoc = targetMeasLocList.FirstOrDefault(m => m.MeasLocName == sourceMeasLoc.MeasLocName);

                    //if (targetMeasLoc == null)
                    //{
                    //    result.Add($"机组{targetTurbineId} 未找到测量位置{sourceMeasLoc.MeasLocName}");
                    //    continue;
                    //}

                    // 创建目标数据
                    var targetData = new WorkCondMeasdWaveDTO
                    {
                        WindTurbineID = targetTurbineId,
                        DauID = sourceData.DauID, // 保持相同的DAU ID
                        MeasDefinitionID = tarmeasd.MeasDefinitionID,
                        measLocId = targetMeasLoc.MeasLocationID, // 使用目标机组的转速测量位置
                        UpperLimitFreqency = sourceData.UpperLimitFreqency, // 转速波形中这个字段表示LineCounts
                        SampleLength = sourceData.SampleLength,
                        EvAvg = sourceData.EvAvg
                    };
                    if (targetData != null)
                    {
                        var addResult = ExecuteWorkCondSpdMeasdOperation(new List<WorkCondMeasdWaveDTO> { targetData }, "add");
                        if (addResult is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                        {
                            if (apiResponse.Code == 1)
                            {
                                result.Add($"机组 {targetTurbineId}: 成功添加转速波形定义 {sourceData.MeasDefinitionID}");
                            }
                            else
                            {
                                result.Add($"机组 {targetTurbineId}: 添加转速波形定义失败 - {apiResponse.Msg}");
                            }
                        }
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 无法映射转速波形定义 {sourceData.MeasDefinitionID} - 转速测量位置不存在");
                    }
                }
                catch (Exception ex)
                {
                    result.Add($"机组 {targetTurbineId}: 添加转速波形定义失败 - {ex.Message}");
                    CMSFramework.Logger.Logger.LogErrorMessage($"[BatchAddWorkCondSpdMeasdBatch]添加转速波形定义失败 机组ID:{targetTurbineId}", ex);
                }
            }

            return result;
        }

        /// <summary>
        /// 批量编辑转速波形定义的批量操作实现
        /// </summary>
        /// <param name="request">批量编辑请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchEditWorkCondSpdMeasdFun(string targetTurbineId, BatchEditWorkCondSpdMeasdRequest request)
        {
            var result = new List<string>();

            foreach (var sourceData in request.SourceData)
            {
                try
                {
                    // 创建目标机组的转速波形定义
                    var curmeasdlist = MeasDefinitionManagement.GetMeasdefinition(sourceData.WindTurbineID, sourceData.MeasDefinitionID);
                    if (curmeasdlist == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量定义");
                        continue;
                    }
                    var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasdlist.MeasDefinitionName);
                    if (tarmeasd == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量定义{curmeasdlist.MeasDefinitionName}");
                        continue;
                    }

                    // 创建目标机组的工况测量定义
                    var targetMeasLocID = sourceData.measLocId.Replace(sourceData.WindTurbineID, targetTurbineId);
                    var targetMeasLoc = DevTreeManagement.GetRotSpdMeasLocation(targetMeasLocID);
                    if (targetMeasLoc == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量位置{targetMeasLocID}");
                        continue;
                    }

                    // 创建目标数据
                    var targetData = new WorkCondMeasdWaveDTO
                    {
                        WindTurbineID = targetTurbineId,
                        DauID = sourceData.DauID, // 保持相同的DAU ID
                        MeasDefinitionID = tarmeasd.MeasDefinitionID,
                        measLocId = targetMeasLoc.MeasLocationID, // 使用目标机组的转速测量位置
                        UpperLimitFreqency = sourceData.UpperLimitFreqency, // 转速波形中这个字段表示LineCounts
                        SampleLength = sourceData.SampleLength,
                        EvAvg = sourceData.EvAvg
                    };
                    if (targetData != null)
                    {
                        var editResult = ExecuteWorkCondSpdMeasdOperation(new List<WorkCondMeasdWaveDTO> { targetData }, "edit");
                        if (editResult is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                        {
                            if (apiResponse.Code == 1)
                            {
                                result.Add($"机组 {targetTurbineId}: 成功编辑转速波形定义 {sourceData.MeasDefinitionID}");
                            }
                            else
                            {
                                result.Add($"机组 {targetTurbineId}: 编辑转速波形定义失败 - {apiResponse.Msg}");
                            }
                        }
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 无法映射转速波形定义 {sourceData.MeasDefinitionID} - 转速测量位置不存在");
                    }
                }
                catch (Exception ex)
                {
                    result.Add($"机组 {targetTurbineId}: 编辑转速波形定义失败 - {ex.Message}");
                    CMSFramework.Logger.Logger.LogErrorMessage($"[BatchEditWorkCondSpdMeasdBatch]编辑转速波形定义失败 机组ID:{targetTurbineId}", ex);
                }
            }

            return result;
        }

        /// <summary>
        /// 批量删除转速波形定义的批量操作实现
        /// </summary>
        /// <param name="request">批量删除请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteWorkCondSpdMeasdFun(string targetTurbineId, BatchDeleteWorkCondSpdMeasdRequest request)
        {
            var result = new List<string>();

            foreach (var sourceDeleteData in request.SourceData)
            {
                try
                {
                    // 获取目标机组的转速测量位置
                    //var rotSpdLocList = DevTreeManagement.GetRotSpdMeasLocListByTurId(targetTurbineId);

                    var curmeasdlist = MeasDefinitionManagement.GetMeasdefinition(sourceDeleteData.WindTurbineID, sourceDeleteData.MeasDefinitionID);
                    if (curmeasdlist == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量定义");
                        continue;
                    }
                    var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasdlist.MeasDefinitionName);
                    if (tarmeasd == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量定义{curmeasdlist.MeasDefinitionName}");
                        continue;
                    }

                    // 创建目标机组的工况测量定义
                    var targetMeasLocID = sourceDeleteData.measLocId.Replace(sourceDeleteData.WindTurbineID, targetTurbineId);
                    var targetMeasLoc = DevTreeManagement.GetRotSpdMeasLocation(targetMeasLocID);
                    if (targetMeasLoc == null)
                    {
                        result.Add($"机组{targetTurbineId} 未找到测量位置{targetMeasLocID}");
                        continue;
                    }

                    // 创建目标数据
                    var targetData = new WorkCondMeasdWaveDTO
                    {
                        WindTurbineID = targetTurbineId,
                        DauID = sourceDeleteData.DauID, // 保持相同的DAU ID
                        MeasDefinitionID = tarmeasd.MeasDefinitionID,
                        measLocId = targetMeasLoc.MeasLocationID,
                    };
                    if (targetData != null)
                    {

                        var deleteResult = ExecuteWorkCondSpdMeasdOperation(new List<WorkCondMeasdWaveDTO> { targetData }, "delete");
                        if (deleteResult is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                        {
                            if (apiResponse.Code == 1)
                            {
                                result.Add($"机组 {targetTurbineId}: 成功删除转速波形定义 {sourceDeleteData.MeasDefinitionID}");
                            }
                            else
                            {
                                result.Add($"机组 {targetTurbineId}: 删除转速波形定义失败 - {apiResponse.Msg}");
                            }
                        }
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 转速测量位置不存在");
                    }
                }
                catch (Exception ex)
                {
                    result.Add($"机组 {targetTurbineId}: 删除转速波形定义失败 - {ex.Message}");
                    CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteWorkCondSpdMeasdBatch]删除转速波形定义失败 机组ID:{targetTurbineId}", ex);
                }
            }

            return result;
        }

        #endregion

        #region 智能映射方法

        /// <summary>
        /// 将工况测量定义映射到目标机组
        /// </summary>
        /// <param name="sourceData">源数据</param>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <returns></returns>
        private async Task<WorkCondMeasdWaveDTO> MapWorkCondMeasdToTargetTurbine(WorkCondMeasdWaveDTO sourceData, string targetTurbineId)
        {
            try
            {
                // 获取源机组的测量位置信息
                var sourceMeasLoc = DevTreeManagement.GetWorkCondMeasLocation(sourceData.measLocId);
                if (sourceMeasLoc == null)
                    return null;

                // 在目标机组中查找同名的测量位置
                var targetMeasLocList = DevTreeManagement.GetWorkCondMeasLocByTurID(targetTurbineId);
                var targetMeasLoc = targetMeasLocList.FirstOrDefault(m => m.MeasLocName == sourceMeasLoc.MeasLocName);

                if (targetMeasLoc == null)
                    return null;

                // 创建目标数据
                var targetData = new WorkCondMeasdWaveDTO
                {
                    WindTurbineID = targetTurbineId,
                    DauID = sourceData.DauID, // 保持相同的DAU ID
                    MeasDefinitionID = sourceData.MeasDefinitionID,
                    measLocId = targetMeasLoc.MeasLocationID,
                    UpperLimitFreqency = sourceData.UpperLimitFreqency,
                    SampleLength = sourceData.SampleLength,
                    EvAvg = sourceData.EvAvg
                };

                return targetData;
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[MapWorkCondMeasdToTargetTurbine]映射工况测量定义失败 源机组:{sourceData.WindTurbineID} 目标机组:{targetTurbineId}", ex);
                return null;
            }
        }

        /// <summary>
        /// 将转速波形定义映射到目标机组
        /// </summary>
        /// <param name="sourceData">源数据</param>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <returns></returns>
        private async Task<WorkCondMeasdWaveDTO> MapWorkCondSpdMeasdToTargetTurbine(WorkCondMeasdWaveDTO sourceData, string targetTurbineId)
        {
            try
            {
                // 获取目标机组的转速测量位置
                var rotSpdLocList = DevTreeManagement.GetRotSpdMeasLocListByTurId(targetTurbineId);
                if (rotSpdLocList.Count == 0)
                    return null;

                // 创建目标数据
                var targetData = new WorkCondMeasdWaveDTO
                {
                    WindTurbineID = targetTurbineId,
                    DauID = sourceData.DauID, // 保持相同的DAU ID
                    MeasDefinitionID = sourceData.MeasDefinitionID,
                    measLocId = rotSpdLocList[0].MeasLocationID, // 使用目标机组的转速测量位置
                    UpperLimitFreqency = sourceData.UpperLimitFreqency, // 转速波形中这个字段表示LineCounts
                    SampleLength = sourceData.SampleLength,
                    EvAvg = sourceData.EvAvg
                };

                return targetData;
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[MapWorkCondSpdMeasdToTargetTurbine]映射转速波形定义失败 源机组:{sourceData.WindTurbineID} 目标机组:{targetTurbineId}", ex);
                return null;
            }
        }

        /// <summary>
        /// 将测量位置映射到目标机组
        /// </summary>
        /// <param name="sourceMeasLocId">源测量位置ID</param>
        /// <param name="sourceTurbineId">源机组ID</param>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <returns></returns>
        private async Task<string> MapMeasLocationToTargetTurbine(string sourceMeasLocId, string sourceTurbineId, string targetTurbineId)
        {
            try
            {
                // 获取源测量位置信息
                var sourceMeasLoc = DevTreeManagement.GetWorkCondMeasLocation(sourceMeasLocId);
                if (sourceMeasLoc == null)
                    return null;

                // 在目标机组中查找同名的测量位置
                var targetMeasLocList = DevTreeManagement.GetWorkCondMeasLocByTurID(targetTurbineId);
                var targetMeasLoc = targetMeasLocList.FirstOrDefault(m => m.MeasLocName == sourceMeasLoc.MeasLocName);

                return targetMeasLoc?.MeasLocationID;
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[MapMeasLocationToTargetTurbine]映射测量位置失败 源位置:{sourceMeasLocId} 源机组:{sourceTurbineId} 目标机组:{targetTurbineId}", ex);
                return null;
            }
        }

        #endregion

        /// <summary>
        /// 跨机组批量删除触发采集规则
        /// </summary>
        /// <param name="request">批量删除请求</param>
        /// <returns></returns>
        [HttpPost("BatchDeleteTriggerGatherDispose")]
        [BatchOperation(nameof(BatchDeleteTriggerGatherDisposeFun))]
        public IActionResult BatchDeleteTriggerGatherDisposeCrossTurbine([FromBody] BatchDeleteTriggerGatherDisposeRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));

            // 执行原有的删除逻辑
            return BatchDeleteTriggerGatherDispose(request.SourceData);
        }

        /// <summary>
        /// 批量删除触发采集规则，操作原子性，全部成功或全部失败
        /// </summary>
        /// <param name="dtos">批量删除DTO</param>
        /// <returns></returns>
        [HttpPost("BatchDeleteTrigger")]
        public IActionResult BatchDeleteTriggerGatherDispose([FromBody] List<TriggerAcquisitionTOD> dtos)
        {
            if (dtos == null || dtos.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            using (var scope = new System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, new System.Transactions.TransactionOptions { IsolationLevel = System.Transactions.IsolationLevel.ReadCommitted }, System.Transactions.TransactionScopeAsyncFlowOption.Enabled))
            {
                try
                {
                    foreach (var dto in dtos)
                    {
                        // 1. 获取测量定义ID
                        List<MeasDefinition> mdfModelsList = null;
                        using (var ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                            mdfModelsList = ctx.MeasDefinitions.Where(item => item.WindTurbineID == dto.TurbineID && item.MeasDefinitionName == dto.TriggerMeasDefName).ToList();
                        }
                        if (mdfModelsList == null || mdfModelsList.Count == 0)
                            throw new Exception($"未找到测量定义: {dto.TriggerMeasDefName}");
                        string MeasDefID = mdfModelsList[0].MeasDefinitionID;
                        // 2. 获取触发规则ID
                        List<MeasTriggerRuleDef> measTriggerDefsList = null;
                        using (var ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                            measTriggerDefsList = ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == dto.TurbineID && item.MeasDefinitionID == MeasDefID && item.RuleName == dto.TriggerRuleName).ToList();
                        }
                        if (measTriggerDefsList == null || measTriggerDefsList.Count == 0)
                            throw new Exception($"未找到触发规则: {dto.TriggerRuleName}");
                        string TriggerRuleID = measTriggerDefsList[0].RuleID;
                        // 3. 删除TriggerProcess
                        using (var ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                            ctx.TriggerProcess.RemoveRange(ctx.TriggerProcess.Where(item => item.WindTurbineID == dto.TurbineID && item.RuleID == TriggerRuleID));
                            ctx.SaveChanges();
                        }
                        // 4. 删除TriggerRuleDefs
                        using (var ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                            ctx.TriggerRuleDefs.RemoveRange(ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == dto.TurbineID && item.MeasDefinitionID == MeasDefID && item.RuleName == dto.TriggerRuleName));
                            ctx.SaveChanges();
                        }
                        // 5. 删除ExecuteMdfs
                        using (var ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                            ctx.ExecuteMdfs.RemoveRange(ctx.ExecuteMdfs.Where(item => item.WindTurbineID == dto.TurbineID && item.RuleID == TriggerRuleID));
                            ctx.SaveChanges();
                        }
                        // 6. 删除TriggerTimes
                        using (var ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                        {
                            ctx.TriggerTimes.RemoveRange(ctx.TriggerTimes.Where(item => item.RuleID == TriggerRuleID));
                            ctx.SaveChanges();
                        }
                        // 7. 更新DAU版本
                        if (!string.IsNullOrEmpty(dto.dauid))
                        {
                            DauManagement.UpdateMeasDefVersion(dto.TurbineID, dto.dauid);
                        }
                    }
                    scope.Complete();
                    return Ok(ApiResponse<string>.Success("OK"));
                }
                catch (Exception ex)
                {
                    return Ok(ApiResponse<string>.Error(ex.Message));
                }
            }
        }


        #region modbus波形定义

        /// <summary>
        /// 批量添加Modbus波形定义
        /// </summary>
        /// <param name="request">批量添加请求</param>
        /// <returns></returns>
        [HttpPost("AddModbusWave")]
        [BatchOperation(nameof(BatchAddModbusWaveFun))]
        public IActionResult BatchAddModbusWave([FromBody] BatchAddModbusWaveRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));

            // 执行原有的添加逻辑
            return ExecuteAddModbusWaveOperation(request.SourceData);
        }

        /// <summary>
        /// 执行添加Modbus波形定义操作
        /// </summary>
        /// <param name="waves">波形定义列表</param>
        /// <returns></returns>
        private IActionResult ExecuteAddModbusWaveOperation(List<WaveModbusDTO> waves)
        {
            var results = new List<string>();

            foreach (var wave in waves)
            {
                try
                {
                    var result = AddModbusWaveSingle(wave);
                    if (result is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                    {
                        if (apiResponse.Code == 1)
                        {
                            results.Add($"机组 {wave.WindTurbineID}: 成功添加波形定义 {wave.WaveDefinitionName}");
                        }
                        else
                        {
                            results.Add($"机组 {wave.WindTurbineID}: 添加波形定义失败 - {apiResponse.Msg}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    results.Add($"机组 {wave.WindTurbineID}: 添加波形定义异常 - {ex.Message}");
                }
            }

            return Ok(ApiResponse<List<string>>.Success(results));
        }

        /// <summary>
        /// 添加modbus设备波形定义（优化版本）
        /// </summary>
        /// <param name="wave"></param>
        /// <returns></returns>
        [HttpPost("AddModbusWaveV1")]
        public IActionResult AddModbusWave(WaveModbusDTO wave)
        {
            return AddModbusWaveSingle(wave);
        }

        /// <summary>
        /// 添加单个Modbus波形定义
        /// </summary>
        /// <param name="wave"></param>
        /// <returns></returns>
        private IActionResult AddModbusWaveSingle(WaveModbusDTO wave)
        {
            try
            {
                // 参数验证
                if (wave == null)
                {
                    return Ok(ApiResponse<string>.Error("波形定义参数不能为空"));
                }

                if (string.IsNullOrEmpty(wave.WindTurbineID) || string.IsNullOrEmpty(wave.MeasLocationID) ||
                    string.IsNullOrEmpty(wave.WaveDefinitionName))
                {
                    return Ok(ApiResponse<string>.Error("必填参数不能为空"));
                }

                // 查询modbus设备，通过modbus设备区分不同的测量位置类型
                var modbus = DauManagement.GetModbusDevicebyModbusDeviceID(wave.WindTurbineID, wave.ModbusDeviceID);
                if (modbus == null)
                {
                    return Ok(ApiResponse<string>.Error("modbus设备不存在"));
                }

                // 根据设备类型处理不同的波形定义
                if (IsSVMDeviceType(modbus.ModbusDevType))
                {
                    return ProcessSVMWaveDefinition(wave, modbus);
                }
                else
                {
                    return ProcessModbusWaveDefinition(wave, modbus);
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("AddModbusWave", ex);
                return Ok(ApiResponse<string>.Error($"添加波形定义失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 判断是否为SVM设备类型
        /// </summary>
        private bool IsSVMDeviceType(EnumModbusDevType deviceType)
        {
            return deviceType == EnumModbusDevType.StaticTIM ||
                   deviceType == EnumModbusDevType.StaticSVM ||
                   deviceType == EnumModbusDevType.DynamicSVM;
        }

        /// <summary>
        /// 处理SVM波形定义
        /// </summary>
        private IActionResult ProcessSVMWaveDefinition(WaveModbusDTO wave, ModbusUnit modbus)
        {
            // 查询svm测量位置定义
            var meas = SVMManagement.GetMeasLoc_SVMListByTurID(wave.WindTurbineID)
                .FirstOrDefault(t => t.MeasLocationID == wave.MeasLocationID);

            if (meas == null)
            {
                return Ok(ApiResponse<string>.Error("SVM测点不存在"));
            }

            // 创建SVM波形定义
            var wavedef = new WaveDef_SVM()
            {
                MeasDefinitionID = wave.MeasDefinitionID,
                MeasLocationID = meas.MeasLocationID,
                WindTurbineID = wave.WindTurbineID,
                WaveDefinitionID = wave.MeasDefinitionID,
                SampleLength = (int)wave.SampleLength,
                SampleRate = (float)wave.SampleRate,
                ParamType = meas.ParamType,
                WaveDefinitionName = wave.WaveDefinitionName,

            };

            List<WaveDef_SVM> svmWaveDeflist = new() { wavedef };

            // 添加SVM设备波形定义
            SVMManagement.AddWaveDefSVM(svmWaveDeflist);

            // 添加特征值配置
            if (wave.evLists != null && wave.evLists.Any())
            {
                AddEigenValueConfiguration(wavedef.MeasDefinitionID, wavedef.MeasLocationID,
                    wavedef.WindTurbineID, wavedef.WaveDefinitionID, wave.evLists);
            }

            UpdateMeasDefVersion(wave.WindTurbineID);
            return Ok(ApiResponse<string>.Success("SVM波形定义添加成功"));
        }

        /// <summary>
        /// 处理Modbus波形定义
        /// </summary>
        private IActionResult ProcessModbusWaveDefinition(WaveModbusDTO wave, ModbusUnit modbus)
        {
            // 查询modbus测量位置定义
            var meas = SVMManagement.GetMeasLoc_ModbusListByTurID(wave.WindTurbineID)
                .FirstOrDefault(t => t.MeasLocationID == wave.MeasLocationID);

            if (meas == null)
            {
                return Ok(ApiResponse<string>.Error("Modbus测点不存在"));
            }

            // 创建Modbus波形定义
            var wavedef = new WaveDef_Modbus()
            {
                MeasDefinitionID = wave.MeasDefinitionID,
                MeasLocationID = meas.MeasLocationID,
                WindTurbineID = wave.WindTurbineID,
                WaveDefinitionID = wave.MeasDefinitionID,
                SampleLength = (int)wave.SampleLength,
                SampleRate = (float)wave.SampleRate,
                SingleType = (EnumSignalType)wave.SingleType,
                WaveDefinitionName = wave.WaveDefinitionName,
                ModbusUnitID = modbus.ModbusUnitID,
                ModbusDeviceID = modbus.ModbusDeviceID,
            };

            List<WaveDef_Modbus> modbusWaveDeflist = new() { wavedef };

            // 添加Modbus设备波形定义
            SVMManagement.AddWaveDefModbus(modbusWaveDeflist);

            // 添加特征值配置
            if (wave.evLists != null && wave.evLists.Any())
            {
                AddEigenValueConfiguration(wavedef.MeasDefinitionID, wavedef.MeasLocationID,
                    wavedef.WindTurbineID, wavedef.WaveDefinitionID, wave.evLists);
            }

            UpdateMeasDefVersion(wave.WindTurbineID);
            return Ok(ApiResponse<string>.Success("Modbus波形定义添加成功"));
        }

        /// <summary>
        /// 添加特征值配置
        /// </summary>
        private void AddEigenValueConfiguration(string measDefinitionID, string measLocationID,
            string windTurbineID, string waveDefinitionID, List<string> evLists)
        {
            List<MeasDef_Ev_Vib> evconf = new List<MeasDef_Ev_Vib>();

            foreach (var ev in evLists)
            {
                evconf.Add(new MeasDef_Ev_Vib()
                {
                    MeasDefinitionID = measDefinitionID,
                    MeasLocationID = measLocationID,
                    WindTurbineID = windTurbineID,
                    WaveDefinitionID = waveDefinitionID,
                    EvType = EnumEigenValueGroupType.GeneralEV,
                    Type = (EnumEigenvalueName)Convert.ToInt32(ev),
                });
            }

            EigenValueManage.AddMdfTimedomainEvConf(evconf);
        }

        /// <summary>
        /// 批量编辑Modbus波形定义
        /// </summary>
        /// <param name="request">批量编辑请求</param>
        /// <returns></returns>
        [HttpPost("EditModbusWave")]
        [BatchOperation(nameof(BatchEditModbusWaveFun))]
        public IActionResult BatchEditModbusWave([FromBody] BatchEditModbusWaveRequest request)
        {
            if (request?.SourceData == null)
                return Ok(ApiResponse<string>.Error("参数不能为空"));

            // 缓存当前的波形定义

            // 执行原有的编辑逻辑
            return ExecuteEditModbusWaveOperation(request.SourceData);
        }

        /// <summary>
        /// 执行编辑Modbus波形定义操作
        /// </summary>
        /// <param name="editRequests">编辑请求列表</param>
        /// <returns></returns>
        private IActionResult ExecuteEditModbusWaveOperation(EditModbusWaveDTO editRequest)
        {
            var results = new List<string>();
            int successCount = 0;
            int failCount = 0;
            try
            {
                var result = EditModbusWaveSingle(editRequest);
                if (result is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                {
                    if (apiResponse.Code == 1)
                    {
                        successCount++;
                        results.Add($"机组 {editRequest.WindTurbineID}: 成功编辑波形定义 {editRequest.WaveDefinitionID}");
                    }
                    else
                    {
                        failCount++;
                        results.Add($"机组 {editRequest.WindTurbineID}: 编辑波形定义失败 - {apiResponse.Msg}");
                    }
                }
            }
            catch (Exception ex)
            {
                failCount++;
                results.Add($"机组 {editRequest.WindTurbineID}: 编辑波形定义异常 - {ex.Message}");
            }


            var summary = $"批量编辑完成：成功 {successCount} 个，失败 {failCount} 个";
            results.Insert(0, summary);

            return Ok(ApiResponse<List<string>>.Success(results));
        }

        /// <summary>
        /// 修改modbus波形定义
        /// </summary>
        /// <param name="editRequest">编辑请求</param>
        /// <returns></returns>
        [HttpPost("EditModbusWaveV1")]
        public IActionResult EditModbusWave(EditModbusWaveDTO editRequest)
        {
            return EditModbusWaveSingle(editRequest);
        }

        /// <summary>
        /// 编辑单个Modbus波形定义
        /// </summary>
        /// <param name="editRequest">编辑请求</param>
        /// <returns></returns>
        private IActionResult EditModbusWaveSingle(EditModbusWaveDTO editRequest)
        {
            try
            {
                // 参数验证
                if (editRequest == null)
                {
                    return Ok(ApiResponse<string>.Error("编辑请求不能为空"));
                }

                if (string.IsNullOrEmpty(editRequest.WindTurbineID) ||
                    string.IsNullOrEmpty(editRequest.MeasDefinitionID) ||
                    string.IsNullOrEmpty(editRequest.WaveDefinitionID) ||
                    string.IsNullOrEmpty(editRequest.MeasLocationID))
                {
                    return Ok(ApiResponse<string>.Error("必填参数不能为空"));
                }

                // 查询现有的波形定义，先尝试Modbus波形
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            // 尝试查找Modbus波形定义
                            var modbusWave = ctx.WDFModbusDefs.FirstOrDefault(w =>
                                w.WindTurbineID == editRequest.WindTurbineID &&
                                w.MeasDefinitionID == editRequest.MeasDefinitionID &&
                                w.WaveDefinitionID == editRequest.WaveDefinitionID &&
                                w.MeasLocationID == editRequest.MeasLocationID);

                            if (modbusWave != null)
                            {
                                // 更新Modbus波形定义
                                if (editRequest.SampleRate.HasValue)
                                {
                                    modbusWave.SampleRate = (float)editRequest.SampleRate.Value;
                                }
                                if (editRequest.SampleLength.HasValue)
                                {
                                    modbusWave.SampleLength = (int)editRequest.SampleLength.Value;
                                }

                                if (!string.IsNullOrEmpty(editRequest.WaveDefinitionName))
                                {
                                    modbusWave.WaveDefinitionName = editRequest.WaveDefinitionName;
                                }

                                ctx.WDFModbusDefs.Attach(modbusWave);
                                ctx.Entry(modbusWave).State = EntityState.Modified;
                            }
                            else
                            {
                                // 尝试查找SVM波形定义
                                var svmWave = ctx.SVMWaveDefinitions.FirstOrDefault(w =>
                                    w.WindTurbineID == editRequest.WindTurbineID &&
                                    w.MeasDefinitionID == editRequest.MeasDefinitionID &&
                                    w.WaveDefinitionID == editRequest.WaveDefinitionID &&
                                    w.MeasLocationID == editRequest.MeasLocationID);

                                if (svmWave != null)
                                {
                                    // 更新SVM波形定义
                                    if (editRequest.SampleRate.HasValue)
                                    {
                                        svmWave.SampleRate = (float)editRequest.SampleRate.Value;
                                    }
                                    if (editRequest.SampleLength.HasValue)
                                    {
                                        svmWave.SampleLength = (int)editRequest.SampleLength.Value;
                                    }

                                    if (!string.IsNullOrEmpty(editRequest.WaveDefinitionName))
                                    {
                                        svmWave.WaveDefinitionName = editRequest.WaveDefinitionName;
                                    }

                                    ctx.SVMWaveDefinitions.Attach(svmWave);
                                    ctx.Entry(svmWave).State = EntityState.Modified;
                                }
                                else
                                {
                                    return Ok(ApiResponse<string>.Error("未找到要编辑的波形定义"));
                                }
                            }

                            // 更新特征值配置
                            if (editRequest.evLists != null && editRequest.evLists.Any())
                            {
                                // 删除现有特征值配置
                                var existingEvs = ctx.TimeDomainEvConfs.Where(ev =>
                                    ev.WindTurbineID == editRequest.WindTurbineID &&
                                    ev.MeasDefinitionID == editRequest.MeasDefinitionID &&
                                    ev.WaveDefinitionID == editRequest.WaveDefinitionID &&
                                    ev.MeasLocationID == editRequest.MeasLocationID).ToList();

                                ctx.TimeDomainEvConfs.RemoveRange(existingEvs);

                                // 添加新的特征值配置
                                List<MeasDef_Ev_Vib> newEvconf = new List<MeasDef_Ev_Vib>();
                                foreach (var ev in editRequest.evLists)
                                {
                                    newEvconf.Add(new MeasDef_Ev_Vib()
                                    {
                                        MeasDefinitionID = editRequest.MeasDefinitionID,
                                        MeasLocationID = editRequest.MeasLocationID,
                                        WindTurbineID = editRequest.WindTurbineID,
                                        WaveDefinitionID = editRequest.WaveDefinitionID,
                                        EvType = EnumEigenValueGroupType.GeneralEV,
                                        Type = (EnumEigenvalueName)Convert.ToInt32(ev),
                                    });
                                }
                                ctx.TimeDomainEvConfs.AddRange(newEvconf);
                            }

                            ctx.SaveChanges();
                            transaction.Commit();

                            UpdateMeasDefVersion(editRequest.WindTurbineID);
                            CMSFramework.Logger.Logger.LogInfoMessage($"成功编辑Modbus波形定义: {editRequest.WindTurbineID}-{editRequest.WaveDefinitionID}");
                            return Ok(ApiResponse<string>.Success("波形定义编辑成功"));
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            CMSFramework.Logger.Logger.LogErrorMessage("EditModbusWave", ex);
                            return Ok(ApiResponse<string>.Error($"编辑波形定义失败: {ex.Message}"));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("EditModbusWave", ex);
                return Ok(ApiResponse<string>.Error($"系统错误: {ex.Message}"));
            }
        }

        /// <summary>
        /// 跨机组批量删除Modbus波形定义
        /// </summary>
        /// <param name="request">批量删除请求</param>
        /// <returns></returns>
        [HttpPost("BatchDeleteModbusWave")]
        [BatchOperation(nameof(BatchDeleteModbusWaveFun))]
        public IActionResult BatchDeleteModbusWaveCrossTurbine([FromBody] BatchDeleteModbusWaveRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));

            // 缓存波形定义

            // 执行原有的删除逻辑
            return BatchDeleteModbusWave(request.SourceData);
        }

        /// <summary>
        /// 批量删除modbus波形定义
        /// </summary>
        /// <param name="deleteRequest">删除请求</param>
        /// <returns></returns>
        [HttpPost("BatchDeleteModbusWaveV1")]
        public IActionResult BatchDeleteModbusWave(List<ModbusWaveIdentifier> deleteRequest)
        {
            try
            {
                // 参数验证
                if (deleteRequest == null || !deleteRequest.Any())
                {
                    return Ok(ApiResponse<string>.Error("删除列表不能为空"));
                }

                // 验证必填字段
                foreach (var wave in deleteRequest)
                {
                    if (string.IsNullOrEmpty(wave.WindTurbineID) ||
                        string.IsNullOrEmpty(wave.MeasDefinitionID) ||
                        string.IsNullOrEmpty(wave.WaveDefinitionID) ||
                        string.IsNullOrEmpty(wave.MeasLocationID))
                    {
                        return Ok(ApiResponse<string>.Error("波形标识参数不能为空"));
                    }
                }

                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            int deletedCount = 0;
                            HashSet<string> updatedTurbines = new HashSet<string>();

                            foreach (var wave in deleteRequest)
                            {
                                // 尝试删除Modbus波形定义
                                var modbusWave = ctx.WDFModbusDefs.FirstOrDefault(w =>
                                    w.WindTurbineID == wave.WindTurbineID &&
                                    w.MeasDefinitionID == wave.MeasDefinitionID &&
                                    w.WaveDefinitionID == wave.WaveDefinitionID &&
                                    w.MeasLocationID == wave.MeasLocationID);

                                if (modbusWave != null)
                                {
                                    ctx.WDFModbusDefs.Remove(modbusWave);
                                    deletedCount++;
                                    updatedTurbines.Add(wave.WindTurbineID);
                                }
                                else
                                {
                                    // 尝试删除SVM波形定义
                                    var svmWave = ctx.SVMWaveDefinitions.FirstOrDefault(w =>
                                        w.WindTurbineID == wave.WindTurbineID &&
                                        w.MeasDefinitionID == wave.MeasDefinitionID &&
                                        w.WaveDefinitionID == wave.WaveDefinitionID &&
                                        w.MeasLocationID == wave.MeasLocationID);

                                    if (svmWave != null)
                                    {
                                        ctx.SVMWaveDefinitions.Remove(svmWave);
                                        deletedCount++;
                                        updatedTurbines.Add(wave.WindTurbineID);
                                    }
                                }

                                // 删除相关的特征值配置
                                var evConfigs = ctx.TimeDomainEvConfs.Where(ev =>
                                    ev.WindTurbineID == wave.WindTurbineID &&
                                    ev.MeasDefinitionID == wave.MeasDefinitionID &&
                                    ev.WaveDefinitionID == wave.WaveDefinitionID &&
                                    ev.MeasLocationID == wave.MeasLocationID).ToList();

                                if (evConfigs.Any())
                                {
                                    ctx.TimeDomainEvConfs.RemoveRange(evConfigs);
                                }
                            }

                            if (deletedCount == 0)
                            {
                                return Ok(ApiResponse<string>.Error("未找到要删除的波形定义"));
                            }

                            ctx.SaveChanges();
                            transaction.Commit();

                            // 更新相关机组的测量定义版本
                            foreach (var turbineId in updatedTurbines)
                            {
                                UpdateMeasDefVersion(turbineId);
                            }

                            CMSFramework.Logger.Logger.LogInfoMessage($"成功删除{deletedCount}个Modbus波形定义");
                            return Ok(ApiResponse<string>.Success($"成功删除{deletedCount}个波形定义"));
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            CMSFramework.Logger.Logger.LogErrorMessage("BatchDeleteModbusWave", ex);
                            return Ok(ApiResponse<string>.Error($"删除波形定义失败: {ex.Message}"));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("BatchDeleteModbusWave", ex);
                return Ok(ApiResponse<string>.Error($"系统错误: {ex.Message}"));
            }
        }

        public static bool UpdateMeasDefVersion(string TurbineID)
        {
            int count = 0;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                List<WindDAU> DAUList = ctx.DAUnits.Where(obj => obj.WindTurbineID == TurbineID).ToList();
                for (int i = 0; i < DAUList.Count; i++)
                {
                    if (DAUList[i] != null)
                    {
                        DAUList[i].MeasDefVersion += 1;
                        ctx.Entry(DAUList[i]).State = EntityState.Modified;
                        count += ctx.SaveChanges();
                    }
                }
            }
            return count > 0;
        }

        /// <summary>
        /// 查询modbus波形定义列表
        /// </summary>
        /// <param name="turbineID">机组ID（可选）</param>
        /// <param name="measDefinitionID">测量定义ID（可选）</param>
        /// <param name="modbusUnitID">Modbus设备ID（可选）</param>
        /// <returns></returns>
        [HttpGet("GetModbusWaveList")]
        public IActionResult GetModbusWaveList(string turbineID, string measDefinitionID)
        {
            try
            {
                List<UnifiedWaveListDTO> unifiedWaves = new List<UnifiedWaveListDTO>();

                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    // 查询Modbus波形定义
                    var modbusWaveList = ctx.WDFModbusDefs.Where(t => t.WindTurbineID == turbineID && t.MeasDefinitionID == measDefinitionID).ToList();

                    // 查询SVM波形定义
                    var svmWaveList = ctx.SVMWaveDefinitions.Where(t => t.WindTurbineID == turbineID && t.MeasDefinitionID == measDefinitionID).ToList();

                    // 获取特征值配置
                    var allEvConfigs = ctx.TimeDomainEvConfs.Where(ev =>
                        (string.IsNullOrEmpty(turbineID) || ev.WindTurbineID == turbineID) &&
                        (string.IsNullOrEmpty(measDefinitionID) || ev.MeasDefinitionID == measDefinitionID))
                        .ToList();

                    // 转换Modbus波形定义为统一DTO
                    foreach (var wave in modbusWaveList)
                    {
                        // 查找modbus名称
                        var modbus = DauManagement.GetModbusDevicebyModbusDeviceID(turbineID,wave.ModbusDeviceID);

                        var waveDto = new UnifiedWaveListDTO
                        {
                            WindTurbineID = wave.WindTurbineID,
                            MeasDefinitionID = wave.MeasDefinitionID,
                            WaveDefinitionID = wave.WaveDefinitionID,
                            MeasLocationID = wave.MeasLocationID,
                            WaveDefinitionName = wave.WaveDefinitionName ?? "",
                            ModbusUnitID = wave.ModbusUnitID,
                            SampleRate = wave.SampleRate,
                            SampleLength = (int)wave.SampleLength,
                            WaveType = "Modbus",
                            SingleType = (int)wave.SingleType,
                            SingleTypeName = GetSignalTypeName(wave.SingleType),
                            ParamType = null,
                            ParamTypeName = null,
                            ModbusDeviceID = wave.ModbusDeviceID,
                            ModbusDeviceName = modbus?.ModbusDeviceName,
                        };

                        // 获取测量位置名称
                        waveDto.MeasLocationName = GetMeasLocationName(wave.MeasLocationID);

                        // 获取特征值列表
                        var evs = allEvConfigs.Where(ev =>
                            ev.WindTurbineID == wave.WindTurbineID &&
                            ev.MeasDefinitionID == wave.MeasDefinitionID &&
                            ev.WaveDefinitionID == wave.WaveDefinitionID &&
                            ev.MeasLocationID == wave.MeasLocationID).ToList();

                        waveDto.EigenValues = evs.Select(ev => ((int)ev.Type).ToString()).ToList();
                        unifiedWaves.Add(waveDto);
                    }

                    // 转换SVM波形定义为统一DTO
                    foreach (var wave in svmWaveList)
                    {
                        // 查询通道定义
                        var channel = TIMManagement.GetModbusChannelByMeasLocID(turbineID,wave.MeasLocationID);
                        if (channel == null)
                        {
                            continue;
                        }
                        // 查找modbus名称
                        var modbus = DauManagement.GetModbusDevicebyModbusDeviceID(turbineID, channel.ModbusDeviceID);
                        var waveDto = new UnifiedWaveListDTO
                        {
                            WindTurbineID = wave.WindTurbineID,
                            MeasDefinitionID = wave.MeasDefinitionID,
                            WaveDefinitionID = wave.WaveDefinitionID,
                            MeasLocationID = wave.MeasLocationID,
                            WaveDefinitionName = wave.WaveDefinitionName ?? "",
                            ModbusUnitID = null,
                            SampleRate = wave.SampleRate,
                            SampleLength = (int)wave.SampleLength,
                            WaveType = "SVM",
                            SingleType = null,
                            SingleTypeName = null,
                            ParamType = (int)wave.ParamType,
                            ParamTypeName = GetParamTypeName((int)wave.ParamType),
                            ModbusDeviceName = modbus?.ModbusDeviceName,
                        };

                        // 获取测量位置名称
                        waveDto.MeasLocationName = GetMeasLocationName(wave.MeasLocationID);

                        // 获取特征值列表
                        var evs = allEvConfigs.Where(ev =>
                            ev.WindTurbineID == wave.WindTurbineID &&
                            ev.MeasDefinitionID == wave.MeasDefinitionID &&
                            ev.WaveDefinitionID == wave.WaveDefinitionID &&
                            ev.MeasLocationID == wave.MeasLocationID).ToList();

                        waveDto.EigenValues = evs.Select(ev => ((int)ev.Type).ToString()).ToList();
                        unifiedWaves.Add(waveDto);
                    }
                }

                return Ok(unifiedWaves);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("GetModbusWaveList", ex);
                return Ok(ApiResponse<List<UnifiedWaveListDTO>>.Error($"查询失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取信号类型名称
        /// </summary>
        private string GetSignalTypeName(EnumSignalType signalType)
        {
            return CommonUtility.GetDecscription(signalType);
        }

        /// <summary>
        /// 获取参数类型名称
        /// </summary>
        private string GetParamTypeName(int paramType)
        {
            // 根据实际的参数类型枚举返回名称
            return paramType.ToString();
        }

        /// <summary>
        /// 获取测量位置名称
        /// </summary>
        private string GetMeasLocationName(string measLocationID)
        {
            try
            {
                using (CMSFramework.EF.DevContext devCtx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                {
                    var measLoc = devCtx.MeasLoc_Modbus.FirstOrDefault(m => m.MeasLocationID == measLocationID)?.MeasLocName;

                    if (string.IsNullOrEmpty(measLoc))
                    {
                        measLoc = devCtx.DevMeasLocSVMs.FirstOrDefault(t => t.MeasLocationID == measLocationID)?.MeasLocName;
                    }

                    return measLoc ?? "";
                }
            }
            catch
            {
                return "";
            }
        }

        #endregion

        #region 批量操作方法

        /// <summary>
        /// 批量创建时域波形定义到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchMakeWaveDefinition(string targetTurbineId, BatchMakeWaveDefinitionRequest request)
        {
            var result = new List<string>();

            try
            {
                var sourceWaveDefinition = request.SourceData;

                var curmeasd = MeasDefinitionManagement.GetMeasdefinition(sourceWaveDefinition.WindTurbineID, sourceWaveDefinition.MeasDefinitionID);
                if (curmeasd == null)
                {
                    result.Add($"机组 {targetTurbineId}: 未找到对应的测量定义");
                    return result;
                }
                var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasd.MeasDefinitionName);

                if (tarmeasd == null)
                {
                    result.Add($"机组 {targetTurbineId}: 未找到对应的测量定义{curmeasd.MeasDefinitionName}");
                    return result;
                }

                // 创建新的波形定义，替换机组ID
                var newWaveDefinition = new WaveDefinitionDTO
                {
                    WindTurbineID = targetTurbineId,
                    WaveDefinitionID = sourceWaveDefinition.WaveDefinitionID,
                    WaveDefinitionName = sourceWaveDefinition.WaveDefinitionName,
                    MeasDefinitionID = tarmeasd.MeasDefinitionID,
                    WindParkID = sourceWaveDefinition.WindParkID,
                    measLocIds = sourceWaveDefinition.measLocIds,
                    TimeWdfUpFreq = sourceWaveDefinition.TimeWdfUpFreq,
                    TimeWdfSampleLength = sourceWaveDefinition.TimeWdfSampleLength,
                    isAddType = sourceWaveDefinition.isAddType,
                    messLocNames = sourceWaveDefinition.messLocNames,
                    signalType = sourceWaveDefinition.signalType,
                    FBEList = sourceWaveDefinition.FBEList,
                    GeneralEVList = sourceWaveDefinition.GeneralEVList,
                };

                if (newWaveDefinition.isAddType == "0")
                {
                    //var _curwave = MeasDefinitionManagement.GetWaveDefById_Time(sourceWaveDefinition.WindTurbineID, sourceWaveDefinition.WaveDefinitionName);
                    var _waveDefs = MeasDefinitionManagement.GetWaveDefByMdfId_Time(targetTurbineId, newWaveDefinition.MeasDefinitionID);
                    if (HttpContext.Items["OriginalWaveDefRecords"] is WaveDef_Time _curwave && _waveDefs.Count > 0)
                    {
                        var tarLoc = _curwave.MeasLocationID.Replace(sourceWaveDefinition.WindTurbineID, targetTurbineId);
                        var targetWave = _waveDefs.FirstOrDefault(t => t.WaveDefinitionName == _curwave.WaveDefinitionName && t.MeasLocationID == tarLoc && t.SignalType == _curwave.SignalType);
                        if (targetWave != null)
                        {
                            newWaveDefinition.WaveDefinitionID = targetWave.WaveDefinitionID;
                        }
                        else
                        {
                            result.Add($"机组 {targetTurbineId}: 未找到对应的波形定义{_curwave.WaveDefinitionName}");
                            return result;
                        }
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 未找到对应的波形定义");
                        return result;
                    }
                }

                // 在目标机组中查找对应的测量位置
                if (!string.IsNullOrEmpty(sourceWaveDefinition.measLocIds))
                {
                    var sourceMeasLocIds = sourceWaveDefinition.measLocIds.Split(',').ToList();
                    var targetMeasLocIds = new List<string>();

                    using (CMSFramework.EF.DevContext devCtx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                    {
                        foreach (var sourceMeasLocId in sourceMeasLocIds)
                        {
                            var targetMeasLocID = sourceMeasLocId.Replace(sourceWaveDefinition.WindTurbineID, targetTurbineId);
                            var targetMeasLoc = devCtx.DevMeasLocVibrations.FirstOrDefault(m => m.MeasLocationID == targetMeasLocID && m.WindTurbineID == targetTurbineId);
                            if (targetMeasLoc == null)
                            {
                                result.Add($"机组 {targetTurbineId}: 未找到对应的测量位置{targetMeasLocID}");
                                continue;
                            }
                            else
                            {
                                targetMeasLocIds.Add(targetMeasLoc.MeasLocationID);
                            }

                        }
                    }

                    if (targetMeasLocIds.Any())
                    {
                        newWaveDefinition.measLocIds = string.Join(",", targetMeasLocIds);
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 未找到对应的测量位置");
                        return result;
                    }
                }

                // 调用原有的创建逻辑
                var batchRequest = new BatchMakeWaveDefinitionRequest { SourceData = newWaveDefinition };
                var createResult = MakeWaveDefinition(batchRequest);

                if (createResult is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                {
                    if (apiResponse.Code == 1)
                    {
                        result.Add($"机组 {targetTurbineId}: 成功创建时域波形定义 {newWaveDefinition.WaveDefinitionName}");
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 创建时域波形定义失败 - {apiResponse.Msg}");
                    }
                }
                else
                {
                    result.Add($"机组 {targetTurbineId}: 创建时域波形定义失败");
                }
            }
            catch (Exception ex)
            {
                result.Add($"机组 {targetTurbineId}: 操作异常 - {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量创建电流电压波形定义到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchMakeWaveDefinitionVoltageCurrent(string targetTurbineId, BatchMakeWaveDefinitionVoltageCurrentRequest request)
        {
            var result = new List<string>();

            try
            {
                var sourceWaveDefinition = request.SourceData;

                var curmeasd = MeasDefinitionManagement.GetMeasdefinition(sourceWaveDefinition.WindTurbineID, sourceWaveDefinition.MeasDefinitionID);
                if (curmeasd == null)
                {
                    result.Add($"机组 {targetTurbineId}: 未找到对应的测量定义");
                    return result;
                }
                var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasd.MeasDefinitionName);

                if (tarmeasd == null)
                {
                    result.Add($"机组 {targetTurbineId}: 未找到对应的测量定义{curmeasd.MeasDefinitionName}");
                    return result;
                }

                // 创建新的波形定义，替换机组ID
                var newWaveDefinition = new WaveDefinitionDTO
                {
                    WindTurbineID = targetTurbineId,
                    WaveDefinitionID = sourceWaveDefinition.WaveDefinitionID,
                    WaveDefinitionName = sourceWaveDefinition.WaveDefinitionName,
                    MeasDefinitionID = tarmeasd.MeasDefinitionID,
                    WindParkID = sourceWaveDefinition.WindParkID,
                    measLocIds = sourceWaveDefinition.measLocIds,
                    TimeWdfUpFreq = sourceWaveDefinition.TimeWdfUpFreq,
                    TimeWdfSampleLength = sourceWaveDefinition.TimeWdfSampleLength,
                    isAddType = sourceWaveDefinition.isAddType,
                    messLocNames = sourceWaveDefinition.messLocNames,
                    signalType = sourceWaveDefinition.signalType,
                    FBEList = sourceWaveDefinition.FBEList,
                    GeneralEVList = sourceWaveDefinition.GeneralEVList,
                };

                if (newWaveDefinition.isAddType == "0")
                {
                    //var _curwave = MeasDefinitionManagement.GetWaveDefById_Time(sourceWaveDefinition.WindTurbineID, sourceWaveDefinition.WaveDefinitionName);
                    var _waveDefs = MeasDefinitionManagement.GetWaveDefByMdfId_VoltageCurrent(targetTurbineId, newWaveDefinition.MeasDefinitionID);
                    if (HttpContext.Items["OriginalVCWaveDefRecords"] is WaveDef_Time _curwave && _waveDefs.Count > 0)
                    {
                        var tarLoc = _curwave.MeasLocationID.Replace(sourceWaveDefinition.WindTurbineID, targetTurbineId);
                        var targetWave = _waveDefs.FirstOrDefault(t => t.WaveDefinitionName == _curwave.WaveDefinitionName && t.MeasLocationID == tarLoc && t.SignalType == _curwave.SignalType);
                        if (targetWave != null)
                        {
                            newWaveDefinition.WaveDefinitionID = targetWave.WaveDefinitionID;
                        }
                        else
                        {
                            result.Add($"机组 {targetTurbineId}: 未找到对应的波形定义{_curwave.WaveDefinitionName}");
                            return result;
                        }
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 未找到对应的波形定义");
                        return result;
                    }
                }
                // 在目标机组中查找对应的测量位置
                if (!string.IsNullOrEmpty(sourceWaveDefinition.measLocIds))
                {
                    var sourceMeasLocIds = sourceWaveDefinition.measLocIds.Split(',').ToList();
                    var targetMeasLocIds = new List<string>();

                    using (CMSFramework.EF.DevContext devCtx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                    {
                        foreach (var sourceMeasLocId in sourceMeasLocIds)
                        {
                            var targetMeasLocID = sourceMeasLocId.Replace(sourceWaveDefinition.WindTurbineID, targetTurbineId);
                            var targetMeasLoc = devCtx.DevMeasLocVoltageCurrents.FirstOrDefault(m => m.MeasLocationID == targetMeasLocID && m.WindTurbineID == targetTurbineId);
                            if (targetMeasLoc == null)
                            {
                                result.Add($"机组 {targetTurbineId}: 未找到对应的测量位置{targetMeasLocID}");
                                continue;
                            }
                            else
                            {
                                targetMeasLocIds.Add(targetMeasLoc.MeasLocationID);
                            }

                        }
                    }

                    if (targetMeasLocIds.Any())
                    {
                        newWaveDefinition.measLocIds = string.Join(",", targetMeasLocIds);
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 未找到对应的测量位置");
                        return result;
                    }
                }

                // 调用原有的创建逻辑
                var batchRequest = new BatchMakeWaveDefinitionVoltageCurrentRequest { SourceData = newWaveDefinition };
                var createResult = MakeWaveDefinitionVoltageCurrent(batchRequest);

                if (createResult is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                {
                    if (apiResponse.Code == 1)
                    {
                        result.Add($"机组 {targetTurbineId}: 成功创建电流电压波形定义 {newWaveDefinition.WaveDefinitionName}");
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 创建电流电压波形定义失败 - {apiResponse.Msg}");
                    }
                }
                else
                {
                    result.Add($"机组 {targetTurbineId}: 创建电流电压波形定义失败");
                }
            }
            catch (Exception ex)
            {
                result.Add($"机组 {targetTurbineId}: 操作异常 - {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量删除时域波形通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteWaveChannel(string targetTurbineId, BatchDeleteWaveChannelRequest request)
        {
            var result = new List<string>();

            try
            {
                var sourceDeleteData = request.SourceData;

                // 创建目标删除数据
                var targetDeleteData = new WaveChannelDeleteDTO
                {
                    WindTurbineID = targetTurbineId,
                };
                var curmeasd = MeasDefinitionManagement.GetMeasdefinition(sourceDeleteData.WindTurbineID, sourceDeleteData.MeasDefId);
                if (curmeasd == null)
                {
                    result.Add($"机组 {targetTurbineId}: 未找到对应的测量定义");
                    return result;
                }
                var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasd.MeasDefinitionName);

                if (tarmeasd == null)
                {
                    result.Add($"机组 {targetTurbineId}: 未找到对应的测量定义{curmeasd.MeasDefinitionName}");
                    return result;
                }

                var original = HttpContext.Items["OriginalWaveDefDefRecords"] as List<WaveDef_Time>;

                // 波形定义
                List<string> sourcewavedef = sourceDeleteData.WaveId.Split(",").ToList();
                List<string> wavedef = new();
                var _waveDefs = MeasDefinitionManagement.GetWaveDefByMdfId_Time(targetTurbineId, tarmeasd.MeasDefinitionID);
                if (_waveDefs != null)
                {
                    foreach (var wave in original)
                    {
                        var _wave = _waveDefs.FirstOrDefault(t => t.WaveDefinitionName == wave.WaveDefinitionName && t.SignalType == wave.SignalType && t.MeasLocationID == wave.MeasLocationID.Replace(sourceDeleteData.WindTurbineID, targetTurbineId));
                        if (_wave != null)
                        {
                            wavedef.Add(_wave.WaveDefinitionID);
                        }
                        else
                        {
                            result.Add($"机组 {targetTurbineId}: 未找到对应的波形定义{wave.WaveDefinitionName}");
                        }
                    }
                }
                if (wavedef.Count == 0)
                {
                    return result;
                }
                targetDeleteData.MeasDefId = tarmeasd.MeasDefinitionID;
                targetDeleteData.WaveId = string.Join(",", wavedef);

                // 调用原有的删除逻辑
                var batchRequest = new BatchDeleteWaveChannelRequest { SourceData = targetDeleteData };
                var deleteResult = DeleteWaveChannel(batchRequest);

                if (deleteResult is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                {
                    if (apiResponse.Code == 1)
                    {
                        result.Add($"机组 {targetTurbineId}: 成功删除时域波形通道 {sourceDeleteData.WaveId}");
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 删除时域波形通道失败 - {apiResponse.Msg}");
                    }
                }
                else
                {
                    result.Add($"机组 {targetTurbineId}: 删除时域波形通道失败");
                }
            }
            catch (Exception ex)
            {
                result.Add($"机组 {targetTurbineId}: 操作异常 - {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量删除电流电压波形通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteWaveChannelVoltageCurrent(string targetTurbineId, BatchDeleteWaveChannelVoltageCurrentRequest request)
        {
            var result = new List<string>();

            try
            {
                var sourceDeleteData = request.SourceData;

                // 创建目标删除数据
                var targetDeleteData = new WaveChannelDeleteDTO
                {
                    WindTurbineID = targetTurbineId,
                };

                var curmeasd = MeasDefinitionManagement.GetMeasdefinition(sourceDeleteData.WindTurbineID, sourceDeleteData.MeasDefId);
                if (curmeasd == null)
                {
                    result.Add($"机组 {targetTurbineId}: 未找到对应的测量定义");
                    return result;
                }
                var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasd.MeasDefinitionName);

                if (tarmeasd == null)
                {
                    result.Add($"机组 {targetTurbineId}: 未找到对应的测量定义{curmeasd.MeasDefinitionName}");
                    return result;
                }

                var original = HttpContext.Items["OriginalVCWaveDefDefRecords"] as List<WaveDef_Time>;

                // 波形定义
                List<string> sourcewavedef = sourceDeleteData.WaveId.Split(",").ToList();
                List<string> wavedef = new();
                var _waveDefs = MeasDefinitionManagement.GetWaveDefByMdfId_VoltageCurrent(targetTurbineId, tarmeasd.MeasDefinitionID);
                if (_waveDefs != null)
                {
                    foreach (var wave in original)
                    {
                        var _wave = _waveDefs.FirstOrDefault(t => t.WaveDefinitionName == wave.WaveDefinitionName && t.SignalType == wave.SignalType && t.MeasLocationID == wave.MeasLocationID.Replace(sourceDeleteData.WindTurbineID, targetTurbineId));
                        if (_wave != null)
                        {
                            wavedef.Add(_wave.WaveDefinitionID);
                        }
                        else
                        {
                            result.Add($"机组 {targetTurbineId}: 未找到对应的波形定义{wave.WaveDefinitionName}");
                        }
                    }
                }
                if (wavedef.Count == 0)
                {
                    return result;
                }
                targetDeleteData.MeasDefId = tarmeasd.MeasDefinitionID;
                targetDeleteData.WaveId = string.Join(",", wavedef);

                // 调用原有的删除逻辑
                var batchRequest = new BatchDeleteWaveChannelVoltageCurrentRequest { SourceData = targetDeleteData };
                var deleteResult = DeleteWaveChannelVoltageCurren(batchRequest);

                if (deleteResult is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                {
                    if (apiResponse.Code == 1)
                    {
                        result.Add($"机组 {targetTurbineId}: 成功删除电流电压波形通道 {sourceDeleteData.WaveId}");
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 删除电流电压波形通道失败 - {apiResponse.Msg}");
                    }
                }
                else
                {
                    result.Add($"机组 {targetTurbineId}: 删除电流电压波形通道失败");
                }
            }
            catch (Exception ex)
            {
                result.Add($"机组 {targetTurbineId}: 操作异常 - {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量创建高频包络波形定义到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchMakeParamEnvDefinition(string targetTurbineId, BatchMakeParamEnvDefinitionRequest request)
        {
            var result = new List<string>();

            try
            {
                var sourceWaveDefinition = request.SourceData;

                var curmeasd = MeasDefinitionManagement.GetMeasdefinition(sourceWaveDefinition.WindTurbineID, sourceWaveDefinition.MeasDefinitionID);
                if (curmeasd == null)
                {
                    result.Add($"机组 {targetTurbineId}: 未找到对应的测量定义");
                    return result;
                }
                var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasd.MeasDefinitionName);

                if (tarmeasd == null)
                {
                    result.Add($"机组 {targetTurbineId}: 未找到对应的测量定义{curmeasd.MeasDefinitionName}");
                    return result;
                }


                // 创建新的波形定义，替换机组ID
                var newWaveDefinition = new WaveDefinitionDTO
                {
                    WindTurbineID = targetTurbineId,
                    WaveDefinitionID = sourceWaveDefinition.WaveDefinitionID,
                    WaveDefinitionName = sourceWaveDefinition.WaveDefinitionName,
                    MeasDefinitionID = tarmeasd.MeasDefinitionID,
                    WindParkID = sourceWaveDefinition.WindParkID,
                    measLocIds = sourceWaveDefinition.measLocIds,
                    TimeWdfUpFreq = sourceWaveDefinition.TimeWdfUpFreq,
                    TimeWdfSampleLength = sourceWaveDefinition.TimeWdfSampleLength,
                    isAddType = sourceWaveDefinition.isAddType,
                    messLocNames = sourceWaveDefinition.messLocNames
                };
                if (newWaveDefinition.isAddType == "0")
                {
                    //var _curwave = MeasDefinitionManagement.GetWaveDefById_Time(sourceWaveDefinition.WindTurbineID, sourceWaveDefinition.WaveDefinitionName);
                    var _waveDefs = MeasDefinitionManagement.GetWaveDefByMeasdId_Envlope(targetTurbineId, newWaveDefinition.MeasDefinitionID);
                    if (HttpContext.Items["OriginalEnvWaveDefRecords"] is WaveDef_Envlope _curwave && _waveDefs.Count > 0)
                    {
                        var tarLoc = _curwave.MeasLocationID.Replace(sourceWaveDefinition.WindTurbineID, targetTurbineId);
                        var targetWave = _waveDefs.FirstOrDefault(t => t.WaveDefinitionName == _curwave.WaveDefinitionName && t.MeasLocationID == tarLoc && t.SignalType == _curwave.SignalType);
                        if (targetWave != null)
                        {
                            newWaveDefinition.WaveDefinitionID = targetWave.WaveDefinitionID;
                        }
                        else
                        {
                            result.Add($"机组 {targetTurbineId}: 未找到对应的波形定义{_curwave.WaveDefinitionName}");
                            return result;
                        }
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 未找到对应的波形定义");
                        return result;
                    }
                }
                if (!string.IsNullOrEmpty(sourceWaveDefinition.measLocIds))
                {
                    var sourceMeasLocIds = sourceWaveDefinition.measLocIds.Split(',').ToList();
                    var targetMeasLocIds = new List<string>();

                    using (CMSFramework.EF.DevContext devCtx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                    {
                        foreach (var sourceMeasLocId in sourceMeasLocIds)
                        {
                            var targetMeasLocID = sourceMeasLocId.Replace(sourceWaveDefinition.WindTurbineID, targetTurbineId);
                            var targetMeasLoc = devCtx.DevMeasLocVibrations.FirstOrDefault(m => m.MeasLocationID == targetMeasLocID && m.WindTurbineID == targetTurbineId);
                            if (targetMeasLoc == null)
                            {
                                result.Add($"机组 {targetTurbineId}: 未找到对应的测量位置{targetMeasLocID}");
                                continue;
                            }
                            else
                            {
                                targetMeasLocIds.Add(targetMeasLoc.MeasLocationID);
                            }

                        }
                    }

                    if (targetMeasLocIds.Any())
                    {
                        newWaveDefinition.measLocIds = string.Join(",", targetMeasLocIds);
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 未找到对应的测量位置");
                        return result;
                    }
                }

                // 调用原有的创建逻辑
                var batchRequest = new BatchMakeParamEnvDefinitionRequest { SourceData = newWaveDefinition };
                var createResult = MakeParamEnvDefinition(batchRequest);

                if (createResult is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                {
                    if (apiResponse.Code == 1)
                    {
                        result.Add($"机组 {targetTurbineId}: 成功创建高频包络波形定义 {newWaveDefinition.WaveDefinitionName}");
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 创建高频包络波形定义失败 - {apiResponse.Msg}");
                    }
                }
                else
                {
                    result.Add($"机组 {targetTurbineId}: 创建高频包络波形定义失败");
                }
            }
            catch (Exception ex)
            {
                result.Add($"机组 {targetTurbineId}: 操作异常 - {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量删除高频包络通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteParamEnvChannel(string targetTurbineId, BatchDeleteParamEnvChannelRequest request)
        {
            var result = new List<string>();

            try
            {
                var sourceDeleteData = request.SourceData;

                // 创建目标删除数据
                var targetDeleteData = new ParamEnvChannelDeleteDTO
                {
                    WindTurbineID = targetTurbineId,
                };
                var curmeasd = MeasDefinitionManagement.GetMeasdefinition(sourceDeleteData.WindTurbineID, sourceDeleteData.MeasDefId);
                if (curmeasd == null)
                {
                    result.Add($"机组 {targetTurbineId}: 未找到对应的测量定义");
                    return result;
                }
                var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasd.MeasDefinitionName);

                if (tarmeasd == null)
                {
                    result.Add($"机组 {targetTurbineId}: 未找到对应的测量定义{curmeasd.MeasDefinitionName}");
                    return result;
                }

                var original = HttpContext.Items["OriginalEnvWaveDefDelRecords"] as List<WaveDef_Envlope>;

                // 波形定义
                List<string> sourcewavedef = sourceDeleteData.WaveId.Split(",").ToList();
                List<string> wavedef = new();
                var _waveDefs = MeasDefinitionManagement.GetWaveDefByMeasdId_Envlope(targetTurbineId, tarmeasd.MeasDefinitionID);
                if (_waveDefs != null)
                {
                    foreach (var wave in original)
                    {
                        var _wave = _waveDefs.FirstOrDefault(t => t.WaveDefinitionName == wave.WaveDefinitionName && t.SignalType == wave.SignalType && t.MeasLocationID == wave.MeasLocationID.Replace(sourceDeleteData.WindTurbineID, targetTurbineId));
                        if (_wave != null)
                        {
                            wavedef.Add(_wave.WaveDefinitionID);
                        }
                        else
                        {
                            result.Add($"机组 {targetTurbineId}: 未找到对应的波形定义{wave.WaveDefinitionName}");
                        }
                    }
                }
                if (wavedef.Count == 0)
                {
                    return result;
                }
                targetDeleteData.MeasDefId = tarmeasd.MeasDefinitionID;
                targetDeleteData.WaveId = string.Join(",", wavedef);

                // 调用原有的删除逻辑
                var batchRequest = new BatchDeleteParamEnvChannelRequest { SourceData = targetDeleteData };
                var deleteResult = DeleteParamEnvChannel(batchRequest);

                if (deleteResult is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                {
                    if (apiResponse.Code == 1)
                    {
                        result.Add($"机组 {targetTurbineId}: 成功删除高频包络通道 {sourceDeleteData.WaveId}");
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId}: 删除高频包络通道失败 - {apiResponse.Msg}");
                    }
                }
                else
                {
                    result.Add($"机组 {targetTurbineId}: 删除高频包络通道失败");
                }
            }
            catch (Exception ex)
            {
                result.Add($"机组 {targetTurbineId}: 操作异常 - {ex.Message}");
            }

            return result;
        }

        #endregion

        #region Modbus波形定义批量操作实现

        /// <summary>
        /// 批量添加Modbus波形定义实现
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量添加请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddModbusWaveFun(string targetTurbineId, BatchAddModbusWaveRequest request)
        {
            var results = new List<string>();
            int successCount = 0;
            int failCount = 0;

            foreach (var sourceWave in request.SourceData)
            {
                try
                {
                    // 映射到目标机组
                    //var targetWave = await MapModbusWaveToTargetTurbine(sourceWave, targetTurbineId);
                    // 查找目标机组中对应的Modbus设备
                    var curModbus = DauManagement.GetModbusDevicebyModbusDeviceID(sourceWave.WindTurbineID, sourceWave.ModbusDeviceID);
                    var targetModbus = DauManagement.GetModbusDevicebyModbusID(targetTurbineId, curModbus.ModbusUnitID);
                    if (targetModbus == null)
                    {
                        results.Add($"机组 {targetTurbineId}: Modbus设备 {curModbus.ModbusDeviceName} 未找到！");
                        continue;
                    }
                    var curmeasdlist = MeasDefinitionManagement.GetMeasdefinition(sourceWave.WindTurbineID, sourceWave.MeasDefinitionID);
                    if (curmeasdlist == null)
                    {
                        results.Add($"机组 {targetTurbineId}: 测量定义 {sourceWave.MeasDefinitionID} 未找到！");
                        continue;
                    }
                    var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasdlist.MeasDefinitionName);
                    if (tarmeasd == null)
                    {
                        results.Add($"机组 {targetTurbineId}: 测量定义 {curmeasdlist.MeasDefinitionName} 未找到！");
                        continue;
                    }
                    // 查找目标机组中对应的测量位置

                    var targetMeasLoc = sourceWave.MeasLocationID.Replace(sourceWave.WindTurbineID, targetTurbineId);

                    using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                    {
                        var measLoc = ctx.MeasLoc_Modbus.FirstOrDefault(t => t.MeasLocationID == targetMeasLoc);
                        if (measLoc == null)
                        {
                            var measLocSVM = ctx.DevMeasLocSVMs.FirstOrDefault(t => t.MeasLocationID == targetMeasLoc);
                            if (measLocSVM == null)
                            {
                                results.Add($"机组 {targetTurbineId}: 测量位置 {targetMeasLoc} 未找到！");
                                continue;
                            }
                        }
                    }

                    var targetWave = new WaveModbusDTO
                    {
                        WindTurbineID = targetTurbineId,
                        MeasLocationID = targetMeasLoc,
                        ModbusDeviceID = targetModbus.ModbusDeviceID,
                        WaveDefinitionName = sourceWave.WaveDefinitionName,
                        SampleRate = sourceWave.SampleRate,
                        SampleLength = sourceWave.SampleLength,
                        SingleType = sourceWave.SingleType,
                        evLists = sourceWave.evLists,
                        MeasDefinitionID = tarmeasd.MeasDefinitionID,
                    };
                    if (targetWave == null)
                    {
                        failCount++;
                        results.Add($"机组 {targetTurbineId}: 无法映射波形定义 {sourceWave.WaveDefinitionName}");
                        continue;
                    }

                    var result = AddModbusWaveSingle(targetWave);
                    if (result is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                    {
                        if (apiResponse.Code == 1)
                        {
                            successCount++;
                            results.Add($"机组 {targetTurbineId}: 成功添加波形定义 {targetWave.WaveDefinitionName}");
                        }
                        else
                        {
                            failCount++;
                            results.Add($"机组 {targetTurbineId}: 添加波形定义失败 - {apiResponse.Msg}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    failCount++;
                    results.Add($"机组 {targetTurbineId}: 添加波形定义异常 - {ex.Message}");
                }
            }
            return results;
        }

        /// <summary>
        /// 批量编辑Modbus波形定义实现
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量编辑请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchEditModbusWaveFun(string targetTurbineId, BatchEditModbusWaveRequest request)
        {
            var results = new List<string>();

            try
            {
                var sourceWave = request.SourceData;

                var curmeasdlist = MeasDefinitionManagement.GetMeasdefinition(sourceWave.WindTurbineID, sourceWave.MeasDefinitionID);
                if (curmeasdlist == null)
                {
                    results.Add($"机组 {targetTurbineId}: 测量定义 {sourceWave.MeasDefinitionID} 未找到！");
                    return results;
                }
                var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasdlist.MeasDefinitionName);
                if (tarmeasd == null)
                {
                    results.Add($"机组 {targetTurbineId}: 测量定义 {curmeasdlist.MeasDefinitionName} 未找到！");
                    return results;
                }


                // 当前通过查询wave进行波形对比，如果需要更过的原始参数对比，使用缓存，当前使用测量位置、设备、测量定义进行对比查询

                // 波形查询
                WaveModbusDTO curwave = new WaveModbusDTO();
                WaveModbusDTO targetwave = new WaveModbusDTO();
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    // 查询Modbus波形定义
                    var modbusWave = ctx.WDFModbusDefs.FirstOrDefault(t => t.WindTurbineID == sourceWave.WindTurbineID && t.WaveDefinitionID == sourceWave.WaveDefinitionID);
                    if (modbusWave == null)
                    {
                        // 查询SVM波形定义
                        var svmWave = ctx.SVMWaveDefinitions.FirstOrDefault(t => t.WindTurbineID == sourceWave.WindTurbineID && t.WaveDefinitionID == sourceWave.WaveDefinitionID);
                        if(svmWave == null)
                        {
                            results.Add($"机组 {targetTurbineId} 无法找到波形定义");
                            return results;
                        }
                        else
                        {
                            curwave.WaveDefinitionID = svmWave.WaveDefinitionID;
                            curwave.MeasDefinitionID = svmWave.MeasDefinitionID;
                            curwave.MeasLocationID = svmWave.MeasLocationID;
                            curwave.ModbusDeviceID = default;

                            // 查询目标波形
                            var _targetwave = ctx.SVMWaveDefinitions.FirstOrDefault(t => t.MeasDefinitionID == tarmeasd.MeasDefinitionID && t.WindTurbineID == targetTurbineId && t.MeasLocationID == sourceWave.MeasLocationID.Replace(sourceWave.WindTurbineID,targetTurbineId));
                            if(_targetwave == null){
                                results.Add($"机组 {targetTurbineId} 无法找到波形定义");
                                return results;
                            }
                            else
                            {
                                targetwave.WaveDefinitionID = _targetwave.WaveDefinitionID;
                                targetwave.MeasLocationID = _targetwave.MeasLocationID;
                            }
                        }
                    }
                    else
                    {
                        curwave.WaveDefinitionID = modbusWave.WaveDefinitionID;
                        curwave.MeasDefinitionID = modbusWave.MeasDefinitionID;
                        curwave.MeasLocationID = modbusWave.MeasLocationID;
                        curwave.ModbusDeviceID = modbusWave.ModbusDeviceID;

                        var targetmodwave = ctx.WDFModbusDefs.FirstOrDefault(t => t.MeasDefinitionID == tarmeasd.MeasDefinitionID && t.WindTurbineID == targetTurbineId && t.MeasLocationID == sourceWave.MeasLocationID.Replace(sourceWave.WindTurbineID, targetTurbineId));
                        if (targetmodwave == null)
                        {
                            results.Add($"机组 {targetTurbineId} 无法找到波形定义");
                            return results;
                        }
                        else
                        {
                            targetwave.WaveDefinitionID = targetmodwave.WaveDefinitionID;
                            targetwave.ModbusDeviceID = targetmodwave.ModbusDeviceID;
                            targetwave.MeasLocationID = targetmodwave.MeasLocationID;
                        }
                    }
                    
                }

                if(curwave.ModbusDeviceID != default)
                {
                    var curModbus = DauManagement.GetModbusDevicebyModbusDeviceID(sourceWave.WindTurbineID, curwave.ModbusDeviceID);
                    var targetModbus = DauManagement.GetModbusDevicebyModbusID(targetTurbineId, curModbus.ModbusUnitID);
                    if (targetModbus == null)
                    {
                        results.Add($"机组 {targetTurbineId}: Modbus设备 {curModbus.ModbusDeviceName} 未找到！");
                        return results;
                    }
                }

                
                
                // 查找目标机组中对应的测量位置

                var targetMeasLoc = sourceWave.MeasLocationID.Replace(sourceWave.WindTurbineID, targetTurbineId);

                using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                {
                    var measLoc = ctx.MeasLoc_Modbus.FirstOrDefault(t => t.MeasLocationID == targetMeasLoc);
                    if (measLoc == null)
                    {
                        var measLocSVM = ctx.DevMeasLocSVMs.FirstOrDefault(t => t.MeasLocationID == targetMeasLoc);
                        if (measLocSVM == null)
                        {
                            results.Add($"机组 {targetTurbineId}: 测量位置 {targetMeasLoc} 未找到！");
                            return results;
                        }
                    }
                }
                var targetEdit = new EditModbusWaveDTO
                {
                    WindTurbineID = targetTurbineId,
                    MeasLocationID = targetwave.MeasLocationID,
                    MeasDefinitionID = tarmeasd.MeasDefinitionID,
                    WaveDefinitionID = targetwave.WaveDefinitionID,
                    WaveDefinitionName = sourceWave.WaveDefinitionName,
                    SampleRate = sourceWave.SampleRate,
                    SampleLength = sourceWave.SampleLength,
                    evLists = sourceWave.evLists
                };
                if (targetEdit == null)
                {
                    results.Add($"机组 {targetTurbineId}: 无法映射波形定义编辑 {sourceWave.WaveDefinitionID}");
                    return results;
                }

                var result = EditModbusWaveSingle(targetEdit);
                if (result is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                {
                    if (apiResponse.Code == 1)
                    {
                        results.Add($"机组 {targetTurbineId}: 成功编辑波形定义 {targetEdit.WaveDefinitionID}");
                    }
                    else
                    {
                        results.Add($"机组 {targetTurbineId}: 编辑波形定义失败 - {apiResponse.Msg}");
                    }
                }
            }
            catch (Exception ex)
            {
                results.Add($"机组 {targetTurbineId}: 编辑波形定义异常 - {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 批量删除Modbus波形定义实现
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量删除请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteModbusWaveFun(string targetTurbineId, BatchDeleteModbusWaveRequest request)
        {
            var results = new List<string>();

            // 映射删除标识符到目标机组
            var targetIdentifiers = new List<ModbusWaveIdentifier>();
            foreach (var sourceIdentifier in request.SourceData)
            {

                var sourceWave = sourceIdentifier;

                var curmeasdlist = MeasDefinitionManagement.GetMeasdefinition(sourceWave.WindTurbineID, sourceWave.MeasDefinitionID);
                if (curmeasdlist == null)
                {
                    results.Add($"机组 {targetTurbineId}: 测量定义 {sourceWave.MeasDefinitionID} 未找到！");
                    continue;
                }
                var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasdlist.MeasDefinitionName);
                if (tarmeasd == null)
                {
                    results.Add($"机组 {targetTurbineId}: 测量定义 {curmeasdlist.MeasDefinitionName} 未找到！");
                    continue;
                }

                // 波形查询
                WaveModbusDTO targetwave = new WaveModbusDTO();
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    // 查询Modbus波形定义
                    var modbusWave = ctx.WDFModbusDefs.FirstOrDefault(t => t.MeasDefinitionID == tarmeasd.MeasDefinitionID && t.WindTurbineID == targetTurbineId && t.MeasLocationID == sourceWave.MeasLocationID.Replace(sourceWave.WindTurbineID, targetTurbineId));
                    if (modbusWave == null)
                    {
                        // 查询SVM波形定义
                        var svmWave = ctx.SVMWaveDefinitions.FirstOrDefault(t => t.MeasDefinitionID == tarmeasd.MeasDefinitionID && t.WindTurbineID == targetTurbineId && t.MeasLocationID == sourceWave.MeasLocationID.Replace(sourceWave.WindTurbineID, targetTurbineId));
                        if (svmWave == null)
                        {
                            results.Add($"机组 {targetTurbineId} 无法找到波形定义");
                            continue;
                        }
                        else
                        {
                            targetwave.WaveDefinitionID = svmWave.WaveDefinitionID;
                            targetwave.MeasDefinitionID = svmWave.MeasDefinitionID;
                            targetwave.MeasLocationID = svmWave.MeasLocationID;
                            targetwave.ModbusDeviceID = default;

                        }
                    }
                    else
                    {
                        targetwave.WaveDefinitionID = modbusWave.WaveDefinitionID;
                        targetwave.MeasDefinitionID = modbusWave.MeasDefinitionID;
                        targetwave.MeasLocationID = modbusWave.MeasLocationID;
                        targetwave.ModbusDeviceID = modbusWave.ModbusDeviceID;
                    }

                }
                // 查找目标机组中对应的波形定义
                var targetIdentifier = new ModbusWaveIdentifier
                {
                    WindTurbineID = targetTurbineId,
                    MeasLocationID = targetwave.MeasLocationID,
                    MeasDefinitionID = tarmeasd.MeasDefinitionID,
                    WaveDefinitionID = targetwave.WaveDefinitionID
                };
                targetIdentifiers.Add(targetIdentifier);
            }

            if (targetIdentifiers.Count > 0)
            {
                try
                {
                    var result = BatchDeleteModbusWave(targetIdentifiers);
                    if (result is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                    {
                        if (apiResponse.Code == 1)
                        {
                            results.Add($"机组 {targetTurbineId}: 成功删除 {targetIdentifiers.Count} 个波形定义");
                        }
                        else
                        {
                            results.Add($"机组 {targetTurbineId}: 删除波形定义失败 - {apiResponse.Msg}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    results.Add($"机组 {targetTurbineId}: 删除波形定义异常 - {ex.Message}");
                }
            }

            return results;
        }

        #endregion

        #region 触发采集批量操作实现

        /// <summary>
        /// 批量添加触发采集实现
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量添加请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddTriggerAcqFun(string targetTurbineId, BatchAddTriggerAcqRequest request)
        {
            var results = new List<string>();
            var sourceTrigger = request.SourceData;
            try
            {
                List<string> tarMeasd = new();
                var measIDs = sourceTrigger.ConditionMonitoringLocIds.Split(",").ToList();
                foreach(var measd in measIDs)
                {
                    var curmeasdlist = MeasDefinitionManagement.GetMeasdefinition(sourceTrigger.TurbineID, measd);
                    if (curmeasdlist == null)
                    {
                        results.Add($"机组 {targetTurbineId}: 无法映射触发采集 {sourceTrigger.TriggerRuleName}");
                        return results;
                    }
                    var trimeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, curmeasdlist.MeasDefinitionName);
                    if (trimeasd == null)
                    {
                        results.Add($"机组 {targetTurbineId}: 被测量定义 {curmeasdlist.MeasDefinitionName} 未找到！");
                        return results;
                    }
                    tarMeasd.Add(trimeasd.MeasDefinitionID);
                }
                var tarmeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, sourceTrigger.TriggerMeasDefName);
                if (tarmeasd == null)
                {
                    results.Add($"机组 {targetTurbineId}: 测量定义 {sourceTrigger.TriggerMeasDefName} 未找到！");
                    return results;
                }

                // 映射到目标机组
                var targetTrigger = new TriggerAcquisitionTOD
                {
                    TurbineID = targetTurbineId,
                    TriggerRuleName = sourceTrigger.TriggerRuleName,
                    TriggerMeasDefName = sourceTrigger.TriggerMeasDefName,
                    ConditionMonitoringLocIds = string.Join(",",tarMeasd),
                    TriggerData = sourceTrigger.TriggerData,
                    isAddType = sourceTrigger.isAddType,
                    dauid = sourceTrigger.dauid,
                    triggertime = sourceTrigger.triggertime
                };
                //var targetTrigger = MapTriggerAcqToTargetTurbine(sourceTrigger, targetTurbineId);
                if (targetTrigger == null)
                {
                    results.Add($"机组 {targetTurbineId}: 无法映射触发采集 {sourceTrigger.TriggerRuleName}");
                    return results;
                }

                var result = AddTriggerGatherDisposeSingle(targetTrigger);
                if (result is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                {
                    if (apiResponse.Code == 1)
                    {
                        results.Add($"机组 {targetTurbineId}: 成功添加触发采集 {targetTrigger.TriggerRuleName}");
                        return results;
                    }
                    else
                    {
                        results.Add($"机组 {targetTurbineId}: 添加触发采集失败 - {apiResponse.Msg}");
                        return results;
                    }
                }
            }
            catch (Exception ex)
            {
                results.Add($"机组 {targetTurbineId}: 添加触发采集异常 - {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 批量删除触发采集实现
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量删除请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteTriggerGatherDisposeFun(string targetTurbineId, BatchDeleteTriggerGatherDisposeRequest request)
        {
            var results = new List<string>();

            // 映射删除数据到目标机组
            var targetTriggers = new List<TriggerAcquisitionTOD>();
            foreach (var sourceTrigger in request.SourceData)
            {
                var trimeasd = MeasDefinitionManagement.GetMeasdefinitionByName(targetTurbineId, sourceTrigger.TriggerMeasDefName);
                if (trimeasd == null)
                {
                    results.Add($"测量定义 {sourceTrigger.TriggerMeasDefName} 未找到！");
                    continue;
                }

                var targetTrigger = new TriggerAcquisitionTOD
                {
                    TurbineID = targetTurbineId,
                    TriggerRuleName = sourceTrigger.TriggerRuleName,
                    TriggerMeasDefName = sourceTrigger.TriggerMeasDefName,
                    ConditionMonitoringLocIds = sourceTrigger.ConditionMonitoringLocIds,
                    TriggerData = sourceTrigger.TriggerData,
                    isAddType = sourceTrigger.isAddType,
                    dauid = sourceTrigger.dauid,
                    triggertime = sourceTrigger.triggertime
                };
                if (targetTrigger != null)
                {
                    targetTriggers.Add(targetTrigger);
                }
            }

            if (targetTriggers.Count > 0)
            {
                try
                {
                    var result = BatchDeleteTriggerGatherDispose(targetTriggers);
                    if (result is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                    {
                        if (apiResponse.Code == 1)
                        {
                            results.Add($"成功删除 {targetTriggers.Count} 个触发采集");
                        }
                        else
                        {
                            results.Add($"删除触发采集失败 - {apiResponse.Msg}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    results.Add($"删除触发采集异常 - {ex.Message}");
                }
            }

            return results;
        }

        #endregion

        #region 映射辅助方法

        /// <summary>
        /// 映射Modbus波形定义到目标机组
        /// </summary>
        /// <param name="sourceWave">源波形定义</param>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <returns></returns>
        private async Task<WaveModbusDTO> MapModbusWaveToTargetTurbine(WaveModbusDTO sourceWave, string targetTurbineId)
        {
            try
            {
                // 查找目标机组中对应的Modbus设备
                var targetModbus = DauManagement.GetModbusDevicebyModbusDeviceID(targetTurbineId, sourceWave.ModbusDeviceID);
                if (targetModbus == null)
                    return null;

                // 查找目标机组中对应的测量位置
                var targetMeasLoc = await MapMeasLocationToTargetTurbine(sourceWave.MeasLocationID, sourceWave.WindTurbineID, targetTurbineId);
                if (targetMeasLoc == null)
                    return null;

                return new WaveModbusDTO
                {
                    WindTurbineID = targetTurbineId,
                    MeasLocationID = targetMeasLoc,
                    ModbusDeviceID = sourceWave.ModbusDeviceID,
                    WaveDefinitionName = sourceWave.WaveDefinitionName,
                    SampleRate = sourceWave.SampleRate,
                    SampleLength = sourceWave.SampleLength,
                    SingleType = sourceWave.SingleType,
                    evLists = sourceWave.evLists
                };
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"MapModbusWaveToTargetTurbine: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 映射编辑Modbus波形定义到目标机组
        /// </summary>
        /// <param name="sourceEdit">源编辑请求</param>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <returns></returns>
        private async Task<EditModbusWaveDTO> MapEditModbusWaveToTargetTurbine(EditModbusWaveDTO sourceEdit, string targetTurbineId)
        {
            try
            {
                // 查找目标机组中对应的测量位置
                var targetMeasLoc = await MapMeasLocationToTargetTurbine(sourceEdit.MeasLocationID, sourceEdit.WindTurbineID, targetTurbineId);
                if (targetMeasLoc == null)
                    return null;

                // 查找目标机组中对应的波形定义
                using (var ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    var targetWaveDef = ctx.WDFModbusDefs.FirstOrDefault(w =>
                        w.WindTurbineID == targetTurbineId &&
                        w.MeasLocationID == targetMeasLoc &&
                        w.WaveDefinitionName == sourceEdit.WaveDefinitionName);

                    if (targetWaveDef == null)
                        return null;

                    return new EditModbusWaveDTO
                    {
                        WindTurbineID = targetTurbineId,
                        MeasLocationID = targetMeasLoc,
                        MeasDefinitionID = sourceEdit.MeasDefinitionID,
                        WaveDefinitionID = targetWaveDef.WaveDefinitionID,
                        WaveDefinitionName = sourceEdit.WaveDefinitionName,
                        SampleRate = sourceEdit.SampleRate,
                        SampleLength = sourceEdit.SampleLength,
                        evLists = sourceEdit.evLists
                    };
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"MapEditModbusWaveToTargetTurbine: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 映射Modbus波形标识符到目标机组
        /// </summary>
        /// <param name="sourceIdentifier">源标识符</param>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <returns></returns>
        private async Task<ModbusWaveIdentifier> MapModbusWaveIdentifierToTargetTurbine(ModbusWaveIdentifier sourceIdentifier, string targetTurbineId)
        {
            try
            {
                // 查找目标机组中对应的测量位置
                var targetMeasLoc = await MapMeasLocationToTargetTurbine(sourceIdentifier.MeasLocationID, sourceIdentifier.WindTurbineID, targetTurbineId);
                if (targetMeasLoc == null)
                    return null;

                // 查找目标机组中对应的波形定义
                using (var ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    var targetWaveDef = ctx.WDFModbusDefs.FirstOrDefault(w =>
                        w.WindTurbineID == targetTurbineId &&
                        w.MeasLocationID == targetMeasLoc);

                    if (targetWaveDef == null)
                        return null;

                    return new ModbusWaveIdentifier
                    {
                        WindTurbineID = targetTurbineId,
                        MeasLocationID = targetMeasLoc,
                        MeasDefinitionID = sourceIdentifier.MeasDefinitionID,
                        WaveDefinitionID = targetWaveDef.WaveDefinitionID
                    };
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"MapModbusWaveIdentifierToTargetTurbine: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 映射触发采集到目标机组
        /// </summary>
        /// <param name="sourceTrigger">源触发采集</param>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <returns></returns>
        private TriggerAcquisitionTOD MapTriggerAcqToTargetTurbine(TriggerAcquisitionTOD sourceTrigger, string targetTurbineId)
        {
            try
            {
                // 查找目标机组中对应的测量定义
                using (var ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    var targetMeasDef = ctx.MeasDefinitions.FirstOrDefault(m =>
                        m.WindTurbineID == targetTurbineId &&
                        m.MeasDefinitionName == sourceTrigger.TriggerMeasDefName);

                    if (targetMeasDef == null)
                        return null;

                    return new TriggerAcquisitionTOD
                    {
                        TurbineID = targetTurbineId,
                        TriggerRuleName = sourceTrigger.TriggerRuleName,
                        TriggerMeasDefName = sourceTrigger.TriggerMeasDefName,
                        ConditionMonitoringLocIds = sourceTrigger.ConditionMonitoringLocIds,
                        TriggerData = sourceTrigger.TriggerData,
                        isAddType = sourceTrigger.isAddType,
                        dauid = sourceTrigger.dauid,
                        triggertime = sourceTrigger.triggertime
                    };
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"MapTriggerAcqToTargetTurbine: {ex.Message}", ex);
                return null;
            }
        }

        #endregion

        #region 测量方案映射辅助方法


        /// <summary>
        /// 映射测量定义ID列表
        /// </summary>
        /// <param name="sourceMeasDefIds">源测量定义ID列表</param>
        /// <param name="sourceTurbineId">源机组ID</param>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <returns></returns>
        private List<int> MapMeasDefinitionIds(List<int> sourceMeasDefIds, string sourceTurbineId, string targetTurbineId)
        {
            try
            {
                var mappedIds = new List<int>();

                using (var ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    foreach (var sourceMeasDefId in sourceMeasDefIds)
                    {
                        // 查找源测量定义
                        var sourceMeasDef = ctx.MeasDefinitions.FirstOrDefault(m => m.MeasDefinitionID == sourceMeasDefId.ToString());
                        if (sourceMeasDef == null)
                        {
                            CMSFramework.Logger.Logger.LogErrorMessage($"[MapMeasDefinitionIds]未找到源测量定义: ID {sourceMeasDefId}", new Exception("未找到源测量定义"));
                            continue;
                        }

                        // 在目标机组中查找同名的测量定义
                        var targetMeasDef = ctx.MeasDefinitions.FirstOrDefault(m =>
                            m.WindTurbineID == targetTurbineId &&
                            m.MeasDefinitionName == sourceMeasDef.MeasDefinitionName);

                        if (targetMeasDef != null)
                        {
                            mappedIds.Add(int.Parse(targetMeasDef.MeasDefinitionID));
                        }
                        else
                        {
                            CMSFramework.Logger.Logger.LogErrorMessage($"[MapMeasDefinitionIds]在目标机组中未找到对应的测量定义: {sourceMeasDef.MeasDefinitionName}", new Exception("未找到对应的测量定义"));
                        }
                    }
                }

                return mappedIds;
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[MapMeasDefinitionIds]映射测量定义ID失败", ex);
                return null;
            }
        }

        #endregion
    }
}
