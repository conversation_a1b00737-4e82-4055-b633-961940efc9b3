using System.ComponentModel.DataAnnotations;

namespace WTCMSLive.WebSite.Core.DTOs
{
    /// <summary>
    /// 网络设备信息DTO
    /// </summary>
    public class NetworkDeviceDTO
    {
        /// <summary>
        /// 设备ID（机组ID）
        /// </summary>
        public string DeviceId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }

        /// <summary>
        /// 设备类型（MCS/DAU）
        /// </summary>
        public string DeviceType { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        public string IpAddress { get; set; }

        /// <summary>
        /// 端口号
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// MAC地址（预留）
        /// </summary>
        public string MacAddress { get; set; }

        /// <summary>
        /// 网关地址（预留）
        /// </summary>
        public string Gateway { get; set; }

        /// <summary>
        /// 子网掩码（预留）
        /// </summary>
        public string SubnetMask { get; set; }

        /// <summary>
        /// 风场ID
        /// </summary>
        public string WindParkId { get; set; }

        /// <summary>
        /// 风场名称
        /// </summary>
        public string WindParkName { get; set; }
    }

    /// <summary>
    /// Ping测试结果DTO
    /// </summary>
    public class PingTestResultDTO
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }

        /// <summary>
        /// 设备类型
        /// </summary>
        public string DeviceType { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        public string IpAddress { get; set; }

        /// <summary>
        /// 测试是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 平均延迟（毫秒）
        /// </summary>
        public long AverageLatency { get; set; }

        /// <summary>
        /// 最小延迟（毫秒）
        /// </summary>
        public long MinLatency { get; set; }

        /// <summary>
        /// 最大延迟（毫秒）
        /// </summary>
        public long MaxLatency { get; set; }

        /// <summary>
        /// 丢包率（百分比）
        /// </summary>
        public double PacketLoss { get; set; }

        /// <summary>
        /// 发送包数
        /// </summary>
        public int PacketsSent { get; set; }

        /// <summary>
        /// 接收包数
        /// </summary>
        public int PacketsReceived { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 测试时间
        /// </summary>
        public DateTime TestTime { get; set; }
    }

    /// <summary>
    /// Telnet测试结果DTO
    /// </summary>
    public class TelnetTestResultDTO
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }

        /// <summary>
        /// 设备类型
        /// </summary>
        public string DeviceType { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        public string IpAddress { get; set; }

        /// <summary>
        /// 端口号
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// 测试是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 连接延迟（毫秒）
        /// </summary>
        public long ConnectionLatency { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 测试时间
        /// </summary>
        public DateTime TestTime { get; set; }
    }

    /// <summary>
    /// 批量测试设备信息DTO
    /// </summary>
    public class BatchTestDeviceDTO
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        [Required(ErrorMessage = "设备ID不能为空")]
        public string DeviceId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }

        /// <summary>
        /// 设备类型（MCS/DAU）
        /// </summary>
        public string DeviceType { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        [Required(ErrorMessage = "IP地址不能为空")]
        public string IpAddress { get; set; }

        /// <summary>
        /// 端口号
        /// </summary>
        public int Port { get; set; }

    }

    /// <summary>
    /// 批量测试请求DTO
    /// </summary>
    public class BatchTestRequestDTO
    {
        /// <summary>
        /// 设备信息列表
        /// </summary>
        [Required(ErrorMessage = "设备信息列表不能为空")]
        public List<BatchTestDeviceDTO> Devices { get; set; }

        /// <summary>
        /// 测试类型（ping/telnet/both）
        /// </summary>
        [Required(ErrorMessage = "测试类型不能为空")]
        public string TestType { get; set; }

    }

    /// <summary>
    /// 单个测试请求DTO
    /// </summary>
    public class SingleTestRequestDTO
    {
        /// <summary>
        /// IP地址
        /// </summary>
        [Required(ErrorMessage = "IP地址不能为空")]
        public string IpAddress { get; set; }

        /// <summary>
        /// 端口号（Telnet测试时需要）
        /// </summary>
        public int? Port { get; set; }

  
    }

    /// <summary>
    /// 批量测试结果DTO
    /// </summary>
    public class BatchTestResultDTO
    {
        /// <summary>
        /// Ping测试结果列表
        /// </summary>
        public List<PingTestResultDTO> PingResults { get; set; } = new List<PingTestResultDTO>();

        /// <summary>
        /// Telnet测试结果列表
        /// </summary>
        public List<TelnetTestResultDTO> TelnetResults { get; set; } = new List<TelnetTestResultDTO>();

        /// <summary>
        /// 测试总数
        /// </summary>
        public int TotalTests { get; set; }

        /// <summary>
        /// 成功数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败数量
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 测试开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 测试结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 总耗时（毫秒）
        /// </summary>
        public long TotalDuration { get; set; }
    }

    /// <summary>
    /// 导出CSV请求DTO
    /// </summary>
    public class ExportCsvRequestDTO
    {
        /// <summary>
        /// 测试结果数据（JSON格式）
        /// </summary>
        [Required(ErrorMessage = "测试结果数据不能为空")]
        public string TestResultData { get; set; }

        /// <summary>
        /// 导出类型（ping/telnet/both）
        /// </summary>
        [Required(ErrorMessage = "导出类型不能为空")]
        public string ExportType { get; set; }

        /// <summary>
        /// 文件名（可选）
        /// </summary>
        public string FileName { get; set; }
    }
}
