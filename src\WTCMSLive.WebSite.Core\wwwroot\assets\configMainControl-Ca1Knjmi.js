import{C as o,bC as a,bD as c,bE as n,bF as i,bG as h,bH as y,bI as S,bJ as l,bK as C,bL as u,bM as M,bN as p}from"./index-BjOW8S1L.js";import{a as s}from"./tools-zTE6InS0.js";const g=o("configMainControl",{state:()=>({mCSInfoList:[],mCSDataList:[],workFromMcsOptions:[],stateRegisterTypeOptions:[],registerStorageOptions:[],byteArrayTypeOptions:[]}),actions:{reset(){this.$reset()},async fetchMCSGetMCSInfoList(t){try{const e=await p(t);return this.mCSInfoList=e,e}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchAddMCS(t){try{return await M(t)}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchEditMCS(t){try{return await u(t)}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchDeleteMCS(t){try{return await C(t)}catch(e){throw console.error("操作失败！",e),e}},async fetchGetMCSGetMCSData(t){try{const e=await l(t);return this.mCSDataList=e,e}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchAddMCSRegister(t){try{return await S(t)}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchAddEditMCSRegister(t){try{return await y(t)}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchDeleteMCSRegister(t){try{return await h(t)}catch(e){throw console.error("操作失败！",e),e}},async fetchGetWorkFromMcs(t){try{const e=await i(t);let r=s(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.workFromMcsOptions=r,e}catch(e){throw console.error("操作失败！",e),e}},async fetchGetStateRegisterTypes(t){try{const e=await n(t);let r=s(e,{label:"key",value:"value",text:"key"},{nother:!0});return this.stateRegisterTypeOptions=r,e}catch(e){throw console.error("操作失败！",e),e}},async fetchGetRegisterStorages(t){try{const e=await c(t);let r=s(e,{label:"key",value:"value",text:"key"},{nother:!0});return this.registerStorageOptions=r,e}catch(e){throw console.error("操作失败！",e),e}},async fetchGetByteArrayTypes(t){try{const e=await a(t);let r=s(e,{label:"key",value:"value",text:"key"},{nother:!0});return this.byteArrayTypeOptions=r,e}catch(e){throw console.error("操作失败！",e),e}},reset(){this.$reset()}}});export{g as u};
