import{C as o,cG as n,cH as c,cI as i,cJ as u,cK as d,cL as l,cM as h,cN as p,cO as L,cP as y,cQ as M,cR as f,cS as v,cT as m,cU as D,cV as w,cW as g,cX as G,cY as W,cZ as C,c_ as q,c$ as k,d0 as b,d1 as T,d2 as V,d3 as S,d4 as E,d5 as U,d6 as B,d7 as F,d8 as I,d9 as O,da as P,db as A,dc as N,dd as j,de as x,df as $,dg as H,dh as J,di as K,dj as Q}from"./index-BjOW8S1L.js";import{a as s,e as a}from"./tools-zTE6InS0.js";const Y=o("measurementDefinition",{state:()=>({measdList:[],timeWaveDefList:[],timeParamEnvDefList:[],workConListForMeasLocList:[],spdMeasdList:[],voltageCurrentWaveDefList:[],dauList:[],vibMeasLocList:[],eigenValueTypeList:[],unusedVoltageCurrentMeasLocList:[],workConOptionList:[],workCondSpdMeasdOptions:[],envelopeFilterList:[],upperLimitFreqList:[],initUpperLimitFreqList:[],initGetSampleLengthList:[],parameUnusedVibMeasLocList:[],measdTriggerList:[],eigenValueTypeList:[],waveTypeList:[],measdDaqIntervalUnitType:[]}),actions:{reset(){this.$reset()},async fetchGetMeasdList(r={}){try{const e=await Q(r);return this.measdList=e,e}catch(e){return console.error("请求报错:",e),[]}},async fetchGetMeasdDaqIntervalUnitType(r={}){try{const e=await K(r);let t=s(e,{label:"value",value:"key"},{nother:!0});return this.measdDaqIntervalUnitType=t,t}catch(e){return console.error("请求报错:",e),[]}},async fetchGetWaveTypeList(r={}){try{const e=await J(r);let t=s(e,{label:"value",value:"key",text:"value"},{nother:!0});return this.waveTypeList=t,t}catch(e){return console.error("请求报错:",e),[]}},async fetchGetTimeWaveDefList(r){try{const e=await H(r);return this.timeWaveDefList=e,e}catch(e){return console.error("请求报错:",e),[]}},async fetchGetTimeParamEnvDefList(r={}){try{const e=await $(r);return this.timeParamEnvDefList=e,e}catch(e){return console.error("请求报错:",e),[]}},async fetchGetVoltageCurrentWaveDefList(r={}){try{const e=await x(r);return this.voltageCurrentWaveDefList=e,e}catch(e){return console.error("请求报错:",e),[]}},async fetchGetWorkConListForMeasLocList(r={}){try{const e=await j(r);return this.workConListForMeasLocList=e,e}catch(e){return console.error("请求报错:",e),[]}},async fetchGetWorkCondSpdMeasdList(r={}){try{const e=await N(r);return this.spdMeasdList=e,e}catch(e){return console.error("请求报错:",e),[]}},async fetchGetDauList(r={}){try{const e=await A(r);let t=s(e,{label:"dauName",value:"dauID"},{nother:!0});return this.dauList=t,t}catch(e){return console.error("请求报错:",e),[]}},async fetchGetInitUpperLimitFreqList(r={}){try{const e=await P(r);let t=s(e,{label:"key",value:"value"},{nother:!0});return this.initUpperLimitFreqList=t,t}catch(e){return console.error("请求报错:",e),[]}},async fetchGetInitGetSampleLengthList(r={}){try{const e=await O(r);let t=a(e,!0);return this.initGetSampleLengthList=t,t}catch(e){return console.error("请求报错:",e),[]}},async fetchGetEigenValueTypeList(r={}){try{const e=await I(r);let t=a(e,!0);return this.eigenValueTypeList=t,t}catch(e){return console.error("请求报错:",e),[]}},async fetchGetUnusedVoltageCurrentMeasLocList(r={}){try{const e=await F(r);let t=s(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.unusedVoltageCurrentMeasLocList=t,t}catch(e){return console.error("请求报错:",e),[]}},async fetchGetShowEnvelopeFilterList(r={}){try{const e=await B(r);let t=s(e,{label:"key",value:"value"},{nother:!0});return this.envelopeFilterList=t,t}catch(e){return console.error("请求报错:",e),[]}},async fetchGetUpperLimitFreqList(r={}){try{const e=await U(r);let t=s(e,{label:"key",value:"value"},{nother:!0});return this.upperLimitFreqList=t,t}catch(e){return console.error("请求报错:",e),[]}},async fetchGetWorkConOptionList(r={}){try{const e=await E(r);let t=s(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.workConOptionList=t,t}catch(e){return console.error("请求报错:",e),[]}},async fetchGetWorkCondSpdMeasdOptions(r={}){try{const e=await S(r);let t=s(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.workCondSpdMeasdOptions=t,t}catch(e){return console.error("请求报错:",e),[]}},async fetchGetVibMeasLocList(r={}){try{const e=await V(r);let t=s(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.vibMeasLocList=t,t}catch(e){return console.error("请求报错:",e),[]}},async fetchGetParameUnusedVibMeasLocList(r={}){try{const e=await T(r);let t=s(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.parameUnusedVibMeasLocList=t,t}catch(e){return console.error("请求报错:",e),[]}},async fetchAddMeasDefinition(r={}){try{return await b(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchEditMeasDefinition(r={}){try{return await k(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchDeleteMeasDef(r={}){try{return await q(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchMakeWaveDefinition(r={}){try{return await C(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchMakeParamEnvDefinition(r={}){try{return await W(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchMakeWaveDefinitionVoltageCurrent(r={}){try{return await G(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchDeleteWaveChannel(r={}){try{return await g(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchDeleteParamEnvChannel(r={}){try{return await w(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchDeleteWaveChannelVoltageCurrenl(r={}){try{return await D(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchBatchAddWorkCondMeasd(r={}){try{return await m(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchBatchEditWorkCondMeasd(r={}){try{return await v(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchBatchDeleteWorkCondMeasd(r={}){try{return await f(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchBatchAddWorkCondSpdMeasd(r={}){try{return await M(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchBatchEditWorkCondSpdMeasd(r={}){try{return await y(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchBatchDeleteWorkCondSpdMeasd(r={}){try{return await L(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchGetMeasdTriggerList(r={}){try{const e=await p(r);return this.measdTriggerList=e,e}catch(e){return console.error("请求报错:",e),[]}},async fetchGetEigenValueType(r={}){try{const e=await h(r);return this.eigenValueTypeList=e,e}catch(e){return console.error("请求报错:",e),[]}},async fetchAddMeasdTirggerAcq(r={}){try{return await l(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchDeleteTriggerGatherDispose(r={}){try{return await d(r)}catch(e){return console.error("请求报错:",e),[]}},async fetchMeasdAddModbusWave(r){try{return await u(r)}catch(e){throw console.error("操作失败:",e),e}},async fetchGetModbusWaveList(r){try{return await i(r)}catch(e){throw console.error("操作失败:",e),e}},async fetchEditModbusWave(r){try{return await c(r)}catch(e){throw console.error("操作失败:",e),e}},async fetchBatchDeleteModbusWave(r){try{return await n(r)}catch(e){throw console.error("操作失败:",e),e}}}});export{Y as u};
