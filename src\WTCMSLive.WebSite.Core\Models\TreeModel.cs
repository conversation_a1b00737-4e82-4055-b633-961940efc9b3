﻿namespace WTCMSLive.WebSite.Models
{
    public class TreeModel
    {
        /// <summary>
        /// 节点key
        /// </summary>
        public string key { get; set; }
        /// <summary>
        /// 节点ID
        /// </summary>
        public string id { get; set; }
        /// <summary>
        ///节点类型
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 节点显示值
        /// </summary>
        public string displayValue { get; set; }
        /// <summary>
        /// 节点下子节点
        /// </summary>
        public TreeModel[] children { get; set; }
        /// <summary>
        /// 实时状态
        /// </summary>
        public string realStatus { get; set; }
        /// <summary>
        /// 诊断状态
        /// </summary>
        public string diagStatus { get; set; }

        /// <summary>
        /// 覆冰状态
        /// </summary>
        public string bvmiceStatus { get; set; }
        /// <summary>
        /// 子节点是否展开(默认展开)
        /// </summary>
        public bool isclose { get; set; }
        /// <summary>
        /// 节点点击链接
        /// </summary>
        public string href { get; set; }
        /// <summary>
        /// 节点title
        /// </summary>
        public string title { get; set; }
        /// <summary>
        /// 节点状态title
        /// </summary>
        public string statusTitle { get; set; }
    }

    public class TreeModel_Ztree
    {
        public string id;
        public string pId;
        public string name;
        public string LinkUrl;
        public TreeModel_Ztree(string id, string pId, string name, string url)
        {
            this.id = id;
            this.pId = pId;
            this.name = name;
            this.LinkUrl = url;
        }
        public TreeModel_Ztree()
        {
            this.id = string.Empty;
            this.pId = string.Empty;
            this.name = string.Empty;
            this.LinkUrl = string.Empty;
        }
    }
}