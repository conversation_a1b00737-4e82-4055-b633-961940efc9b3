using System.Threading.Channels;
using WTCMSLive.WebSite.Core.DTOs;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// DAU配置队列服务实现
    /// 使用 Channel 提供线程安全的高性能队列
    /// </summary>
    public class DAUConfigQueueService : IDAUConfigQueueService
    {
        private readonly Channel<DAUConfigDTO> _channel;
        private readonly ILogger<DAUConfigQueueService> _logger;

        public DAUConfigQueueService(ILogger<DAUConfigQueueService> logger)
        {
            _logger = logger;
            
            // 创建无界队列，支持多生产者多消费者
            var options = new UnboundedChannelOptions
            {
                SingleReader = true,  // 只有一个后台服务消费
                SingleWriter = false  // 可能有多个线程推送数据
            };
            
            _channel = Channel.CreateUnbounded<DAUConfigDTO>(options);
            
            _logger.LogInformation("[DAUConfigQueueService]DAU配置队列服务已初始化");
        }

        /// <summary>
        /// 将DAU配置数据加入队列
        /// </summary>
        public async Task EnqueueAsync(DAUConfigDTO config, CancellationToken cancellationToken = default)
        {
            if (config == null)
            {
                _logger.LogWarning("[EnqueueAsync]尝试加入空的DAU配置数据，已忽略");
                return;
            }

            try
            {
                await _channel.Writer.WriteAsync(config, cancellationToken);
                
                _logger.LogDebug("[EnqueueAsync]DAU配置数据已加入队列: WindParkID={WindParkID}, WindTurbineID={WindTurbineID}", 
                    config.WindParkID, config.WindTurbienID);
            }
            catch (ChannelClosedException)
            {
                _logger.LogError("[EnqueueAsync]队列已关闭，无法加入数据");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[EnqueueAsync]加入DAU配置数据到队列失败");
                throw;
            }
        }

        /// <summary>
        /// 从队列中取出DAU配置数据
        /// </summary>
        public async ValueTask<DAUConfigDTO> DequeueAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var config = await _channel.Reader.ReadAsync(cancellationToken);
                
                _logger.LogDebug("[DequeueAsync]从队列中取出DAU配置数据: WindParkID={WindParkID}, WindTurbineID={WindTurbineID}", 
                    config.WindParkID, config.WindTurbienID);
                
                return config;
            }
            catch (ChannelClosedException)
            {
                _logger.LogWarning("[DequeueAsync]队列已关闭");
                throw;
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("[DequeueAsync]队列读取操作已取消");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DequeueAsync]从队列中取出数据失败");
                throw;
            }
        }
    }
}

