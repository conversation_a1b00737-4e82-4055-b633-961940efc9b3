﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.BusinessEntity;
using WTCMSLive.IDALService;

namespace WTCMSLive.BusinessEntityConvert
{
    // Author: GuoKaile
    // Create: 2013-09-02
    /// <summary>
    /// 实体转换， 从DA层实体转换为业务层实
    /// </summary>
    public static class ConvertEntityDAToBusinessDAU
    {
        private static IDALService.IDeviceService devService =
                       AppFramework.ServiceBus.ServiceLocator.GetService<IDeviceService>();
        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 采集单元实体转换
        /// </summary>
        /// <param name="_DAUnit"></param>
        /// <returns></returns>
        public static DataSourceV2 ConvertDAU(WTCMSLive.Entity.Models.DAUnit _DAUnit)
        {
            if (_DAUnit == null)
            {
                return null;
            }

            DataSourceV2 dataSource = new DataSourceV2();

            dataSource.AssocWindTurbineID = _DAUnit.WindTurbineID.ToString();

            dataSource.AssocWindTurbineName = _DAUnit.DAUnitName;
            dataSource.AssocWindTurbineID = _DAUnit.WindTurbineID.ToString();
            dataSource.RTAlarmStatusDAU = ConvertAlarmStatusDAU(_DAUnit.AlarmStatusRTDAU);
            dataSource.DataAcquisitionInterval = _DAUnit.DataAcquisitionInterval;
            dataSource.DAUMeasDefVersion = _DAUnit.DAUMeasDefVersion.ToString();
            dataSource.DAUSoftwareVersion = _DAUnit.DAUSoftwareVersion.ToString();
            dataSource.MeasDefVersion = _DAUnit.MeasDefVersion.ToString();
            dataSource.Ds_asset_id = _DAUnit.WindTurbineID.ToString();
            //dataSource.Ds_type_code = _DAUnit
            //dataSource.Ds_type_name =
            dataSource.IP = _DAUnit.IPAddress;
            dataSource.IsAvailable = _DAUnit.Enable == 0 ? false : true;
            dataSource.Name = _DAUnit.DAUnitName;
            dataSource.user_tag_ident = _DAUnit.DAUSN;
            dataSource.WaveSaveInterval = _DAUnit.WaveSaveInterval;
            dataSource.WindParkID = _DAUnit.WindParkID.ToString();
            dataSource.TrendSaveInterval = _DAUnit.TrendSaveInterval;

            // 振动通道转换
            dataSource.DAUChannelList = new List<DAUChannelV2>();

            foreach (WTCMSLive.Entity.Models.DAUVibChannel item in _DAUnit.DAUVibChannels)
            {
                dataSource.DAUChannelList.Add(
                    ConvertDAUChannelVib(item)
                );
            }

            // 过程量通道转换
            dataSource.ProcessChannelList = new List<DAUChannel_Process>();

            foreach (WTCMSLive.Entity.Models.DAUProcessChannel item in _DAUnit.DAUProcessChannels)
            {
                dataSource.ProcessChannelList.Add(
                        ConvertDAUChannelProcess(item)
                    );
            }

            // 转速通道转换
            dataSource.RotSpeedChannelList = new List<RotSpeedChannelV2>();

            foreach (WTCMSLive.Entity.Models.DAURotSpdChannel item in _DAUnit.DAURotSpdChannels)
            {
                dataSource.RotSpeedChannelList.Add(
                        ConvertDAUChannelRotSpd(item)
                    );
            }

            return dataSource;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 采集单元通道实体转换，振动
        /// </summary>
        /// <param name="_vibChannel"></param>
        /// <returns></returns>
        public static DAUChannelV2 ConvertDAUChannelVib(WTCMSLive.Entity.Models.DAUVibChannel _vibChannel)
        {
            DAUChannelV2 chan = new DAUChannelV2();

            if (!string.IsNullOrEmpty(_vibChannel.WindTurbineID))
            {
                chan.Ds_asset_id = _vibChannel.WindTurbineID.ToString();
            }

            chan.Channel_Type_Code = DAQChannel_type.ChannelType_Vib.Channel_Type_Code;
            chan.ChannelNumber = _vibChannel.ChannelNumber;
            chan.Coeff_a = Math.Round(_vibChannel.CoeffA.Value,2).ToString();
            chan.Coeff_b = Math.Round(_vibChannel.CoeffB.Value,2).ToString();
            chan.Description = _vibChannel.Description;
            chan.MaxBiasVolt = (float)_vibChannel.MaxVolt;
            chan.MinBiasVolt = (float)_vibChannel.MinVolt;
            chan.MeasLocVibID = _vibChannel.MeasLocationID.ToString();

            if (_vibChannel.DevMeasLocVibration != null)
            {
                chan.MeasLocVibName = _vibChannel.DevMeasLocVibration.MeasLocationName;
            }
            else if (_vibChannel.MeasLocationID != null)
            {
                WTCMSLive.Entity.Models.DevMeasLocVibration devMeasLocVibration = devService.GetDevMeasLocVibByMeasLocID(_vibChannel.MeasLocationID);
                if (devMeasLocVibration != null)
                {
                    chan.MeasLocVibName = devMeasLocVibration.MeasLocationName;
                }
                else {
                    chan.MeasLocVibName = string.Empty;
                }
            }
            //chan.Tr_type_code = 
            //chan.Tr_type_name = 

            return chan;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 采集单元通道实体转换，过程量
        /// </summary>
        /// <param name="_vibChannel"></param>
        /// <returns></returns>
        public static DAUChannel_Process ConvertDAUChannelProcess(WTCMSLive.Entity.Models.DAUProcessChannel _proChannel)
        {
            DAUChannel_Process chan = new DAUChannel_Process();

            if (!string.IsNullOrEmpty(_proChannel.WindTurbineID))
            {
                chan.Ds_asset_id = _proChannel.WindTurbineID.ToString();
            }

            chan.ChannelNumber = _proChannel.ChannelNumber;
            chan.Coeff_a = (float)_proChannel.CoeffA;
            chan.Coeff_b = (float)_proChannel.CoeffB;
            chan.MeasLoc_ProcessId = _proChannel.MeasLocationID.ToString();
            if (_proChannel.DevMeasLocProcess != null)
            {
                chan.MeasLoc_ProcessName = _proChannel.DevMeasLocProcess.MeasLocationName;
            }
            else
            {
                chan.MeasLoc_ProcessName = devService.GetDevMeasLocProcessByMeasLocID(_proChannel.MeasLocationID).MeasLocationName;
            }
            chan.PowerCoeff_a = (float)_proChannel.CoeffA;
            chan.PowerCoeff_b = (float)_proChannel.CoeffB;

            return chan;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 采集单元通道实体转换，转速
        /// </summary>
        /// <param name="_vibChannel"></param>
        /// <returns></returns>
        public static RotSpeedChannelV2 ConvertDAUChannelRotSpd(WTCMSLive.Entity.Models.DAURotSpdChannel _rotSpdChannel)
        {
            RotSpeedChannelV2 chan = new RotSpeedChannelV2();

            if (!string.IsNullOrEmpty(_rotSpdChannel.WindTurbineID))
            {
                chan.Ds_asset_id = _rotSpdChannel.WindTurbineID.ToString();
            }

            chan.ChannelNumber = _rotSpdChannel.ChannelNumber;
            chan.MeasLocRotSpdID = _rotSpdChannel.MeasLocationID.ToString();
            if (_rotSpdChannel.DevMeasLocRotSpd != null)
            {
                chan.MeasLoc_Rotspd = ConvertEntityDAToBusinessDEV.ConvertMeasLoc_RotSpd(_rotSpdChannel.DevMeasLocRotSpd);
            }
            if (_rotSpdChannel.DevMeasLocRotSpd != null)
            {
                chan.MeasLocRotSpdName = _rotSpdChannel.DevMeasLocRotSpd.MeasLocationName;
            }
            else
            {
                 Entity.Models.DevMeasLocRotSpd measLocRot=devService.GetDevMeasLocRotSpdByMeasLocID(_rotSpdChannel.MeasLocationID);
                chan.MeasLocRotSpdName = measLocRot.MeasLocationName;
            }

            return chan;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 采集单元状态实体转换
        /// </summary>
        /// <param name="_alarmStateDAU"></param>
        /// <returns></returns>
        public static RTAlarmStatus_DAU ConvertAlarmStatusDAU(WTCMSLive.Entity.Models.AlarmStatusRTDAU _alarmStateDAU)
        {
            if (_alarmStateDAU == null)
            {
                return null;
            }

            RTAlarmStatus_DAU alarmStateDAU = new RTAlarmStatus_DAU();

            alarmStateDAU.AlarmState = (EnumDAUStatus)_alarmStateDAU.AlarmState;
            alarmStateDAU.DAUName = _alarmStateDAU.DAUnit.DAUnitName;
            alarmStateDAU.Ds_asset_id = _alarmStateDAU.WindTurbineID.ToString();
            alarmStateDAU.StatusUpdateTime =(DateTime) _alarmStateDAU.StatusUpdateTime;
            alarmStateDAU.WindTurbineName = _alarmStateDAU.DAUnit.DAUnitName;
            alarmStateDAU.sensorRTList = ConvertRTAlarmStatus_ChannelList(_alarmStateDAU.AlarmStatusRTSensors.ToList());

            return alarmStateDAU;
        }

        /// <summary>
        /// 采集单元列表
        /// </summary>
        /// <param name="_alarmStateDAUList"></param>
        /// <returns></returns>
        public static List<RTAlarmStatus_DAU> ConvertAlarmStatusDAUList(List<WTCMSLive.Entity.Models.AlarmStatusRTDAU> _alarmStateDAUList)
        {
            if (_alarmStateDAUList == null)
            {
                return null;
            }

            List<RTAlarmStatus_DAU> dauList = new List<RTAlarmStatus_DAU>();

            foreach (WTCMSLive.Entity.Models.AlarmStatusRTDAU _entity in _alarmStateDAUList)
            {
                RTAlarmStatus_DAU alarmStateDAU = new RTAlarmStatus_DAU();

                alarmStateDAU.AlarmState = (EnumDAUStatus)_entity.AlarmState;
                alarmStateDAU.DAUName = _entity.DAUnit.DAUnitName;
                alarmStateDAU.Ds_asset_id = _entity.WindTurbineID.ToString();
                alarmStateDAU.StatusUpdateTime =(DateTime) _entity.StatusUpdateTime;
                alarmStateDAU.WindTurbineName = _entity.DAUnit.DAUnitName;

                dauList.Add(alarmStateDAU);
            }

            return dauList;
        }
        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 采集单元通道实体转换
        /// </summary>
        /// <param name="_alarmStateSensor"></param>
        /// <returns></returns>
        public static RTAlarmStatus_Channel ConvertAlarmSatusChannel(WTCMSLive.Entity.Models.AlarmStatusRTSensor _alarmStateSensor)
        {
            RTAlarmStatus_Channel alarmStateChan = new RTAlarmStatus_Channel();
            alarmStateChan.AlarmState = (EnumDAUStatus)_alarmStateSensor.AlarmState;
            alarmStateChan.ChannelNumber = _alarmStateSensor.ChannelNumber;
            alarmStateChan.DCDataValue = (double)_alarmStateSensor.DCDataValue;
            alarmStateChan.Ds_asset_id = _alarmStateSensor.WindTurbineID.ToString();

            for (int i = 0; i < _alarmStateSensor.AlarmStatusRTDAU.DAUnit.DAUVibChannels.ToList().Count; i++)
            {
                if (_alarmStateSensor.AlarmStatusRTDAU.DAUnit.DAUVibChannels.ToList()[i].ChannelNumber == alarmStateChan.ChannelNumber)
                {
                    //根据测量位置ID和部件ID取得振动测量位置
                    WTCMSLive.Entity.Models.DevMeasLocVibration devMeasLocVib = devService.GetDevMeasLocVibByMeasLocID(_alarmStateSensor.AlarmStatusRTDAU.DAUnit.DAUVibChannels.ToList()[i].MeasLocationID);
                    alarmStateChan.MeasLocationName = devMeasLocVib.MeasLocationName;// _alarmStateSensor.AlarmStatusRTDAU.DAUnit.DAUVibChannels.ToList()[i].DevMeasLocVibration.MeasLocationName;
                }
            }
            alarmStateChan.StatusUpdateTime =(DateTime) _alarmStateSensor.StatusUpdateTime;

            return alarmStateChan;
        }

        /// <summary>
        /// 采集单元通道列表
        /// </summary>
        /// <param name="_list"></param>
        /// <returns></returns>
        public static List<RTAlarmStatus_Channel> ConvertAlarmSatusChannelList(List<WTCMSLive.Entity.Models.AlarmStatusRTSensor> _list)
        {
            if (_list == null)
            {
                return null;
            }

            List<RTAlarmStatus_Channel> sensorList = new List<RTAlarmStatus_Channel>();

            foreach (WTCMSLive.Entity.Models.AlarmStatusRTSensor _entity in _list)
            {
                RTAlarmStatus_Channel sensorRT = new RTAlarmStatus_Channel();
                sensorRT.AlarmState = (EnumDAUStatus)_entity.AlarmState;
                sensorRT.ChannelNumber = _entity.ChannelNumber;
                sensorRT.DCDataValue = (double)_entity.DCDataValue;
                sensorRT.Ds_asset_id = _entity.WindTurbineID.ToString();

                for (int i = 0; i < _entity.AlarmStatusRTDAU.DAUnit.DAUVibChannels.ToList().Count; i++)
                {
                    if (_entity.AlarmStatusRTDAU.DAUnit.DAUVibChannels.ToList()[i].ChannelNumber == sensorRT.ChannelNumber)
                    {
                        sensorRT.MeasLocationName = _entity.AlarmStatusRTDAU.DAUnit.DAUVibChannels.ToList()[i].DevMeasLocVibration.MeasLocationName;
                    }
                }

                sensorRT.StatusUpdateTime = (DateTime)_entity.StatusUpdateTime;

                sensorList.Add(sensorRT);
            }

            return sensorList;
        }
        /// <summary>
        /// DAU 实时状态转换
        /// </summary>
        /// <param name="_list"></param>
        /// <returns></returns>
        public static List<RTAlarmStatus_Channel> ConvertRTAlarmStatus_ChannelList(List<WTCMSLive.Entity.Models.AlarmStatusRTSensor> _list)
        {
            if (_list == null)
            {
                return null;
            }
            List<RTAlarmStatus_Channel> sensorList = new List<RTAlarmStatus_Channel>();
            foreach (WTCMSLive.Entity.Models.AlarmStatusRTSensor _entity in _list)
            {
                RTAlarmStatus_Channel sensorRT = new RTAlarmStatus_Channel();
                sensorRT.AlarmState = (EnumDAUStatus)_entity.AlarmState;
                sensorRT.ChannelNumber = _entity.ChannelNumber;
                sensorRT.DCDataValue = (double)_entity.DCDataValue;
                sensorRT.Ds_asset_id = _entity.WindTurbineID.ToString();
                sensorRT.StatusUpdateTime = (DateTime)_entity.StatusUpdateTime;
                for (int i = 0; i < _entity.AlarmStatusRTDAU.DAUnit.DAUVibChannels.ToList().Count; i++)
                {
                    if (_entity.AlarmStatusRTDAU.DAUnit.DAUVibChannels.ToList()[i].ChannelNumber == sensorRT.ChannelNumber)
                    {
                        if (_entity.AlarmStatusRTDAU.DAUnit.DAUVibChannels.ToList()[i].DevMeasLocVibration != null)
                        {
                            sensorRT.MeasLocationName = _entity.AlarmStatusRTDAU.DAUnit.DAUVibChannels.ToList()[i].DevMeasLocVibration.MeasLocationName;
                        }
                    }
                }
                sensorList.Add(sensorRT);                
            }
            return sensorList;
        }
        /// <summary>
        /// DAU日志转换
        /// </summary>
        /// <param name="_list"></param>
        /// <returns></returns>
        public static List<DAURunLog> ConvertDAURunLog(List<WTCMSLive.Entity.Models.DAURunLog> _list)
        {
            if (_list == null)
            {
                return null;
            }
            List<DAURunLog> logList = new List<DAURunLog>();
            foreach (WTCMSLive.Entity.Models.DAURunLog _entity in _list)
            {
                DAURunLog runLog = new DAURunLog();
                runLog.WindTurbineID = _entity.WindTurbineID;
                runLog.EventTime = _entity.EventTime;
                runLog.AlarmState = _entity.AlarmState;
                runLog.LogTitle = _entity.LogTitle;
                logList.Add(runLog);
            }
            return logList;
        }

        #region  转速偏置电压
        public static RTAlarmStatus_RSChannel ConvertRSChannel(WTCMSLive.Entity.Models.AlarmStatusRTRSSensor rsChannel)
        {
            if (rsChannel == null) return null;
            RTAlarmStatus_RSChannel mySensor = new RTAlarmStatus_RSChannel();
            mySensor.WindTurbineID = rsChannel.WindTurbineID;
            mySensor.ChannelNumber = rsChannel.ChannelNumber;
            mySensor.AlarmState = (EnumRotSpeedChannelStatus)rsChannel.AlarmState;
            mySensor.StatusUpdateTime = rsChannel.StatusUpdateTime.Value;
            return mySensor;
        }
        #endregion
    }
}
