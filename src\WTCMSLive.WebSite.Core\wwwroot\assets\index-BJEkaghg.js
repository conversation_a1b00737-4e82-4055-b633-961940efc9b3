import{a0 as v,b as w,aP as j,aQ as pe,ep as ve,a4 as ne,y as Z,r as ge,w as C,dE as me,h as le,ae as ie,a5 as be,aa as Se,j as B,af as z,a8 as X,aw as ye,aE as Me,ah as Te,Z as $e,_ as we,ax as xe,a1 as k,e5 as He}from"./index-BjOW8S1L.js";import{R as Ee,v as I}from"./styleChecker-CFtINSLw.js";import{w as E}from"./index-7iPMz_Qy.js";function te(){const e=t=>{e.current=t};return e}const V=(e,t)=>{let{height:o,offset:l,prefixCls:s,onInnerResize:i}=e,{slots:d}=t;var a;let f={},r={display:"flex",flexDirection:"column"};return l!==void 0&&(f={height:`${o}px`,position:"relative",overflow:"hidden"},r=v(v({},r),{transform:`translateY(${l}px)`,position:"absolute",left:0,right:0,top:0})),w("div",{style:f},[w(Ee,{onResize:p=>{let{offsetHeight:h}=p;h&&i&&i()}},{default:()=>[w("div",{style:r,class:j({[`${s}-holder-inner`]:s})},[(a=d.default)===null||a===void 0?void 0:a.call(d)])]})])};V.displayName="Filter";V.inheritAttrs=!1;V.props={prefixCls:String,height:Number,offset:Number,onInnerResize:Function};const re=(e,t)=>{let{setRef:o}=e,{slots:l}=t;var s;const i=pe((s=l.default)===null||s===void 0?void 0:s.call(l));return i&&i.length?ve(i[0],{ref:o}):i};re.props={setRef:{type:Function,default:()=>{}}};const Re=20;function oe(e){return"touches"in e?e.touches[0].pageY:e.pageY}const Ce=ne({compatConfig:{MODE:3},name:"ScrollBar",inheritAttrs:!1,props:{prefixCls:String,scrollTop:Number,scrollHeight:Number,height:Number,count:Number,onScroll:{type:Function},onStartMove:{type:Function},onStopMove:{type:Function}},setup(){return{moveRaf:null,scrollbarRef:te(),thumbRef:te(),visibleTimeout:null,state:Z({dragging:!1,pageY:null,startTop:null,visible:!1})}},watch:{scrollTop:{handler(){this.delayHidden()},flush:"post"}},mounted(){var e,t;(e=this.scrollbarRef.current)===null||e===void 0||e.addEventListener("touchstart",this.onScrollbarTouchStart,I?{passive:!1}:!1),(t=this.thumbRef.current)===null||t===void 0||t.addEventListener("touchstart",this.onMouseDown,I?{passive:!1}:!1)},beforeUnmount(){this.removeEvents(),clearTimeout(this.visibleTimeout)},methods:{delayHidden(){clearTimeout(this.visibleTimeout),this.state.visible=!0,this.visibleTimeout=setTimeout(()=>{this.state.visible=!1},2e3)},onScrollbarTouchStart(e){e.preventDefault()},onContainerMouseDown(e){e.stopPropagation(),e.preventDefault()},patchEvents(){window.addEventListener("mousemove",this.onMouseMove),window.addEventListener("mouseup",this.onMouseUp),this.thumbRef.current.addEventListener("touchmove",this.onMouseMove,I?{passive:!1}:!1),this.thumbRef.current.addEventListener("touchend",this.onMouseUp)},removeEvents(){window.removeEventListener("mousemove",this.onMouseMove),window.removeEventListener("mouseup",this.onMouseUp),this.scrollbarRef.current.removeEventListener("touchstart",this.onScrollbarTouchStart,I?{passive:!1}:!1),this.thumbRef.current&&(this.thumbRef.current.removeEventListener("touchstart",this.onMouseDown,I?{passive:!1}:!1),this.thumbRef.current.removeEventListener("touchmove",this.onMouseMove,I?{passive:!1}:!1),this.thumbRef.current.removeEventListener("touchend",this.onMouseUp)),E.cancel(this.moveRaf)},onMouseDown(e){const{onStartMove:t}=this.$props;v(this.state,{dragging:!0,pageY:oe(e),startTop:this.getTop()}),t(),this.patchEvents(),e.stopPropagation(),e.preventDefault()},onMouseMove(e){const{dragging:t,pageY:o,startTop:l}=this.state,{onScroll:s}=this.$props;if(E.cancel(this.moveRaf),t){const i=oe(e)-o,d=l+i,a=this.getEnableScrollRange(),f=this.getEnableHeightRange(),r=f?d/f:0,p=Math.ceil(r*a);this.moveRaf=E(()=>{s(p)})}},onMouseUp(){const{onStopMove:e}=this.$props;this.state.dragging=!1,e(),this.removeEvents()},getSpinHeight(){const{height:e,scrollHeight:t}=this.$props;let o=e/t*100;return o=Math.max(o,Re),o=Math.min(o,e/2),Math.floor(o)},getEnableScrollRange(){const{scrollHeight:e,height:t}=this.$props;return e-t||0},getEnableHeightRange(){const{height:e}=this.$props,t=this.getSpinHeight();return e-t||0},getTop(){const{scrollTop:e}=this.$props,t=this.getEnableScrollRange(),o=this.getEnableHeightRange();return e===0||t===0?0:e/t*o},showScroll(){const{height:e,scrollHeight:t}=this.$props;return t>e}},render(){const{dragging:e,visible:t}=this.state,{prefixCls:o}=this.$props,l=this.getSpinHeight()+"px",s=this.getTop()+"px",i=this.showScroll(),d=i&&t;return w("div",{ref:this.scrollbarRef,class:j(`${o}-scrollbar`,{[`${o}-scrollbar-show`]:i}),style:{width:"8px",top:0,bottom:0,right:0,position:"absolute",display:d?void 0:"none"},onMousedown:this.onContainerMouseDown,onMousemove:this.delayHidden},[w("div",{ref:this.thumbRef,class:j(`${o}-scrollbar-thumb`,{[`${o}-scrollbar-thumb-moving`]:e}),style:{width:"100%",height:l,top:s,left:0,position:"absolute",background:"rgba(0, 0, 0, 0.5)",borderRadius:"99px",cursor:"pointer",userSelect:"none"},onMousedown:this.onMouseDown},null)])}});function Ie(e,t,o,l){const s=new Map,i=new Map,d=ge(Symbol("update"));C(e,()=>{d.value=Symbol("update")});let a;function f(){E.cancel(a)}function r(){f(),a=E(()=>{s.forEach((h,c)=>{if(h&&h.offsetParent){const{offsetHeight:g}=h;i.get(c)!==g&&(d.value=Symbol("update"),i.set(c,h.offsetHeight))}})})}function p(h,c){const g=t(h);s.get(g),c?(s.set(g,c.$el||c),r()):s.delete(g)}return me(()=>{f()}),[p,r,i,d]}function Le(e,t,o,l,s,i,d,a){let f;return r=>{if(r==null){a();return}E.cancel(f);const p=t.value,h=l.itemHeight;if(typeof r=="number")d(r);else if(r&&typeof r=="object"){let c;const{align:g}=r;"index"in r?{index:c}=r:c=p.findIndex(T=>s(T)===r.key);const{offset:b=0}=r,O=(T,L)=>{if(T<0||!e.value)return;const D=e.value.clientHeight;let S=!1,M=L;if(D){const F=L||g;let _=0,x=0,H=0;const Y=Math.min(p.length,c);for(let y=0;y<=Y;y+=1){const U=s(p[y]);x=_;const N=o.get(U);H=x+(N===void 0?h:N),_=H,y===c&&N===void 0&&(S=!0)}const P=e.value.scrollTop;let $=null;switch(F){case"top":$=x-b;break;case"bottom":$=H-D+b;break;default:{const y=P+D;x<P?M="top":H>y&&(M="bottom")}}$!==null&&$!==P&&d($)}f=E(()=>{S&&i(),O(T-1,M)},2)};O(5)}}}const De=typeof navigator=="object"&&/Firefox/i.test(navigator.userAgent),se=(e,t)=>{let o=!1,l=null;function s(){clearTimeout(l),o=!0,l=setTimeout(()=>{o=!1},50)}return function(i){let d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const a=i<0&&e.value||i>0&&t.value;return d&&a?(clearTimeout(l),o=!1):(!a||o)&&s(),!o&&a}};function Fe(e,t,o,l){let s=0,i=null,d=null,a=!1;const f=se(t,o);function r(h){if(!e.value)return;E.cancel(i);const{deltaY:c}=h;s+=c,d=c,!f(c)&&(De||h.preventDefault(),i=E(()=>{l(s*(a?10:1)),s=0}))}function p(h){e.value&&(a=h.detail===d)}return[r,p]}const Pe=14/15;function Be(e,t,o){let l=!1,s=0,i=null,d=null;const a=()=>{i&&(i.removeEventListener("touchmove",f),i.removeEventListener("touchend",r))},f=c=>{if(l){const g=Math.ceil(c.touches[0].pageY);let b=s-g;s=g,o(b)&&c.preventDefault(),clearInterval(d),d=setInterval(()=>{b*=Pe,(!o(b,!0)||Math.abs(b)<=.1)&&clearInterval(d)},16)}},r=()=>{l=!1,a()},p=c=>{a(),c.touches.length===1&&!l&&(l=!0,s=Math.ceil(c.touches[0].pageY),i=c.target,i.addEventListener("touchmove",f,{passive:!1}),i.addEventListener("touchend",r))},h=()=>{};le(()=>{document.addEventListener("touchmove",h,{passive:!1}),C(e,c=>{t.value.removeEventListener("touchstart",p),a(),clearInterval(d),c&&t.value.addEventListener("touchstart",p,{passive:!1})},{immediate:!0})}),ie(()=>{document.removeEventListener("touchmove",h)})}var Oe=function(e,t){var o={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&t.indexOf(l)<0&&(o[l]=e[l]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,l=Object.getOwnPropertySymbols(e);s<l.length;s++)t.indexOf(l[s])<0&&Object.prototype.propertyIsEnumerable.call(e,l[s])&&(o[l[s]]=e[l[s]]);return o};const _e=[],Ne={overflowY:"auto",overflowAnchor:"none"};function ze(e,t,o,l,s,i){let{getKey:d}=i;return e.slice(t,o+1).map((a,f)=>{const r=t+f,p=s(a,r,{}),h=d(a);return w(re,{key:h,setRef:c=>l(a,c)},{default:()=>[p]})})}const Ge=ne({compatConfig:{MODE:3},name:"List",inheritAttrs:!1,props:{prefixCls:String,data:be.array,height:Number,itemHeight:Number,fullHeight:{type:Boolean,default:void 0},itemKey:{type:[String,Number,Function],required:!0},component:{type:[String,Object]},virtual:{type:Boolean,default:void 0},children:Function,onScroll:Function,onMousedown:Function,onMouseenter:Function,onVisibleChange:Function},setup(e,t){let{expose:o}=t;const l=B(()=>{const{height:n,itemHeight:u,virtual:m}=e;return!!(m!==!1&&n&&u)}),s=B(()=>{const{height:n,itemHeight:u,data:m}=e;return l.value&&m&&u*m.length>n}),i=Z({scrollTop:0,scrollMoving:!1}),d=B(()=>e.data||_e),a=z([]);C(d,()=>{a.value=Te(d.value).slice()},{immediate:!0});const f=z(n=>{});C(()=>e.itemKey,n=>{typeof n=="function"?f.value=n:f.value=u=>u==null?void 0:u[n]},{immediate:!0});const r=z(),p=z(),h=z(),c=n=>f.value(n),g={getKey:c};function b(n){let u;typeof n=="function"?u=n(i.scrollTop):u=n;const m=_(u);r.value&&(r.value.scrollTop=m),i.scrollTop=m}const[O,T,L,D]=Ie(a,c),S=Z({scrollHeight:void 0,start:0,end:0,offset:void 0}),M=z(0);le(()=>{X(()=>{var n;M.value=((n=p.value)===null||n===void 0?void 0:n.offsetHeight)||0})}),ye(()=>{X(()=>{var n;M.value=((n=p.value)===null||n===void 0?void 0:n.offsetHeight)||0})}),C([l,a],()=>{l.value||v(S,{scrollHeight:void 0,start:0,end:a.value.length-1,offset:void 0})},{immediate:!0}),C([l,a,M,s],()=>{l.value&&!s.value&&v(S,{scrollHeight:M.value,start:0,end:a.value.length-1,offset:void 0}),r.value&&(i.scrollTop=r.value.scrollTop)},{immediate:!0}),C([s,l,()=>i.scrollTop,a,D,()=>e.height,M],()=>{if(!l.value||!s.value)return;let n=0,u,m,R;const W=a.value.length,ue=a.value,Q=i.scrollTop,{itemHeight:J,height:ee}=e,de=Q+ee;for(let A=0;A<W;A+=1){const he=ue[A],fe=c(he);let K=L.get(fe);K===void 0&&(K=J);const G=n+K;u===void 0&&G>=Q&&(u=A,m=n),R===void 0&&G>de&&(R=A),n=G}u===void 0&&(u=0,m=0,R=Math.ceil(ee/J)),R===void 0&&(R=W-1),R=Math.min(R+1,W),v(S,{scrollHeight:n,start:u,end:R,offset:m})},{immediate:!0});const F=B(()=>S.scrollHeight-e.height);function _(n){let u=n;return Number.isNaN(F.value)||(u=Math.min(u,F.value)),u=Math.max(u,0),u}const x=B(()=>i.scrollTop<=0),H=B(()=>i.scrollTop>=F.value),Y=se(x,H);function P(n){b(n)}function $(n){var u;const{scrollTop:m}=n.currentTarget;m!==i.scrollTop&&b(m),(u=e.onScroll)===null||u===void 0||u.call(e,n)}const[y,U]=Fe(l,x,H,n=>{b(u=>u+n)});Be(l,r,(n,u)=>Y(n,u)?!1:(y({preventDefault(){},deltaY:n}),!0));function N(n){l.value&&n.preventDefault()}const q=()=>{r.value&&(r.value.removeEventListener("wheel",y,I?{passive:!1}:!1),r.value.removeEventListener("DOMMouseScroll",U),r.value.removeEventListener("MozMousePixelScroll",N))};Me(()=>{X(()=>{r.value&&(q(),r.value.addEventListener("wheel",y,I?{passive:!1}:!1),r.value.addEventListener("DOMMouseScroll",U),r.value.addEventListener("MozMousePixelScroll",N))})}),ie(()=>{q()});const ae=Le(r,a,L,e,c,T,b,()=>{var n;(n=h.value)===null||n===void 0||n.delayHidden()});o({scrollTo:ae});const ce=B(()=>{let n=null;return e.height&&(n=v({[e.fullHeight?"height":"maxHeight"]:e.height+"px"},Ne),l.value&&(n.overflowY="hidden",i.scrollMoving&&(n.pointerEvents="none"))),n});return C([()=>S.start,()=>S.end,a],()=>{if(e.onVisibleChange){const n=a.value.slice(S.start,S.end+1);e.onVisibleChange(n,a.value)}},{flush:"post"}),{state:i,mergedData:a,componentStyle:ce,onFallbackScroll:$,onScrollBar:P,componentRef:r,useVirtual:l,calRes:S,collectHeight:T,setInstance:O,sharedConfig:g,scrollBarRef:h,fillerInnerRef:p,delayHideScrollBar:()=>{var n;(n=h.value)===null||n===void 0||n.delayHidden()}}},render(){const e=v(v({},this.$props),this.$attrs),{prefixCls:t="rc-virtual-list",height:o,itemHeight:l,fullHeight:s,data:i,itemKey:d,virtual:a,component:f="div",onScroll:r,children:p=this.$slots.default,style:h,class:c}=e,g=Oe(e,["prefixCls","height","itemHeight","fullHeight","data","itemKey","virtual","component","onScroll","children","style","class"]),b=j(t,c),{scrollTop:O}=this.state,{scrollHeight:T,offset:L,start:D,end:S}=this.calRes,{componentStyle:M,onFallbackScroll:F,onScrollBar:_,useVirtual:x,collectHeight:H,sharedConfig:Y,setInstance:P,mergedData:$,delayHideScrollBar:y}=this;return w("div",Se({style:v(v({},h),{position:"relative"}),class:b},g),[w(f,{class:`${t}-holder`,style:M,ref:"componentRef",onScroll:F,onMouseenter:y},{default:()=>[w(V,{prefixCls:t,height:T,offset:L,onInnerResize:H,ref:"fillerInnerRef"},{default:()=>ze($,D,S,P,p,Y)})]}),x&&w(Ce,{ref:"scrollBarRef",prefixCls:t,scrollTop:O,height:o,scrollHeight:T,count:$.length,onScroll:_,onStartMove:()=>{this.state.scrollMoving=!0},onStopMove:()=>{this.state.scrollMoving=!1}},null)])}}),Ye=new xe("antCheckboxEffect",{"0%":{transform:"scale(1)",opacity:.5},"100%":{transform:"scale(1.6)",opacity:0}}),Ae=e=>{const{checkboxCls:t}=e,o=`${t}-wrapper`;return[{[`${t}-group`]:v(v({},k(e)),{display:"inline-flex",flexWrap:"wrap",columnGap:e.marginXS,[`> ${e.antCls}-row`]:{flex:1}}),[o]:v(v({},k(e)),{display:"inline-flex",alignItems:"baseline",cursor:"pointer","&:after":{display:"inline-block",width:0,overflow:"hidden",content:"'\\a0'"},[`& + ${o}`]:{marginInlineStart:0},[`&${o}-in-form-item`]:{'input[type="checkbox"]':{width:14,height:14}}}),[t]:v(v({},k(e)),{position:"relative",whiteSpace:"nowrap",lineHeight:1,cursor:"pointer",alignSelf:"center",[`${t}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0,margin:0,[`&:focus-visible + ${t}-inner`]:v({},He(e))},[`${t}-inner`]:{boxSizing:"border-box",position:"relative",top:0,insetInlineStart:0,display:"block",width:e.checkboxSize,height:e.checkboxSize,direction:"ltr",backgroundColor:e.colorBgContainer,border:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,borderCollapse:"separate",transition:`all ${e.motionDurationSlow}`,"&:after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"21.5%",display:"table",width:e.checkboxSize/14*5,height:e.checkboxSize/14*8,border:`${e.lineWidthBold}px solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`}},"& + span":{paddingInlineStart:e.paddingXS,paddingInlineEnd:e.paddingXS}})},{[t]:{"&-indeterminate":{[`${t}-inner`]:{"&:after":{top:"50%",insetInlineStart:"50%",width:e.fontSizeLG/2,height:e.fontSizeLG/2,backgroundColor:e.colorPrimary,border:0,transform:"translate(-50%, -50%) scale(1)",opacity:1,content:'""'}}}}},{[`${o}:hover ${t}:after`]:{visibility:"visible"},[`
        ${o}:not(${o}-disabled),
        ${t}:not(${t}-disabled)
      `]:{[`&:hover ${t}-inner`]:{borderColor:e.colorPrimary}},[`${o}:not(${o}-disabled)`]:{[`&:hover ${t}-checked:not(${t}-disabled) ${t}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"},[`&:hover ${t}-checked:not(${t}-disabled):after`]:{borderColor:e.colorPrimaryHover}}},{[`${t}-checked`]:{[`${t}-inner`]:{backgroundColor:e.colorPrimary,borderColor:e.colorPrimary,"&:after":{opacity:1,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`}},"&:after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderRadius:e.borderRadiusSM,visibility:"hidden",border:`${e.lineWidthBold}px solid ${e.colorPrimary}`,animationName:Ye,animationDuration:e.motionDurationSlow,animationTimingFunction:"ease-in-out",animationFillMode:"backwards",content:'""',transition:`all ${e.motionDurationSlow}`}},[`
        ${o}-checked:not(${o}-disabled),
        ${t}-checked:not(${t}-disabled)
      `]:{[`&:hover ${t}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"},[`&:hover ${t}:after`]:{borderColor:e.colorPrimaryHover}}},{[`${o}-disabled`]:{cursor:"not-allowed"},[`${t}-disabled`]:{[`&, ${t}-input`]:{cursor:"not-allowed",pointerEvents:"none"},[`${t}-inner`]:{background:e.colorBgContainerDisabled,borderColor:e.colorBorder,"&:after":{borderColor:e.colorTextDisabled}},"&:after":{display:"none"},"& + span":{color:e.colorTextDisabled},[`&${t}-indeterminate ${t}-inner::after`]:{background:e.colorTextDisabled}}}]};function Ue(e,t){const o=we(t,{checkboxCls:`.${e}`,checkboxSize:t.controlInteractiveSize});return[Ae(o)]}const Xe=$e("Checkbox",(e,t)=>{let{prefixCls:o}=t;return[Ue(o,e)]});export{Ge as L,te as c,Ue as g,Xe as u};
