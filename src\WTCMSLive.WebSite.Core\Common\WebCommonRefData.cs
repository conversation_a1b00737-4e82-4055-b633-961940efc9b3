﻿namespace WTCMSLive.WebSite.Common
{
    public class WebCommonRefData
    {
        /// <summary>
        /// 获取信号类型
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, string>> GetSignalTypeList()
        {
            return new List<KeyValuePair<string, string>> {
                new KeyValuePair<string, string>("加速度","加速度"),
            };
        }


        /// <summary>
        /// 获取下限频率列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, float>> GetLowerLimitFreqList()
        {
            return new List<KeyValuePair<string, float>> {
                new KeyValuePair<string, float>("0.16 Hz",0.16F),
                new KeyValuePair<string, float>("2 Hz", 2),
                new KeyValuePair<string, float>("10 Hz", 10),
                new KeyValuePair<string, float>("50 Hz", 50),
                new KeyValuePair<string, float>("100 Hz", 100),
                new KeyValuePair<string, float>("1.25K Hz", 1250),
                new KeyValuePair<string, float>("2.5K Hz", 2500),
                new KeyValuePair<string, float>("5K Hz", 5000),
            };
        }


        /// <summary>
        /// 获取上限频率列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, float>> GetUpperLimitFreqList()
        {
            return new List<KeyValuePair<string, float>> {
                new KeyValuePair<string, float>("1 Hz", 1),
                new KeyValuePair<string, float>("25 Hz", 25),
                new KeyValuePair<string, float>("50 Hz", 50),
                new KeyValuePair<string, float>("1K Hz", 1000),
                new KeyValuePair<string, float>("2K Hz", 2000),
                new KeyValuePair<string, float>("4K Hz", 4000),
                new KeyValuePair<string, float>("5K Hz", 5000),
                new KeyValuePair<string, float>("8K Hz", 8000),
                new KeyValuePair<string, float>("10k Hz", 10000),
                new KeyValuePair<string, float>("16K Hz", 16000),
                new KeyValuePair<string, float>("20K Hz", 20000),
                new KeyValuePair<string, float>("25k Hz", 25000),
                new KeyValuePair<string, float>("40k Hz", 40000),
                new KeyValuePair<string, float>("50k Hz", 50000),
                new KeyValuePair<string, float>("100 Hz", 100F),
                new KeyValuePair<string, float>("200 Hz", 200),
                new KeyValuePair<string, float>("500 Hz", 500),
               // new KeyValuePair<string, float>("2K Hz", 2000),
            };
        }

        public static List<KeyValuePair<string, float>> GetAMS_Process_UpperLimitFreqList()
        {
            return new List<KeyValuePair<string, float>> {
                new KeyValuePair<string, float>("1k Hz", 1000),
                new KeyValuePair<string, float>("2k Hz", 2000),
                new KeyValuePair<string, float>("4k Hz", 4000),
                new KeyValuePair<string, float>("8k Hz", 8000),
                new KeyValuePair<string, float>("20k Hz", 20000),
                new KeyValuePair<string, float>("100 Hz", 100F),
                new KeyValuePair<string, float>("200 Hz", 200),
                new KeyValuePair<string, float>("500 Hz", 500)
            };
        }

        public static List<KeyValuePair<string, float>> GetAMS_CMS_UpperLimitFreqList()
        {
            return new List<KeyValuePair<string, float>> {
                new KeyValuePair<string, float>("1 Hz", 1F),
                new KeyValuePair<string, float>("10 Hz", 10F),
                new KeyValuePair<string, float>("100 Hz", 100F),
                new KeyValuePair<string, float>("200 Hz", 200),
                new KeyValuePair<string, float>("500 Hz", 500),
                new KeyValuePair<string, float>("1k Hz", 1000),
                new KeyValuePair<string, float>("2k Hz", 2000),
                new KeyValuePair<string, float>("4k Hz", 4000),
                new KeyValuePair<string, float>("5k Hz", 5000),
                new KeyValuePair<string, float>("8k Hz", 8000),
                new KeyValuePair<string, float>("10k Hz", 10000),
                new KeyValuePair<string, float>("20k Hz", 20000),
                new KeyValuePair<string, float>("40k Hz", 40000),
            };
        }


        /// <summary>
        /// 获取包络上限频率列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, float>> GetParamUpperLimitFreqList()
        {
            return new List<KeyValuePair<string, float>> {
                new KeyValuePair<string, float>("500 Hz", 500),
                new KeyValuePair<string, float>("1K Hz", 1000),
                new KeyValuePair<string, float>("2K Hz", 2000)
            };
        }


        /// <summary>
        /// 获取采样时间长度列表， 单位：秒
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, int>> GetSampleTimeLengthList()
        {
            return new List<KeyValuePair<string, int>> {
                new KeyValuePair<string, int>("1 秒", 1),
                new KeyValuePair<string, int>("3 秒", 3),
                new KeyValuePair<string, int>("4 秒", 4),
                new KeyValuePair<string, int>("5 秒", 5),
                new KeyValuePair<string, int>("10 秒", 10),
                new KeyValuePair<string, int>("15 秒", 15),
                new KeyValuePair<string, int>("20 秒", 20),
                new KeyValuePair<string, int>("30 秒", 30),
                new KeyValuePair<string, int>("40 秒", 40),
                new KeyValuePair<string, int>("50 秒", 50),
                new KeyValuePair<string, int>("90 秒", 90),
            };
        }

        /// <summary>
        /// 获取晃度仪支持的采样频率列表，单位：Hz
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, float>> GetSVMAcquisitionFrequency()
        {
            return new List<KeyValuePair<string, float>> {
                new KeyValuePair<string, float>("0.5 Hz", 0.5f),
                new KeyValuePair<string, float>("1 Hz", 1),
                new KeyValuePair<string, float>("2 Hz", 2),
                new KeyValuePair<string, float>("4 Hz", 4),
                new KeyValuePair<string, float>("8 Hz", 8),
                new KeyValuePair<string, float>("16 Hz", 16),
                new KeyValuePair<string, float>("32 Hz", 32),
                new KeyValuePair<string, float>("50 Hz", 50),
                new KeyValuePair<string, float>("64 Hz", 64),
                new KeyValuePair<string, float>("128 Hz", 128),
                new KeyValuePair<string, float>("256 Hz", 256),
            };
        }


        /// <summary>
        /// 获取每周期采样点数
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, int>> GetDotNumberPerCycleList()
        {
            return new List<KeyValuePair<string, int>> {
                new KeyValuePair<string, int>("8", 8),
                new KeyValuePair<string, int>("16", 16),
                new KeyValuePair<string, int>("32", 32),
                new KeyValuePair<string, int>("64", 64),
                new KeyValuePair<string, int>("128", 128),
                new KeyValuePair<string, int>("256", 256),
            };
        }


        /// <summary>
        /// 获取采样周期数
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, int>> GetCycleNumberList()
        {
            return new List<KeyValuePair<string, int>> {
                new KeyValuePair<string, int>("10", 10),
                new KeyValuePair<string, int>("20", 20),
                new KeyValuePair<string, int>("32", 32),
                new KeyValuePair<string, int>("50", 50),
                new KeyValuePair<string, int>("64", 64),
                new KeyValuePair<string, int>("100", 100),
                new KeyValuePair<string, int>("128", 128),
                new KeyValuePair<string, int>("200", 200),
                new KeyValuePair<string, int>("256", 256),
            };
        }



        /// <summary>
        /// 获取包络带宽列表
        /// 包络解调高通滤波器和包络带宽之间的约束关系是：
        /// 包络带宽不能大于包络高通滤波器的截止频率，
        /// 比如：高通滤波器的截止频率500Hz，则包络带宽的选择不能大于500Hz。
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, int>> GetEnvelopeBandWidthList()
        {
            return new List<KeyValuePair<string, int>> {
                new KeyValuePair<string, int>("10 Hz", 10),
                new KeyValuePair<string, int>("20 Hz", 20),
                new KeyValuePair<string, int>("50 Hz", 50),
                new KeyValuePair<string, int>("100 Hz", 100),
                new KeyValuePair<string, int>("200 Hz", 200),
                new KeyValuePair<string, int>("500 Hz", 500),
                new KeyValuePair<string, int>("800 Hz", 800),
                new KeyValuePair<string, int>("1K Hz", 1000),
                new KeyValuePair<string, int>("2K Hz", 2000),
                new KeyValuePair<string, int>("5K Hz", 5000),
                new KeyValuePair<string, int>("10K Hz", 10000),
            };
        }



        /// <summary>
        /// 获取包络滤波器列表
        /// 500Hz, 1KHz, 2KHz, 5KHz，10KHz，20KHz 
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, int>> GetEnvelopeFilterList()
        {
            return new List<KeyValuePair<string, int>> {
                new KeyValuePair<string, int>("500 Hz", 500),
                new KeyValuePair<string, int>("1K Hz", 1000),
                new KeyValuePair<string, int>("2K Hz", 2000),
                new KeyValuePair<string, int>("5K Hz", 5000),
                new KeyValuePair<string, int>("10K Hz", 10000),
                //new KeyValuePair<string, int>("20K Hz", 20000),
            };
        }


        /// <summary>
        /// 获取分析阶次列表
        /// 阶次包络设置选项
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, float>> GetAnalysisOrderList()
        {
            return new List<KeyValuePair<string, float>> {
                new KeyValuePair<string, float>("12.5 阶", 12.5F),
                new KeyValuePair<string, float>("25 阶", 25),
                new KeyValuePair<string, float>("50 阶", 50),
                new KeyValuePair<string, float>("100 阶", 100),
                new KeyValuePair<string, float>("200 阶", 200),
                new KeyValuePair<string, float>("400 阶", 400),
            };
        }


        /// <summary>
        /// 获取测量位置方向列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, string>> GetLocOrientionList()
        {
            return new List<KeyValuePair<string, string>> {
                new KeyValuePair<string, string>("水平","水平"),
                new KeyValuePair<string, string>("水平3点","水平3点"),
                new KeyValuePair<string, string>("水平9点","水平9点"),
                new KeyValuePair<string, string>("垂直","垂直"),
                new KeyValuePair<string, string>("垂直6点","垂直6点"),
                new KeyValuePair<string, string>("垂直12点","垂直12点"),
                new KeyValuePair<string, string>("轴向","轴向"),
                new KeyValuePair<string, string>("径向","径向"),
            };
        }



        /// <summary>
        /// 获取部件截面列表
        /// </summary>
        /// <param name，"_comType"></param>
        /// <returns></returns>
        public static List<KeyValuePair<string, string>> GetComSectionList(string _comType)
        {
            if (_comType == "齿轮箱")
            {
                return new List<KeyValuePair<string, string>> {
                    new KeyValuePair<string, string>("无","无"),
                    new KeyValuePair<string, string>("输入轴","输入轴"),
                    new KeyValuePair<string, string>("输出轴","输出轴"),
                    new KeyValuePair<string, string>("主轴承","主轴承"),
                    new KeyValuePair<string, string>("第一级","第一级"),
                    new KeyValuePair<string, string>("第二级","第二级"),
                    new KeyValuePair<string, string>("第三级","第三级"),
                    new KeyValuePair<string, string>("内齿圈","内齿圈"),
                    new KeyValuePair<string, string>("低速轴","低速轴"),
                    new KeyValuePair<string, string>("中间轴","中间轴"),
                    new KeyValuePair<string, string>("高速轴","高速轴"),
                };

            }

            return new List<KeyValuePair<string, string>> {
                    new KeyValuePair<string, string>("无","无"),
                    new KeyValuePair<string, string>("驱动端","驱动端"),
                    new KeyValuePair<string, string>("非驱动端","非驱动端"),
                };
        }
    }
}