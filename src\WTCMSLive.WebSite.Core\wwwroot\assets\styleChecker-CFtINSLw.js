import{a4 as k,y as Ln,h as De,aw as Ke,dE as qt,w as X,e9 as ue,ea as In,a0 as m,a5 as P,eE as Kt,b as T,aD as Ze,aa as q,p as Zt,eF as Bn,r as G,aQ as Qt,aP as re,af as S,ae as Jt,j as B,aJ as Qe,ep as en,dH as Hn,e7 as zn,eG as Vn,F as tn,dL as Fn,dF as oe,a8 as pe,aM as Wn,aC as jn,v as Xn,dn as st,eH as kn,aE as nn,ec as se,eI as Yn,eJ as at,at as Un,eK as Gn,ci as qn,eL as lt,e2 as Kn,e3 as Zn,aR as Qn,dG as Jn,ag as eo,ax as V,eM as to,ac as no,ar as ge,Z as oo,_ as on,a1 as io,a7 as ro,aI as so,eN as ao,dI as lo,ck as uo,a3 as co}from"./index-BjOW8S1L.js";import{w as j,s as fo,M as rn,r as po,t as ut,a as Te,v as ho,x as Xe,y as sn,z as mo,i as vo,A as ct,o as ft,e as go}from"./index-7iPMz_Qy.js";import{i as yo}from"./initDefaultProps-P4j1rGDC.js";var an=function(){if(typeof Map<"u")return Map;function e(t,n){var o=-1;return t.some(function(i,r){return i[0]===n?(o=r,!0):!1}),o}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(n){var o=e(this.__entries__,n),i=this.__entries__[o];return i&&i[1]},t.prototype.set=function(n,o){var i=e(this.__entries__,n);~i?this.__entries__[i][1]=o:this.__entries__.push([n,o])},t.prototype.delete=function(n){var o=this.__entries__,i=e(o,n);~i&&o.splice(i,1)},t.prototype.has=function(n){return!!~e(this.__entries__,n)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(n,o){o===void 0&&(o=null);for(var i=0,r=this.__entries__;i<r.length;i++){var s=r[i];n.call(o,s[1],s[0])}},t}()}(),ke=typeof window<"u"&&typeof document<"u"&&window.document===document,Se=function(){return typeof global<"u"&&global.Math===Math?global:typeof self<"u"&&self.Math===Math?self:typeof window<"u"&&window.Math===Math?window:Function("return this")()}(),wo=function(){return typeof requestAnimationFrame=="function"?requestAnimationFrame.bind(Se):function(e){return setTimeout(function(){return e(Date.now())},1e3/60)}}(),bo=2;function Oo(e,t){var n=!1,o=!1,i=0;function r(){n&&(n=!1,e()),o&&a()}function s(){wo(r)}function a(){var u=Date.now();if(n){if(u-i<bo)return;o=!0}else n=!0,o=!1,setTimeout(s,t);i=u}return a}var _o=20,Co=["top","right","bottom","left","width","height","size","weight"],Po=typeof MutationObserver<"u",xo=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=Oo(this.refresh.bind(this),_o)}return e.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},e.prototype.removeObserver=function(t){var n=this.observers_,o=n.indexOf(t);~o&&n.splice(o,1),!n.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){var t=this.updateObservers_();t&&this.refresh()},e.prototype.updateObservers_=function(){var t=this.observers_.filter(function(n){return n.gatherActive(),n.hasActive()});return t.forEach(function(n){return n.broadcastActive()}),t.length>0},e.prototype.connect_=function(){!ke||this.connected_||(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),Po?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){!ke||!this.connected_||(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(t){var n=t.propertyName,o=n===void 0?"":n,i=Co.some(function(r){return!!~o.indexOf(r)});i&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),ln=function(e,t){for(var n=0,o=Object.keys(t);n<o.length;n++){var i=o[n];Object.defineProperty(e,i,{value:t[i],enumerable:!1,writable:!1,configurable:!0})}return e},ae=function(e){var t=e&&e.ownerDocument&&e.ownerDocument.defaultView;return t||Se},un=Re(0,0,0,0);function $e(e){return parseFloat(e)||0}function pt(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce(function(o,i){var r=e["border-"+i+"-width"];return o+$e(r)},0)}function To(e){for(var t=["top","right","bottom","left"],n={},o=0,i=t;o<i.length;o++){var r=i[o],s=e["padding-"+r];n[r]=$e(s)}return n}function So(e){var t=e.getBBox();return Re(0,0,t.width,t.height)}function $o(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return un;var o=ae(e).getComputedStyle(e),i=To(o),r=i.left+i.right,s=i.top+i.bottom,a=$e(o.width),u=$e(o.height);if(o.boxSizing==="border-box"&&(Math.round(a+r)!==t&&(a-=pt(o,"left","right")+r),Math.round(u+s)!==n&&(u-=pt(o,"top","bottom")+s)),!Ao(e)){var l=Math.round(a+r)-t,f=Math.round(u+s)-n;Math.abs(l)!==1&&(a-=l),Math.abs(f)!==1&&(u-=f)}return Re(i.left,i.top,a,u)}var Eo=function(){return typeof SVGGraphicsElement<"u"?function(e){return e instanceof ae(e).SVGGraphicsElement}:function(e){return e instanceof ae(e).SVGElement&&typeof e.getBBox=="function"}}();function Ao(e){return e===ae(e).document.documentElement}function Mo(e){return ke?Eo(e)?So(e):$o(e):un}function Do(e){var t=e.x,n=e.y,o=e.width,i=e.height,r=typeof DOMRectReadOnly<"u"?DOMRectReadOnly:Object,s=Object.create(r.prototype);return ln(s,{x:t,y:n,width:o,height:i,top:n,right:t+o,bottom:i+n,left:t}),s}function Re(e,t,n,o){return{x:e,y:t,width:n,height:o}}var Ro=function(){function e(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=Re(0,0,0,0),this.target=t}return e.prototype.isActive=function(){var t=Mo(this.target);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t},e}(),No=function(){function e(t,n){var o=Do(n);ln(this,{target:t,contentRect:o})}return e}(),Lo=function(){function e(t,n,o){if(this.activeObservations_=[],this.observations_=new an,typeof t!="function")throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=t,this.controller_=n,this.callbackCtx_=o}return e.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(t instanceof ae(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var n=this.observations_;n.has(t)||(n.set(t,new Ro(t)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(t instanceof ae(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var n=this.observations_;n.has(t)&&(n.delete(t),n.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach(function(n){n.isActive()&&t.activeObservations_.push(n)})},e.prototype.broadcastActive=function(){if(this.hasActive()){var t=this.callbackCtx_,n=this.activeObservations_.map(function(o){return new No(o.target,o.broadcastRect())});this.callback_.call(t,n,t),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),cn=typeof WeakMap<"u"?new WeakMap:new an,fn=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=xo.getInstance(),o=new Lo(t,n,this);cn.set(this,o)}return e}();["observe","unobserve","disconnect"].forEach(function(e){fn.prototype[e]=function(){var t;return(t=cn.get(this))[e].apply(t,arguments)}});var pn=function(){return typeof Se.ResizeObserver<"u"?Se.ResizeObserver:fn}();const qs=k({compatConfig:{MODE:3},name:"ResizeObserver",props:{disabled:Boolean,onResize:Function},emits:["resize"],setup(e,t){let{slots:n}=t;const o=Ln({width:0,height:0,offsetHeight:0,offsetWidth:0});let i=null,r=null;const s=()=>{r&&(r.disconnect(),r=null)},a=f=>{const{onResize:d}=e,p=f[0].target,{width:c,height:h}=p.getBoundingClientRect(),{offsetWidth:v,offsetHeight:O}=p,y=Math.floor(c),b=Math.floor(h);if(o.width!==y||o.height!==b||o.offsetWidth!==v||o.offsetHeight!==O){const _={width:y,height:b,offsetWidth:v,offsetHeight:O};m(o,_),d&&Promise.resolve().then(()=>{d(m(m({},_),{offsetWidth:v,offsetHeight:O}),p)})}},u=In(),l=()=>{const{disabled:f}=e;if(f){s();return}const d=ue(u);d!==i&&(s(),i=d),!r&&d&&(r=new pn(a),r.observe(d))};return De(()=>{l()}),Ke(()=>{l()}),qt(()=>{s()}),X(()=>e.disabled,()=>{l()},{flush:"post"}),()=>{var f;return(f=n.default)===null||f===void 0?void 0:f.call(n)[0]}}});let ee=!1;try{const e=Object.defineProperty({},"passive",{get(){ee=!0}});window.addEventListener("testPassive",null,e),window.removeEventListener("testPassive",null,e)}catch{}function ce(e,t,n,o){if(e&&e.addEventListener){let i=o;i===void 0&&ee&&(t==="touchstart"||t==="touchmove"||t==="wheel")&&(i={passive:!1}),e.addEventListener(t,n,i)}return{remove:()=>{e&&e.removeEventListener&&e.removeEventListener(t,n)}}}const Ee=["blue","purple","cyan","green","magenta","pink","red","orange","yellow","volcano","geekblue","lime","gold"],Io=(e,t,n,o,i)=>{const r=e/2,s=0,a=r,u=n*1/Math.sqrt(2),l=r-n*(1-1/Math.sqrt(2)),f=r-t*(1/Math.sqrt(2)),d=n*(Math.sqrt(2)-1)+t*(1/Math.sqrt(2)),p=2*r-f,c=d,h=2*r-u,v=l,O=2*r-s,y=a,b=r*Math.sqrt(2)+n*(Math.sqrt(2)-2),_=n*(Math.sqrt(2)-1);return{pointerEvents:"none",width:e,height:e,overflow:"hidden","&::after":{content:'""',position:"absolute",width:b,height:b,bottom:0,insetInline:0,margin:"auto",borderRadius:{_skip_check_:!0,value:`0 0 ${t}px 0`},transform:"translateY(50%) rotate(-135deg)",boxShadow:i,zIndex:0,background:"transparent"},"&::before":{position:"absolute",bottom:0,insetInlineStart:0,width:e,height:e/2,background:o,clipPath:{_multi_value_:!0,value:[`polygon(${_}px 100%, 50% ${_}px, ${2*r-_}px 100%, ${_}px 100%)`,`path('M ${s} ${a} A ${n} ${n} 0 0 0 ${u} ${l} L ${f} ${d} A ${t} ${t} 0 0 1 ${p} ${c} L ${h} ${v} A ${n} ${n} 0 0 0 ${O} ${y} Z')`]},content:'""'}}};function Bo(e,t){return Ee.reduce((n,o)=>{const i=e[`${o}-1`],r=e[`${o}-3`],s=e[`${o}-6`],a=e[`${o}-7`];return m(m({},n),t(o,{lightColor:i,lightBorderColor:r,darkColor:s,textColor:a}))},{})}function Ks(e,t){const n=m({},e);for(let o=0;o<t.length;o+=1){const i=t[o];delete n[i]}return n}function Ho(){return""}function zo(e){return e?e.ownerDocument:window.document}function dn(){}const Vo=()=>({action:P.oneOfType([P.string,P.arrayOf(P.string)]).def([]),showAction:P.any.def([]),hideAction:P.any.def([]),getPopupClassNameFromAlign:P.any.def(Ho),onPopupVisibleChange:Function,afterPopupVisibleChange:P.func.def(dn),popup:P.any,arrow:P.bool.def(!0),popupStyle:{type:Object,default:void 0},prefixCls:P.string.def("rc-trigger-popup"),popupClassName:P.string.def(""),popupPlacement:String,builtinPlacements:P.object,popupTransitionName:String,popupAnimation:P.any,mouseEnterDelay:P.number.def(0),mouseLeaveDelay:P.number.def(.1),zIndex:Number,focusDelay:P.number.def(0),blurDelay:P.number.def(.15),getPopupContainer:Function,getDocument:P.func.def(zo),forceRender:{type:Boolean,default:void 0},destroyPopupOnHide:{type:Boolean,default:!1},mask:{type:Boolean,default:!1},maskClosable:{type:Boolean,default:!0},popupAlign:P.object.def(()=>({})),popupVisible:{type:Boolean,default:void 0},defaultPopupVisible:{type:Boolean,default:!1},maskTransitionName:String,maskAnimation:String,stretch:String,alignPoint:{type:Boolean,default:void 0},autoDestroy:{type:Boolean,default:!1},mobile:Object,getTriggerDOMNode:Function}),Je={visible:Boolean,prefixCls:String,zIndex:Number,destroyPopupOnHide:Boolean,forceRender:Boolean,arrow:{type:Boolean,default:!0},animation:[String,Object],transitionName:String,stretch:{type:String},align:{type:Object},point:{type:Object},getRootDomNode:{type:Function},getClassNameFromAlign:{type:Function},onAlign:{type:Function},onMouseenter:{type:Function},onMouseleave:{type:Function},onMousedown:{type:Function},onTouchstart:{type:Function}},Fo=m(m({},Je),{mobile:{type:Object}}),Wo=m(m({},Je),{mask:Boolean,mobile:{type:Object},maskAnimation:String,maskTransitionName:String});function hn(e){const{prefixCls:t,visible:n,zIndex:o,mask:i,maskAnimation:r,maskTransitionName:s}=e;if(!i)return null;let a={};return(s||r)&&(a=Kt({prefixCls:t,transitionName:s,animation:r})),T(Ze,q({appear:!0},a),{default:()=>[Zt(T("div",{style:{zIndex:o},class:`${t}-mask`},null),[[Bn("if"),n]])]})}hn.displayName="Mask";const jo=k({compatConfig:{MODE:3},name:"MobilePopupInner",inheritAttrs:!1,props:Fo,emits:["mouseenter","mouseleave","mousedown","touchstart","align"],setup(e,t){let{expose:n,slots:o}=t;const i=G();return n({forceAlign:()=>{},getElement:()=>i.value}),()=>{var r;const{zIndex:s,visible:a,prefixCls:u,mobile:{popupClassName:l,popupStyle:f,popupMotion:d={},popupRender:p}={}}=e,c=m({zIndex:s},f);let h=Qt((r=o.default)===null||r===void 0?void 0:r.call(o));h.length>1&&(h=T("div",{class:`${u}-content`},[h])),p&&(h=p(h));const v=re(u,l);return T(Ze,q({ref:i},d),{default:()=>[a?T("div",{class:v,style:c},[h]):null]})}}});var Xo=function(e,t,n,o){function i(r){return r instanceof n?r:new n(function(s){s(r)})}return new(n||(n=Promise))(function(r,s){function a(f){try{l(o.next(f))}catch(d){s(d)}}function u(f){try{l(o.throw(f))}catch(d){s(d)}}function l(f){f.done?r(f.value):i(f.value).then(a,u)}l((o=o.apply(e,t||[])).next())})};const dt=["measure","align",null,"motion"],ko=(e,t)=>{const n=S(null),o=S(),i=S(!1);function r(u){i.value||(n.value=u)}function s(){j.cancel(o.value)}function a(u){s(),o.value=j(()=>{let l=n.value;switch(n.value){case"align":l="motion";break;case"motion":l="stable";break}r(l),u==null||u()})}return X(e,()=>{r("measure")},{immediate:!0,flush:"post"}),De(()=>{X(n,()=>{switch(n.value){case"measure":t();break}n.value&&(o.value=j(()=>Xo(void 0,void 0,void 0,function*(){const u=dt.indexOf(n.value),l=dt[u+1];l&&u!==-1&&r(l)})))},{immediate:!0,flush:"post"})}),Jt(()=>{i.value=!0,s()}),[n,a]},Yo=e=>{const t=S({width:0,height:0});function n(i){t.value={width:i.offsetWidth,height:i.offsetHeight}}return[B(()=>{const i={};if(e.value){const{width:r,height:s}=t.value;e.value.indexOf("height")!==-1&&s?i.height=`${s}px`:e.value.indexOf("minHeight")!==-1&&s&&(i.minHeight=`${s}px`),e.value.indexOf("width")!==-1&&r?i.width=`${r}px`:e.value.indexOf("minWidth")!==-1&&r&&(i.minWidth=`${r}px`)}return i}),n]};function ht(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,o)}return n}function mt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?ht(Object(n),!0).forEach(function(o){Uo(e,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ht(Object(n)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(n,o))})}return e}function Ye(e){"@babel/helpers - typeof";return Ye=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ye(e)}function Uo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var le,Go={Webkit:"-webkit-",Moz:"-moz-",ms:"-ms-",O:"-o-"};function Ae(){if(le!==void 0)return le;le="";var e=document.createElement("p").style,t="Transform";for(var n in Go)n+t in e&&(le=n);return le}function mn(){return Ae()?"".concat(Ae(),"TransitionProperty"):"transitionProperty"}function Ne(){return Ae()?"".concat(Ae(),"Transform"):"transform"}function vt(e,t){var n=mn();n&&(e.style[n]=t,n!=="transitionProperty"&&(e.style.transitionProperty=t))}function He(e,t){var n=Ne();n&&(e.style[n]=t,n!=="transform"&&(e.style.transform=t))}function qo(e){return e.style.transitionProperty||e.style[mn()]}function Ko(e){var t=window.getComputedStyle(e,null),n=t.getPropertyValue("transform")||t.getPropertyValue(Ne());if(n&&n!=="none"){var o=n.replace(/[^0-9\-.,]/g,"").split(",");return{x:parseFloat(o[12]||o[4],0),y:parseFloat(o[13]||o[5],0)}}return{x:0,y:0}}var Zo=/matrix\((.*)\)/,Qo=/matrix3d\((.*)\)/;function Jo(e,t){var n=window.getComputedStyle(e,null),o=n.getPropertyValue("transform")||n.getPropertyValue(Ne());if(o&&o!=="none"){var i,r=o.match(Zo);if(r)r=r[1],i=r.split(",").map(function(a){return parseFloat(a,10)}),i[4]=t.x,i[5]=t.y,He(e,"matrix(".concat(i.join(","),")"));else{var s=o.match(Qo)[1];i=s.split(",").map(function(a){return parseFloat(a,10)}),i[12]=t.x,i[13]=t.y,He(e,"matrix3d(".concat(i.join(","),")"))}}else He(e,"translateX(".concat(t.x,"px) translateY(").concat(t.y,"px) translateZ(0)"))}var ei=/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source,ve;function gt(e){var t=e.style.display;e.style.display="none",e.offsetHeight,e.style.display=t}function ie(e,t,n){var o=n;if(Ye(t)==="object"){for(var i in t)t.hasOwnProperty(i)&&ie(e,i,t[i]);return}if(typeof o<"u"){typeof o=="number"&&(o="".concat(o,"px")),e.style[t]=o;return}return ve(e,t)}function ti(e){var t,n,o,i=e.ownerDocument,r=i.body,s=i&&i.documentElement;return t=e.getBoundingClientRect(),n=Math.floor(t.left),o=Math.floor(t.top),n-=s.clientLeft||r.clientLeft||0,o-=s.clientTop||r.clientTop||0,{left:n,top:o}}function vn(e,t){var n=e["page".concat(t?"Y":"X","Offset")],o="scroll".concat(t?"Top":"Left");if(typeof n!="number"){var i=e.document;n=i.documentElement[o],typeof n!="number"&&(n=i.body[o])}return n}function gn(e){return vn(e)}function yn(e){return vn(e,!0)}function de(e){var t=ti(e),n=e.ownerDocument,o=n.defaultView||n.parentWindow;return t.left+=gn(o),t.top+=yn(o),t}function et(e){return e!=null&&e==e.window}function wn(e){return et(e)?e.document:e.nodeType===9?e:e.ownerDocument}function ni(e,t,n){var o=n,i="",r=wn(e);return o=o||r.defaultView.getComputedStyle(e,null),o&&(i=o.getPropertyValue(t)||o[t]),i}var oi=new RegExp("^(".concat(ei,")(?!px)[a-z%]+$"),"i"),ii=/^(top|right|bottom|left)$/,ze="currentStyle",Ve="runtimeStyle",Q="left",ri="px";function si(e,t){var n=e[ze]&&e[ze][t];if(oi.test(n)&&!ii.test(t)){var o=e.style,i=o[Q],r=e[Ve][Q];e[Ve][Q]=e[ze][Q],o[Q]=t==="fontSize"?"1em":n||0,n=o.pixelLeft+ri,o[Q]=i,e[Ve][Q]=r}return n===""?"auto":n}typeof window<"u"&&(ve=window.getComputedStyle?ni:si);function ye(e,t){return e==="left"?t.useCssRight?"right":e:t.useCssBottom?"bottom":e}function yt(e){if(e==="left")return"right";if(e==="right")return"left";if(e==="top")return"bottom";if(e==="bottom")return"top"}function wt(e,t,n){ie(e,"position")==="static"&&(e.style.position="relative");var o=-999,i=-999,r=ye("left",n),s=ye("top",n),a=yt(r),u=yt(s);r!=="left"&&(o=999),s!=="top"&&(i=999);var l="",f=de(e);("left"in t||"top"in t)&&(l=qo(e)||"",vt(e,"none")),"left"in t&&(e.style[a]="",e.style[r]="".concat(o,"px")),"top"in t&&(e.style[u]="",e.style[s]="".concat(i,"px")),gt(e);var d=de(e),p={};for(var c in t)if(t.hasOwnProperty(c)){var h=ye(c,n),v=c==="left"?o:i,O=f[c]-d[c];h===c?p[h]=v+O:p[h]=v-O}ie(e,p),gt(e),("left"in t||"top"in t)&&vt(e,l);var y={};for(var b in t)if(t.hasOwnProperty(b)){var _=ye(b,n),$=t[b]-f[b];b===_?y[_]=p[_]+$:y[_]=p[_]-$}ie(e,y)}function ai(e,t){var n=de(e),o=Ko(e),i={x:o.x,y:o.y};"left"in t&&(i.x=o.x+t.left-n.left),"top"in t&&(i.y=o.y+t.top-n.top),Jo(e,i)}function li(e,t,n){if(n.ignoreShake){var o=de(e),i=o.left.toFixed(0),r=o.top.toFixed(0),s=t.left.toFixed(0),a=t.top.toFixed(0);if(i===s&&r===a)return}n.useCssRight||n.useCssBottom?wt(e,t,n):n.useCssTransform&&Ne()in document.body.style?ai(e,t):wt(e,t,n)}function tt(e,t){for(var n=0;n<e.length;n++)t(e[n])}function bn(e){return ve(e,"boxSizing")==="border-box"}var ui=["margin","border","padding"],Ue=-1,ci=2,Ge=1,fi=0;function pi(e,t,n){var o={},i=e.style,r;for(r in t)t.hasOwnProperty(r)&&(o[r]=i[r],i[r]=t[r]);n.call(e);for(r in t)t.hasOwnProperty(r)&&(i[r]=o[r])}function fe(e,t,n){var o=0,i,r,s;for(r=0;r<t.length;r++)if(i=t[r],i)for(s=0;s<n.length;s++){var a=void 0;i==="border"?a="".concat(i).concat(n[s],"Width"):a=i+n[s],o+=parseFloat(ve(e,a))||0}return o}var W={getParent:function(t){var n=t;do n.nodeType===11&&n.host?n=n.host:n=n.parentNode;while(n&&n.nodeType!==1&&n.nodeType!==9);return n}};tt(["Width","Height"],function(e){W["doc".concat(e)]=function(t){var n=t.document;return Math.max(n.documentElement["scroll".concat(e)],n.body["scroll".concat(e)],W["viewport".concat(e)](n))},W["viewport".concat(e)]=function(t){var n="client".concat(e),o=t.document,i=o.body,r=o.documentElement,s=r[n];return o.compatMode==="CSS1Compat"&&s||i&&i[n]||s}});function bt(e,t,n){var o=n;if(et(e))return t==="width"?W.viewportWidth(e):W.viewportHeight(e);if(e.nodeType===9)return t==="width"?W.docWidth(e):W.docHeight(e);var i=t==="width"?["Left","Right"]:["Top","Bottom"],r=Math.floor(t==="width"?e.getBoundingClientRect().width:e.getBoundingClientRect().height),s=bn(e),a=0;(r==null||r<=0)&&(r=void 0,a=ve(e,t),(a==null||Number(a)<0)&&(a=e.style[t]||0),a=Math.floor(parseFloat(a))||0),o===void 0&&(o=s?Ge:Ue);var u=r!==void 0||s,l=r||a;return o===Ue?u?l-fe(e,["border","padding"],i):a:u?o===Ge?l:l+(o===ci?-fe(e,["border"],i):fe(e,["margin"],i)):a+fe(e,ui.slice(o),i)}var di={position:"absolute",visibility:"hidden",display:"block"};function Ot(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var o,i=t[0];return i.offsetWidth!==0?o=bt.apply(void 0,t):pi(i,di,function(){o=bt.apply(void 0,t)}),o}tt(["width","height"],function(e){var t=e.charAt(0).toUpperCase()+e.slice(1);W["outer".concat(t)]=function(o,i){return o&&Ot(o,e,i?fi:Ge)};var n=e==="width"?["Left","Right"]:["Top","Bottom"];W[e]=function(o,i){var r=i;if(r!==void 0){if(o){var s=bn(o);return s&&(r+=fe(o,["padding","border"],n)),ie(o,e,r)}return}return o&&Ot(o,e,Ue)}});function On(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e}var w={getWindow:function(t){if(t&&t.document&&t.setTimeout)return t;var n=t.ownerDocument||t;return n.defaultView||n.parentWindow},getDocument:wn,offset:function(t,n,o){if(typeof n<"u")li(t,n,o||{});else return de(t)},isWindow:et,each:tt,css:ie,clone:function(t){var n,o={};for(n in t)t.hasOwnProperty(n)&&(o[n]=t[n]);var i=t.overflow;if(i)for(n in t)t.hasOwnProperty(n)&&(o.overflow[n]=t.overflow[n]);return o},mix:On,getWindowScrollLeft:function(t){return gn(t)},getWindowScrollTop:function(t){return yn(t)},merge:function(){for(var t={},n=0;n<arguments.length;n++)w.mix(t,n<0||arguments.length<=n?void 0:arguments[n]);return t},viewportWidth:0,viewportHeight:0};On(w,W);var Fe=w.getParent;function qe(e){if(w.isWindow(e)||e.nodeType===9)return null;var t=w.getDocument(e),n=t.body,o,i=w.css(e,"position"),r=i==="fixed"||i==="absolute";if(!r)return e.nodeName.toLowerCase()==="html"?null:Fe(e);for(o=Fe(e);o&&o!==n&&o.nodeType!==9;o=Fe(o))if(i=w.css(o,"position"),i!=="static")return o;return null}var _t=w.getParent;function hi(e){if(w.isWindow(e)||e.nodeType===9)return!1;var t=w.getDocument(e),n=t.body,o=null;for(o=_t(e);o&&o!==n&&o!==t;o=_t(o)){var i=w.css(o,"position");if(i==="fixed")return!0}return!1}function nt(e,t){for(var n={left:0,right:1/0,top:0,bottom:1/0},o=qe(e),i=w.getDocument(e),r=i.defaultView||i.parentWindow,s=i.body,a=i.documentElement;o;){if((navigator.userAgent.indexOf("MSIE")===-1||o.clientWidth!==0)&&o!==s&&o!==a&&w.css(o,"overflow")!=="visible"){var u=w.offset(o);u.left+=o.clientLeft,u.top+=o.clientTop,n.top=Math.max(n.top,u.top),n.right=Math.min(n.right,u.left+o.clientWidth),n.bottom=Math.min(n.bottom,u.top+o.clientHeight),n.left=Math.max(n.left,u.left)}else if(o===s||o===a)break;o=qe(o)}var l=null;if(!w.isWindow(e)&&e.nodeType!==9){l=e.style.position;var f=w.css(e,"position");f==="absolute"&&(e.style.position="fixed")}var d=w.getWindowScrollLeft(r),p=w.getWindowScrollTop(r),c=w.viewportWidth(r),h=w.viewportHeight(r),v=a.scrollWidth,O=a.scrollHeight,y=window.getComputedStyle(s);if(y.overflowX==="hidden"&&(v=r.innerWidth),y.overflowY==="hidden"&&(O=r.innerHeight),e.style&&(e.style.position=l),t||hi(e))n.left=Math.max(n.left,d),n.top=Math.max(n.top,p),n.right=Math.min(n.right,d+c),n.bottom=Math.min(n.bottom,p+h);else{var b=Math.max(v,d+c);n.right=Math.min(n.right,b);var _=Math.max(O,p+h);n.bottom=Math.min(n.bottom,_)}return n.top>=0&&n.left>=0&&n.bottom>n.top&&n.right>n.left?n:null}function mi(e,t,n,o){var i=w.clone(e),r={width:t.width,height:t.height};return o.adjustX&&i.left<n.left&&(i.left=n.left),o.resizeWidth&&i.left>=n.left&&i.left+r.width>n.right&&(r.width-=i.left+r.width-n.right),o.adjustX&&i.left+r.width>n.right&&(i.left=Math.max(n.right-r.width,n.left)),o.adjustY&&i.top<n.top&&(i.top=n.top),o.resizeHeight&&i.top>=n.top&&i.top+r.height>n.bottom&&(r.height-=i.top+r.height-n.bottom),o.adjustY&&i.top+r.height>n.bottom&&(i.top=Math.max(n.bottom-r.height,n.top)),w.mix(i,r)}function ot(e){var t,n,o;if(!w.isWindow(e)&&e.nodeType!==9)t=w.offset(e),n=w.outerWidth(e),o=w.outerHeight(e);else{var i=w.getWindow(e);t={left:w.getWindowScrollLeft(i),top:w.getWindowScrollTop(i)},n=w.viewportWidth(i),o=w.viewportHeight(i)}return t.width=n,t.height=o,t}function Ct(e,t){var n=t.charAt(0),o=t.charAt(1),i=e.width,r=e.height,s=e.left,a=e.top;return n==="c"?a+=r/2:n==="b"&&(a+=r),o==="c"?s+=i/2:o==="r"&&(s+=i),{left:s,top:a}}function we(e,t,n,o,i){var r=Ct(t,n[1]),s=Ct(e,n[0]),a=[s.left-r.left,s.top-r.top];return{left:Math.round(e.left-a[0]+o[0]-i[0]),top:Math.round(e.top-a[1]+o[1]-i[1])}}function Pt(e,t,n){return e.left<n.left||e.left+t.width>n.right}function xt(e,t,n){return e.top<n.top||e.top+t.height>n.bottom}function vi(e,t,n){return e.left>n.right||e.left+t.width<n.left}function gi(e,t,n){return e.top>n.bottom||e.top+t.height<n.top}function be(e,t,n){var o=[];return w.each(e,function(i){o.push(i.replace(t,function(r){return n[r]}))}),o}function Oe(e,t){return e[t]=-e[t],e}function Tt(e,t){var n;return/%$/.test(e)?n=parseInt(e.substring(0,e.length-1),10)/100*t:n=parseInt(e,10),n||0}function St(e,t){e[0]=Tt(e[0],t.width),e[1]=Tt(e[1],t.height)}function _n(e,t,n,o){var i=n.points,r=n.offset||[0,0],s=n.targetOffset||[0,0],a=n.overflow,u=n.source||e;r=[].concat(r),s=[].concat(s),a=a||{};var l={},f=0,d=!!(a&&a.alwaysByViewport),p=nt(u,d),c=ot(u);St(r,c),St(s,t);var h=we(c,t,i,r,s),v=w.merge(c,h);if(p&&(a.adjustX||a.adjustY)&&o){if(a.adjustX&&Pt(h,c,p)){var O=be(i,/[lr]/gi,{l:"r",r:"l"}),y=Oe(r,0),b=Oe(s,0),_=we(c,t,O,y,b);vi(_,c,p)||(f=1,i=O,r=y,s=b)}if(a.adjustY&&xt(h,c,p)){var $=be(i,/[tb]/gi,{t:"b",b:"t"}),N=Oe(r,1),x=Oe(s,1),I=we(c,t,$,N,x);gi(I,c,p)||(f=1,i=$,r=N,s=x)}f&&(h=we(c,t,i,r,s),w.mix(v,h));var A=Pt(h,c,p),D=xt(h,c,p);if(A||D){var g=i;A&&(g=be(i,/[lr]/gi,{l:"r",r:"l"})),D&&(g=be(i,/[tb]/gi,{t:"b",b:"t"})),i=g,r=n.offset||[0,0],s=n.targetOffset||[0,0]}l.adjustX=a.adjustX&&A,l.adjustY=a.adjustY&&D,(l.adjustX||l.adjustY)&&(v=mi(h,c,p,l))}return v.width!==c.width&&w.css(u,"width",w.width(u)+v.width-c.width),v.height!==c.height&&w.css(u,"height",w.height(u)+v.height-c.height),w.offset(u,{left:v.left,top:v.top},{useCssRight:n.useCssRight,useCssBottom:n.useCssBottom,useCssTransform:n.useCssTransform,ignoreShake:n.ignoreShake}),{points:i,offset:r,targetOffset:s,overflow:l}}function yi(e,t){var n=nt(e,t),o=ot(e);return!n||o.left+o.width<=n.left||o.top+o.height<=n.top||o.left>=n.right||o.top>=n.bottom}function it(e,t,n){var o=n.target||t,i=ot(o),r=!yi(o,n.overflow&&n.overflow.alwaysByViewport);return _n(e,i,n,r)}it.__getOffsetParent=qe;it.__getVisibleRectForElement=nt;function wi(e,t,n){var o,i,r=w.getDocument(e),s=r.defaultView||r.parentWindow,a=w.getWindowScrollLeft(s),u=w.getWindowScrollTop(s),l=w.viewportWidth(s),f=w.viewportHeight(s);"pageX"in t?o=t.pageX:o=a+t.clientX,"pageY"in t?i=t.pageY:i=u+t.clientY;var d={left:o,top:i,width:0,height:0},p=o>=0&&o<=a+l&&i>=0&&i<=u+f,c=[n.points[0],"cc"];return _n(e,d,mt(mt({},n),{},{points:c}),p)}function he(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,i=e;if(Array.isArray(e)&&(i=Qe(e)[0]),!i)return null;const r=en(i,t,o);return r.props=n?m(m({},r.props),t):r.props,Hn(typeof r.props.class!="object"),r}function Zs(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return e.map(o=>he(o,t,n))}function Qs(e,t,n){Fn(en(e,m({},t)),n)}const Cn=e=>(e||[]).some(t=>zn(t)?!(t.type===Vn||t.type===tn&&!Cn(t.children)):!0)?e:null;function Js(e,t,n,o){var i;const r=(i=e[t])===null||i===void 0?void 0:i.call(e,n);return Cn(r)?r:o==null?void 0:o()}function bi(e,t){return e===t?!0:!e||!t?!1:"pageX"in t&&"pageY"in t?e.pageX===t.pageX&&e.pageY===t.pageY:"clientX"in t&&"clientY"in t?e.clientX===t.clientX&&e.clientY===t.clientY:!1}function Oi(e,t){e!==document.activeElement&&oe(t,e)&&typeof e.focus=="function"&&e.focus()}function $t(e,t){let n=null,o=null;function i(s){let[{target:a}]=s;if(!document.documentElement.contains(a))return;const{width:u,height:l}=a.getBoundingClientRect(),f=Math.floor(u),d=Math.floor(l);(n!==f||o!==d)&&Promise.resolve().then(()=>{t({width:f,height:d})}),n=f,o=d}const r=new pn(i);return e&&r.observe(e),()=>{r.disconnect()}}const _i=(e,t)=>{let n=!1,o=null;function i(){clearTimeout(o)}function r(s){if(!n||s===!0){if(e()===!1)return;n=!0,i(),o=setTimeout(()=>{n=!1},t.value)}else i(),o=setTimeout(()=>{n=!1,r()},t.value)}return[r,()=>{n=!1,i()}]};function Ci(){this.__data__=[],this.size=0}function Pn(e,t){return e===t||e!==e&&t!==t}function Le(e,t){for(var n=e.length;n--;)if(Pn(e[n][0],t))return n;return-1}var Pi=Array.prototype,xi=Pi.splice;function Ti(e){var t=this.__data__,n=Le(t,e);if(n<0)return!1;var o=t.length-1;return n==o?t.pop():xi.call(t,n,1),--this.size,!0}function Si(e){var t=this.__data__,n=Le(t,e);return n<0?void 0:t[n][1]}function $i(e){return Le(this.__data__,e)>-1}function Ei(e,t){var n=this.__data__,o=Le(n,e);return o<0?(++this.size,n.push([e,t])):n[o][1]=t,this}function Y(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}Y.prototype.clear=Ci;Y.prototype.delete=Ti;Y.prototype.get=Si;Y.prototype.has=$i;Y.prototype.set=Ei;function Ai(){this.__data__=new Y,this.size=0}function Mi(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}function Di(e){return this.__data__.get(e)}function Ri(e){return this.__data__.has(e)}var me=fo(Object,"create");function Ni(){this.__data__=me?me(null):{},this.size=0}function Li(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var Ii="__lodash_hash_undefined__",Bi=Object.prototype,Hi=Bi.hasOwnProperty;function zi(e){var t=this.__data__;if(me){var n=t[e];return n===Ii?void 0:n}return Hi.call(t,e)?t[e]:void 0}var Vi=Object.prototype,Fi=Vi.hasOwnProperty;function Wi(e){var t=this.__data__;return me?t[e]!==void 0:Fi.call(t,e)}var ji="__lodash_hash_undefined__";function Xi(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=me&&t===void 0?ji:t,this}function te(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}te.prototype.clear=Ni;te.prototype.delete=Li;te.prototype.get=zi;te.prototype.has=Wi;te.prototype.set=Xi;function ki(){this.size=0,this.__data__={hash:new te,map:new(rn||Y),string:new te}}function Yi(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function Ie(e,t){var n=e.__data__;return Yi(t)?n[typeof t=="string"?"string":"hash"]:n.map}function Ui(e){var t=Ie(this,e).delete(e);return this.size-=t?1:0,t}function Gi(e){return Ie(this,e).get(e)}function qi(e){return Ie(this,e).has(e)}function Ki(e,t){var n=Ie(this,e),o=n.size;return n.set(e,t),this.size+=n.size==o?0:1,this}function ne(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}ne.prototype.clear=ki;ne.prototype.delete=Ui;ne.prototype.get=Gi;ne.prototype.has=qi;ne.prototype.set=Ki;var Zi=200;function Qi(e,t){var n=this.__data__;if(n instanceof Y){var o=n.__data__;if(!rn||o.length<Zi-1)return o.push([e,t]),this.size=++n.size,this;n=this.__data__=new ne(o)}return n.set(e,t),this.size=n.size,this}function K(e){var t=this.__data__=new Y(e);this.size=t.size}K.prototype.clear=Ai;K.prototype.delete=Mi;K.prototype.get=Di;K.prototype.has=Ri;K.prototype.set=Qi;var Ji="__lodash_hash_undefined__";function er(e){return this.__data__.set(e,Ji),this}function tr(e){return this.__data__.has(e)}function Me(e){var t=-1,n=e==null?0:e.length;for(this.__data__=new ne;++t<n;)this.add(e[t])}Me.prototype.add=Me.prototype.push=er;Me.prototype.has=tr;function nr(e,t){for(var n=-1,o=e==null?0:e.length;++n<o;)if(t(e[n],n,e))return!0;return!1}function or(e,t){return e.has(t)}var ir=1,rr=2;function xn(e,t,n,o,i,r){var s=n&ir,a=e.length,u=t.length;if(a!=u&&!(s&&u>a))return!1;var l=r.get(e),f=r.get(t);if(l&&f)return l==t&&f==e;var d=-1,p=!0,c=n&rr?new Me:void 0;for(r.set(e,t),r.set(t,e);++d<a;){var h=e[d],v=t[d];if(o)var O=s?o(v,h,d,t,e,r):o(h,v,d,e,t,r);if(O!==void 0){if(O)continue;p=!1;break}if(c){if(!nr(t,function(y,b){if(!or(c,b)&&(h===y||i(h,y,n,o,r)))return c.push(b)})){p=!1;break}}else if(!(h===v||i(h,v,n,o,r))){p=!1;break}}return r.delete(e),r.delete(t),p}var Et=po.Uint8Array;function sr(e){var t=-1,n=Array(e.size);return e.forEach(function(o,i){n[++t]=[i,o]}),n}function ar(e){var t=-1,n=Array(e.size);return e.forEach(function(o){n[++t]=o}),n}var lr=1,ur=2,cr="[object Boolean]",fr="[object Date]",pr="[object Error]",dr="[object Map]",hr="[object Number]",mr="[object RegExp]",vr="[object Set]",gr="[object String]",yr="[object Symbol]",wr="[object ArrayBuffer]",br="[object DataView]",At=ut?ut.prototype:void 0,We=At?At.valueOf:void 0;function Or(e,t,n,o,i,r,s){switch(n){case br:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case wr:return!(e.byteLength!=t.byteLength||!r(new Et(e),new Et(t)));case cr:case fr:case hr:return Pn(+e,+t);case pr:return e.name==t.name&&e.message==t.message;case mr:case gr:return e==t+"";case dr:var a=sr;case vr:var u=o&lr;if(a||(a=ar),e.size!=t.size&&!u)return!1;var l=s.get(e);if(l)return l==t;o|=ur,s.set(e,t);var f=xn(a(e),a(t),o,i,r,s);return s.delete(e),f;case yr:if(We)return We.call(e)==We.call(t)}return!1}function _r(e,t){for(var n=-1,o=t.length,i=e.length;++n<o;)e[i+n]=t[n];return e}function Cr(e,t,n){var o=t(e);return Te(e)?o:_r(o,n(e))}function Pr(e,t){for(var n=-1,o=e==null?0:e.length,i=0,r=[];++n<o;){var s=e[n];t(s,n,e)&&(r[i++]=s)}return r}function xr(){return[]}var Tr=Object.prototype,Sr=Tr.propertyIsEnumerable,Mt=Object.getOwnPropertySymbols,$r=Mt?function(e){return e==null?[]:(e=Object(e),Pr(Mt(e),function(t){return Sr.call(e,t)}))}:xr;function Er(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}var Ar=9007199254740991,Mr=/^(?:0|[1-9]\d*)$/;function Dr(e,t){var n=typeof e;return t=t??Ar,!!t&&(n=="number"||n!="symbol"&&Mr.test(e))&&e>-1&&e%1==0&&e<t}var Rr=Object.prototype,Nr=Rr.hasOwnProperty;function Lr(e,t){var n=Te(e),o=!n&&ho(e),i=!n&&!o&&Xe(e),r=!n&&!o&&!i&&sn(e),s=n||o||i||r,a=s?Er(e.length,String):[],u=a.length;for(var l in e)(t||Nr.call(e,l))&&!(s&&(l=="length"||i&&(l=="offset"||l=="parent")||r&&(l=="buffer"||l=="byteLength"||l=="byteOffset")||Dr(l,u)))&&a.push(l);return a}function Ir(e){return vo(e)?Lr(e):mo(e)}function Dt(e){return Cr(e,Ir,$r)}var Br=1,Hr=Object.prototype,zr=Hr.hasOwnProperty;function Vr(e,t,n,o,i,r){var s=n&Br,a=Dt(e),u=a.length,l=Dt(t),f=l.length;if(u!=f&&!s)return!1;for(var d=u;d--;){var p=a[d];if(!(s?p in t:zr.call(t,p)))return!1}var c=r.get(e),h=r.get(t);if(c&&h)return c==t&&h==e;var v=!0;r.set(e,t),r.set(t,e);for(var O=s;++d<u;){p=a[d];var y=e[p],b=t[p];if(o)var _=s?o(b,y,p,t,e,r):o(y,b,p,e,t,r);if(!(_===void 0?y===b||i(y,b,n,o,r):_)){v=!1;break}O||(O=p=="constructor")}if(v&&!O){var $=e.constructor,N=t.constructor;$!=N&&"constructor"in e&&"constructor"in t&&!(typeof $=="function"&&$ instanceof $&&typeof N=="function"&&N instanceof N)&&(v=!1)}return r.delete(e),r.delete(t),v}var Fr=1,Rt="[object Arguments]",Nt="[object Array]",_e="[object Object]",Wr=Object.prototype,Lt=Wr.hasOwnProperty;function jr(e,t,n,o,i,r){var s=Te(e),a=Te(t),u=s?Nt:ct(e),l=a?Nt:ct(t);u=u==Rt?_e:u,l=l==Rt?_e:l;var f=u==_e,d=l==_e,p=u==l;if(p&&Xe(e)){if(!Xe(t))return!1;s=!0,f=!1}if(p&&!f)return r||(r=new K),s||sn(e)?xn(e,t,n,o,i,r):Or(e,t,u,n,o,i,r);if(!(n&Fr)){var c=f&&Lt.call(e,"__wrapped__"),h=d&&Lt.call(t,"__wrapped__");if(c||h){var v=c?e.value():e,O=h?t.value():t;return r||(r=new K),i(v,O,n,o,r)}}return p?(r||(r=new K),Vr(e,t,n,o,i,r)):!1}function Tn(e,t,n,o,i){return e===t?!0:e==null||t==null||!ft(e)&&!ft(t)?e!==e&&t!==t:jr(e,t,n,o,Tn,i)}function Xr(e,t){return Tn(e,t)}const kr={align:Object,target:[Object,Function],onAlign:Function,monitorBufferTime:Number,monitorWindowResize:Boolean,disabled:Boolean};function It(e){return typeof e!="function"?null:e()}function Bt(e){return typeof e!="object"||!e?null:e}const Yr=k({compatConfig:{MODE:3},name:"Align",props:kr,emits:["align"],setup(e,t){let{expose:n,slots:o}=t;const i=G({}),r=G(),[s,a]=_i(()=>{const{disabled:p,target:c,align:h,onAlign:v}=e;if(!p&&c&&r.value){const O=r.value;let y;const b=It(c),_=Bt(c);i.value.element=b,i.value.point=_,i.value.align=h;const{activeElement:$}=document;return b&&go(b)?y=it(O,b,h):_&&(y=wi(O,_,h)),Oi($,O),v&&y&&v(O,y),!0}return!1},B(()=>e.monitorBufferTime)),u=G({cancel:()=>{}}),l=G({cancel:()=>{}}),f=()=>{const p=e.target,c=It(p),h=Bt(p);r.value!==l.value.element&&(l.value.cancel(),l.value.element=r.value,l.value.cancel=$t(r.value,s)),(i.value.element!==c||!bi(i.value.point,h)||!Xr(i.value.align,e.align))&&(s(),u.value.element!==c&&(u.value.cancel(),u.value.element=c,u.value.cancel=$t(c,s)))};De(()=>{pe(()=>{f()})}),Ke(()=>{pe(()=>{f()})}),X(()=>e.disabled,p=>{p?a():s()},{immediate:!0,flush:"post"});const d=G(null);return X(()=>e.monitorWindowResize,p=>{p?d.value||(d.value=ce(window,"resize",s)):d.value&&(d.value.remove(),d.value=null)},{flush:"post"}),qt(()=>{u.value.cancel(),l.value.cancel(),d.value&&d.value.remove(),a()}),n({forceAlign:()=>s(!0)}),()=>{const p=o==null?void 0:o.default();return p?he(p[0],{ref:r},!0,!0):null}}}),Ur=k({compatConfig:{MODE:3},name:"PopupInner",inheritAttrs:!1,props:Je,emits:["mouseenter","mouseleave","mousedown","touchstart","align"],setup(e,t){let{expose:n,attrs:o,slots:i}=t;const r=S(),s=S(),a=S(),[u,l]=Yo(Wn(e,"stretch")),f=()=>{e.stretch&&l(e.getRootDomNode())},d=S(!1);let p;X(()=>e.visible,x=>{clearTimeout(p),x?p=setTimeout(()=>{d.value=e.visible}):d.value=!1},{immediate:!0});const[c,h]=ko(d,f),v=S(),O=()=>e.point?e.point:e.getRootDomNode,y=()=>{var x;(x=r.value)===null||x===void 0||x.forceAlign()},b=(x,I)=>{var A;const D=e.getClassNameFromAlign(I),g=a.value;a.value!==D&&(a.value=D),c.value==="align"&&(g!==D?Promise.resolve().then(()=>{y()}):h(()=>{var C;(C=v.value)===null||C===void 0||C.call(v)}),(A=e.onAlign)===null||A===void 0||A.call(e,x,I))},_=B(()=>{const x=typeof e.animation=="object"?e.animation:Kt(e);return["onAfterEnter","onAfterLeave"].forEach(I=>{const A=x[I];x[I]=D=>{h(),c.value="stable",A==null||A(D)}}),x}),$=()=>new Promise(x=>{v.value=x});X([_,c],()=>{!_.value&&c.value==="motion"&&h()},{immediate:!0}),n({forceAlign:y,getElement:()=>s.value.$el||s.value});const N=B(()=>{var x;return!(!((x=e.align)===null||x===void 0)&&x.points&&(c.value==="align"||c.value==="stable"))});return()=>{var x;const{zIndex:I,align:A,prefixCls:D,destroyPopupOnHide:g,onMouseenter:C,onMouseleave:F,onTouchstart:M=()=>{},onMousedown:L}=e,E=c.value,R=[m(m({},u.value),{zIndex:I,opacity:E==="motion"||E==="stable"||!d.value?null:0,pointerEvents:!d.value&&E!=="stable"?"none":null}),o.style];let Z=Qt((x=i.default)===null||x===void 0?void 0:x.call(i,{visible:e.visible}));Z.length>1&&(Z=T("div",{class:`${D}-content`},[Z]));const U=re(D,o.class,a.value,!e.arrow&&`${D}-arrow-hidden`),Be=d.value||!e.visible?jn(_.value.name,_.value):{};return T(Ze,q(q({ref:s},Be),{},{onBeforeEnter:$}),{default:()=>!g||e.visible?Zt(T(Yr,{target:O(),key:"popup",ref:r,monitorWindowResize:!0,disabled:N.value,align:A,onAlign:b},{default:()=>T("div",{class:U,onMouseenter:C,onMouseleave:F,onMousedown:st(L,["capture"]),[ee?"onTouchstartPassive":"onTouchstart"]:st(M,["capture"]),style:R},[Z])}),[[Xn,d.value]]):null})}}}),Gr=k({compatConfig:{MODE:3},name:"Popup",inheritAttrs:!1,props:Wo,setup(e,t){let{attrs:n,slots:o,expose:i}=t;const r=S(!1),s=S(!1),a=S(),u=S();return X([()=>e.visible,()=>e.mobile],()=>{r.value=e.visible,e.visible&&e.mobile&&(s.value=!0)},{immediate:!0,flush:"post"}),i({forceAlign:()=>{var l;(l=a.value)===null||l===void 0||l.forceAlign()},getElement:()=>{var l;return(l=a.value)===null||l===void 0?void 0:l.getElement()}}),()=>{const l=m(m(m({},e),n),{visible:r.value}),f=s.value?T(jo,q(q({},l),{},{mobile:e.mobile,ref:a}),{default:o.default}):T(Ur,q(q({},l),{},{ref:a}),{default:o.default});return T("div",{ref:u},[T(hn,l,null),f])}}});function qr(e,t,n){return n?e[0]===t[0]:e[0]===t[0]&&e[1]===t[1]}function Ht(e,t,n){const o=e[t]||{};return m(m({},o),n)}function Kr(e,t,n,o){const{points:i}=n,r=Object.keys(e);for(let s=0;s<r.length;s+=1){const a=r[s];if(qr(e[a].points,i,o))return`${t}-placement-${a}`}return""}const Zr={methods:{setState(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=typeof e=="function"?e(this.$data,this.$props):e;if(this.getDerivedStateFromProps){const o=this.getDerivedStateFromProps(kn(this),m(m({},this.$data),n));if(o===null)return;n=m(m({},n),o||{})}m(this.$data,n),this._.isMounted&&this.$forceUpdate(),pe(()=>{t&&t()})},__emit(){const e=[].slice.call(arguments,0);let t=e[0];t=`on${t[0].toUpperCase()}${t.substring(1)}`;const n=this.$props[t]||this.$attrs[t];if(e.length&&n)if(Array.isArray(n))for(let o=0,i=n.length;o<i;o++)n[o](...e.slice(1));else n(...e.slice(1))}}};let je;function Sn(e){if(typeof document>"u")return 0;if(je===void 0){const t=document.createElement("div");t.style.width="100%",t.style.height="200px";const n=document.createElement("div"),o=n.style;o.position="absolute",o.top="0",o.left="0",o.pointerEvents="none",o.visibility="hidden",o.width="200px",o.height="150px",o.overflow="hidden",n.appendChild(t),document.body.appendChild(n);const i=t.offsetWidth;n.style.overflow="scroll";let r=t.offsetWidth;i===r&&(r=n.clientWidth),document.body.removeChild(n),je=i-r}return je}function zt(e){const t=e.match(/^(.*)px$/),n=Number(t==null?void 0:t[1]);return Number.isNaN(n)?Sn():n}function ea(e){if(typeof document>"u"||!e||!(e instanceof Element))return{width:0,height:0};const{width:t,height:n}=getComputedStyle(e,"::-webkit-scrollbar");return{width:zt(t),height:zt(n)}}const Qr=`vc-util-locker-${Date.now()}`;let Vt=0;function Jr(){return document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)&&window.innerWidth>document.body.offsetWidth}function es(e){const t=B(()=>!!e&&!!e.value);Vt+=1;const n=`${Qr}_${Vt}`;nn(o=>{if(se()){if(t.value){const i=Sn(),r=Jr();Yn(`
html body {
  overflow-y: hidden;
  ${r?`width: calc(100% - ${i}px);`:""}
}`,n)}else at(n);o(()=>{at(n)})}},{flush:"post"})}let J=0;const xe=se(),Ft=e=>{if(!xe)return null;if(e){if(typeof e=="string")return document.querySelectorAll(e)[0];if(typeof e=="function")return e();if(typeof e=="object"&&e instanceof window.HTMLElement)return e}return document.body},ts=k({compatConfig:{MODE:3},name:"PortalWrapper",inheritAttrs:!1,props:{wrapperClassName:String,forceRender:{type:Boolean,default:void 0},getContainer:P.any,visible:{type:Boolean,default:void 0},autoLock:Un(),didUpdate:Function},setup(e,t){let{slots:n}=t;const o=S(),i=S(),r=S(),s=S(1),a=se()&&document.createElement("div"),u=()=>{var c,h;o.value===a&&((h=(c=o.value)===null||c===void 0?void 0:c.parentNode)===null||h===void 0||h.removeChild(o.value)),o.value=null};let l=null;const f=function(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1)||o.value&&!o.value.parentNode?(l=Ft(e.getContainer),l?(l.appendChild(o.value),!0):!1):!0},d=()=>xe?(o.value||(o.value=a,f(!0)),p(),o.value):null,p=()=>{const{wrapperClassName:c}=e;o.value&&c&&c!==o.value.className&&(o.value.className=c)};return Ke(()=>{p(),f()}),es(B(()=>e.autoLock&&e.visible&&se()&&(o.value===document.body||o.value===a))),De(()=>{let c=!1;X([()=>e.visible,()=>e.getContainer],(h,v)=>{let[O,y]=h,[b,_]=v;xe&&(l=Ft(e.getContainer),l===document.body&&(O&&!b?J+=1:c&&(J-=1))),c&&(typeof y=="function"&&typeof _=="function"?y.toString()!==_.toString():y!==_)&&u(),c=!0},{immediate:!0,flush:"post"}),pe(()=>{f()||(r.value=j(()=>{s.value+=1}))})}),Jt(()=>{const{visible:c}=e;xe&&l===document.body&&(J=c&&J?J-1:J),u(),j.cancel(r.value)}),()=>{const{forceRender:c,visible:h}=e;let v=null;const O={getOpenCount:()=>J,getContainer:d};return s.value&&(c||h||i.value)&&(v=T(Gn,{getContainer:d,ref:i,didUpdate:e.didUpdate},{default:()=>{var y;return(y=n.default)===null||y===void 0?void 0:y.call(n,O)}})),v}}}),ns=["onClick","onMousedown","onTouchstart","onMouseenter","onMouseleave","onFocus","onBlur","onContextmenu"],os=k({compatConfig:{MODE:3},name:"Trigger",mixins:[Zr],inheritAttrs:!1,props:Vo(),setup(e){const t=B(()=>{const{popupPlacement:i,popupAlign:r,builtinPlacements:s}=e;return i&&s?Ht(s,i,r):r}),n=S(null),o=i=>{n.value=i};return{vcTriggerContext:eo("vcTriggerContext",{}),popupRef:n,setPopupRef:o,triggerRef:S(null),align:t,focusTime:null,clickOutsideHandler:null,contextmenuOutsideHandler1:null,contextmenuOutsideHandler2:null,touchOutsideHandler:null,attachId:null,delayTimer:null,hasPopupMouseDown:!1,preClickTime:null,preTouchTime:null,mouseDownTimeout:null,childOriginEvents:{}}},data(){const e=this.$props;let t;return this.popupVisible!==void 0?t=!!e.popupVisible:t=!!e.defaultPopupVisible,ns.forEach(n=>{this[`fire${n}`]=o=>{this.fireEvents(n,o)}}),{prevPopupVisible:t,sPopupVisible:t,point:null}},watch:{popupVisible(e){e!==void 0&&(this.prevPopupVisible=this.sPopupVisible,this.sPopupVisible=e)}},created(){Qn("vcTriggerContext",{onPopupMouseDown:this.onPopupMouseDown,onPopupMouseenter:this.onPopupMouseenter,onPopupMouseleave:this.onPopupMouseleave}),Jn(this)},deactivated(){this.setPopupVisible(!1)},mounted(){this.$nextTick(()=>{this.updatedCal()})},updated(){this.$nextTick(()=>{this.updatedCal()})},beforeUnmount(){this.clearDelayTimer(),this.clearOutsideHandler(),clearTimeout(this.mouseDownTimeout),j.cancel(this.attachId)},methods:{updatedCal(){const e=this.$props;if(this.$data.sPopupVisible){let n;!this.clickOutsideHandler&&(this.isClickToHide()||this.isContextmenuToShow())&&(n=e.getDocument(this.getRootDomNode()),this.clickOutsideHandler=ce(n,"mousedown",this.onDocumentClick)),this.touchOutsideHandler||(n=n||e.getDocument(this.getRootDomNode()),this.touchOutsideHandler=ce(n,"touchstart",this.onDocumentClick,ee?{passive:!1}:!1)),!this.contextmenuOutsideHandler1&&this.isContextmenuToShow()&&(n=n||e.getDocument(this.getRootDomNode()),this.contextmenuOutsideHandler1=ce(n,"scroll",this.onContextmenuClose)),!this.contextmenuOutsideHandler2&&this.isContextmenuToShow()&&(this.contextmenuOutsideHandler2=ce(window,"blur",this.onContextmenuClose))}else this.clearOutsideHandler()},onMouseenter(e){const{mouseEnterDelay:t}=this.$props;this.fireEvents("onMouseenter",e),this.delaySetPopupVisible(!0,t,t?null:e)},onMouseMove(e){this.fireEvents("onMousemove",e),this.setPoint(e)},onMouseleave(e){this.fireEvents("onMouseleave",e),this.delaySetPopupVisible(!1,this.$props.mouseLeaveDelay)},onPopupMouseenter(){const{vcTriggerContext:e={}}=this;e.onPopupMouseenter&&e.onPopupMouseenter(),this.clearDelayTimer()},onPopupMouseleave(e){var t;if(e&&e.relatedTarget&&!e.relatedTarget.setTimeout&&oe((t=this.popupRef)===null||t===void 0?void 0:t.getElement(),e.relatedTarget))return;this.isMouseLeaveToHide()&&this.delaySetPopupVisible(!1,this.$props.mouseLeaveDelay);const{vcTriggerContext:n={}}=this;n.onPopupMouseleave&&n.onPopupMouseleave(e)},onFocus(e){this.fireEvents("onFocus",e),this.clearDelayTimer(),this.isFocusToShow()&&(this.focusTime=Date.now(),this.delaySetPopupVisible(!0,this.$props.focusDelay))},onMousedown(e){this.fireEvents("onMousedown",e),this.preClickTime=Date.now()},onTouchstart(e){this.fireEvents("onTouchstart",e),this.preTouchTime=Date.now()},onBlur(e){oe(e.target,e.relatedTarget||document.activeElement)||(this.fireEvents("onBlur",e),this.clearDelayTimer(),this.isBlurToHide()&&this.delaySetPopupVisible(!1,this.$props.blurDelay))},onContextmenu(e){e.preventDefault(),this.fireEvents("onContextmenu",e),this.setPopupVisible(!0,e)},onContextmenuClose(){this.isContextmenuToShow()&&this.close()},onClick(e){if(this.fireEvents("onClick",e),this.focusTime){let n;if(this.preClickTime&&this.preTouchTime?n=Math.min(this.preClickTime,this.preTouchTime):this.preClickTime?n=this.preClickTime:this.preTouchTime&&(n=this.preTouchTime),Math.abs(n-this.focusTime)<20)return;this.focusTime=0}this.preClickTime=0,this.preTouchTime=0,this.isClickToShow()&&(this.isClickToHide()||this.isBlurToHide())&&e&&e.preventDefault&&e.preventDefault(),e&&e.domEvent&&e.domEvent.preventDefault();const t=!this.$data.sPopupVisible;(this.isClickToHide()&&!t||t&&this.isClickToShow())&&this.setPopupVisible(!this.$data.sPopupVisible,e)},onPopupMouseDown(){const{vcTriggerContext:e={}}=this;this.hasPopupMouseDown=!0,clearTimeout(this.mouseDownTimeout),this.mouseDownTimeout=setTimeout(()=>{this.hasPopupMouseDown=!1},0),e.onPopupMouseDown&&e.onPopupMouseDown(...arguments)},onDocumentClick(e){if(this.$props.mask&&!this.$props.maskClosable)return;const t=e.target,n=this.getRootDomNode(),o=this.getPopupDomNode();(!oe(n,t)||this.isContextMenuOnly())&&!oe(o,t)&&!this.hasPopupMouseDown&&this.delaySetPopupVisible(!1,.1)},getPopupDomNode(){var e;return((e=this.popupRef)===null||e===void 0?void 0:e.getElement())||null},getRootDomNode(){var e,t,n,o;const{getTriggerDOMNode:i}=this.$props;if(i){const r=((t=(e=this.triggerRef)===null||e===void 0?void 0:e.$el)===null||t===void 0?void 0:t.nodeName)==="#comment"?null:ue(this.triggerRef);return ue(i(r))}try{const r=((o=(n=this.triggerRef)===null||n===void 0?void 0:n.$el)===null||o===void 0?void 0:o.nodeName)==="#comment"?null:ue(this.triggerRef);if(r)return r}catch{}return ue(this)},handleGetPopupClassFromAlign(e){const t=[],n=this.$props,{popupPlacement:o,builtinPlacements:i,prefixCls:r,alignPoint:s,getPopupClassNameFromAlign:a}=n;return o&&i&&t.push(Kr(i,r,e,s)),a&&t.push(a(e)),t.join(" ")},getPopupAlign(){const e=this.$props,{popupPlacement:t,popupAlign:n,builtinPlacements:o}=e;return t&&o?Ht(o,t,n):n},getComponent(){const e={};this.isMouseEnterToShow()&&(e.onMouseenter=this.onPopupMouseenter),this.isMouseLeaveToHide()&&(e.onMouseleave=this.onPopupMouseleave),e.onMousedown=this.onPopupMouseDown,e[ee?"onTouchstartPassive":"onTouchstart"]=this.onPopupMouseDown;const{handleGetPopupClassFromAlign:t,getRootDomNode:n,$attrs:o}=this,{prefixCls:i,destroyPopupOnHide:r,popupClassName:s,popupAnimation:a,popupTransitionName:u,popupStyle:l,mask:f,maskAnimation:d,maskTransitionName:p,zIndex:c,stretch:h,alignPoint:v,mobile:O,arrow:y,forceRender:b}=this.$props,{sPopupVisible:_,point:$}=this.$data,N=m(m({prefixCls:i,arrow:y,destroyPopupOnHide:r,visible:_,point:v?$:null,align:this.align,animation:a,getClassNameFromAlign:t,stretch:h,getRootDomNode:n,mask:f,zIndex:c,transitionName:u,maskAnimation:d,maskTransitionName:p,class:s,style:l,onAlign:o.onPopupAlign||dn},e),{ref:this.setPopupRef,mobile:O,forceRender:b});return T(Gr,N,{default:this.$slots.popup||(()=>Zn(this,"popup"))})},attachParent(e){j.cancel(this.attachId);const{getPopupContainer:t,getDocument:n}=this.$props,o=this.getRootDomNode();let i;t?(o||t.length===0)&&(i=t(o)):i=n(this.getRootDomNode()).body,i?i.appendChild(e):this.attachId=j(()=>{this.attachParent(e)})},getContainer(){const{$props:e}=this,{getDocument:t}=e,n=t(this.getRootDomNode()).createElement("div");return n.style.position="absolute",n.style.top="0",n.style.left="0",n.style.width="100%",this.attachParent(n),n},setPopupVisible(e,t){const{alignPoint:n,sPopupVisible:o,onPopupVisibleChange:i}=this;this.clearDelayTimer(),o!==e&&(Kn(this,"popupVisible")||this.setState({sPopupVisible:e,prevPopupVisible:o}),i&&i(e)),n&&t&&e&&this.setPoint(t)},setPoint(e){const{alignPoint:t}=this.$props;!t||!e||this.setState({point:{pageX:e.pageX,pageY:e.pageY}})},handlePortalUpdate(){this.prevPopupVisible!==this.sPopupVisible&&this.afterPopupVisibleChange(this.sPopupVisible)},delaySetPopupVisible(e,t,n){const o=t*1e3;if(this.clearDelayTimer(),o){const i=n?{pageX:n.pageX,pageY:n.pageY}:null;this.delayTimer=setTimeout(()=>{this.setPopupVisible(e,i),this.clearDelayTimer()},o)}else this.setPopupVisible(e,n)},clearDelayTimer(){this.delayTimer&&(clearTimeout(this.delayTimer),this.delayTimer=null)},clearOutsideHandler(){this.clickOutsideHandler&&(this.clickOutsideHandler.remove(),this.clickOutsideHandler=null),this.contextmenuOutsideHandler1&&(this.contextmenuOutsideHandler1.remove(),this.contextmenuOutsideHandler1=null),this.contextmenuOutsideHandler2&&(this.contextmenuOutsideHandler2.remove(),this.contextmenuOutsideHandler2=null),this.touchOutsideHandler&&(this.touchOutsideHandler.remove(),this.touchOutsideHandler=null)},createTwoChains(e){let t=()=>{};const n=lt(this);return this.childOriginEvents[e]&&n[e]?this[`fire${e}`]:(t=this.childOriginEvents[e]||n[e]||t,t)},isClickToShow(){const{action:e,showAction:t}=this.$props;return e.indexOf("click")!==-1||t.indexOf("click")!==-1},isContextMenuOnly(){const{action:e}=this.$props;return e==="contextmenu"||e.length===1&&e[0]==="contextmenu"},isContextmenuToShow(){const{action:e,showAction:t}=this.$props;return e.indexOf("contextmenu")!==-1||t.indexOf("contextmenu")!==-1},isClickToHide(){const{action:e,hideAction:t}=this.$props;return e.indexOf("click")!==-1||t.indexOf("click")!==-1},isMouseEnterToShow(){const{action:e,showAction:t}=this.$props;return e.indexOf("hover")!==-1||t.indexOf("mouseenter")!==-1},isMouseLeaveToHide(){const{action:e,hideAction:t}=this.$props;return e.indexOf("hover")!==-1||t.indexOf("mouseleave")!==-1},isFocusToShow(){const{action:e,showAction:t}=this.$props;return e.indexOf("focus")!==-1||t.indexOf("focus")!==-1},isBlurToHide(){const{action:e,hideAction:t}=this.$props;return e.indexOf("focus")!==-1||t.indexOf("blur")!==-1},forcePopupAlign(){var e;this.$data.sPopupVisible&&((e=this.popupRef)===null||e===void 0||e.forceAlign())},fireEvents(e,t){this.childOriginEvents[e]&&this.childOriginEvents[e](t);const n=this.$props[e]||this.$attrs[e];n&&n(t)},close(){this.setPopupVisible(!1)}},render(){const{$attrs:e}=this,t=Qe(qn(this)),{alignPoint:n,getPopupContainer:o}=this.$props,i=t[0];this.childOriginEvents=lt(i);const r={key:"trigger"};this.isContextmenuToShow()?r.onContextmenu=this.onContextmenu:r.onContextmenu=this.createTwoChains("onContextmenu"),this.isClickToHide()||this.isClickToShow()?(r.onClick=this.onClick,r.onMousedown=this.onMousedown,r[ee?"onTouchstartPassive":"onTouchstart"]=this.onTouchstart):(r.onClick=this.createTwoChains("onClick"),r.onMousedown=this.createTwoChains("onMousedown"),r[ee?"onTouchstartPassive":"onTouchstart"]=this.createTwoChains("onTouchstart")),this.isMouseEnterToShow()?(r.onMouseenter=this.onMouseenter,n&&(r.onMousemove=this.onMouseMove)):r.onMouseenter=this.createTwoChains("onMouseenter"),this.isMouseLeaveToHide()?r.onMouseleave=this.onMouseleave:r.onMouseleave=this.createTwoChains("onMouseleave"),this.isFocusToShow()||this.isBlurToHide()?(r.onFocus=this.onFocus,r.onBlur=this.onBlur):(r.onFocus=this.createTwoChains("onFocus"),r.onBlur=l=>{l&&(!l.relatedTarget||!oe(l.target,l.relatedTarget))&&this.createTwoChains("onBlur")(l)});const s=re(i&&i.props&&i.props.class,e.class);s&&(r.class=s);const a=he(i,m(m({},r),{ref:"triggerRef"}),!0,!0),u=T(ts,{key:"portal",getContainer:o&&(()=>o(this.getRootDomNode())),didUpdate:this.handlePortalUpdate,visible:this.$data.sPopupVisible},{default:this.getComponent});return T(tn,null,[a,u])}});function is(e,t,n,o){for(var i=e.length,r=n+-1;++r<i;)if(t(e[r],r,e))return r;return-1}function rs(e){return e!==e}function ss(e,t,n){for(var o=n-1,i=e.length;++o<i;)if(e[o]===t)return o;return-1}function as(e,t,n){return t===t?ss(e,t,n):is(e,rs,n)}function ta(e,t){var n=e==null?0:e.length;return!!n&&as(e,t,0)>-1}const ls=e=>({animationDuration:e,animationFillMode:"both"}),us=e=>({animationDuration:e,animationFillMode:"both"}),cs=function(e,t,n,o){const r=(arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1)?"&":"";return{[`
      ${r}${e}-enter,
      ${r}${e}-appear
    `]:m(m({},ls(o)),{animationPlayState:"paused"}),[`${r}${e}-leave`]:m(m({},us(o)),{animationPlayState:"paused"}),[`
      ${r}${e}-enter${e}-enter-active,
      ${r}${e}-appear${e}-appear-active
    `]:{animationName:t,animationPlayState:"running"},[`${r}${e}-leave${e}-leave-active`]:{animationName:n,animationPlayState:"running",pointerEvents:"none"}}},fs=new V("antZoomIn",{"0%":{transform:"scale(0.2)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),ps=new V("antZoomOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.2)",opacity:0}}),Wt=new V("antZoomBigIn",{"0%":{transform:"scale(0.8)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),jt=new V("antZoomBigOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.8)",opacity:0}}),ds=new V("antZoomUpIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 0%"}}),hs=new V("antZoomUpOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 0%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0}}),ms=new V("antZoomLeftIn",{"0%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"0% 50%"}}),vs=new V("antZoomLeftOut",{"0%":{transform:"scale(1)",transformOrigin:"0% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0}}),gs=new V("antZoomRightIn",{"0%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"100% 50%"}}),ys=new V("antZoomRightOut",{"0%":{transform:"scale(1)",transformOrigin:"100% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0}}),ws=new V("antZoomDownIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 100%"}}),bs=new V("antZoomDownOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 100%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0}}),Os={zoom:{inKeyframes:fs,outKeyframes:ps},"zoom-big":{inKeyframes:Wt,outKeyframes:jt},"zoom-big-fast":{inKeyframes:Wt,outKeyframes:jt},"zoom-left":{inKeyframes:ms,outKeyframes:vs},"zoom-right":{inKeyframes:gs,outKeyframes:ys},"zoom-up":{inKeyframes:ds,outKeyframes:hs},"zoom-down":{inKeyframes:ws,outKeyframes:bs}},_s=(e,t)=>{const{antCls:n}=e,o=`${n}-${t}`,{inKeyframes:i,outKeyframes:r}=Os[t];return[cs(o,i,r,t==="zoom-big-fast"?e.motionDurationFast:e.motionDurationMid),{[`
        ${o}-enter,
        ${o}-appear
      `]:{transform:"scale(0)",opacity:0,animationTimingFunction:e.motionEaseOutCirc,"&-prepare":{transform:"none"}},[`${o}-leave`]:{animationTimingFunction:e.motionEaseInOutCirc}}]},na=e=>({[e.componentCls]:{[`${e.antCls}-motion-collapse-legacy`]:{overflow:"hidden","&-active":{transition:`height ${e.motionDurationMid} ${e.motionEaseInOut},
        opacity ${e.motionDurationMid} ${e.motionEaseInOut} !important`}},[`${e.antCls}-motion-collapse`]:{overflow:"hidden",transition:`height ${e.motionDurationMid} ${e.motionEaseInOut},
        opacity ${e.motionDurationMid} ${e.motionEaseInOut} !important`}}}),oa=["xxxl","xxl","xl","lg","md","sm","xs"],Cs=e=>({xs:`(max-width: ${e.screenXSMax}px)`,sm:`(min-width: ${e.screenSM}px)`,md:`(min-width: ${e.screenMD}px)`,lg:`(min-width: ${e.screenLG}px)`,xl:`(min-width: ${e.screenXL}px)`,xxl:`(min-width: ${e.screenXXL}px)`,xxxl:`{min-width: ${e.screenXXXL}px}`});function ia(){const[,e]=to();return B(()=>{const t=Cs(e.value),n=new Map;let o=-1,i={};return{matchHandlers:{},dispatch(r){return i=r,n.forEach(s=>s(i)),n.size>=1},subscribe(r){return n.size||this.register(),o+=1,n.set(o,r),r(i),o},unsubscribe(r){n.delete(r),n.size||this.unregister()},unregister(){Object.keys(t).forEach(r=>{const s=t[r],a=this.matchHandlers[s];a==null||a.mql.removeListener(a==null?void 0:a.listener)}),n.clear()},register(){Object.keys(t).forEach(r=>{const s=t[r],a=l=>{let{matches:f}=l;this.dispatch(m(m({},i),{[r]:f}))},u=window.matchMedia(s);u.addListener(a),this.matchHandlers[s]={mql:u,listener:a},a(u)})},responsiveMap:t}})}const H={adjustX:1,adjustY:1},z=[0,0],$n={left:{points:["cr","cl"],overflow:H,offset:[-4,0],targetOffset:z},right:{points:["cl","cr"],overflow:H,offset:[4,0],targetOffset:z},top:{points:["bc","tc"],overflow:H,offset:[0,-4],targetOffset:z},bottom:{points:["tc","bc"],overflow:H,offset:[0,4],targetOffset:z},topLeft:{points:["bl","tl"],overflow:H,offset:[0,-4],targetOffset:z},leftTop:{points:["tr","tl"],overflow:H,offset:[-4,0],targetOffset:z},topRight:{points:["br","tr"],overflow:H,offset:[0,-4],targetOffset:z},rightTop:{points:["tl","tr"],overflow:H,offset:[4,0],targetOffset:z},bottomRight:{points:["tr","br"],overflow:H,offset:[0,4],targetOffset:z},rightBottom:{points:["bl","br"],overflow:H,offset:[4,0],targetOffset:z},bottomLeft:{points:["tl","bl"],overflow:H,offset:[0,4],targetOffset:z},leftBottom:{points:["br","bl"],overflow:H,offset:[-4,0],targetOffset:z}},Ps={prefixCls:String,id:String,overlayInnerStyle:P.any},xs=k({compatConfig:{MODE:3},name:"TooltipContent",props:Ps,setup(e,t){let{slots:n}=t;return()=>{var o;return T("div",{class:`${e.prefixCls}-inner`,id:e.id,role:"tooltip",style:e.overlayInnerStyle},[(o=n.overlay)===null||o===void 0?void 0:o.call(n)])}}});var Ts=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,o=Object.getOwnPropertySymbols(e);i<o.length;i++)t.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(n[o[i]]=e[o[i]]);return n};function Xt(){}const Ss=k({compatConfig:{MODE:3},name:"Tooltip",inheritAttrs:!1,props:{trigger:P.any.def(["hover"]),defaultVisible:{type:Boolean,default:void 0},visible:{type:Boolean,default:void 0},placement:P.string.def("right"),transitionName:String,animation:P.any,afterVisibleChange:P.func.def(()=>{}),overlayStyle:{type:Object,default:void 0},overlayClassName:String,prefixCls:P.string.def("rc-tooltip"),mouseEnterDelay:P.number.def(.1),mouseLeaveDelay:P.number.def(.1),getPopupContainer:Function,destroyTooltipOnHide:{type:Boolean,default:!1},align:P.object.def(()=>({})),arrowContent:P.any.def(null),tipId:String,builtinPlacements:P.object,overlayInnerStyle:{type:Object,default:void 0},popupVisible:{type:Boolean,default:void 0},onVisibleChange:Function,onPopupAlign:Function,arrow:{type:Boolean,default:!0}},setup(e,t){let{slots:n,attrs:o,expose:i}=t;const r=S(),s=()=>{const{prefixCls:f,tipId:d,overlayInnerStyle:p}=e;return[e.arrow?T("div",{class:`${f}-arrow`,key:"arrow"},[no(n,e,"arrowContent")]):null,T(xs,{key:"content",prefixCls:f,id:d,overlayInnerStyle:p},{overlay:n.overlay})]};i({getPopupDomNode:()=>r.value.getPopupDomNode(),triggerDOM:r,forcePopupAlign:()=>{var f;return(f=r.value)===null||f===void 0?void 0:f.forcePopupAlign()}});const u=S(!1),l=S(!1);return nn(()=>{const{destroyTooltipOnHide:f}=e;if(typeof f=="boolean")u.value=f;else if(f&&typeof f=="object"){const{keepParent:d}=f;u.value=d===!0,l.value=d===!1}}),()=>{const{overlayClassName:f,trigger:d,mouseEnterDelay:p,mouseLeaveDelay:c,overlayStyle:h,prefixCls:v,afterVisibleChange:O,transitionName:y,animation:b,placement:_,align:$,destroyTooltipOnHide:N,defaultVisible:x}=e,I=Ts(e,["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","afterVisibleChange","transitionName","animation","placement","align","destroyTooltipOnHide","defaultVisible"]),A=m({},I);e.visible!==void 0&&(A.popupVisible=e.visible);const D=m(m(m({popupClassName:f,prefixCls:v,action:d,builtinPlacements:$n,popupPlacement:_,popupAlign:$,afterPopupVisibleChange:O,popupTransitionName:y,popupAnimation:b,defaultPopupVisible:x,destroyPopupOnHide:u.value,autoDestroy:l.value,mouseLeaveDelay:c,popupStyle:h,mouseEnterDelay:p},A),o),{onPopupVisibleChange:e.onVisibleChange||Xt,onPopupAlign:e.onPopupAlign||Xt,ref:r,arrow:!!e.arrow,popup:s()});return T(os,D,{default:n.default})}}}),$s=()=>({trigger:[String,Array],open:{type:Boolean,default:void 0},visible:{type:Boolean,default:void 0},placement:String,color:String,transitionName:String,overlayStyle:ge(),overlayInnerStyle:ge(),overlayClassName:String,openClassName:String,prefixCls:String,mouseEnterDelay:Number,mouseLeaveDelay:Number,getPopupContainer:Function,arrowPointAtCenter:{type:Boolean,default:void 0},arrow:{type:[Boolean,Object],default:!0},autoAdjustOverflow:{type:[Boolean,Object],default:void 0},destroyTooltipOnHide:{type:Boolean,default:void 0},align:ge(),builtinPlacements:ge(),children:Array,onVisibleChange:Function,"onUpdate:visible":Function,onOpenChange:Function,"onUpdate:open":Function}),Es={adjustX:1,adjustY:1},kt={adjustX:0,adjustY:0},As=[0,0];function Yt(e){return typeof e=="boolean"?e?Es:kt:m(m({},kt),e)}function Ms(e){const{arrowWidth:t=4,horizontalArrowShift:n=16,verticalArrowShift:o=8,autoAdjustOverflow:i,arrowPointAtCenter:r}=e,s={left:{points:["cr","cl"],offset:[-4,0]},right:{points:["cl","cr"],offset:[4,0]},top:{points:["bc","tc"],offset:[0,-4]},bottom:{points:["tc","bc"],offset:[0,4]},topLeft:{points:["bl","tc"],offset:[-(n+t),-4]},leftTop:{points:["tr","cl"],offset:[-4,-(o+t)]},topRight:{points:["br","tc"],offset:[n+t,-4]},rightTop:{points:["tl","cr"],offset:[4,-(o+t)]},bottomRight:{points:["tr","bc"],offset:[n+t,4]},rightBottom:{points:["bl","cr"],offset:[4,o+t]},bottomLeft:{points:["tl","bc"],offset:[-(n+t),4]},leftBottom:{points:["br","cl"],offset:[-4,o+t]}};return Object.keys(s).forEach(a=>{s[a]=r?m(m({},s[a]),{overflow:Yt(i),targetOffset:As}):m(m({},$n[a]),{overflow:Yt(i)}),s[a].ignoreShake=!0}),s}function Ds(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];for(let t=0,n=e.length;t<n;t++)if(e[t]!==void 0)return e[t]}const Rs=Ee.map(e=>`${e}-inverse`),Ns=["success","processing","error","default","warning"];function Ls(e){return(arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0)?[...Rs,...Ee].includes(e):Ee.includes(e)}function ra(e){return Ns.includes(e)}function Is(e,t){const n=Ls(t),o=re({[`${e}-${t}`]:t&&n}),i={},r={};return t&&!n&&(i.background=t,r["--antd-arrow-background-color"]=t),{className:o,overlayStyle:i,arrowStyle:r}}function Ce(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return e.map(n=>`${t}${n}`).join(",")}const En=8;function Bs(e){const t=En,{sizePopupArrow:n,contentRadius:o,borderRadiusOuter:i,limitVerticalRadius:r}=e,s=n/2-Math.ceil(i*(Math.sqrt(2)-1)),a=(o>12?o+2:12)-s,u=r?t-s:a;return{dropdownArrowOffset:a,dropdownArrowOffsetVertical:u}}function Hs(e,t){const{componentCls:n,sizePopupArrow:o,marginXXS:i,borderRadiusXS:r,borderRadiusOuter:s,boxShadowPopoverArrow:a}=e,{colorBg:u,showArrowCls:l,contentRadius:f=e.borderRadiusLG,limitVerticalRadius:d}=t,{dropdownArrowOffsetVertical:p,dropdownArrowOffset:c}=Bs({sizePopupArrow:o,contentRadius:f,borderRadiusOuter:s,limitVerticalRadius:d}),h=o/2+i;return{[n]:{[`${n}-arrow`]:[m(m({position:"absolute",zIndex:1,display:"block"},Io(o,r,s,u,a)),{"&:before":{background:u}})],[[`&-placement-top ${n}-arrow`,`&-placement-topLeft ${n}-arrow`,`&-placement-topRight ${n}-arrow`].join(",")]:{bottom:0,transform:"translateY(100%) rotate(180deg)"},[`&-placement-top ${n}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},[`&-placement-topLeft ${n}-arrow`]:{left:{_skip_check_:!0,value:c}},[`&-placement-topRight ${n}-arrow`]:{right:{_skip_check_:!0,value:c}},[[`&-placement-bottom ${n}-arrow`,`&-placement-bottomLeft ${n}-arrow`,`&-placement-bottomRight ${n}-arrow`].join(",")]:{top:0,transform:"translateY(-100%)"},[`&-placement-bottom ${n}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(-100%)"},[`&-placement-bottomLeft ${n}-arrow`]:{left:{_skip_check_:!0,value:c}},[`&-placement-bottomRight ${n}-arrow`]:{right:{_skip_check_:!0,value:c}},[[`&-placement-left ${n}-arrow`,`&-placement-leftTop ${n}-arrow`,`&-placement-leftBottom ${n}-arrow`].join(",")]:{right:{_skip_check_:!0,value:0},transform:"translateX(100%) rotate(90deg)"},[`&-placement-left ${n}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(100%) rotate(90deg)"},[`&-placement-leftTop ${n}-arrow`]:{top:p},[`&-placement-leftBottom ${n}-arrow`]:{bottom:p},[[`&-placement-right ${n}-arrow`,`&-placement-rightTop ${n}-arrow`,`&-placement-rightBottom ${n}-arrow`].join(",")]:{left:{_skip_check_:!0,value:0},transform:"translateX(-100%) rotate(-90deg)"},[`&-placement-right ${n}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(-100%) rotate(-90deg)"},[`&-placement-rightTop ${n}-arrow`]:{top:p},[`&-placement-rightBottom ${n}-arrow`]:{bottom:p},[Ce(["&-placement-topLeft","&-placement-top","&-placement-topRight"].map(v=>v+=":not(&-arrow-hidden)"),l)]:{paddingBottom:h},[Ce(["&-placement-bottomLeft","&-placement-bottom","&-placement-bottomRight"].map(v=>v+=":not(&-arrow-hidden)"),l)]:{paddingTop:h},[Ce(["&-placement-leftTop","&-placement-left","&-placement-leftBottom"].map(v=>v+=":not(&-arrow-hidden)"),l)]:{paddingRight:{_skip_check_:!0,value:h}},[Ce(["&-placement-rightTop","&-placement-right","&-placement-rightBottom"].map(v=>v+=":not(&-arrow-hidden)"),l)]:{paddingLeft:{_skip_check_:!0,value:h}}}}}const zs=e=>{const{componentCls:t,tooltipMaxWidth:n,tooltipColor:o,tooltipBg:i,tooltipBorderRadius:r,zIndexPopup:s,controlHeight:a,boxShadowSecondary:u,paddingSM:l,paddingXS:f,tooltipRadiusOuter:d}=e;return[{[t]:m(m(m(m({},io(e)),{position:"absolute",zIndex:s,display:"block","&":[{width:"max-content"},{width:"intrinsic"}],maxWidth:n,visibility:"visible","&-hidden":{display:"none"},"--antd-arrow-background-color":i,[`${t}-inner`]:{minWidth:a,minHeight:a,padding:`${l/2}px ${f}px`,color:o,textAlign:"start",textDecoration:"none",wordWrap:"break-word",backgroundColor:i,borderRadius:r,boxShadow:u},[["&-placement-left","&-placement-leftTop","&-placement-leftBottom","&-placement-right","&-placement-rightTop","&-placement-rightBottom"].join(",")]:{[`${t}-inner`]:{borderRadius:Math.min(r,En)}},[`${t}-content`]:{position:"relative"}}),Bo(e,(p,c)=>{let{darkColor:h}=c;return{[`&${t}-${p}`]:{[`${t}-inner`]:{backgroundColor:h},[`${t}-arrow`]:{"--antd-arrow-background-color":h}}}})),{"&-rtl":{direction:"rtl"}})},Hs(on(e,{borderRadiusOuter:d}),{colorBg:"var(--antd-arrow-background-color)",showArrowCls:"",contentRadius:r,limitVerticalRadius:!0}),{[`${t}-pure`]:{position:"relative",maxWidth:"none"}}]},Vs=(e,t)=>oo("Tooltip",o=>{if((t==null?void 0:t.value)===!1)return[];const{borderRadius:i,colorTextLightSolid:r,colorBgDefault:s,borderRadiusOuter:a}=o,u=on(o,{tooltipMaxWidth:250,tooltipColor:r,tooltipBorderRadius:i,tooltipBg:s,tooltipRadiusOuter:a>4?4:a});return[zs(u),_s(o,"zoom-big-fast")]},o=>{let{zIndexPopupBase:i,colorBgSpotlight:r}=o;return{zIndexPopup:i+70,colorBgDefault:r}})(e),Fs=(e,t)=>{const n={},o=m({},e);return t.forEach(i=>{e&&i in e&&(n[i]=e[i],delete o[i])}),{picked:n,omitted:o}},Ws=()=>m(m({},$s()),{title:P.any}),sa=()=>({trigger:"hover",align:{},placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1,arrowPointAtCenter:!1,autoAdjustOverflow:!0}),js=k({compatConfig:{MODE:3},name:"ATooltip",inheritAttrs:!1,props:yo(Ws(),{trigger:"hover",align:{},placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1,arrowPointAtCenter:!1,autoAdjustOverflow:!0}),slots:Object,setup(e,t){let{slots:n,emit:o,attrs:i,expose:r}=t;const{prefixCls:s,getPopupContainer:a,direction:u,rootPrefixCls:l}=ro("tooltip",e),f=B(()=>{var g;return(g=e.open)!==null&&g!==void 0?g:e.visible}),d=G(Ds([e.open,e.visible])),p=G();let c;X(f,g=>{j.cancel(c),c=j(()=>{d.value=!!g})});const h=()=>{var g;const C=(g=e.title)!==null&&g!==void 0?g:n.title;return!C&&C!==0},v=g=>{const C=h();f.value===void 0&&(d.value=C?!1:g),C||(o("update:visible",g),o("visibleChange",g),o("update:open",g),o("openChange",g))};r({getPopupDomNode:()=>p.value.getPopupDomNode(),open:d,forcePopupAlign:()=>{var g;return(g=p.value)===null||g===void 0?void 0:g.forcePopupAlign()}});const y=B(()=>{var g;const{builtinPlacements:C,autoAdjustOverflow:F,arrow:M,arrowPointAtCenter:L}=e;let E=L;return typeof M=="object"&&(E=(g=M.pointAtCenter)!==null&&g!==void 0?g:L),C||Ms({arrowPointAtCenter:E,autoAdjustOverflow:F})}),b=g=>g||g==="",_=g=>{const C=g.type;if(typeof C=="object"&&g.props&&((C.__ANT_BUTTON===!0||C==="button")&&b(g.props.disabled)||C.__ANT_SWITCH===!0&&(b(g.props.disabled)||b(g.props.loading))||C.__ANT_RADIO===!0&&b(g.props.disabled))){const{picked:F,omitted:M}=Fs(uo(g),["position","left","right","top","bottom","float","display","zIndex"]),L=m(m({display:"inline-block"},F),{cursor:"not-allowed",lineHeight:1,width:g.props&&g.props.block?"100%":void 0}),E=m(m({},M),{pointerEvents:"none"}),R=he(g,{style:E},!0);return T("span",{style:L,class:`${s.value}-disabled-compatible-wrapper`},[R])}return g},$=()=>{var g,C;return(g=e.title)!==null&&g!==void 0?g:(C=n.title)===null||C===void 0?void 0:C.call(n)},N=(g,C)=>{const F=y.value,M=Object.keys(F).find(L=>{var E,R;return F[L].points[0]===((E=C.points)===null||E===void 0?void 0:E[0])&&F[L].points[1]===((R=C.points)===null||R===void 0?void 0:R[1])});if(M){const L=g.getBoundingClientRect(),E={top:"50%",left:"50%"};M.indexOf("top")>=0||M.indexOf("Bottom")>=0?E.top=`${L.height-C.offset[1]}px`:(M.indexOf("Top")>=0||M.indexOf("bottom")>=0)&&(E.top=`${-C.offset[1]}px`),M.indexOf("left")>=0||M.indexOf("Right")>=0?E.left=`${L.width-C.offset[0]}px`:(M.indexOf("right")>=0||M.indexOf("Left")>=0)&&(E.left=`${-C.offset[0]}px`),g.style.transformOrigin=`${E.left} ${E.top}`}},x=B(()=>Is(s.value,e.color)),I=B(()=>i["data-popover-inject"]),[A,D]=Vs(s,B(()=>!I.value));return()=>{var g,C;const{openClassName:F,overlayClassName:M,overlayStyle:L,overlayInnerStyle:E}=e;let R=(C=Qe((g=n.default)===null||g===void 0?void 0:g.call(n)))!==null&&C!==void 0?C:null;R=R.length===1?R[0]:R;let Z=d.value;if(f.value===void 0&&h()&&(Z=!1),!R)return null;const U=_(so(R)&&!ao(R)?R:T("span",null,[R])),rt=re({[F||`${s.value}-open`]:!0,[U.props&&U.props.class]:U.props&&U.props.class}),Be=re(M,{[`${s.value}-rtl`]:u.value==="rtl"},x.value.className,D.value),Dn=m(m({},x.value.overlayStyle),E),Rn=x.value.arrowStyle,Nn=m(m(m({},i),e),{prefixCls:s.value,arrow:!!e.arrow,getPopupContainer:a==null?void 0:a.value,builtinPlacements:y.value,visible:Z,ref:p,overlayClassName:Be,overlayStyle:m(m({},Rn),L),overlayInnerStyle:Dn,onVisibleChange:v,onPopupAlign:N,transitionName:lo(l.value,"zoom-big-fast",e.transitionName)});return A(T(Ss,Nn,{default:()=>[d.value?he(U,{class:rt}):U],arrowContent:()=>T("span",{class:`${s.value}-arrow-content`},null),overlay:$}))}}}),aa=co(js);function An(e,t){return e.classList?e.classList.contains(t):` ${e.className} `.indexOf(` ${t} `)>-1}function Ut(e,t){e.classList?e.classList.add(t):An(e,t)||(e.className=`${e.className} ${t}`)}function Gt(e,t){if(e.classList)e.classList.remove(t);else if(An(e,t)){const n=e.className;e.className=` ${n} `.replace(` ${t} `," ")}}const la=function(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"ant-motion-collapse",t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return{name:e,appear:t,css:!0,onBeforeEnter:n=>{n.style.height="0px",n.style.opacity="0",Ut(n,e)},onEnter:n=>{pe(()=>{n.style.height=`${n.scrollHeight}px`,n.style.opacity="1"})},onAfterEnter:n=>{n&&(Gt(n,e),n.style.height=null,n.style.opacity=null)},onBeforeLeave:n=>{Ut(n,e),n.style.height=`${n.offsetHeight}px`,n.style.opacity=null},onLeave:n=>{setTimeout(()=>{n.style.height="0px",n.style.opacity="0"})},onAfterLeave:n=>{n&&(Gt(n,e),n.style&&(n.style.height=null,n.style.opacity=null))}}},Xs=()=>se()&&window.document.documentElement,Mn=e=>{if(se()&&window.document.documentElement){const t=Array.isArray(e)?e:[e],{documentElement:n}=window.document;return t.some(o=>o in n.style)}return!1},ks=(e,t)=>{if(!Mn(e))return!1;const n=document.createElement("div"),o=n.style[e];return n.style[e]=t,n.style[e]!==o};function ua(e,t){return!Array.isArray(e)&&t!==void 0?ks(e,t):Mn(e)}let Pe;const ca=()=>{if(!Xs())return!1;if(Pe!==void 0)return Pe;const e=document.createElement("div");return e.style.display="flex",e.style.flexDirection="column",e.style.rowGap="1px",e.appendChild(document.createElement("div")),e.appendChild(document.createElement("div")),document.body.appendChild(e),Pe=e.scrollHeight===1,document.body.removeChild(e),Pe};export{ca as $,or as A,Zr as B,Hs as C,Ee as D,os as E,Bs as F,Io as G,Ms as H,Bo as I,Ls as J,ra as K,Pn as L,Lr as M,ne as N,_r as O,ts as P,$r as Q,qs as R,Me as S,aa as T,xr as U,Cr as V,Et as W,K as X,Dt as Y,Tn as Z,is as _,he as a,fs as a0,Xr as a1,cs as b,la as c,_s as d,Xs as e,ce as f,na as g,Ds as h,Dr as i,sa as j,Ir as k,$s as l,Zs as m,ua as n,Ks as o,Js as p,Ut as q,oa as r,Gt as s,Qs as t,ia as u,ee as v,Sn as w,ea as x,ar as y,ta as z};
