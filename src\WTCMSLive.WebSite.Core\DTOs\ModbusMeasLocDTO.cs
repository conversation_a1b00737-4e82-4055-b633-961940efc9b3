﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;

namespace WTCMSLive.WebSite.Core.DTOs
{
    public class ModbusMeasLocDTO
    {

        public string? MeasLocationID { get; set; }

        public string? WindTurbineID { get; set; }

        public string? MeasLocName { get; set; }

        public string? SectionName { get; set; }
        public string? Orientation { get; set; }
        public string? ComponentID { get; set; }

        public string? MeasType { get;set; }
        public string? PhysicalType { get; set; }

    }

    /// <summary>
    /// SVM和Modbus测量位置统一响应DTO
    /// </summary>
    public class UnifiedMeasLocDTO
    {
        public string MeasLocationID { get; set; }
        public string WindTurbineID { get; set; }
        public string MeasLocName { get; set; }
        public string? SectionName { get; set; }
        public string? Orientation { get; set; }
        public string? ComponentID { get; set; }
        public string MeasType { get; set; } // "svm" 或 "modbus"
        public int OrderSeq { get; set; }
        public string? ComponentName { get; set; }
        public string? PhysicalType { get; set; }
    }
}
