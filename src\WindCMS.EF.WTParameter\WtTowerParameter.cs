﻿using System;
using System.Collections.Generic;

namespace WindCMS.EF.WTParameter
{
    /// <summary>
    /// 存储与塔筒相关的参数，包括法兰和索力监测系统
    /// </summary>
    public partial class WtTowerParameter
    {
        public WtTowerParameter()
        {
            WtParametersFlanges = new HashSet<WtParametersFlange>();
        }

        /// <summary>
        /// 塔筒参数的唯一标识符
        /// </summary>
        public int TowerId { get; set; }
        /// <summary>
        /// 对应的机组型号ID（外键）
        /// </summary>
        public string ModelId { get; set; } = null!;
        /// <summary>
        /// 塔筒类型
        /// </summary>
        public string? TowerType { get; set; }
        /// <summary>
        /// 塔筒总高度（单位：mm）
        /// </summary>
        public decimal? TowerHeight { get; set; }
        /// <summary>
        /// 混凝土塔筒高度（单位：mm）
        /// </summary>
        public decimal? ConcreteTowerHeight { get; set; }
        /// <summary>
        /// 钢制塔筒高度（单位：mm）
        /// </summary>
        public decimal? SteelTowerHeight { get; set; }
        /// <summary>
        /// 塔基直径（单位：mm）
        /// </summary>
        public decimal? BaseDiameter { get; set; }
        /// <summary>
        /// 塔筒理论一阶固有频率（单位：Hz）
        /// </summary>
        public decimal? TowerNaturalFrequency { get; set; }
        /// <summary>
        /// 塔筒理论一阶固有频率上限（单位：Hz）
        /// </summary>
        public decimal? TowerNaturalFrequencyUpper { get; set; }
        /// <summary>
        /// 塔筒理论一阶固有频率下限（单位：Hz）
        /// </summary>
        public decimal? TowerNaturalFrequencyLower { get; set; }
        /// <summary>
        /// 塔筒理论二阶固有频率（单位：Hz）
        /// </summary>
        public decimal? SecondOrderNaturalFrequency { get; set; }
        /// <summary>
        /// 法兰层数
        /// </summary>
        public int? FlangeCount { get; set; }
        /// <summary>
        /// 存储多层法兰的详细参数（嵌套结构）
        /// </summary>
        public string? FlangeDetails { get; set; }
        /// <summary>
        /// 拉索簇数
        /// </summary>
        public int? CableClusterCount { get; set; }
        /// <summary>
        /// 每簇拉索根数
        /// </summary>
        public int? CablePerCluster { get; set; }
        /// <summary>
        /// 每根拉索的钢丝条数
        /// </summary>
        public int? WirePerCable { get; set; }
        /// <summary>
        /// 每条钢丝直径（单位：mm）
        /// </summary>
        public decimal? WireDiameter { get; set; }
        /// <summary>
        /// 拉索总长度（单位：m）
        /// </summary>
        public decimal? CableLength { get; set; }
        /// <summary>
        /// 是否有换向器或摩擦片（1表示有，0表示无）
        /// </summary>
        public bool? HasSwitch { get; set; }
        /// <summary>
        /// 换向器或摩擦片高度（单位：m）
        /// </summary>
        public decimal? SwitchHeight { get; set; }
        /// <summary>
        /// 叶轮并网转频（单位：Hz）
        /// </summary>
        public decimal? GridFrequency { get; set; }
        /// <summary>
        /// 叶轮最大转频（单位：Hz）
        /// </summary>
        public decimal? MaxGridFrequency { get; set; }
        /// <summary>
        /// 塔筒节数
        /// </summary>
        public int? TowerSectionCount { get; set; }
        /// <summary>
        /// 挠度系数
        /// </summary>
        public decimal? DeflectionCoefficient { get; set; }
        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime? CreatedAt { get; set; }
        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        public virtual ICollection<WtParametersFlange> WtParametersFlanges { get; set; }

        /// <summary>
        /// 塔顶直径（单位：mm）
        /// </summary>
        public decimal? TopDiameter { get; set; }
    }
}
