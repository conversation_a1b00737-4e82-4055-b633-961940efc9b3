﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.BusinessEntity;
using WTCMSLive.BusinessEntity.SVM;
using WTCMSLive.Entity.Enum;
using WTCMSLive.Entity.Models;
using WTCMSLive.IDALService;

namespace WTCMSLive.BusinessEntityConvert
{
    public static class ConvertEntityDAToBusinessDEV
    {
        static IWTMService WTMSvc = AppFramework.ServiceBus.ServiceLocator.GetService<IWTMService>();
        static IDeviceService devSvc = AppFramework.ServiceBus.ServiceLocator.GetService<IDeviceService>();
        static IMDFService mdfSvc = AppFramework.ServiceBus.ServiceLocator.GetService<IMDFService>();
        static IDAUService dauSvc = AppFramework.ServiceBus.ServiceLocator.GetService<IDAUService>();
        static IMCSystemService MCSvc = AppFramework.ServiceBus.ServiceLocator.GetService<IMCSystemService>();
        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 风场实体转换
        /// </summary>
        /// <param name="_windPark"></param>
        /// <returns></returns>
        public static WindPark ConvertWinPark(DevWindPark _DEVWindPark)
        {
            WindPark windpark = new WindPark();
            windpark.Address = _DEVWindPark.Address;
            windpark.ContactMan = _DEVWindPark.ContactMan;
            windpark.ContactTel = _DEVWindPark.ContactTel;
            windpark.Description = _DEVWindPark.Description;
            windpark.OperationalDate = _DEVWindPark.OperationalDate;
            windpark.PostCode = _DEVWindPark.PostCode;
            windpark.WindParkCode = _DEVWindPark.WindParkCode;
            windpark.WindParkID = _DEVWindPark.WindParkID.ToString();
            windpark.WindParkName = _DEVWindPark.WindParkName;
            windpark.WindTurbineList = new List<WindTurbine>();
            windpark.WTTotalNumber = _DEVWindPark.DevWindTurbines.Count;
            return windpark;
        }

        public static List<WindPark> ConvertWindParkList(List<DevWindPark> _DevWindParkList)
        {
            List<WindPark> _windParkList = new List<WindPark>();
            foreach (DevWindPark item in _DevWindParkList)
            {
                WindPark windPark = ConvertWinPark(item);
                _windParkList.Add(windPark);
            }
            return _windParkList;
        }

        public static WindTurbine ConvertWindTurbine(DevWindTurbine _DevWindTurbine)
        {
            WindTurbine turBine = new WindTurbine();
            if (_DevWindTurbine == null)
                return turBine;
            turBine.Databaseversion = "";
            turBine.FactedPower = 0;
            //turBine.IsAcquisitionPower = _DevWindTurbine.IsAcquisitionPower == "1" ? true : false;
            turBine.Mcs = new MCS();
            if (_DevWindTurbine.MCSystem != null)
            {
                turBine.Mcs = ConvertMCS(_DevWindTurbine.MCSystem);
            }
            if (_DevWindTurbine.MinWorkingRotSpeed != null)
            {
                turBine.MinWorkingRotSpeed = int.Parse(_DevWindTurbine.MinWorkingRotSpeed.ToString());
            }
            turBine.OperationalDate = (DateTime)_DevWindTurbine.OperationalDate;
            List<MeasLoc_Process> measLocProList = new List<MeasLoc_Process>();
            if (_DevWindTurbine.DevMeasLocProcesses != null && _DevWindTurbine.DevMeasLocProcesses.Count > 0)
            {
                foreach (DevMeasLocProcess pro in _DevWindTurbine.DevMeasLocProcesses)
                {
                    MeasLoc_Process measLocPro = ConvertMeasLoc_Process(pro);
                    measLocProList.Add(measLocPro);
                }
            }
            turBine.ProcessMeasLocList = measLocProList;
            if (_DevWindTurbine.DevMeasLocRotSpds != null && _DevWindTurbine.DevMeasLocRotSpds.Count > 0)
            {
                turBine.RotSpdMeasLoc = ConvertMeasLoc_RotSpd(_DevWindTurbine.DevMeasLocRotSpds.First());
            }
            turBine.TurbineRTAlarmStatus = ConvertDAToBusinessEntityRTStatus.ConvertDeviceRTAlarmStatus_Turbine(_DevWindTurbine.AlarmStatusRTTurbine);
            List<WindTurbineComponent> turComponentList = new List<WindTurbineComponent>();
            List<MeasLoc_SVM> measLocSVMList = new List<MeasLoc_SVM>();
            if (_DevWindTurbine.DevTurComponents != null && _DevWindTurbine.DevTurComponents.Count > 0)
            {
                foreach (DevTurComponent comp in _DevWindTurbine.DevTurComponents)
                {
                    WindTurbineComponent turComponent = ConvertWindTurbineComponent(comp);
                    turComponentList.Add(turComponent);
                    if (comp.SVMMeasLocations != null && comp.SVMMeasLocations.Count > 0)
                    {
                        foreach (SVMMeasLocation SVMLoc in comp.SVMMeasLocations)
                        {
                            MeasLoc_SVM measLocSVM = ConvertMeasLoc_SVM(SVMLoc);
                            measLocSVMList.Add(measLocSVM);
                        }
                    }
                }
            }
            turBine.TurComponentList = turComponentList;
            turBine.MeasLocSVMList = measLocSVMList;

            turBine.WindParkID = (_DevWindTurbine.WindParkID).ToString();
            turBine.WindTurbineCode = _DevWindTurbine.WindTurbineCode;
            turBine.WindTurbineID = _DevWindTurbine.WindTurbineID.ToString();
            turBine.WindTurbineModel = _DevWindTurbine.WindTurbineModel;
            turBine.WindTurbineName = _DevWindTurbine.WindTurbineName;

            if (!string.IsNullOrEmpty(_DevWindTurbine.WindTurbineName))
            {
                turBine.WTurbineModel = ConvertEntityDAToBusinessWTM.ConvertWindTurbineModel(WTMSvc.GetWTModelByID(_DevWindTurbine.WindTurbineModel));
                turBine.FactedPower = turBine.WTurbineModel.RatedPower;
            }
            return turBine;
        }

        public static List<WindTurbine> ConvertWindTurbineList(ICollection<DevWindTurbine> _DevWindTurbineList)
        {
            List<WindTurbine> turbineList = new List<WindTurbine>();
            foreach (DevWindTurbine item in _DevWindTurbineList)
            {
                WindTurbine tur = ConvertWindTurbine(item);
                turbineList.Add(tur);
            }
            return turbineList;
        }

        public static WindTurbineComponent ConvertWindTurbineComponent(DevTurComponent _DevTurComponent)
        {
            WindTurbineComponent turComponent = new WindTurbineComponent();
            turComponent.CompManufacturer = _DevTurComponent.Manufacturer;
            turComponent.ComponentID = _DevTurComponent.ComponentID.ToString();
            turComponent.ComponentModel = _DevTurComponent.ComponentModel;
            turComponent.ComponentName = _DevTurComponent.ComponentType;
            turComponent.CompRTAlarmStatus = null;
            turComponent.OrderSeq = int.Parse(_DevTurComponent.OrderSeq.ToString());

            if (_DevTurComponent.DevMeasLocVibrations != null && _DevTurComponent.DevMeasLocVibrations.Count != 0)
            {
                turComponent.VibMeasLocList = ConvertMeasLoc_VibList(_DevTurComponent.DevMeasLocVibrations.ToList());
            }
            else
            {
                turComponent.VibMeasLocList = ConvertMeasLoc_VibList(devSvc.GetDevMeasLocVibByCompID(_DevTurComponent.WindTurbineID, _DevTurComponent.ComponentID));
            }

            turComponent.WindTurbineID = _DevTurComponent.WindTurbineID.ToString();

            return turComponent;
        }

        public static List<WindTurbineComponent> ConvertWindTurbineComponentList(List<DevTurComponent> _DevTurComponentList)
        {
            List<WindTurbineComponent> turComponentList = new List<WindTurbineComponent>();
            foreach (DevTurComponent item in _DevTurComponentList)
            {
                WindTurbineComponent turComponent = ConvertWindTurbineComponent(item);
                turComponentList.Add(turComponent);
            }
            return turComponentList;
        }

        public static MeasLoc_Vib ConvertMeasLoc_Vib(DevMeasLocVibration _DevMeasLocVibration)
        {
            MeasLoc_Vib measLocVib = new MeasLoc_Vib();
            if (_DevMeasLocVibration == null)
                return measLocVib;
            measLocVib.AlarmDefinitionList = null;
            measLocVib.ComponentID = _DevMeasLocVibration.ComponentID.ToString();
            measLocVib.ComponentName = devSvc.GetDevTurComponentByCompID(_DevMeasLocVibration.ComponentID).ComponentType;
            measLocVib.DAUName = "";
            measLocVib.EVFreqBandList = null;
            measLocVib.EVFreqItemList = null;
            measLocVib.EVHarmonicClusterList = null;
            measLocVib.EVNarrorBandList = null;
            measLocVib.EVSideFreqClusterrList = null;
            measLocVib.GearRatio = float.Parse(_DevMeasLocVibration.GearRatio.ToString());
            measLocVib.LocRTAlarmStatus = null;
            measLocVib.MeasLocationID = _DevMeasLocVibration.MeasLocationID.ToString();
            measLocVib.MeasLocName = _DevMeasLocVibration.MeasLocationName;
            measLocVib.OrderSeq = int.Parse(_DevMeasLocVibration.OrderSeq.ToString());
            measLocVib.Orientation = _DevMeasLocVibration.Orientation;
            measLocVib.SectionName = _DevMeasLocVibration.SectionName;
            measLocVib.WindTurbineID = _DevMeasLocVibration.WindTurbineID.ToString();
           DAUVibChannel ch= _DevMeasLocVibration.DAUVibChannels.ToList().Find(item => item.MeasLocationID == _DevMeasLocVibration.MeasLocationID);
           if (ch != null)
           {
               measLocVib.ChannelNumber = ch.ChannelNumber.ToString();
           }
            measLocVib.Unit = "";

            //List<MeasLoc_RotSpd> rotMeasLocList =
            //    Presenter.GetRotSpdMeasLocListByTurId(_turbineID);

            List<DevMeasLocRotSpd> rotMeasLocList = devSvc.GetDevMeasLocRotSpdByTurID(measLocVib.WindTurbineID);

            if (rotMeasLocList.Count != 0)
            {
                measLocVib.RotSpdMeasLocID = rotMeasLocList[0].MeasLocationID.ToString();
            }

            return measLocVib;
        }

        public static List<MeasLoc_Vib> ConvertMeasLoc_VibList(List<DevMeasLocVibration> _DevMeasLocVibrationList)
        {
            List<MeasLoc_Vib> measLocVibList = new List<MeasLoc_Vib>();
            foreach (DevMeasLocVibration item in _DevMeasLocVibrationList)
            {
                MeasLoc_Vib measLocVib = ConvertMeasLoc_Vib(item);
                measLocVibList.Add(measLocVib);
            }
            return measLocVibList;
        }

        public static MeasLoc_Process ConvertMeasLoc_Process(DevMeasLocProcess _DevMeasLocProcess)
        {
            if (_DevMeasLocProcess == null) return null;
            MeasLoc_Process measlocPro = new MeasLoc_Process();
            MCSRegister reg = MCSvc.GetMCSRegisterByMeasLocId(_DevMeasLocProcess.WindTurbineID, _DevMeasLocProcess.MeasLocationID);
            if (reg != null)
            {
                measlocPro.ChannelNumber = reg.ChannelNumber.ToString();
            }
            measlocPro.DAUName = "";
            measlocPro.Eu_type_code = "";
            measlocPro.FieldBusType = _DevMeasLocProcess.FieldBusType == 1 ? WTCMSLive.BusinessEntity.EnumWorkConDataSource.WindDAU : WTCMSLive.BusinessEntity.EnumWorkConDataSource.ModbusOnTcp;
            measlocPro.MeasLocationID = _DevMeasLocProcess.MeasLocationID.ToString();
            measlocPro.MeasLocName = _DevMeasLocProcess.MeasLocationName;
            measlocPro.OrderSeq = int.Parse(_DevMeasLocProcess.OrderSeq.ToString());
            measlocPro.Param_Type_Code = _DevMeasLocProcess.ParamTypeCode.ToString();
            measlocPro.Param_Type_Name = "";
            if (measlocPro.FieldBusType == BusinessEntity.EnumWorkConDataSource.ModbusOnTcp)
            {
                MCSRegister mcs = MCSvc.GetMCSRegisterByMeasLocId(_DevMeasLocProcess.WindTurbineID, _DevMeasLocProcess.MeasLocationID);
                if (mcs != null)
                {
                    measlocPro.ParmaChannelNumber = mcs.ChannelNumber.ToString();
                }
            }
            else
            {
                DAUProcessChannel channel = dauSvc.GetDAUChannelProcess(_DevMeasLocProcess.WindTurbineID, _DevMeasLocProcess.MeasLocationID);
                if (channel != null)
                {
                    measlocPro.ParmaChannelNumber = channel.ChannelNumber.ToString();
                }
            }
            measlocPro.ServerAddress = "";
            measlocPro.WindTurbineID = _DevMeasLocProcess.WindTurbineID.ToString();
            if (_DevMeasLocProcess.MDFWorkConditions.Count > 0)
            {
                measlocPro.Unit = "true";//标志测量定义下，是否有波形定义
            }
            else
            {
                measlocPro.Unit = string.Empty;
            }
            return measlocPro;
        }

        public static WaveDef_Process ConvertWaveMeasLoc_Process(MDFWorkCondition myWork)
        {
            WaveDef_Process myProcess = new WaveDef_Process();
            myProcess.WindTurbineID = myWork.WindTurbineID.ToString();
            myProcess.MeasDefinitionID = myWork.MeasDefinitionID.ToString();
            myProcess.MeasLocationID = myWork.MeasLocationID.ToString();
            myProcess.UpperLimitFreqency = myWork.UpperLimitFreqency == null ? 0 : float.Parse(myWork.UpperLimitFreqency.ToString());
            myProcess.SampleLength = myWork.SampleLength==null ? 0 : short.Parse(myWork.SampleLength.ToString());
            return myProcess;
        }

        public static List<MeasLoc_Process> ConvertMeasLoc_ProcessList(List<DevMeasLocProcess> _DevMeasLocProcessList)
        {
            List<MeasLoc_Process> measlocProList = new List<MeasLoc_Process>();
            foreach (DevMeasLocProcess item in _DevMeasLocProcessList)
            {
                MeasLoc_Process measlocPro = ConvertMeasLoc_Process(item);
                measlocProList.Add(measlocPro);
            }
            return measlocProList;
        }

        public static MeasLoc_RotSpd ConvertMeasLoc_RotSpd(DevMeasLocRotSpd _DevMeasLocRotSpd)
        {
            MeasLoc_RotSpd measLocRotSpd = new MeasLoc_RotSpd();

            string channelNum = string.Empty;
            string dauName = string.Empty;
            if (_DevMeasLocRotSpd.DAURotSpdChannels != null && _DevMeasLocRotSpd.DAURotSpdChannels.Count > 0)
            {
                foreach (DAURotSpdChannel item in _DevMeasLocRotSpd.DAURotSpdChannels)
                {
                    channelNum = item.ChannelNumber.ToString();
                    dauName = item.DAUnit.DAUnitName;
                    continue;
                }

            }
            else
            {
                DAURotSpdChannel rsChannel = dauSvc.GetDAUChannelRotSpd(_DevMeasLocRotSpd.WindTurbineID);
                if (rsChannel != null)
                {
                    channelNum = rsChannel.ChannelNumber.ToString();
                    dauName = rsChannel.DAUnit.DAUnitName;
                }
                measLocRotSpd.Unit = string.Empty;
            }
            if (_DevMeasLocRotSpd.MDFWaveDefRotSpds != null && _DevMeasLocRotSpd.MDFWaveDefRotSpds.Count > 0)
            {
                measLocRotSpd.Unit = "true";//标志测量定义下，是否有波形定义
            }
            else
            {
                measLocRotSpd.Unit = string.Empty;
            }
            measLocRotSpd.ChannelNumber = channelNum;
            measLocRotSpd.DAUName = dauName;
            measLocRotSpd.GearRatio = float.Parse(_DevMeasLocRotSpd.GearRatio.ToString());
            measLocRotSpd.MeasLocationID = _DevMeasLocRotSpd.MeasLocationID.ToString();
            measLocRotSpd.MeasLocName = _DevMeasLocRotSpd.MeasLocationName;
            measLocRotSpd.OrderSeq = 0;
            measLocRotSpd.WindTurbineID = _DevMeasLocRotSpd.WindTurbineID.ToString();
            measLocRotSpd.LineCounts = Convert.ToInt32(_DevMeasLocRotSpd.CoderLineCounts);

            return measLocRotSpd;
        }

        public static List<MeasLoc_RotSpd> ConvertMeasLoc_RotSpdList(List<DevMeasLocRotSpd> _DevMeasLocRotSpdList)
        {
            List<MeasLoc_RotSpd> measlocSpdList = new List<MeasLoc_RotSpd>();

            foreach (DevMeasLocRotSpd item in _DevMeasLocRotSpdList)
            {
                MeasLoc_RotSpd measlocSpd = ConvertMeasLoc_RotSpd(item);
                measlocSpdList.Add(measlocSpd);
            }

            return measlocSpdList;
        }

        public static MeasLoc_SVM ConvertMeasLoc_SVM(SVMMeasLocation _SVMMeasLocation)
        {
            MeasLoc_SVM measLocSVM = new MeasLoc_SVM();
            measLocSVM.MeasLocationID = _SVMMeasLocation.MeasLocationID.ToString();
            measLocSVM.MeasurementLocationName = _SVMMeasLocation.MeasurementLocationName;
            measLocSVM.OrderSeq = int.Parse(_SVMMeasLocation.OrderSeq.ToString());
            measLocSVM.ParamType = EnumSVMParamType.Axisl;
            measLocSVM.WindTurbineID = _SVMMeasLocation.WindTurbineID.ToString();
            return measLocSVM;
        }

        public static List<MeasLoc_SVM> ConvertMeasLoc_SVMList(List<SVMMeasLocation> _SVMMeasLocationList)
        {
            List<MeasLoc_SVM> measlocSVMList = new List<MeasLoc_SVM>();
            foreach (SVMMeasLocation item in _SVMMeasLocationList)
            {
                MeasLoc_SVM measlocSVM = ConvertMeasLoc_SVM(item);
                measlocSVMList.Add(measlocSVM);
            }
            return measlocSVMList;
        }

        public static MCS ConvertMCS(MCSystem _MCSystem)
        {
            MCS _MCS = null;

            if (_MCSystem != null)
            {
                _MCS = new MCS();
                _MCS.FieldBusType = _MCSystem.FieldBusType;
                _MCS.MCSID = _MCSystem.WindTurbineID.ToString();
                if (_MCSystem.MCSRegisters != null)
                {
                    _MCS.MCSChannelStateList = ConvertMCSChannelStateParamList(_MCSystem.MCSRegisters.ToList());
                    _MCS.MCSChannelValueList = ConvertMCSChannelValueParamList(_MCSystem.MCSRegisters.ToList());
                }
                _MCS.MCSIP = _MCSystem.MCSIP;
                _MCS.MCSPort = (int)_MCSystem.MCSPort;
                _MCS.WindTurbineID = _MCSystem.WindTurbineID.ToString();
            }
            return _MCS;
        }



        public static MCSChannelStateParam ConvertMCSChannelStateParam(MCSRegister _MCSRegister)
        {
            if (_MCSRegister == null) return null;

            MCSChannelStateParam mcChannel = new MCSChannelStateParam();
            mcChannel.ByteArrayType = (WTCMSLive.BusinessEntity.EnumMCSChannelByteArrayType)_MCSRegister.ByteArrayType;
            mcChannel.ChannelNumber = _MCSRegister.ChannelNumber.ToString();
            mcChannel.DataType = (WTCMSLive.BusinessEntity.EnumMCSChannelDataType)_MCSRegister.DataType;
            if (_MCSRegister.MCSRegisterState != null)
            {
                mcChannel.JudgeType = (WTCMSLive.BusinessEntity.EnumMCSChannelJudgeType)_MCSRegister.MCSRegisterState.JudgeType;
            }
            mcChannel.MCSID = _MCSRegister.WindTurbineID.ToString();

            mcChannel.MeasLocProcessID = _MCSRegister.MeasLocationID.ToString();
            var ProcessMeasLoc = devSvc.GetDevMeasLocProcessByMeasLocID(_MCSRegister.MeasLocationID);
            mcChannel.MeasLocProcessName = ProcessMeasLoc == null ? "" : ProcessMeasLoc.MeasLocationName;
            mcChannel.ParamMeaning = _MCSRegister.ParamMeaning;
            mcChannel.RegisterStorageType = (WTCMSLive.BusinessEntity.EnumMCSChannelStorageType)_MCSRegister.RegisterStorageType;
            mcChannel.RegisterType = (WTCMSLive.BusinessEntity.EnumMCSChannelType)_MCSRegister.RegisterType;
            if (_MCSRegister.MCSRegisterState != null)
            {
                List<short> stateValueList = new List<short>();
                foreach (string item in _MCSRegister.MCSRegisterState.StateValues.Split(','))
                {
                    stateValueList.Add(short.Parse(item));
                }
                mcChannel.StateValues = stateValueList;
            }
            return mcChannel;
        }

        public static List<MCSChannelStateParam> ConvertMCSChannelStateParamList(List<MCSRegister> _MCSRegisterList)
        {
            List<MCSChannelStateParam> MCSChannelStateParamList = new List<MCSChannelStateParam>();
            foreach (MCSRegister item in _MCSRegisterList)
            {
                if (item.DataType == (short)WTCMSLive.BusinessEntity.EnumMCSChannelDataType.Status)
                {
                    MCSChannelStateParam evFB = ConvertMCSChannelStateParam(item);
                    MCSChannelStateParamList.Add(evFB);
                }
            }
            return MCSChannelStateParamList;
        }

        public static MCSChannelValueParam ConvertMCSChannelValueParam(MCSRegister _MCSRegister)
        {
            if (_MCSRegister == null) return null;

            MCSChannelValueParam mcChannel = new MCSChannelValueParam();
            mcChannel.ByteArrayType = (WTCMSLive.BusinessEntity.EnumMCSChannelByteArrayType)_MCSRegister.ByteArrayType;
            mcChannel.ChannelNumber = _MCSRegister.ChannelNumber.ToString();
            mcChannel.DataType = (WTCMSLive.BusinessEntity.EnumMCSChannelDataType)_MCSRegister.DataType;
            if (_MCSRegister.MCSRegisterValue != null)
            {
                mcChannel.Coeff = (float)_MCSRegister.MCSRegisterValue.ConvertCoefficient;
            }
            mcChannel.MCSID = _MCSRegister.WindTurbineID.ToString();
            mcChannel.MeasLocProcessID = _MCSRegister.MeasLocationID.ToString();
            var processMeasLoc = devSvc.GetDevMeasLocProcessByMeasLocID(_MCSRegister.MeasLocationID);
            mcChannel.MeasLocProcessName = processMeasLoc == null ? "" : processMeasLoc.MeasLocationName;
            mcChannel.ParamMeaning = _MCSRegister.ParamMeaning;
            mcChannel.RegisterStorageType = (WTCMSLive.BusinessEntity.EnumMCSChannelStorageType)_MCSRegister.RegisterStorageType;
            mcChannel.RegisterType = (WTCMSLive.BusinessEntity.EnumMCSChannelType)_MCSRegister.RegisterType;
            return mcChannel;
        }

        public static List<MCSChannelValueParam> ConvertMCSChannelValueParamList(List<MCSRegister> _MCSRegisterList)
        {
            List<MCSChannelValueParam> MCSChannelValueParamList = new List<MCSChannelValueParam>();
            foreach (MCSRegister item in _MCSRegisterList)
            {
                if (item.DataType == (short)WTCMSLive.BusinessEntity.EnumMCSChannelDataType.Data)
                {
                    MCSChannelValueParam evFB = ConvertMCSChannelValueParam(item);
                    MCSChannelValueParamList.Add(evFB);
                }
            }
            return MCSChannelValueParamList;
        }

        //public static DeviceRTAlarmStatus ConvertDeviceRTAlarmStatus_Turbine(AlarmStatusRTTurbine _AlarmStatusRTTurbine)
        //{
        //    DeviceRTAlarmStatus devRTAlarmStatus = null;

        //    if (_AlarmStatusRTTurbine != null)
        //    {
        //        devRTAlarmStatus = new DeviceRTAlarmStatus();
        //        devRTAlarmStatus.AlarmDegree = int.Parse(_AlarmStatusRTTurbine.AlarmDegree.ToString());
        //        devRTAlarmStatus.AlarmType = GetAlarmType((short)_AlarmStatusRTTurbine.AlarmDegree);
        //        devRTAlarmStatus.AlarmUpdateTime = _AlarmStatusRTTurbine.AlarmUpdateTime;
        //        devRTAlarmStatus.DevSegmentID = _AlarmStatusRTTurbine.WindTurbineID.ToString();
        //        devRTAlarmStatus.DevSegmentName = _AlarmStatusRTTurbine.devwindturbine.WindTurbineName;
        //        devRTAlarmStatus.ParentSegmentID = _AlarmStatusRTTurbine.devwindturbine.WindParkID.ToString();
        //    }

        //    return devRTAlarmStatus;
        //}

        //public static DeviceRTAlarmStatus ConvertDeviceRTAlarmStatus_Component(AlarmStatusRTComponent _AlarmStatusRTComponent)
        //{
        //    DeviceRTAlarmStatus devRTAlarmStatus = null;

        //    if (_AlarmStatusRTComponent != null)
        //    {
        //        devRTAlarmStatus = new DeviceRTAlarmStatus();
        //        devRTAlarmStatus.AlarmDegree = int.Parse(_AlarmStatusRTComponent.AlarmDegree.ToString());
        //        devRTAlarmStatus.AlarmType = GetAlarmType((short)_AlarmStatusRTComponent.AlarmDegree);
        //        devRTAlarmStatus.AlarmUpdateTime = _AlarmStatusRTComponent.AlarmUpdateTime;
        //        devRTAlarmStatus.DevSegmentID = _AlarmStatusRTComponent.ComponentID.ToString();
        //        devRTAlarmStatus.DevSegmentName = _AlarmStatusRTComponent.devturcomponent.ComponentType;
        //        devRTAlarmStatus.ParentSegmentID = _AlarmStatusRTComponent.WindTurbineID.ToString();
        //    }

        //    return devRTAlarmStatus;
        //}

        //public static DeviceRTAlarmStatus ConvertDeviceRTAlarmStatus_MeasLocation(AlarmStatusRTMeasLoc _AlarmStatusRTMeasLoc)
        //{
        //    DeviceRTAlarmStatus devRTAlarmStatus = null;

        //    if (_AlarmStatusRTMeasLoc != null)
        //    {
        //        devRTAlarmStatus = new DeviceRTAlarmStatus();
        //        devRTAlarmStatus.AlarmDegree = int.Parse(_AlarmStatusRTMeasLoc.AlarmDegree.ToString());
        //        devRTAlarmStatus.AlarmType = GetAlarmType((short)_AlarmStatusRTMeasLoc.AlarmDegree);
        //        devRTAlarmStatus.AlarmUpdateTime = _AlarmStatusRTMeasLoc.AlarmUpdateTime;
        //        devRTAlarmStatus.DevSegmentID = _AlarmStatusRTMeasLoc.MeasLocationID.ToString();
        //        devRTAlarmStatus.DevSegmentName = _AlarmStatusRTMeasLoc.devmeaslocvibration.MeasLocationName;
        //        devRTAlarmStatus.ParentSegmentID = _AlarmStatusRTMeasLoc.ComponentID.ToString();
        //    }

        //    return devRTAlarmStatus;
        //}

        //public static List<DeviceRTAlarmStatus> ConvertDeviceRT_MeasLocList(List<AlarmStatusRTMeasLoc> _entityList)
        //{
        //    List<DeviceRTAlarmStatus> _devRTAlarmStatus = new List<DeviceRTAlarmStatus>();
        //    foreach (AlarmStatusRTMeasLoc item in _entityList)
        //    {
        //        _devRTAlarmStatus.Add(ConvertDeviceRTAlarmStatus_MeasLocation(item));
        //    }
        //    return _devRTAlarmStatus;
        //}

        //private static AlarmType GetAlarmType(short _alarmDegree)
        //{
        //    AlarmType type = AlarmType.AlarmType_Unknown;

        //    if (_alarmDegree == (short)AlarmType.AlarmType_Alarm.AlarmDegree)
        //    {
        //        type = AlarmType.AlarmType_Alarm;
        //    }
        //    else if (_alarmDegree == (short)AlarmType.AlarmType_CommunicationError.AlarmDegree)
        //    {
        //        type = AlarmType.AlarmType_CommunicationError;
        //    }
        //    else if (_alarmDegree == (short)AlarmType.AlarmType_Normal.AlarmDegree)
        //    {
        //        type = AlarmType.AlarmType_Normal;
        //    }
        //    else if (_alarmDegree == (short)AlarmType.AlarmType_Warning.AlarmDegree)
        //    {
        //        type = AlarmType.AlarmType_Warning;
        //    }

        //    return type;

        //}


        public static List<WindTurbine> ConvertOnlyWindTurbineInfoList(List<DevWindTurbine> list)
        {
            List<WindTurbine> turList = new List<WindTurbine>();
            foreach (var item in list)
            {
                WindTurbine tur = new WindTurbine();
                tur.WindParkID = item.WindParkID.ToString();
                tur.WindTurbineID = item.WindTurbineID.ToString();
                tur.WindTurbineCode = item.WindTurbineCode;
                tur.WindTurbineName = item.WindTurbineName;
                tur.WindTurbineModel = item.WindTurbineModel;
                tur.OperationalDate = (DateTime)item.OperationalDate;
                //tur.IsAcquisitionPower = item.IsAcquisitionPower == "1" ? true : false;
                tur.MinWorkingRotSpeed = (int)item.MinWorkingRotSpeed;
                turList.Add(tur);
            }
            return turList;
        }

        #region 报警设置
        /// <summary>
        /// 获取报警定义表数据
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <param name="_MeasLocId"></param>
        /// <param name="_EigenValueId"></param>
        /// <param name="_WorkCon"></param>
        /// <param name="MeasLocType"></param>
        /// <returns></returns>
        public static List<AlarmDefinition> ConvertMDFAlarmDefToBusiness(List<WTCMSLive.Entity.Models.MDFAlarmDef> myEFList)
        {
            List<AlarmDefinition> myAlarmList = new List<AlarmDefinition>();
            foreach (MDFAlarmDef myDef in myEFList)
            {
                AlarmDefinition myAlarmDefinition = new AlarmDefinition();
                myAlarmDefinition.WindTurbineID = myDef.WindTurbineID.ToString();
                myAlarmDefinition.MeasLocationID = myDef.MeasLocationID.ToString();
                //myAlarmDefinition.MeasDefinitionName =
                myAlarmDefinition.EigenValueID = myDef.EigenValueID;
                myAlarmDefinition.EigenValueName = myDef.EigenValueID.IndexOf("&&") > -1 ? myDef.EigenValueID.Split(new string[] { "&&" }, StringSplitOptions.None)[1] : "";
                //用波形定义存储阈值组ID，懒得加字段
                myAlarmDefinition.MeasDefinitionID = myDef.ThresholdGroup.ToString();
                //工况参数
                myAlarmDefinition.OutPowerBandCode = myDef.WorkConParameter;
                myAlarmDefinition.WorkConditionParamTypeCode = myDef.WorkConParameter.ToString();
                myAlarmDefinition.MaxOutPower = double.Parse(myDef.UpperLimitValue.ToString());
                myAlarmDefinition.MinOutPower = double.Parse(myDef.LowerLimitValue.ToString());
                myAlarmList.Add(myAlarmDefinition);
            }
            return myAlarmList;
        }
        /// <summary>
        /// 获取带阈值的报警定义数据
        /// </summary>
        /// <param name="myEFList"></param>
        /// <param name="myThresholdList"></param>
        /// <returns></returns>
        public static List<AlarmDefinition> ConvertMDFAlarmDefToBusiness(List<WTCMSLive.Entity.Models.MDFAlarmDef> myEFList, List<MDFAlarmDefThresholdGroup> myThresholdList)
        {
            List<AlarmDefinition> myAlarmList = new List<AlarmDefinition>();
            foreach (MDFAlarmDef myDef in myEFList)
            {
                List<MDFAlarmDefThresholdGroup> ThresholdList = myThresholdList.FindAll(item => item.ThresholdGroup == myDef.ThresholdGroup);
                if (ThresholdList == null || ThresholdList.Count < 1)
                {
                    continue;
                }
                foreach (MDFAlarmDefThresholdGroup Threshold in ThresholdList)
                {
                    AlarmDefinition myAlarmDefinition = new AlarmDefinition();
                    myAlarmDefinition.WindTurbineID = myDef.WindTurbineID.ToString();
                    myAlarmDefinition.MeasLocationID = myDef.MeasLocationID.ToString();
                    //myAlarmDefinition.MeasDefinitionName =
                    myAlarmDefinition.EigenValueID = myDef.EigenValueID;
                    myAlarmDefinition.EigenValueName = myDef.EigenValueID.IndexOf("&&") > -1 ? myDef.EigenValueID.Split(new string[] { "&&" }, StringSplitOptions.None)[1] : "";
                    //用波形定义存储阈值组ID，懒得加字段
                    myAlarmDefinition.MeasDefinitionID = myDef.ThresholdGroup.ToString();
                    //工况参数
                    myAlarmDefinition.OutPowerBandCode = myDef.WorkConParameter;
                    myAlarmDefinition.WorkConditionParamTypeCode = myDef.WorkConParameter.ToString();
                    myAlarmDefinition.MaxOutPower = double.Parse(myDef.UpperLimitValue.ToString());
                    myAlarmDefinition.MinOutPower = double.Parse(myDef.LowerLimitValue.ToString());
                    myAlarmDefinition.AlarmDegree = Threshold.AlarmDegree.ToString();
                    myAlarmDefinition.AlarmValue = Convert.ToDouble(Threshold.ThresholdValue);
                    myAlarmList.Add(myAlarmDefinition);

                }
            }
            return myAlarmList;
        }


        public static List<AlarmDefinition> ConvertMDFAlarmDefToBusinessData(List<WTCMSLive.Entity.Models.MDFAlarmDef> myEFList)
        {
            List<AlarmDefinition> myAlarmList = new List<AlarmDefinition>();
            foreach (MDFAlarmDef myDef in myEFList)
            {
                List<MDFAlarmDefThresholdGroup> myThresholdList = mdfSvc.GetMDFAlarmDefThresholdGroupByDegreeList(myDef.WindTurbineID, myDef.ThresholdGroup);
                foreach (MDFAlarmDefThresholdGroup mygroup in myThresholdList)
                {
                    AlarmDefinition myAlarmDefinition = new AlarmDefinition();
                    myAlarmDefinition.WindTurbineID = myDef.WindTurbineID.ToString();
                    myAlarmDefinition.MeasLocationID = myDef.MeasLocationID.ToString();
                    //myAlarmDefinition.MeasDefinitionName =
                    myAlarmDefinition.EigenValueID = myDef.EigenValueID;
                    myAlarmDefinition.EigenValueName = myDef.EigenValueID.IndexOf("&&") > -1 ? myDef.EigenValueID.Split(new string[] { "&&" }, StringSplitOptions.None)[1] : "";
                    //用波形定义存储阈值组ID，懒得加字段
                    myAlarmDefinition.MeasDefinitionID = myDef.ThresholdGroup.ToString();
                    //工况参数
                    myAlarmDefinition.OutPowerBandCode = myDef.WorkConParameter;
                    myAlarmDefinition.WorkConditionParamTypeCode = myDef.WorkConParameter.ToString();
                    myAlarmDefinition.MaxOutPower = double.Parse(myDef.UpperLimitValue.ToString());
                    myAlarmDefinition.MinOutPower = double.Parse(myDef.LowerLimitValue.ToString());
                    myAlarmDefinition.AlarmDegree = mygroup.AlarmDegree.ToString();
                    myAlarmDefinition.AlarmValue = double.Parse(mygroup.ThresholdValue.ToString());
                    myAlarmList.Add(myAlarmDefinition);
                }
            }
            return myAlarmList;
        }

        public static List<AlarmDefinition> ConvertMDFAlarmDefThresholdGroupToBusiness(List<MDFAlarmDefThresholdGroup> myThresholdList)
        {
            if (myThresholdList == null)
                return null;
            List<AlarmDefinition> myAlarmList = new List<AlarmDefinition>();
            foreach (MDFAlarmDefThresholdGroup myThreshold in myThresholdList)
            {
                AlarmDefinition myAlarmDefinition = new AlarmDefinition();
                myAlarmDefinition.WindTurbineID = myThreshold.WindTurbineID.ToString();
                myAlarmDefinition.MeasDefinitionID = myThreshold.ThresholdGroup.ToString();
                myAlarmDefinition.AlarmDegree = myThreshold.AlarmDegree.ToString();
                myAlarmDefinition.AlarmValue = double.Parse(myThreshold.ThresholdValue.ToString());
                myAlarmList.Add(myAlarmDefinition);
            }
            return myAlarmList;
        }
        /// <summary>
        /// 把EF报警定义实体转为业务实体
        /// </summary>
        /// <param name="myAlarmDef"></param>
        /// <returns></returns>
        public static AlarmDefinition ConvertMDFAlarmDefToBusiness(MDFAlarmDef myAlarmDef)
        {
            if (myAlarmDef == null)
                return null;
            AlarmDefinition myAlarmDefinition = new AlarmDefinition();
            myAlarmDefinition.WindTurbineID = myAlarmDef.WindTurbineID.ToString();
            myAlarmDefinition.MeasLocationID = myAlarmDef.MeasLocationID.ToString();
            //myAlarmDefinition.MeasDefinitionName =
            myAlarmDefinition.EigenValueID = myAlarmDef.EigenValueID;
            myAlarmDefinition.EigenValueName = myAlarmDef.EigenValueID.IndexOf("&&") > -1 ? myAlarmDef.EigenValueID.Split(new string[] { "&&" }, StringSplitOptions.None)[1] : "";
            //用测量定义存储阈值组ID，懒得加字段
            myAlarmDefinition.MeasDefinitionID = myAlarmDef.ThresholdGroup.ToString();
            //工况参数
            myAlarmDefinition.OutPowerBandCode = myAlarmDef.WorkConParameter;
            myAlarmDefinition.MaxOutPower = double.Parse(myAlarmDef.UpperLimitValue.ToString());
            myAlarmDefinition.MinOutPower = double.Parse(myAlarmDef.LowerLimitValue.ToString());
            return myAlarmDefinition;
        }


        #endregion
    }
}
