﻿using CMSFramework.BusinessEntity;

namespace WTCMSLive.WebSite.Models
{
    public class WindPark_Web:WindPark
    {
        public string WindParkID { get; set; }
        //-------------
        /// <summary>
        ///诊断等级：警告
        /// </summary>
        public string diagnosis_Waring
        {get;set;}
        
        /// <summary>
        /// 诊断等级：危险
        /// </summary>
        public string diagnosis_Danger
        { get; set; }
        
        /// <summary>
        /// 定期诊断数
        /// </summary>
        public string diagnosis_count
        { get; set; }

        /// <summary>
        /// 已诊断
        /// </summary>
        public string finishedDiag
        { get; set; }

        /// <summary>
        /// 申请诊断数
        /// </summary>
        public string diagnosis_app
        { get; set; }

        /// <summary>
        /// 采集单元通信异常
        /// </summary>
        public string DAU_Fault
        { get; set; }

        /// <summary>
        /// 传感器故障
        /// </summary>
        public string Sensor_Fault
        { get; set; }

        /// <summary>
        /// 无数据
        /// </summary>
        public string NoData
        { get; set; }

        public int WTTotalNumber
        {
            get;
            set;
        }
    }
}