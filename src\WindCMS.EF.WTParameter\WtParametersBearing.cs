﻿using System;
using System.Collections.Generic;

namespace WindCMS.EF.WTParameter
{
    /// <summary>
    /// 存储齿轮箱轴承的详细参数
    /// </summary>
    public partial class WtParametersBearing
    {
        /// <summary>
        /// 轴承参数的唯一标识符
        /// </summary>
        public int BearingId { get; set; }
        /// <summary>
        /// 齿轮箱级别参数的外键
        /// </summary>
        public string? StageId { get; set; }
        /// <summary>
        /// 轴承制造商
        /// </summary>
        public string? BearingManufacturer { get; set; }
        /// <summary>
        /// 轴承型号
        /// </summary>
        public string? BearingModel { get; set; }
        /// <summary>
        /// 轴承类型
        /// </summary>
        public string? BearingType { get; set; }
        /// <summary>
        /// 轴承节圆直径（单位：mm）
        /// </summary>
        public decimal? BearingPitchDiameter { get; set; }
        /// <summary>
        /// 轴承滚动体直径（单位：mm）
        /// </summary>
        public decimal? BearingRollDiameter { get; set; }
        /// <summary>
        /// 轴承滚动体个数
        /// </summary>
        public int? BearingRollCount { get; set; }
        /// <summary>
        /// 轴承接触角（单位：度）
        /// </summary>
        public decimal? BearingContactAngle { get; set; }
        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime? CreatedAt { get; set; }
        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
        /// <summary>
        /// 部件类型
        /// </summary>
        public string? CompType { get; set; }
        /// <summary>
        /// 轴承种类
        /// </summary>
        public string? BearingName { get; set; }
    }
}
