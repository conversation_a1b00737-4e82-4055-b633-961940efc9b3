﻿using AppFramework.Utility;
using WTCMSLive.BusinessModel;
using CMSFramework.BusinessEntity;
using Newtonsoft.Json.Linq;
using CMSFramework.DevTreeEntities;

namespace WTCMSLive.WebSite.Models
{
    /// <summary>
    /// 特征值相关数据
    /// </summary>
    public class EigenValueManager
    {
        public Dictionary<string, MeasLoc_Vib> measDefDic = new Dictionary<string, MeasLoc_Vib>();

        public string[] ComplexEigenValue = new string[] { "NF", "ICEI" };

        /// <summary>
        /// 取得实时状态特征值
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        public string GetRTEigenValue(string turbineID)
        {
            List<EigenValueData_Vib> sortDataList = GetRTEigenValueList(turbineID);
            BaseTableModel tableModel = CreateRTEigenValueTable(sortDataList);
            return tableModel.ToJson();
        }
        /// <summary>
        /// 获取参与状态判断的特征值
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        public string GetAlarmRTEigenValue(string turbineID) {
            List<EigenValueData_Vib> sortDataList = GetRTEigenValueList(turbineID);
            BaseTableModel tableModel = GetTurbineAlarmEnvList(turbineID,sortDataList);
            return tableModel.ToJson();
        }
        /// <summary>
        /// 获取实时计算特征值:DAU采集
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        private List<EigenValueData_Vib> GetRTEigenValueV2List(string turbineID)
        {
            // 获取机组对象
            var park = DevTreeManagement.GetWindParkByTurID(turbineID);
            var turbine = DevTreeManagement.GetWindTurbine(turbineID);
            var measLocs = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
            //获取对象
            string key = $"{park.WindParkName}_{turbine.WindTurbineName}";
            List<EigenValueData_Vib> evList = new List<EigenValueData_Vib>();
            foreach (string keys in EvdataDic.evRTDic.Keys)
            {
                if (keys.Contains(key))
                {
                    // 数据转换
                    JToken data = EvdataDic.evRTDic[keys];
                    EigenValueData_Vib data_Vib = new EigenValueData_Vib()
                    {
                        AcquisitionTime = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1)).AddSeconds(data["timestamp"].Value<long>()),
                        AlarmDegree = EnumAlarmDegree.AlarmDeg_Normal,
                        EigenValueCode = data["tags"]["FeatureType"].Value<string>(),
                        EigenValueID = "",
                        Eigen_Value = data["fields"]["feature"].Value<double>(),
                        MeasLocationID = measLocs.FirstOrDefault(obj => obj.MeasLocName == data["tags"]["MeasurePos"].Value<string>()).MeasLocationID,
                        MeasDefinitionID = "",
                    };
                    evList.Add(data_Vib);
                }
            }
            return evList;
        }
        /// <summary>
        /// 筛选参与报警的特征值数据
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="rtdata"></param>
        /// <returns></returns>
        private BaseTableModel GetTurbineAlarmEnvList(string turbineID, List<EigenValueData_Vib> rtdata)
        {
            //1. 获取VDI特征值ID
            List<AlarmDefinition> alarmDefList = AlarmDefConfig.GetAlarmListByTurID(turbineID);
            //2. 获取已配置特征值ID
            //List<AlarmDefinition> alarmDefList = AlarmDefinitionManage.GetAlarmDefListByTurID(turbineID);
            //3. 查找对应的实时特征值
            List<EigenValueData_Vib> dataList = new List<EigenValueData_Vib>();
            //不存在报警定义？
            if (alarmDefList.Count == 0)
            {
                //返回所有RMS特征值
                rtdata.RemoveAll(item => (item.EigenValueCode.Contains("RMS") == false));
                dataList = rtdata;
            }
            else
            {
                foreach (var item in rtdata) {
                    var data = alarmDefList.FirstOrDefault(obj => obj.EigenValueID == item.EigenValueID);
                    if (data != null)
                    {
                        dataList.Add(item);
                    }
                }
            }
            return CreateRTEigenValueTable(dataList);
        }
        public List<EigenValueData_Vib> GetRTEigenValueList(string turbineID)
        {
            if (measDefDic.Count == 0)
            {
                List<MeasLoc_Vib> measDefList = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
                List<MeasLoc_VoltageCurrent> measDefListVol = DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(turbineID);
                
                for (int i = 0; i < measDefList.Count; i++)
                {
                    measDefDic.Add(measDefList[i].MeasLocationID, measDefList[i]);
                }
                for(int i = 0;i< measDefListVol.Count; i++)
                {
                    if (!measDefDic.ContainsKey(measDefListVol[i].MeasLocationID))
                    {
                        measDefDic.Add(measDefListVol[i].MeasLocationID, measDefListVol[i].MeasLocVoltageCurrentConvertToVib());
                    }
                }

                
            }
            Dictionary<string, List<EigenValueData_Vib>> eigenValueCategory = new Dictionary<string, List<EigenValueData_Vib>>();
            List<EigenValueData_Vib> dataList = RealTimeDataManage.GetRTEVDataListByTurID(turbineID);
            List<MeasDefinition> mdfList = MeasDefinitionManagement.GetMeasDefListByTurId(turbineID);

            //Modify by zhanggw 删除非最新采集的特征值数据
            //if (dataList.Count > 0)
            //{
            //    if (mdfList.Count == 1)
            //    {
            //        DateTime time = dataList.OrderByDescending(item => item.AcquisitionTime).FirstOrDefault().AcquisitionTime;
            //        dataList.RemoveAll(obj => obj.AcquisitionTime != time);
            //    }
            //    else
            //    {
            //        mdfList.ForEach(mdf =>
            //        {
            //            var list = dataList.FindAll(item => item.MeasDefinitionID == mdf.MeasDefinitionID).OrderByDescending(item => item.AcquisitionTime);
            //            if (list.Count() > 0)
            //            {
            //                DateTime time = list.FirstOrDefault().AcquisitionTime;
            //                dataList.RemoveAll(obj => obj.MeasDefinitionID == mdf.MeasDefinitionID && obj.AcquisitionTime != time);
            //            }
            //        });
            //    }
            //}
            //为测量定义排序做准备
            dataList.ForEach(item => item.EngUnitName = "1000");
            //测量位置排序
            List<MeasLoc_Vib> Viblist = DevTreeManagement.GetVibMeasLocationByTurId(turbineID).OrderBy(item => item.OrderSeq).ToList();
            //电流电压过程量测量位置
            List<MeasLoc_VoltageCurrent> measLoc_VoltageCurrents = DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(turbineID).OrderBy(item => item.OrderSeq).ToList();
            
            foreach (var vib in Viblist)
            {
                dataList.ForEach(item =>
                {
                    if (item.MeasLocationID == vib.MeasLocationID)
                    {
                        item.EngUnitName = vib.OrderSeq.ToString();
                    }
                });
            }
            dataList = dataList.OrderBy(item => item.EngUnitName).ToList();
            // 添加实时计算特征值
            dataList.AddRange(GetRTEigenValueV2List(turbineID));

            //以测量位置分类
            foreach (EigenValueData_Vib item in dataList)
            {
                // Modify by zhanggw 网站不显示固有频率相关数据
                //Modify by wangy 网站不显示ICEI特征值
                if (item.EigenValueCode.Contains("NF") || item.EigenValueCode.Contains("ICEI"))
                {
                    continue;
                }
                // 20180125
                if (eigenValueCategory.ContainsKey(item.MeasLocationID))
                {
                    eigenValueCategory[item.MeasLocationID].Add(item);
                }
                else
                {
                    eigenValueCategory.Add(item.MeasLocationID, new List<EigenValueData_Vib>());
                    eigenValueCategory[item.MeasLocationID].Add(item);
                }
            }
            List<EigenValueData_Vib> sortDataList = new List<EigenValueData_Vib>();
            //排序
            List<string> keys = eigenValueCategory.Keys.ToList();
            //循环的时候用排序好的测量位置循环，不然显示的数据部件对齐有问题
            //wangy 2016年4月27日 16:10:10
            foreach (var measloc in Viblist)
            {
                if (keys.Find(item => item == measloc.MeasLocationID) != null)
                {
                    sortDataList.AddRange(EigenValueManage.SortCMSEigenValue(eigenValueCategory[measloc.MeasLocationID]));
                }
            }

            // 电流电压过程量数据添加
            foreach (var meas in measLoc_VoltageCurrents)
            {
                if (keys.Find(item => item == meas.MeasLocationID) != null)
                {
                    // 标识为过程量
                    eigenValueCategory[meas.MeasLocationID].ForEach(t => t.EngUnitName = "-999");
                    sortDataList.AddRange(eigenValueCategory[meas.MeasLocationID]);
                }
            }
            List<MeasLoc_Modbus> modbusList = SVMManagement.GetMeasLoc_ModbusListByTurID(turbineID);
            foreach (var meas in modbusList)
            {
                if (keys.Find(item => item == meas.MeasLocationID) != null)
                {
                    // 标识为过程量
                    eigenValueCategory[meas.MeasLocationID].ForEach(t => t.EngUnitName = "-999");
                    sortDataList.AddRange(eigenValueCategory[meas.MeasLocationID]);
                }
            }

            return sortDataList;
        }

        /// <summary>
        /// 给出部件列表顺序
        /// </summary>
        /// <param name="p"></param>
        /// <returns></returns>
        public string SetComponentOrder(string component)
        {
            switch (component)
            {
                case "主轴承":
                    return "1";
                case "齿轮箱":
                    return "2";
                case "发电机":
                    return "3";
                case "机舱":
                    return "4";
                case "塔筒":
                    return "5";
                case "叶片":
                    return "6";
                default:
                    return "7";
            }
        }

        public BaseTableModel GetSVMRTEigenValueTable(string turbineID)
        {
            List<EigenValueData_SVM> dataList = RealTimeDataManage.GetSVMRTEVDataListByTurID(turbineID);
            BaseTableModel tableModel = new BaseTableModel();
            if (dataList.Count > 0)
            {
                //List<MeasEvent_EigenValue> measList = RealTimeDataManage.GetEVDataRTMeasEventByTurbineID(turbineID);
                //measList.ForEach(item =>
                //{
                //    dataList.RemoveAll(i => i.AcquisitionTime != item.AcquisitionTime && item.MeasDefinitionID.ToString() == i.MeasDefinitionID);
                //});
                var list = dataList.GroupBy(x => new { x.MeasDefinitionID }).Select(group => new { MeasDefID = group.Key });
                //foreach (var data in list)
                //{
                //    DateTime time = dataList.Where(i => i.MeasDefinitionID == data.MeasDefID.MeasDefinitionID.ToString()).OrderByDescending(item => item.AcquisitionTime).First().AcquisitionTime;
                //    dataList.RemoveAll(item => item.AcquisitionTime != time && item.MeasDefinitionID == data.MeasDefID.MeasDefinitionID.ToString());
                //}
                tableModel.tableName = "SVMRTEigenValue";
                List<Rows> rows = new List<Rows>();
                List<MeasLoc_SVM> locList = SVMManagement.GetMeasLoc_SVMListByTurID(turbineID);
                foreach (var data in list)
                {
                    List<EigenValueData_SVM> tempList = dataList.Where(item => item.MeasDefinitionID == data.MeasDefID.MeasDefinitionID.ToString()).ToList();
                    foreach (EnumSVMParamType svmdata in Enum.GetValues(typeof(EnumSVMParamType)))
                    {
                        string SVMLocId = locList.Find(item => item.MeasLocName == AppFramework.Utility.EnumHelper.GetDescription(svmdata))?.MeasLocationID;
                        if (string.IsNullOrEmpty(SVMLocId))
                            continue;
                        foreach (EigenValueData_SVM SVMEigenData in tempList.Where(item => item.MeasLocationID == SVMLocId))
                        {
                            Rows cells = new Rows();
                            cells.cells = CreateSVMRTEigenValueCell(SVMEigenData, locList);
                            rows.Add(cells);
                        }
                    }
                }
                tableModel.rows = rows.ToArray();
            }
            return tableModel;
        }

        private Cell[] CreateSVMRTEigenValueCell(EigenValueData_SVM sVMEigenValueData, List<MeasLoc_SVM> locList)
        {

            List<Cell> cells = new List<Cell>();
            MeasLoc_SVM MeasLoc = new MeasLoc_SVM ();
            for (int i = 0; i < locList.Count; i++)
            {
                if (sVMEigenValueData.MeasLocationID == locList[i].MeasLocationID)
                {
                    MeasLoc = locList[i];
                    break;
                }
            }
            Cell cell0 = new Cell();
            cell0.displayValue = DevTreeManagement.GetTurbComponent(MeasLoc.ComponentID).ComponentName;
            cells.Add(cell0);
            Cell cell1 = new Cell();//测量位置
            cell1.displayValue = MeasLoc.MeasLocName;
            cells.Add(cell1);
            Cell cell2 = new Cell();//特征值
            cell2.displayValue = CompoundSVMEVName(sVMEigenValueData.EigenValueID)??EnumHelper.GetDescription(sVMEigenValueData.EigenValueType);
            cells.Add(cell2);
            Cell cell3 = new Cell();//有效值
            double SVMEigenValue = sVMEigenValueData.Eigen_Value;
            string eigenUnit = "";
            //if (cell2.displayValue.IndexOf("加速度") > -1 || cell1.displayValue.IndexOf("加速度") >-1 )
            //{   //数据库里面的值，存储加速度时候会缩小1000倍，所以显示的时候需要扩大1000
            //    SVMEigenValue = SVMEigenValue * 1000;
            //}
            if (cell2.displayValue.IndexOf("加速度") > -1 || cell2.displayValue.Contains("振幅"))
            {
                eigenUnit = " mg";
            }else if (cell2.displayValue.Contains("位移偏移"))
            {
                eigenUnit = " m";
            }else if (cell2.displayValue.Equals("固有频率"))
            {
                eigenUnit = " Hz";
            }
            else
            {
                eigenUnit = " °";
            }

            if (eigenUnit.Equals(" mg"))
            {
                SVMEigenValue = SVMEigenValue * 1000;
            }

            cell3.displayValue = SVMEigenValue.ToString("F3")+ eigenUnit;
            cells.Add(cell3);
            Cell cell4 = new Cell();//状态
            if (AlarmType.GetAlarmTypeByDegree(sVMEigenValueData.AlarmDegree) != AlarmType.AlarmType_Unknown)
            {
                cell4.displayValue = AlarmType.GetAlarmTypeByDegree(sVMEigenValueData.AlarmDegree).AlarmTypeName;
                cell4.color = AlarmType.GetAlarmTypeByDegree(sVMEigenValueData.AlarmDegree).AlarmTypeColor;
            }
            cells.Add(cell4);
            Cell cell5 = new Cell();//测量定义
            cell5.displayValue = sVMEigenValueData.MeasDefinitionID.ToString();
            cell5.type = "hide";
            cells.Add(cell5);
            Cell cell6 = new Cell();
            cell6.displayValue = sVMEigenValueData.AcquisitionTime.ToString("yyyy-MM-dd HH:mm:ss");
            cells.Add(cell6);
            return cells.ToArray();
        }

        private string CompoundSVMEVName(string evid)
        {
            string str = null;
            if (evid.Contains("&&Actrual"))
            {
                str = "合成倾角";
            }else if (evid.Contains("&&Offset"))
            {
                str = "合成方向";
            }
            return str;
        }

        public BaseTableModel CreateRTEigenValueTable(List<EigenValueData_Vib> dataList)
        {
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "RTEigenValue";
            List<Rows> rows = new List<Rows>();
            for (int i = 0; i < dataList.Count; i++)
            {
                Rows cells = new Rows();
                cells.cells = CreateRTEigenValueCell(dataList[i]);
                rows.Add(cells);
            }
            tableModel.rows = rows.ToArray();
            return tableModel;
        }

        public static AlarmType AlarmType_Unknown = new AlarmType
        {
            AlarmDegree = EnumAlarmDegree.AlarmDeg_Unknown,
            AlarmTypeColor = "#A0A0A0",
            AlarmTypeName = "未知"
        };//696969

        /// <summary>
        /// 正常
        /// </summary>
        public static AlarmType AlarmType_Normal = new AlarmType
        {
            AlarmDegree = EnumAlarmDegree.AlarmDeg_Normal,
            AlarmTypeColor = "#00FF00",
            AlarmTypeName = "正常"
        };//008000

        ///// <summary>
        ///// 系统异常
        ///// </summary>
        //public static AlarmType AlarmType_CommunicationError = new AlarmType { 
        //    AlarmDegree = EnumAlarmDegree.AlarmDeg_SystemError, AlarmTypeColor = "#4169E1", AlarmTypeName = "系统异常" };

        /// <summary>
        /// 注意
        /// </summary>
        public static AlarmType AlarmType_Warning = new AlarmType
        {
            AlarmDegree = EnumAlarmDegree.AlarmDeg_Warning,
            AlarmTypeColor = "#FFFF00",
            AlarmTypeName = "注意"
        };

        /// <summary>
        /// 危险
        /// </summary>
        public static AlarmType AlarmType_Alarm = new AlarmType
        {
            AlarmDegree = EnumAlarmDegree.AlarmDeg_Alarm,
            AlarmTypeColor = "#FF0000",
            AlarmTypeName = "危险"
        };
        public static AlarmType GetAlarmTypeByDegree(EnumAlarmDegree _alarmDeg)
        {
            switch (_alarmDeg)
            {
                case EnumAlarmDegree.AlarmDeg_Unknown:
                    return AlarmType_Unknown;

                case EnumAlarmDegree.AlarmDeg_Normal:
                    return AlarmType_Normal;

                //case EnumAlarmDegree.AlarmDeg_SystemError:
                //    return AlarmType_CommunicationError;

                case EnumAlarmDegree.AlarmDeg_Warning:
                    return AlarmType_Warning;

                case EnumAlarmDegree.AlarmDeg_Alarm:
                    return AlarmType_Alarm;

                //case EnumAlarmDegree.AlarmDeg_ICECommon:
                //    return AlarmType_ICECommon;

                //case EnumAlarmDegree.AlarmDeg_ICESerious:
                //    return AlarmType_ICESerious;

                //case EnumAlarmDegree.AlarmDeg_Harm:
                //    return AlarmType_Harm;

                //case EnumAlarmDegree.AlarmDeg_AlarmSerious:
                //    return AlarmType_AlarmSerious;
                default:
                    return AlarmType_Unknown;
            }
        }

        private Cell[] CreateRTEigenValueCell(EigenValueData_Vib eigenvaluedata)
        {
            List<Cell> cells = new List<Cell>();
            Cell cell0 = new Cell();//部件
            cell0.displayValue = measDefDic[eigenvaluedata.MeasLocationID].DevTurComponent.ComponentName;
            cells.Add(cell0);
            Cell cell1 = new Cell();//测量位置
            cell1.displayValue = measDefDic[eigenvaluedata.MeasLocationID].MeasLocName;
            cells.Add(cell1);
            Cell cell2 = new Cell();//有效值
            cell2.displayValue = EigenValueManage.GetFreBandByCode(eigenvaluedata.EigenValueCode).Equals("") ? eigenvaluedata.EigenValueCode : EigenValueManage.GetFreBandByCode(eigenvaluedata.EigenValueCode);
            string WebSiteType = System.Configuration.ConfigurationManager.AppSettings["ViewModel"];
            if (WebSiteType == "BVM")
            {
                cell2.displayValue += " (" + eigenvaluedata.EigenValueCode.Split('_')[0] + ")";
            }
            cells.Add(cell2);
            Cell cell3 = new Cell();//特征值
            string eigenUnit = "";
            if (eigenvaluedata.EigenValueCode.IndexOf("VRMS") > -1)
            {
                eigenUnit = " mm/s";
            }
            else if (eigenvaluedata.EigenValueCode.IndexOf("RMS") > -1 || eigenvaluedata.EigenValueCode.IndexOf("PK") > -1 || eigenvaluedata.EigenValueCode.IndexOf("PPK") > -1)
            {
                eigenUnit = " m/s^2";
            }

            // 设置过程量单位为空，
            if (eigenvaluedata.EngUnitName.Equals("-999"))
            {
                eigenUnit = "";
            }
            cell3.displayValue = eigenvaluedata.Eigen_Value.ToString("f3") + eigenUnit;
            cells.Add(cell3);
            Cell cell4 = new Cell();//状态
            if (GetAlarmTypeByDegree(eigenvaluedata.AlarmDegree) != AlarmType_Unknown)
            {
                cell4.displayValue = GetAlarmTypeByDegree(eigenvaluedata.AlarmDegree).AlarmTypeName;
                cell4.color = GetAlarmTypeByDegree(eigenvaluedata.AlarmDegree).AlarmTypeColor;
            }
            cell4.type = "text";
            cells.Add(cell4);
            Cell cell5 = new Cell();//测量定义
            cell5.displayValue = eigenvaluedata.MeasDefinitionID.ToString();
            cell5.type = "hide";
            cells.Add(cell5);
            Cell cell6 = new Cell();
            cell6.displayValue = eigenvaluedata.AcquisitionTime.ToString("yyyy-MM-dd HH:mm:ss");
            cells.Add(cell6);
            return cells.ToArray();
        }


        public static Dictionary<string, int> GetGeneralEigenValueCodeList()
        {
            // 通用特征值
            //List<EnumEigenvalueName> general = new List<EnumEigenvalueName>() {
            //    EnumEigenvalueName.Enum_DC,
            //    EnumEigenvalueName.Enum_RMS,
            //    EnumEigenvalueName.Enum_PK,
            //    EnumEigenvalueName.Enum_PPK,
            //    EnumEigenvalueName.Enum_KTS,
            //    EnumEigenvalueName.Enum_CF,
            //    EnumEigenvalueName.Enum_SK,
            //    EnumEigenvalueName.Enum_EIF,
            //};

            List<int> general = new List<int>()
            {
                1,2,3,4,5,6,7,8,9,10,36,37,38,39,41
            };
            Dictionary<string, int> res = new Dictionary<string, int>();
            
            foreach(var i in general)
            {
                var ev = (EnumEigenvalueName)i;
                if (!res.ContainsKey(EnumHelper.GetDescription(ev)))
                {
                    res.Add(EnumHelper.GetDescription(ev), i);
                }
            }
            return res;
        }


        public static Dictionary<string, int> GetSVMEigenValueCodeList()
        {
            // 通用特征值
            //List<EnumEigenvalueName> general = new List<EnumEigenvalueName>() {
            //    EnumEigenvalueName.Enum_DC,
            //    EnumEigenvalueName.Enum_RMS,
            //    EnumEigenvalueName.Enum_PK,
            //    EnumEigenvalueName.Enum_PPK,
            //    EnumEigenvalueName.Enum_KTS,
            //    EnumEigenvalueName.Enum_CF,
            //    EnumEigenvalueName.Enum_SK,
            //    EnumEigenvalueName.Enum_EIF,
            //};

            List<int> general = new List<int>()
            {
                 1,36,3,4,44,45,38,39,40
            };
            Dictionary<string, int> res = new Dictionary<string, int>();

            foreach (var i in general)
            {
                var ev = (EnumEigenvalueName)i;
                if (!res.ContainsKey(EnumHelper.GetDescription(ev)))
                {
                    res.Add(EnumHelper.GetDescription(ev), i);
                }
            }
            return res;
        }
    }
    /// <summary>
    /// 特征值详细历史 前台使用数据类
    /// </summary>
    /// 
    public class EVdataHis_UI
    {
        /// <summary>
        /// 主Key，根据时间设置不同的特征值
        /// </summary>
        public string Time { get; set; }
        /// <summary>
        /// 工况情报table
        /// </summary>
        public BaseTableModel baseInfo { get; set; }
        /// <summary>
        /// 特征值table
        /// </summary>
        public BaseTableModel EVtable { get; set; }
        /// <summary>
        /// 晃度特征值table
        /// </summary>
        public BaseTableModel SVMEVtable { get; set; }
    }

}