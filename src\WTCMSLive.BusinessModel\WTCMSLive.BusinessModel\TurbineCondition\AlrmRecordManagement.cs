﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using CMSFramework.BusinessEntity;
using Microsoft.EntityFrameworkCore;

namespace WTCMSLive.BusinessModel
{
    public class AlrmRecordManagement
    {
        #region 诊断任务
        /// <summary>
        ///  获取所有机组的最新诊断情况
        /// </summary>
        /// <returns></returns>
        public static List<DiagnosisAssignment> GetRegularDiagnosisList()
        {
            List<DiagnosisAssignment> regularDiaList = null;
            using (CMSFramework.EF.DiagContext ctx = new CMSFramework.EF.DiagContext(ConfigInfo.DBConnName))
            {
                regularDiaList = ctx.DiagnosisAssignmentRecords.ToList();
            }
            return regularDiaList;
        }

        /// <summary>
        /// 取得最新诊断任务情报
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        public static DiagnosisAssignment GetDiagnosisAssignmentByID(string turbineID)
        {
            DiagnosisAssignment regularDia = null;
            using (CMSFramework.EF.DiagContext ctx = new CMSFramework.EF.DiagContext(ConfigInfo.DBConnName))
            {
                regularDia = ctx.DiagnosisAssignmentRecords.Where(item => item.WindTurbineID == turbineID).OrderByDescending(item => item.CreateTime).FirstOrDefault();
                regularDia.DiagnosisHandleLogs = ctx.DiagnosisHandleLogRecords.Where(item => item.WindTurbineID == turbineID).ToList();
            }
            return regularDia;
        }
        /// <summary>
        /// 申请诊断
        /// </summary>
        /// <param name="_diagnosisAssignment"></param>
        /// <returns></returns>
        public static bool AddDiagnosisAssignment(DiagnosisAssignment _diagnosisAssignment)
        {
            int count=0;
            using (CMSFramework.EF.DiagContext ctx = new CMSFramework.EF.DiagContext(ConfigInfo.DBConnName))
            {
                ctx.DiagnosisAssignmentRecords.Add(_diagnosisAssignment);
                count = ctx.SaveChanges();
            }
            return count > 0;
        }
        /// <summary>
        /// 更新诊断情报(取消诊断,状态变更)
        /// </summary>
        /// <param name="_diagnosisAssignment"></param>
        /// <returns></returns>
        public static bool UpdateDiagnosisAssignment(DiagnosisAssignment _diagnosisAssignment)
        {
            int count = 0;
            using (CMSFramework.EF.DiagContext ctx = new CMSFramework.EF.DiagContext(ConfigInfo.DBConnName))
            {
                ctx.DiagnosisAssignmentRecords.Attach(_diagnosisAssignment);
                ctx.Entry(_diagnosisAssignment).State = EntityState.Modified;
                count = ctx.SaveChanges();
            }
            return count > 0;
        }

        #endregion

        #region  HNSY 
        public static List<AlarmRecord> GetAlarmRecordByTurbineId(string _turbineID, DateTime _begintime, DateTime _endtime)
        {
            List<AlarmRecord> list = new List<AlarmRecord>();
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                list = ctx.MonAlarmRecords.Where(item => item.WindTurbineID == _turbineID && item.AlarmTime >= _begintime && item.AlarmTime <= _endtime).ToList();
            }
            return list;
        }

        public static List<AlarmRecord> GetAlarmRecordList(DateTime _begintime, DateTime _endtime)
        {
            List<AlarmRecord> list = new List<AlarmRecord>();
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                list = ctx.MonAlarmRecords.Where(item => item.AlarmTime >= _begintime && item.AlarmTime <= _endtime).ToList();
            }
            return list;
        }
        #endregion

    }
}
