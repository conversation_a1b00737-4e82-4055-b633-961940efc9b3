using System;
using System.Runtime.Serialization;

namespace WTCMSLive.WebSite.Core.DTOs
{
    public class WindTurbineDTO
    {
        public string WindParkId { get; set; }
        public string WindTurbineID { get; set; }
        public string? WindTurbineName { get; set; }
        public string? WindTurbineModel { get; set; }
        public string? OperationalDate { get; set; }
        public float? FactedPower { get; set; }
        public string? McsIP { get; set; }
        public string? ComponentIds { get; set; }
        public string? WindTurbineCode { get; set; }
        public int MinWorkingRotSpeed { get; set; }

        public string? Location { get; set; }

        
        public float? RatedGeneratorSpeed { get; set; }

      
        public float? GridConnectedGeneratorSpeed { get; set; }

        public List<string>? ComponentName { get;set; }
    }
} 