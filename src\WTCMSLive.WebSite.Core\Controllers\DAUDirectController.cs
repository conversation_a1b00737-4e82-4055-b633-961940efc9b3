using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WTCMSLive.WebSite.Core.DTOs;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core.Services;

namespace WTCMSLive.WebSite.Core.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DAUDirectController : ControllerBase
    {
        private readonly IDAUConfigQueueService _queueService;
        private readonly ILogger<DAUDirectController> _logger;

        public DAUDirectController(
            IDAUConfigQueueService queueService,
            ILogger<DAUDirectController> logger)
        {
            _queueService = queueService;
            _logger = logger;
        }


        #region 设备管理

        /// <summary>
        /// 启动采集
        /// </summary>
        /// <param name="daus"></param>
        /// <returns></returns>
        [HttpPost("StartAcquisition")]
        public IActionResult StartAcquisition([FromBody] List<DAUDirectDTO> daus)
        {

            return Ok(ApiResponse<string>.Success("OK"));

        }


        /// <summary>
        /// 停止采集
        /// </summary>
        /// <param name="daus"></param>
        /// <returns></returns>
        [HttpPost("StopAcquisition")]
        public IActionResult StopAcquisition([FromBody] List<DAUDirectDTO> daus)
        {

            return Ok(ApiResponse<string>.Success("OK"));

        }

        /// <summary>
        /// 获取录波数据
        /// </summary>
        /// <param name="daus"></param>
        /// <returns></returns>
        [HttpPost("GetWaveFormData")]
        public IActionResult GetWaveFormData([FromBody] List<DAUDirectDTO> daus)
        {

            return Ok(ApiResponse<string>.Success("OK"));

        }


        /// <summary>
        /// 测量定义下发
        /// </summary>
        /// <param name="daus"></param>
        /// <returns></returns>
        [HttpPost("SetMeasureDefinition")]
        public IActionResult SetMeasureDefinition([FromBody] List<DAUDirectDTO> daus)
        {

            return Ok(ApiResponse<string>.Success("OK"));

        }

        /// <summary>
        /// 推送配置
        /// </summary>
        /// <param name="daus"></param>
        /// <returns></returns>

        [HttpPost("SetSFTPConfig")]
        public IActionResult SetSFTPConfig()
        {

            return Ok(ApiResponse<string>.Success("OK"));

        }



        [HttpPost("SetAdvancedParameters")]
        public IActionResult SetAdvancedParameters()
        {

            return Ok(ApiResponse<string>.Success("OK"));

        }


        #endregion


        #region 数据验证

        /// <summary>
        /// 获取文件列表
        /// </summary>
        /// <param name="sTime"></param>
        /// <param name="eTime"></param>
        /// <returns></returns>
        [HttpPost("GetWaveDBFileList")]
        public IActionResult GetWaveDBFileList(DateTime sTime,DateTime eTime)
        {
            string sftpPath = "/home/<USER>";
            string sftpUser = "cmsdata";
            string sftpPwd = "123456";


            return Ok(ApiResponse<string>.Success("OK"));
        }

        #endregion

        /// <summary>
        /// 推送单个DAU配置到队列
        /// </summary>
        /// <param name="config">DAU配置数据</param>
        /// <returns></returns>
        [HttpPost("push")]
        public async Task<IActionResult> PushConfig([FromBody] DAUConfigDTO config)
        {
            if (config == null)
            {
                return BadRequest(new { success = false, message = "配置数据不能为空" });
            }

            if (string.IsNullOrWhiteSpace(config.WindParkID) || string.IsNullOrWhiteSpace(config.WindTurbienID))
            {
                return BadRequest(new { success = false, message = "WindParkID 和 WindTurbineID 不能为空" });
            }

            try
            {
                await _queueService.EnqueueAsync(config);
                
                _logger.LogInformation("DAU配置已加入队列: WindParkID={WindParkID}, WindTurbineID={WindTurbineID}",
                    config.WindParkID, config.WindTurbienID);

                return Ok(new
                {
                    success = true,
                    message = "配置已加入处理队列",
                    data = new
                    {
                        windParkID = config.WindParkID,
                        windTurbineID = config.WindTurbienID,
                        queuedAt = DateTime.Now
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加入DAU配置到队列失败");
                return StatusCode(500, new { success = false, message = "加入队列失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 批量推送DAU配置到队列
        /// </summary>
        /// <param name="configs">DAU配置数据列表</param>
        /// <returns></returns>
        [HttpPost("push-batch")]
        public async Task<IActionResult> PushBatchConfigs([FromBody] List<DAUConfigDTO> configs)
        {
            if (configs == null || configs.Count == 0)
            {
                return BadRequest(new { success = false, message = "配置数据列表不能为空" });
            }

            var results = new List<object>();
            int successCount = 0;
            int failCount = 0;

            foreach (var config in configs)
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(config.WindParkID) || string.IsNullOrWhiteSpace(config.WindTurbienID))
                    {
                        results.Add(new
                        {
                            windParkID = config.WindParkID,
                            windTurbineID = config.WindTurbienID,
                            success = false,
                            message = "WindParkID 和 WindTurbineID 不能为空"
                        });
                        failCount++;
                        continue;
                    }

                    await _queueService.EnqueueAsync(config);
                    
                    results.Add(new
                    {
                        windParkID = config.WindParkID,
                        windTurbineID = config.WindTurbienID,
                        success = true,
                        message = "已加入队列"
                    });
                    successCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "加入DAU配置到队列失败: WindParkID={WindParkID}, WindTurbineID={WindTurbineID}",
                        config.WindParkID, config.WindTurbienID);
                    
                    results.Add(new
                    {
                        windParkID = config.WindParkID,
                        windTurbineID = config.WindTurbienID,
                        success = false,
                        message = ex.Message
                    });
                    failCount++;
                }
            }

            return Ok(new
            {
                success = true,
                message = $"批量处理完成: 成功 {successCount} 个, 失败 {failCount} 个",
                summary = new
                {
                    total = configs.Count,
                    success = successCount,
                    failed = failCount
                },
                results
            });
        }

        /// <summary>
        /// 测试推送示例数据
        /// </summary>
        /// <returns></returns>
        [HttpPost("test")]
        public async Task<IActionResult> TestPush()
        {
            var testConfig = new DAUConfigDTO
            {
                WindParkID = "TEST001",
                WindTurbienID = "TEST001-WT01",
                WindTurbienName = "测试机组1号",
                DAUIP = "*************",
                DAUPort = "8000",
                DAUStatus = "Normal"
            };

            try
            {
                await _queueService.EnqueueAsync(testConfig);
                
                return Ok(new
                {
                    success = true,
                    message = "测试数据已加入队列",
                    testData = testConfig
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试推送失败");
                return StatusCode(500, new { success = false, message = "测试失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 模拟推送多个风场的数据
        /// </summary>
        /// <param name="windParkCount">风场数量</param>
        /// <param name="turbinesPerPark">每个风场的机组数量</param>
        /// <returns></returns>
        [HttpPost("simulate")]
        public async Task<IActionResult> SimulatePush(
            [FromQuery] int windParkCount = 2,
            [FromQuery] int turbinesPerPark = 3)
        {
            if (windParkCount <= 0 || windParkCount > 10)
            {
                return BadRequest(new { success = false, message = "风场数量必须在 1-10 之间" });
            }

            if (turbinesPerPark <= 0 || turbinesPerPark > 20)
            {
                return BadRequest(new { success = false, message = "机组数量必须在 1-20 之间" });
            }

            var configs = new List<DAUConfigDTO>();

            for (int i = 1; i <= windParkCount; i++)
            {
                string windParkID = $"SIM{i:D3}";
                
                for (int j = 1; j <= turbinesPerPark; j++)
                {
                    configs.Add(new DAUConfigDTO
                    {
                        WindParkID = windParkID,
                        WindTurbienID = $"{windParkID}-WT{j:D2}",
                        WindTurbienName = $"模拟风场{i}机组{j}号",
                        DAUIP = $"192.168.{i}.{j + 100}",
                        DAUPort = "8000",
                        DAUStatus = j % 3 == 0 ? "Offline" : "Normal"
                    });
                }
            }

            int successCount = 0;
            foreach (var config in configs)
            {
                try
                {
                    await _queueService.EnqueueAsync(config);
                    successCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "模拟推送失败: {WindParkID}-{WindTurbineID}",
                        config.WindParkID, config.WindTurbienID);
                }
            }

            return Ok(new
            {
                success = true,
                message = $"模拟数据已生成并加入队列",
                summary = new
                {
                    windParkCount,
                    turbinesPerPark,
                    totalConfigs = configs.Count,
                    successCount,
                    failedCount = configs.Count - successCount
                },
                sampleData = configs.Take(3)
            });
        }
    }
}

