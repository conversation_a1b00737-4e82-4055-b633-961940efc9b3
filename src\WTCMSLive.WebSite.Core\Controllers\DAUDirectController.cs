using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WTCMSLive.WebSite.Core.DTOs;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core.Services;
using Renci.SshNet;
using Renci.SshNet.Sftp;

namespace WTCMSLive.WebSite.Core.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DAUDirectController : ControllerBase
    {
        private readonly IDAUConfigQueueService _queueService;
        private readonly ILogger<DAUDirectController> _logger;

        public DAUDirectController(
            IDAUConfigQueueService queueService,
            ILogger<DAUDirectController> logger)
        {
            _queueService = queueService;
            _logger = logger;
        }


        #region 设备管理

        /// <summary>
        /// 启动采集
        /// </summary>
        /// <param name="daus"></param>
        /// <returns></returns>
        [HttpPost("StartAcquisition")]
        public IActionResult StartAcquisition([FromBody] List<DAUDirectDTO> daus)
        {

            return Ok(ApiResponse<string>.Success("OK"));

        }


        /// <summary>
        /// 停止采集
        /// </summary>
        /// <param name="daus"></param>
        /// <returns></returns>
        [HttpPost("StopAcquisition")]
        public IActionResult StopAcquisition([FromBody] List<DAUDirectDTO> daus)
        {

            return Ok(ApiResponse<string>.Success("OK"));

        }

        /// <summary>
        /// 获取录波数据
        /// </summary>
        /// <param name="daus"></param>
        /// <returns></returns>
        [HttpPost("GetWaveFormData")]
        public IActionResult GetWaveFormData([FromBody] List<DAUDirectDTO> daus)
        {

            return Ok(ApiResponse<string>.Success("OK"));

        }


        /// <summary>
        /// 测量定义下发
        /// </summary>
        /// <param name="daus"></param>
        /// <returns></returns>
        [HttpPost("SetMeasureDefinition")]
        public IActionResult SetMeasureDefinition([FromBody] List<DAUDirectDTO> daus)
        {

            return Ok(ApiResponse<string>.Success("OK"));

        }

        /// <summary>
        /// 推送配置
        /// </summary>
        /// <param name="daus"></param>
        /// <returns></returns>

        [HttpPost("SetSFTPConfig")]
        public IActionResult SetSFTPConfig()
        {

            return Ok(ApiResponse<string>.Success("OK"));

        }



        [HttpPost("SetAdvancedParameters")]
        public IActionResult SetAdvancedParameters()
        {

            return Ok(ApiResponse<string>.Success("OK"));

        }


        #endregion


        #region 数据验证

        /// <summary>
        /// 获取文件列表
        /// </summary>
        /// <param name="sTime">开始时间</param>
        /// <param name="eTime">结束时间</param>
        /// <returns></returns>
        [HttpGet("GetWaveDBFileList")]
        public async Task<IActionResult> GetWaveDBFileList(DateTime sTime, DateTime eTime)
        {
            try
            {
                // SFTP服务器配置
                string sftpHost = "************"; // 可以从配置文件或数据库获取
                int sftpPort = 22;
                string sftpPath = "/home/<USER>/CMSDATA";
                string sftpUser = "root";
                string sftpPwd = "forlinx";

                _logger.LogInformation("开始连接SFTP服务器: {Host}:{Port}, 用户: {User}, 路径: {Path}",
                    sftpHost, sftpPort, sftpUser, sftpPath);

                // 创建SFTP连接
                using var client = new SftpClient(sftpHost, sftpPort, sftpUser, sftpPwd);

                // 设置连接超时
                client.ConnectionInfo.Timeout = TimeSpan.FromSeconds(30);

                try
                {
                    await Task.Run(() => client.Connect());
                }
                catch (Renci.SshNet.Common.SshConnectionException ex)
                {
                    _logger.LogError(ex, "SFTP连接失败: {Host}:{Port}", sftpHost, sftpPort);
                    return BadRequest(ApiResponse<object>.Error("SFTP服务器连接失败"));
                }
                catch (Renci.SshNet.Common.SshAuthenticationException ex)
                {
                    _logger.LogError(ex, "SFTP认证失败: 用户 {User}", sftpUser);
                    return BadRequest(ApiResponse<object>.Error("SFTP认证失败"));
                }

                if (!client.IsConnected)
                {
                    _logger.LogError("SFTP连接失败，连接状态为false");
                    return BadRequest(ApiResponse<object>.Error("SFTP连接失败"));
                }

                _logger.LogInformation("SFTP连接成功，开始扫描目录: {Path}", sftpPath);

                // 检查根目录是否存在
                try
                {
                    if (!client.Exists(sftpPath))
                    {
                        _logger.LogWarning("指定的SFTP路径不存在: {Path}", sftpPath);
                        return BadRequest(ApiResponse<object>.Error($"路径不存在: {sftpPath}"));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "检查SFTP路径时发生错误: {Path}", sftpPath);
                    return BadRequest(ApiResponse<object>.Error("检查路径失败"));
                }

                // 递归扫描目录并构建文件树
                var rootNode = await ScanDirectoryRecursiveSimple(client, sftpPath, sTime, eTime);

                try
                {
                    client.Disconnect();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "断开SFTP连接时发生警告");
                }

                _logger.LogInformation("SFTP文件扫描完成");

                // 直接返回层级数据，不使用ApiResponse包装
                return Ok(rootNode);
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "SFTP操作超时");
                return BadRequest(ApiResponse<object>.Error("操作超时"));
            }
            catch (ArgumentException ex)
            {
                _logger.LogError(ex, "SFTP连接参数错误");
                return BadRequest(ApiResponse<object>.Error("连接参数错误"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取SFTP文件列表时发生未知错误");
                return BadRequest(ApiResponse<object>.Error($"获取文件列表失败: {ex.Message}"));
            }
        }

        #endregion

        /// <summary>
        /// 推送单个DAU配置到队列
        /// </summary>
        /// <param name="config">DAU配置数据</param>
        /// <returns></returns>
        [HttpPost("push")]
        public async Task<IActionResult> PushConfig([FromBody] DAUConfigDTO config)
        {
            if (config == null)
            {
                return BadRequest(new { success = false, message = "配置数据不能为空" });
            }

            if (string.IsNullOrWhiteSpace(config.WindParkID) || string.IsNullOrWhiteSpace(config.WindTurbienID))
            {
                return BadRequest(new { success = false, message = "WindParkID 和 WindTurbineID 不能为空" });
            }

            try
            {
                await _queueService.EnqueueAsync(config);
                
                _logger.LogInformation("DAU配置已加入队列: WindParkID={WindParkID}, WindTurbineID={WindTurbineID}",
                    config.WindParkID, config.WindTurbienID);

                return Ok(new
                {
                    success = true,
                    message = "配置已加入处理队列",
                    data = new
                    {
                        windParkID = config.WindParkID,
                        windTurbineID = config.WindTurbienID,
                        queuedAt = DateTime.Now
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加入DAU配置到队列失败");
                return StatusCode(500, new { success = false, message = "加入队列失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 批量推送DAU配置到队列
        /// </summary>
        /// <param name="configs">DAU配置数据列表</param>
        /// <returns></returns>
        [HttpPost("push-batch")]
        public async Task<IActionResult> PushBatchConfigs([FromBody] List<DAUConfigDTO> configs)
        {
            if (configs == null || configs.Count == 0)
            {
                return BadRequest(new { success = false, message = "配置数据列表不能为空" });
            }

            var results = new List<object>();
            int successCount = 0;
            int failCount = 0;

            foreach (var config in configs)
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(config.WindParkID) || string.IsNullOrWhiteSpace(config.WindTurbienID))
                    {
                        results.Add(new
                        {
                            windParkID = config.WindParkID,
                            windTurbineID = config.WindTurbienID,
                            success = false,
                            message = "WindParkID 和 WindTurbineID 不能为空"
                        });
                        failCount++;
                        continue;
                    }

                    await _queueService.EnqueueAsync(config);
                    
                    results.Add(new
                    {
                        windParkID = config.WindParkID,
                        windTurbineID = config.WindTurbienID,
                        success = true,
                        message = "已加入队列"
                    });
                    successCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "加入DAU配置到队列失败: WindParkID={WindParkID}, WindTurbineID={WindTurbineID}",
                        config.WindParkID, config.WindTurbienID);
                    
                    results.Add(new
                    {
                        windParkID = config.WindParkID,
                        windTurbineID = config.WindTurbienID,
                        success = false,
                        message = ex.Message
                    });
                    failCount++;
                }
            }

            return Ok(new
            {
                success = true,
                message = $"批量处理完成: 成功 {successCount} 个, 失败 {failCount} 个",
                summary = new
                {
                    total = configs.Count,
                    success = successCount,
                    failed = failCount
                },
                results
            });
        }

        /// <summary>
        /// 测试推送示例数据
        /// </summary>
        /// <returns></returns>
        [HttpPost("test")]
        public async Task<IActionResult> TestPush()
        {
            var testConfig = new DAUConfigDTO
            {
                WindParkID = "TEST001",
                WindTurbienID = "TEST001-WT01",
                WindTurbienName = "测试机组1号",
                DAUIP = "*************",
                DAUPort = "8000",
                DAUStatus = "Normal"
            };

            try
            {
                await _queueService.EnqueueAsync(testConfig);
                
                return Ok(new
                {
                    success = true,
                    message = "测试数据已加入队列",
                    testData = testConfig
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试推送失败");
                return StatusCode(500, new { success = false, message = "测试失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 模拟推送多个风场的数据
        /// </summary>
        /// <param name="windParkCount">风场数量</param>
        /// <param name="turbinesPerPark">每个风场的机组数量</param>
        /// <returns></returns>
        [HttpPost("simulate")]
        public async Task<IActionResult> SimulatePush(
            [FromQuery] int windParkCount = 2,
            [FromQuery] int turbinesPerPark = 3)
        {
            if (windParkCount <= 0 || windParkCount > 10)
            {
                return BadRequest(new { success = false, message = "风场数量必须在 1-10 之间" });
            }

            if (turbinesPerPark <= 0 || turbinesPerPark > 20)
            {
                return BadRequest(new { success = false, message = "机组数量必须在 1-20 之间" });
            }

            var configs = new List<DAUConfigDTO>();

            for (int i = 1; i <= windParkCount; i++)
            {
                string windParkID = $"SIM{i:D3}";
                
                for (int j = 1; j <= turbinesPerPark; j++)
                {
                    configs.Add(new DAUConfigDTO
                    {
                        WindParkID = windParkID,
                        WindTurbienID = $"{windParkID}-WT{j:D2}",
                        WindTurbienName = $"模拟风场{i}机组{j}号",
                        DAUIP = $"192.168.{i}.{j + 100}",
                        DAUPort = "8000",
                        DAUStatus = j % 3 == 0 ? "Offline" : "Normal"
                    });
                }
            }

            int successCount = 0;
            foreach (var config in configs)
            {
                try
                {
                    await _queueService.EnqueueAsync(config);
                    successCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "模拟推送失败: {WindParkID}-{WindTurbineID}",
                        config.WindParkID, config.WindTurbienID);
                }
            }

            return Ok(new
            {
                success = true,
                message = $"模拟数据已生成并加入队列",
                summary = new
                {
                    windParkCount,
                    turbinesPerPark,
                    totalConfigs = configs.Count,
                    successCount,
                    failedCount = configs.Count - successCount
                },
                sampleData = configs.Take(3)
            });
        }

        #region 私有辅助方法

        /// <summary>
        /// 递归扫描目录并构建简化的文件树
        /// </summary>
        /// <param name="client">SFTP客户端</param>
        /// <param name="currentPath">当前扫描路径</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns></returns>
        private async Task<SftpDirectoryNodeDTO> ScanDirectoryRecursiveSimple(SftpClient client, string currentPath, DateTime startTime, DateTime endTime)
        {
            var currentDirName = Path.GetFileName(currentPath);
            if (string.IsNullOrEmpty(currentDirName))
                currentDirName = "CMSDATA";

            var result = new SftpDirectoryNodeDTO
            {
                Name = currentDirName,
                Path = currentPath,
                IsDirectory = true,
                LastModified = null
            };

            try
            {
                _logger.LogDebug("扫描目录: {Path}", currentPath);

                // 获取当前目录下的所有项目
                var items = await Task.Run(() => client.ListDirectory(currentPath));

                foreach (var item in items.Where(i => i.Name != "." && i.Name != ".."))
                {
                    try
                    {
                        if (item.IsDirectory)
                        {
                            // 递归扫描子目录
                            var subDirNode = await ScanDirectoryRecursiveSimple(client, item.FullName, startTime, endTime);
                            result.Children.Add(subDirNode);
                        }
                        else if (item.IsRegularFile)
                        {
                            // 检查文件时间是否在范围内
                            if (item.LastWriteTime >= startTime && item.LastWriteTime <= endTime)
                            {
                                var fileNode = new SftpDirectoryNodeDTO
                                {
                                    Name = item.Name,
                                    Path = item.FullName,
                                    IsDirectory = false,
                                    Size = item.Length,
                                    LastModified = item.LastWriteTime
                                };
                                result.Children.Add(fileNode);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "处理项目失败: {Path}", item.FullName);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扫描目录时发生错误: {Path}", currentPath);
            }

            return result;
        }

        /// <summary>
        /// 处理目录中的文件，根据时间范围过滤
        /// </summary>
        /// <param name="files">文件列表</param>
        /// <param name="rootPath">根路径</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns></returns>
        private List<SftpFileInfoDTO> ProcessFilesInDirectory(
            IEnumerable<SftpFile> files,
            string rootPath,
            DateTime startTime,
            DateTime endTime)
        {
            var result = new List<SftpFileInfoDTO>();

            foreach (var file in files)
            {
                // 根据创建时间过滤文件
                if (file.LastWriteTime >= startTime && file.LastWriteTime <= endTime)
                {
                    var fileInfo = new SftpFileInfoDTO
                    {
                        FileName = file.Name,
                        FullPath = file.FullName,
                        FileSize = file.Length,
                        FileSizeFormatted = FormatFileSize(file.Length),
                        CreatedTime = file.LastWriteTime,
                        ModifiedTime = file.LastWriteTime,
                        FileExtension = Path.GetExtension(file.Name),
                        RelativePath = GetRelativePath(file.FullName, rootPath)
                    };

                    result.Add(fileInfo);
                }
            }

            return result.OrderBy(f => f.FileName).ToList();
        }

        /// <summary>
        /// 计算统计信息
        /// </summary>
        /// <param name="response">响应对象</param>
        private void CalculateStatistics(SftpFileListResponseDTO response)
        {
            response.TotalFileCount = 0;
            response.TotalDirectoryCount = response.RootDirectories.Count;

            foreach (var rootDir in response.RootDirectories)
            {
                CalculateDirectoryStatistics(rootDir);
                response.TotalFileCount += rootDir.TotalFileCount;
                response.TotalDirectoryCount += rootDir.TotalSubDirectoryCount;
            }
        }

        /// <summary>
        /// 递归计算目录统计信息
        /// </summary>
        /// <param name="directory">目录节点</param>
        private void CalculateDirectoryStatistics(SftpDirectoryNodeDTO directory)
        {
            directory.TotalFileCount = directory.Files.Count;
            directory.TotalSubDirectoryCount = directory.SubDirectories.Count;

            foreach (var subDir in directory.SubDirectories)
            {
                CalculateDirectoryStatistics(subDir);
                directory.TotalFileCount += subDir.TotalFileCount;
                directory.TotalSubDirectoryCount += subDir.TotalSubDirectoryCount;
            }
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        /// <param name="bytes">字节数</param>
        /// <returns></returns>
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 获取相对路径
        /// </summary>
        /// <param name="fullPath">完整路径</param>
        /// <param name="rootPath">根路径</param>
        /// <returns></returns>
        private string GetRelativePath(string fullPath, string rootPath)
        {
            if (fullPath.StartsWith(rootPath))
            {
                var relativePath = fullPath.Substring(rootPath.Length);
                return relativePath.StartsWith("/") ? relativePath.Substring(1) : relativePath;
            }
            return fullPath;
        }

        #endregion
    }
}

