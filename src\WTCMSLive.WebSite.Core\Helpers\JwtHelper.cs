using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core.Models.DTOs;

namespace WTCMSLive.WebSite.Core.Helpers
{
    /// <summary>
    /// JWT帮助类，封装JWT生成和验证逻辑
    /// </summary>
    public static class JwtHelper
    {
        /// <summary>
        /// 生成JWT Token
        /// </summary>
        /// <param name="account">账号</param>
        /// <param name="userId">用户ID</param>
        /// <param name="roleName">角色名称</param>
        /// <param name="jwtSettings">JWT配置</param>
        /// <returns>JWT Token字符串</returns>
        public static string GenerateToken(string account, string userId, string roleName, JwtSettings jwtSettings)
        {
            var key = Encoding.ASCII.GetBytes(jwtSettings.SecretKey);
            var tokenHandler = new JwtSecurityTokenHandler();
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.Name, account),
                    new Claim(ClaimTypes.NameIdentifier, userId),
                    new Claim(ClaimTypes.Role, roleName)
                }),
                Expires = DateTime.UtcNow.AddMinutes(jwtSettings.ExpirationInMinutes),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
                Issuer = jwtSettings.Issuer,
                Audience = jwtSettings.Audience
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        /// <summary>
        /// 验证JWT Token并提取Claims
        /// </summary>
        /// <param name="token">JWT Token字符串</param>
        /// <param name="jwtSettings">JWT配置</param>
        /// <returns>验证结果和Claims</returns>
        public static (bool IsValid, ClaimsPrincipal Principal, string ErrorMessage) ValidateToken(string token, JwtSettings jwtSettings)
        {
            try
            {
                var key = Encoding.ASCII.GetBytes(jwtSettings.SecretKey);
                var tokenHandler = new JwtSecurityTokenHandler();
                
                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidIssuer = jwtSettings.Issuer,
                    ValidAudience = jwtSettings.Audience,
                    ClockSkew = TimeSpan.Zero,
                    ValidateLifetime = false // 我们手动验证过期时间以支持宽限期
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);
                
                // 手动检查过期时间，支持宽限期
                var jwtToken = validatedToken as JwtSecurityToken;
                if (jwtToken?.ValidTo < DateTime.UtcNow.AddMinutes(-jwtSettings.RefreshGracePeriodInMinutes))
                {
                    return (false, null, "Token已过期超过宽限期");
                }

                return (true, principal, null);
            }
            catch (SecurityTokenExpiredException)
            {
                return (false, null, "Token已过期");
            }
            catch (SecurityTokenException ex)
            {
                return (false, null, $"Token验证失败: {ex.Message}");
            }
            catch (Exception ex)
            {
                return (false, null, $"Token验证异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 从Claims中提取用户信息
        /// </summary>
        /// <param name="principal">Claims主体</param>
        /// <returns>用户信息</returns>
        public static (string Account, string UserId, string Role) ExtractUserInfo(ClaimsPrincipal principal)
        {
            var account = principal.FindFirst(ClaimTypes.Name)?.Value;
            var userId = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var role = principal.FindFirst(ClaimTypes.Role)?.Value;

            return (account, userId, role);
        }

        /// <summary>
        /// 检查Token是否在续签宽限期内
        /// </summary>
        /// <param name="token">JWT Token字符串</param>
        /// <param name="jwtSettings">JWT配置</param>
        /// <returns>是否在宽限期内</returns>
        public static bool IsTokenInRefreshGracePeriod(string token, JwtSettings jwtSettings)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var jwtToken = tokenHandler.ReadJwtToken(token);
                
                var expirationTime = jwtToken.ValidTo;
                var gracePeriodStart = DateTime.UtcNow;
                var gracePeriodEnd = DateTime.UtcNow.AddMinutes(jwtSettings.RefreshGracePeriodInMinutes);

                // Token应该在当前时间之后过期，但在宽限期内
                return expirationTime > gracePeriodStart && expirationTime <= gracePeriodEnd;
            }
            catch
            {
                return false;
            }
        }
    }
}
