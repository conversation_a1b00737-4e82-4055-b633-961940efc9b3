import{C as _}from"./ChangeLanguage-Dy5BmE5j.js";import{dk as u,r as d,h as p,c as v,i as e,b as a,n as m,g as r,t as n,x as h,o as f,q as g}from"./index-BjOW8S1L.js";import{_ as y}from"./_plugin-vue_export-helper-DlAUqK2U.js";const w={id:"userLayout",class:g(["user-layout-wrapper"])},x={class:"container"},V={class:"changelang"},C={class:"user-layout-content"},L={class:"footer"},k={class:"copyright"},B={__name:"UserLayout",setup(N){const c=u(),t=d(""),i=new Date().getFullYear();return p(async()=>{let o=await c.fetchGetVersion();t.value=o}),(o,s)=>{const l=m("router-view");return f(),v("div",w,[e("div",x,[e("div",V,[a(_)]),e("div",C,[a(l)]),e("div",L,[e("div",k,[r(" © "+n(h(i))+" V"+n(t.value)+" ",1),s[0]||(s[0]=e("span",null,"配置网站",-1)),s[1]||(s[1]=r(" All Rights Reserved ",-1))])])])])}}},D=y(B,[["__scopeId","data-v-e13a30b4"]]);export{D as default};
