﻿using CMSFramework.BusinessEntity;
using Microsoft.Data.Sqlite;
using MySql.Data.MySqlClient;
using System.Data.Common;

namespace WTCMSLive.WebSite.Helpers
{
    public class MySqlConnect
    {
        //public static string _MysqlBaseDB = "server=localhost;User Id=WTMANA;password=*******;Persist Security Info=True;database=wtlivedb;charset=utf8";
        //public static string _MysqlBaseRTDB = "server=localhost;User Id=WTMANA;password=*******;Persist Security Info=True;database=wtlivedbtrend;charset=utf8";
        public static string _MysqlBaseDB = ConfigInfo.DBConnName;
        public static string _MysqlBaseRTDB = ConfigInfo.DBConnNameTrend;
        public static DbConnection GetMysqlConnection()
        {
            if (_MysqlConnection == null)
            {
                if (ConfigInfo.DBConnProviderName.ToLower().Contains("sqlite"))
                {
                    _MysqlConnection = new SqliteConnection(ConfigInfo.DBConnNameStr);
                }
                else
                {
                    _MysqlConnection = new MySqlConnection(ConfigInfo.DBConnName);
                }
                
            }

            return _MysqlConnection;
        }

        private static DbConnection _MysqlConnection { get; set; }
    }
}