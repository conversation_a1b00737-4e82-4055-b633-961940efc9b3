import{c as Q,W as X}from"./table-RP3jLHlo.js";import{O as Z}from"./index-CzSbT6op.js";import{z as m,r as o,u as ee,y as ae,w as te,f as g,d as i,o as s,i as u,b as f,c as k,F as V,e as S,g as ne,t as y,s as le,q as oe,A as P}from"./index-BjOW8S1L.js";import{S as re,g as A}from"./tools-zTE6InS0.js";import{L as ie}from"./index-BXWgJpPi.js";import{u as se}from"./statusMonitor-eEhdeKkD.js";import{u as me}from"./devTree-Dwa9wLl9.js";import{_ as ue}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as ce,a as de}from"./index-D82yULGq.js";import{M as ve}from"./index-BnSFuLp6.js";import{a as pe}from"./ActionButton-C_grUmdF.js";import"./styleChecker-CFtINSLw.js";import"./index-7iPMz_Qy.js";import"./initDefaultProps-P4j1rGDC.js";import"./index-DTxROkTj.js";import"./shallowequal-gCpTBdTi.js";import"./index-kP-mINcM.js";import"./index-BJEkaghg.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";const fe={class:"border"},he=["onClick"],_e={class:"modalMarkLines"},De=["onClick"],ge={class:"chartBox"},ke={key:1,class:"nodata"},be={__name:"eigenvalue",setup(Me){const b=se(),B=me(),N=(e={})=>[{label:"发电机转速(CMS)",value:e.windParkName},{label:"风速",value:e.windParkName},{label:"输出功率(KW)",value:e.windParkName},{label:"发电机转速(CMS)",value:e.windParkName},{label:"桨距角",value:e.windParkName},{label:"偏航角度",value:e.windParkName}];let h=[m().subtract(3,"month"),m()];const M=o(!1),x=ee(),L=o(""),w=o(""),c=o(x.params.id),_=o(h),C=o(!1),Y=o(N({})),d=o({time:h}),v=o([]),n=o({}),a=ae({tableData:[],chartInformation:{title:"",legendArr:[]},currentMarkLine:{}}),O=()=>{let e=B.findAncestorsWithNodes(c.value);e&&e.length&&e.length>1&&(L.value=e[e.length-2].id)},F=async()=>{if(c.value){const e=await b.fetchGetWorkCondEVRT({windParkID:L.value,turbineID:c.value});e&&e.length?Y.value=e:Y.value=N()}},R=async()=>{M.value=!0,a.tableData=await b.fetchGetEigenValueRT({windParkID:L.value,turbineID:c.value}),M.value=!1};te(()=>x.params.id,e=>{e&&(c.value=e,O(),R(),F())},{immediate:!0});const W=async e=>{w.value=`${e.measLocationName}_${e.eigenValueName}`,d.value={...d.value,...e},await I(),E()},I=async()=>{const e=d.value;let l=await b.fetchGetTrendAnalyseByTime({turbineID:c.value,timeBegin:m(_.value[0]).format("YYYY-MM-DD"),timeEnd:m(_.value[1]).format("YYYY-MM-DD"),workConValue:"",workConParame:"",measType:e.dataType,EigenCode:e.eigenValueCode,measLoc:e.measLocationID,UseAlarmDef:!1});if(l&&l.length){const D={time:l[0].timeValueData,lineData:[{line:l[0].eigenValueData}],markLine:[]};let r=[];l[0].thresholdLineDatas&&l[0].thresholdLineDatas.length&&(r=[...r,...l[0].thresholdLineDatas.map(p=>({lineName:p.lineName,markLine:[p.warningLine,p.alarmLine]}))]),r&&r.length&&(a.currentMarkLine=r[0],v.value=r,D.markLine=r[0].markLine),n.value=D,a.chartInformation={title:`${e.measLocationName}`,legendArr:[e.eigenValueName]}}else n.value={},a.currentMarkLine={},v.value=[]},$=e=>{if(a.currentMarkLine&&a.currentMarkLine.lineName==e.lineName){a.currentMarkLine={},n.value={...n.value,markLine:[]};return}a.currentMarkLine=e,n.value={...n.value,markLine:e.markLine}},z=e=>{_.value=[m(e.time[0]).format("YYYY-MM-DD"),m(e.time[1]).format("YYYY-MM-DD")],I()},E=()=>{C.value=!0},q=e=>{C.value=!1,d.value={time:h},_.value=h},G=[{title:"部件",dataIndex:"compName",headerOperations:{filters:[]}},{title:"测量位置",dataIndex:"measLocationName",headerOperations:{filters:[]}},{title:"特征值",dataIndex:"eigenValueName",otherColumn:!0},{title:"数值",dataIndex:"eigenValue"},{title:"状态",dataIndex:"alarmDegree",headerOperations:{filters:[{text:"正常",value:3},{text:"注意",value:5},{text:"危险",value:6},{text:"未知",value:2}]},customRender:({record:e})=>e.alarmDegree?P("span",{style:{color:A(e.alarmDegree).color}},A(e.alarmDegree).text):""},{title:"更新时间",dataIndex:"acquisitionTime",headerOperations:{sorter:!0,date:!0},customRender:({record:e})=>e.acquisitionTime?P("span",{},m(e.acquisitionTime).format("YYYY-MM-DD HH:mm:ss")):""}],H=[{title:"选择时间",dataIndex:"time",inputType:"rangePicker",timeFormat:["YYYY-MM-DD","YYYY-MM-DD"],noMinLabelWidth:!0}],K={showToolbox:!0};return(e,l)=>{const D=de,r=ce,p=pe,j=ve,U=re;return s(),g(U,{spinning:M.value,size:"large"},{default:i(()=>[u("div",null,[f(Q,{tableTitle:"工况特征值详情",defaultCollapse:!0,batchApply:!1},{content:i(()=>[u("div",fe,[f(r,{column:4,size:"small"},{default:i(()=>[(s(!0),k(V,null,S(Y.value,t=>(s(),g(D,{label:t.label,key:t.label},{default:i(()=>[ne(y(t.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:1}),u("div",null,[f(X,{ref:"table",size:"default","table-key":"0","table-title":"特征值列表","table-columns":G,"table-operate":[],noBatchApply:!0,defaultPageSize:50,recordKey:t=>`${t.measLocationID}&&${t.eigenValueCode}`,"table-datas":a.tableData},{otherColumn:i(({column:t,record:T,text:J})=>[u("a",{onClick:Le=>W(T)},y(J),9,he)]),_:1},8,["recordKey","table-datas"])]),f(j,{maskClosable:!1,destroyOnClose:!0,width:"800px",open:C.value,title:w.value,footer:"",onCancel:q},{default:i(()=>[f(Z,{titleCol:H,ref:"formRef",initFormData:d.value,formlayout:"inline",onSubmit:z},null,8,["initFormData"]),v.value&&v.value.length?(s(),g(p,{key:0,placement:"bottom",overlayClassName:"myPopover"},{content:i(()=>[u("ul",_e,[(s(!0),k(V,null,S(v.value,t=>(s(),k("li",{onClick:T=>$(t),class:oe({active:t.lineName==a.currentMarkLine.lineName})},y(t.lineName),11,De))),256))])]),default:i(()=>[l[0]||(l[0]=u("span",{class:"modalMarkLineBtn"},"报警规则",-1))]),_:1,__:[0]})):le("",!0),u("div",ge,[n.value&&n.value.time&&n.value.time.length?(s(),g(ie,{key:0,boxId:"chart1",chartOptions:K,informations:a.chartInformation,chartData:n.value},null,8,["informations","chartData"])):(s(),k("div",ke,"暂无数据"))])]),_:1},8,["open","title"])])]),_:1},8,["spinning"])}}},qe=ue(be,[["__scopeId","data-v-2c9f6d25"]]);export{qe as default};
