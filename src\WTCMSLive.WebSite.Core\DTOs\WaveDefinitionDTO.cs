﻿namespace WTCMSLive.WebSite.Core.DTOs
{
    public class WaveDefinitionDTO
    {
        public string WaveDefinitionID { get; set; }
        public string WaveDefinitionName { get; set; }
        public string MeasDefinitionID { get; set; }
        public string WindTurbineID { get; set; }
        public string WindParkID { get; set; }
        public string measLocIds { get; set; }
        public float TimeWdfUpFreq { get; set; }
        public float TimeWdfSampleLength { get; set; }
        public string isAddType { get; set; }
        public string messLocNames { get; set; }
        public string dauid { get; set; }
        public int signalType { get; set; }
        public string GeneralEVList { get; set; }
        public string FBEList { get; set; }
        public bool isAll { get; set; } = false;
        
        public float SampleLength { get; set; }
        public int EnvBandWidth { get; set; }
        public float EnvFiterFreq { get; set; }
    }
}
