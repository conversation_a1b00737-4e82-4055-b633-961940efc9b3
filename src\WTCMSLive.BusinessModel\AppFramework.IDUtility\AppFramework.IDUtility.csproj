﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <OutputType>Library</OutputType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <AssemblyTitle>AppFramework.IDUtility</AssemblyTitle>
    <Company>Microsoft</Company>
    <Product>AppFramework.IDUtility</Product>
    <Copyright>Copyright © Microsoft 2015</Copyright>
    <AssemblyVersion>1.0.1.31</AssemblyVersion>
    <FileVersion>1.0.1.31</FileVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DocumentationFile>bin\Debug\AppFramework.IDUtility.XML</DocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="AppFrameWork.Utility">
      <HintPath>..\..\AppNetCore\AppFramework.Utility.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.TypeDef">
      <HintPath>..\..\AppNetCore\CMSFramework.TypeDef.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>