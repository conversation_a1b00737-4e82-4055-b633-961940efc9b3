﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WindCMS.EF.WTParameter
{
    public class WtGearboxModel
    {
        public WtGearboxModel()
        {
            // 如果需要，可以在此处初始化相关集合
        }

        /// <summary>
        /// 齿轮箱信息的唯一标识符
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 齿轮箱厂家
        /// </summary>
        public string Manufacturer { get; set; } = null!;

        /// <summary>
        /// 齿轮箱型号
        /// </summary>
        public string Model { get; set; } = null!;

        /// <summary>
        /// 传动比
        /// </summary>
        public decimal? GearRatio { get; set; }

        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime? CreatedAt { get; set; }

        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
    }
}
