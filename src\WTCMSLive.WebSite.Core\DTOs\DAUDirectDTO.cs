﻿namespace WTCMSLive.WebSite.Core.DTOs
{
    public class DAUDirectDTO
    {
        public string? WindTurbineID { get; set; }
        public string? DauID { get; set; }

        public string? IP { get; set; }

        public int Port { get; set; }
        public string? WindParkID { get; set; }
    }

    /// <summary>
    /// 精简的SFTP目录节点DTO（用于构建树形结构）
    /// </summary>
    public class SftpDirectoryNodeDTO
    {
        /// <summary>
        /// 目录名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 完整路径
        /// </summary>
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// 是否为目录
        /// </summary>
        public bool IsDirectory { get; set; }

        /// <summary>
        /// 文件大小（仅文件有效）
        /// </summary>
        public long? Size { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? LastModified { get; set; }

        /// <summary>
        /// 子项（目录和文件）
        /// </summary>
        public List<SftpDirectoryNodeDTO> Children { get; set; } = new List<SftpDirectoryNodeDTO>();
    }


}
