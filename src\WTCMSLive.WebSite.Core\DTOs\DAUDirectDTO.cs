﻿namespace WTCMSLive.WebSite.Core.DTOs
{
    public class DAUDirectDTO
    {
        public string? WindTurbineID { get; set; }
        public string? DauID { get; set; }

        public string? IP { get; set; }

        public int Port { get; set; }
        public string? WindParkID { get; set; }
    }

    /// <summary>
    /// SFTP文件列表响应DTO
    /// </summary>
    public class SftpFileListResponseDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息（如果有）
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 文件总数
        /// </summary>
        public int TotalFileCount { get; set; }

        /// <summary>
        /// 目录总数
        /// </summary>
        public int TotalDirectoryCount { get; set; }

        /// <summary>
        /// 根目录列表
        /// </summary>
        public List<SftpDirectoryNodeDTO> RootDirectories { get; set; } = new List<SftpDirectoryNodeDTO>();

        /// <summary>
        /// 查询时间范围
        /// </summary>
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 查询执行时间
        /// </summary>
        public DateTime QueryTime { get; set; }
    }

    /// <summary>
    /// SFTP目录节点DTO（用于构建树形结构）
    /// </summary>
    public class SftpDirectoryNodeDTO
    {
        /// <summary>
        /// 目录名称
        /// </summary>
        public string DirectoryName { get; set; } = string.Empty;

        /// <summary>
        /// 目录完整路径
        /// </summary>
        public string FullPath { get; set; } = string.Empty;

        /// <summary>
        /// 目录创建时间
        /// </summary>
        public DateTime? CreatedTime { get; set; }

        /// <summary>
        /// 目录修改时间
        /// </summary>
        public DateTime? ModifiedTime { get; set; }

        /// <summary>
        /// 该目录下的文件列表
        /// </summary>
        public List<SftpFileInfoDTO> Files { get; set; } = new List<SftpFileInfoDTO>();

        /// <summary>
        /// 子目录列表
        /// </summary>
        public List<SftpDirectoryNodeDTO> SubDirectories { get; set; } = new List<SftpDirectoryNodeDTO>();

        /// <summary>
        /// 该目录下文件总数（包括子目录）
        /// </summary>
        public int TotalFileCount { get; set; }

        /// <summary>
        /// 该目录下子目录总数（包括嵌套）
        /// </summary>
        public int TotalSubDirectoryCount { get; set; }
    }

    /// <summary>
    /// SFTP文件信息DTO
    /// </summary>
    public class SftpFileInfoDTO
    {
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 文件完整路径
        /// </summary>
        public string FullPath { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件大小（格式化显示）
        /// </summary>
        public string FileSizeFormatted { get; set; } = string.Empty;

        /// <summary>
        /// 文件创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 文件修改时间
        /// </summary>
        public DateTime ModifiedTime { get; set; }

        /// <summary>
        /// 文件扩展名
        /// </summary>
        public string FileExtension { get; set; } = string.Empty;

        /// <summary>
        /// 相对于根路径的路径
        /// </summary>
        public string RelativePath { get; set; } = string.Empty;
    }
}
