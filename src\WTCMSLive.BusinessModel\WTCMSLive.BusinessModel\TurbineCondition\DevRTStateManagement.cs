﻿using CMSFramework.BusinessEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace WTCMSLive.BusinessModel
{
    /// <summary>
    /// 设备树实时状态管理
    /// </summary>
    public static class DevRTStateManagement
    {
        /// <summary>
        /// 获取风场部件状态
        /// </summary>
        /// <param name="_windParkID"></param>
        /// <returns></returns>
        public static List<AlarmStatus_Component> GetTurComRTStateListWindPark(string _windParkID)
        {
            List<WindTurbineComponent> windTurbineComponent = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                windTurbineComponent = ctx.DevTurComponents.ToList();
            }
            List<AlarmStatus_Component> alarmStatusList = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                //alarmStatusList = ctx.AlarmStatus_Components.Where(item => item.WindTurbineID.Contains(_windParkID)&&(!item.ComponentID.Contains("BLD"))).ToList();
                alarmStatusList = ctx.AlarmStatus_Components.ToList();
                alarmStatusList = alarmStatusList.Where(item => item.WindTurbineID.Contains(_windParkID) && (!item.ComponentID.Contains("BLD"))).ToList();

                if (windTurbineComponent != null && windTurbineComponent.Count > 0)
                {
                    alarmStatusList.ForEach(item =>
                    {
                        item.DevSegmentName = windTurbineComponent.Find(com => com.ComponentID == item.ComponentID).ComponentName;
                    });
                }
            }
 /*           List<AlarmStatus_Component> _alarmStatusList = new List<AlarmStatus_Component>();*/
            // 叶片特殊处理
            using (CMSFramework.EF.BVMMonContext ctx = new CMSFramework.EF.BVMMonContext (ConfigInfo.DBConnName))
            {
               //List<AlarmStatus_BladeCom> alarms = ctx.BladeComponentMonAlarm.Where(o => o.WindTurbineID.Contains(_windParkID)).ToList();
               List<AlarmStatus_BladeCom> alarms = ctx.BladeComponentMonAlarm.ToList();
               alarms = alarms.Where(o => o.WindTurbineID.Contains(_windParkID)).ToList();
                alarms.ForEach(item =>
                {
                    var alarm = alarms.FirstOrDefault(o => o.ComponentID == item.ComponentID);
                    if (alarm != null)
                    {
                        AlarmStatus_Component alarmStatus = new AlarmStatus_Component()
                        {
                            WindTurbineID = item.WindTurbineID,
                            ComponentID = item.ComponentID,
                            AlarmDegree = EnumAlarmDegree.AlarmDeg_Normal,
                            DevSegmentName = windTurbineComponent.Find(com => com.ComponentID == item.ComponentID).ComponentName,
                            AlarmUpdateTime = DateTime.Now
                        };
                        // 先判断结构损伤和桨距角偏差，这两个只存在Warning
                        //if (alarm.StructDmgAlarmDegree == EnumAlarmDegree.AlarmDeg_Warning || alarm.PbAlarmDegree == EnumAlarmDegree.AlarmDeg_Warning)
                        //{
                        //    alarmStatus.AlarmDegree = EnumAlarmDegree.AlarmDeg_Warning;
                        //}
                        alarmStatus.AlarmDegree = alarmStatus.AlarmDegree > alarm.StructDmgAlarmDegree ? alarmStatus.AlarmDegree : alarm.StructDmgAlarmDegree;
                        alarmStatus.AlarmDegree = alarmStatus.AlarmDegree > alarm.PbAlarmDegree ? alarmStatus.AlarmDegree : alarm.PbAlarmDegree;

                        alarmStatus.AlarmDegree = alarmStatus.AlarmDegree > alarm.IceAlarmDegree ? alarmStatus.AlarmDegree : alarm.IceAlarmDegree;
                        alarmStatus.AlarmDegree = alarmStatus.AlarmDegree > alarm.SurfaceDmgAlarmDegree ? alarmStatus.AlarmDegree : alarm.SurfaceDmgAlarmDegree;

                        alarmStatusList.Add(alarmStatus);
                    }
                });
            }
            return alarmStatusList;
        }
        ///<summary>
        ///查询报警覆冰信息
        /// </summary>
        /// <returns></returns>
        /// 
        public static List<AlarmStatus_BladeCom> GetAlarmIceList() {
            List<AlarmStatus_BladeCom> alarmIceList = null;
            using (CMSFramework.EF.BVMMonContext ctx = new CMSFramework.EF.BVMMonContext(ConfigInfo.DBConnName))
            {
                alarmIceList = ctx.BladeComponentMonAlarm.ToList();


            }


            return alarmIceList;
        }
        /// <summary>
        /// 获取机组实时状态列表
        /// </summary>
        /// <returns></returns>
        public static List<AlarmStatus_Turbine> GetTurRTAList()
        {
            List<AlarmStatus_Turbine> alarmStatusList = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                alarmStatusList = ctx.AlarmStatus_Turbines.ToList();
            }
            return alarmStatusList;
        }

        //public static List<AlarmStatus_TurbineFreeze> GetTurRTFreezeAList()
        //{
        //    List<AlarmStatus_TurbineFreeze> alarmStatusList = null;
        //    using (CMSFramework.EF.BladeMonContext ctx = new CMSFramework.EF.BladeMonContext(ConfigInfo.DBConnName))
        //    {
        //        alarmStatusList = ctx.BladeFreezeMonAlarm.ToList();
        //    }
        //    return alarmStatusList;
        //}

        /// <summary>
        /// 获取风场机组实时状态列表
        /// </summary>
        /// <param name="windParkID"></param>
        /// <returns></returns>
        public static List<AlarmStatus_Turbine> GetTurRTAlarmStatusList(string windParkID)
        {
            List<AlarmStatus_Turbine> alarmStatusList = new List<AlarmStatus_Turbine>();
            //List<string> turbineList = WTCMSLive.BusinessModel.DevTreeManagement.GetTurbinesListByWindParkId(windParkID).Select(t=>t.WindTurbineID).ToList();
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                //alarmStatusList = ctx.AlarmStatus_Turbines.Where(item => item.WindTurbineID.Contains(windParkID)).ToList();

                //alarmStatusList = (from at in ctx.AlarmStatus_Turbines
                //                   join t in turbineList on at.WindTurbineID equals t
                //                   select at).ToList();

                var asl = ctx.AlarmStatus_Turbines.ToList();
                if(asl!= null && asl.Count > 0)
                {
                    asl.ForEach(t =>
                    {
                        if (t.WindTurbineID.Contains(windParkID))
                        {
                            alarmStatusList.Add(t);
                        }
                    });
                }
                                 
            }
            return alarmStatusList;
        }
        //
        //public static List<AlarmStatus_TurbineFreeze> GetTurRTAlarmFreezeStatusList(string windParkID)
        //{
        //    List<AlarmStatus_TurbineFreeze> alarmStatusList = null;
        //    using (CMSFramework.EF.BladeMonContext ctx = new CMSFramework.EF.BladeMonContext(ConfigInfo.DBConnName))
        //    {
        //        //alarmStatusList = ctx.BladeFreezeMonAlarm.Where(item => item.WindTurbineID.Contains(windParkID)).ToList();
        //        alarmStatusList = ctx.BladeFreezeMonAlarm.ToList();
        //        alarmStatusList = alarmStatusList.Where(item => item.WindTurbineID.Contains(windParkID)).ToList();
        //    }
        //    return alarmStatusList;
        //}
        #region HNSY 定制网站调用
        /// <summary>
        /// 获取所有的测量位置实时列表
        /// </summary>
        /// <returns></returns>
        public static List<AlarmStatus_MeasLocVib> GetAlarmStatus_MeasLocVibList()
        {
            List<AlarmStatus_MeasLocVib> alarmStatusList = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                alarmStatusList = ctx.AlarmStatus_MeasLocVibs.ToList();
            }
            return alarmStatusList;
        }
        /// <summary>
        /// 获取机组下的测量位置状态
        /// </summary>
        /// <param name="WindTurbineId"></param>
        /// <returns></returns>
        public static List<AlarmStatus_MeasLocVib> GetAlarmStatus_MeasLocVibListByTurId(string WindTurbineId)
        {
            List<AlarmStatus_MeasLocVib> alarmStatusList = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                alarmStatusList = ctx.AlarmStatus_MeasLocVibs.Where(item => item.WindTurbineID == WindTurbineId).ToList();
            }
            return alarmStatusList;
        }

        public static List<AlarmEvent> GetAlarmEventList(string windParkID)
        {
            List<AlarmEvent> alarmStatusList = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                alarmStatusList = ctx.MonAlarmEvents.Where(item=>item.WindParkID == windParkID).ToList();
            }
            return alarmStatusList;
        }

        public static List<AlarmEventItem> GetAlarmEventItemList(DateTime begin, DateTime end)
        {
            List<AlarmEventItem> alarmStatusList = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                alarmStatusList = ctx.AlarmEventItems.Where(item => item.AlarmTime >= begin && item.AlarmTime <= end).ToList();
            }
            return alarmStatusList;
        }

        public static List<AlarmEventItem> GetAlarmEventItemList(string turbineId, DateTime begin, DateTime end)
        {
            List<AlarmEventItem> alarmStatusList = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                alarmStatusList = ctx.AlarmEventItems.Where(item => item.WindTurbineID == turbineId && item.AlarmTime >= begin && item.AlarmTime <= end).ToList();
            }
            return alarmStatusList;
        }

        public static List<AlarmEventItem> GetAlarmEventItemList()
        {
            List<AlarmEventItem> alarmStatusList = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                alarmStatusList = ctx.AlarmEventItems.ToList();
            }
            return alarmStatusList;
        }

        #endregion
    }
}
