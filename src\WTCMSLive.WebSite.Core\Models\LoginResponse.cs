using WTCMSLive.WebSite.Core.DTOs;

namespace WTCMSLive.WebSite.Core.Models
{
    public class LoginResponse
    {
        public string Token { get; set; }
        public string UserName { get; set; }
        public string UserId { get; set; }
        public string Role { get; set; }
        public DateTime Expiration { get; set; }

        /// <summary>
        /// 用户角色信息
        /// </summary>
        public RoleDTO UserRole { get; set; }

    }

    /// <summary>
    /// Token续签响应
    /// </summary>
    public class RefreshTokenResponse
    {
        /// <summary>
        /// 新的JWT Token
        /// </summary>
        public string Token { get; set; }

        /// <summary>
        /// Token过期时间
        /// </summary>
        public DateTime Expiration { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 用户角色
        /// </summary>
        public string Role { get; set; }
    }
}