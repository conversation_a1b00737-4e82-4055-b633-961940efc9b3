﻿using CMSFramework.BusinessEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace WTCMSLive.BusinessModel
{
    /// <summary>
    /// 数据库交互层 历史数据查询
    /// </summary>
    public class HistoryDataManage
    {
        #region 工况数据
        /// <summary>
        /// 获取测量定义下工况， 小时、天、趋势
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_mdfId"></param>
        /// <param name="_dateTime"></param>
        /// <returns></returns>
        public static List<WorkingConditionData> GetCondataList_His(string _turID, string _mdfId, DateTime _dateTime)
        {
            //小时和天表不存储工况，所以直接获取趋势中的数据返回
            //@wangy 2016年9月13日
            List<WorkingConditionData> wkConDataList = null;
            // 工况趋势库
            using (CMSFramework.EF.ValueDataContext_His ctx = new CMSFramework.EF.ValueDataContext_His(ConfigInfo.DBConnName))
            {
                wkConDataList = ctx.WorkingConditionDatas.Where(item => item.WindTurbineID == _turID).ToList();
            }
            return wkConDataList;
        }
        #endregion 工况数据
    }
}
