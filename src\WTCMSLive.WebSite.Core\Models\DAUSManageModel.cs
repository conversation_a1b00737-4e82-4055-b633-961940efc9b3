﻿using CMSFramework.BusinessEntity;
using WTCMSLive.BusinessModel;

namespace WTCMSLive.WebSite.Models
{
    public class DAUSManageModel
    {
        /// <summary>
        /// 根据机组id和采集单元id获取采集单元信息。
        /// </summary>
        /// <param name="turbineId"></param>
        /// <param name="dauId"></param>
        /// <returns></returns>
        public static WindDAU GetDAUByTrubineIdAndDauId(string turbineId,string dauId)
        {
            WindDAU dau = null;
            using(CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                dau = ctx.DAUnits.FirstOrDefault(p => p.WindTurbineID == turbineId && p.DauID == dauId);
                if (dau == null)
                    return dau;
                dau.RotSpeedChannelList = ctx.DAURotSpdChannels.Where(item => item.WindTurbineID == turbineId && item.DauID == dauId).ToList();
                dau.DAUChannelList = ctx.DAUVibChannels.Where(item => item.WindTurbineID == turbineId && item.DauID == dauId).ToList();
                dau.ProcessChannelList = ctx.DAUProcessChannels.Where(item => item.WindTurbineID == turbineId && item.DauID == dauId).ToList();
                dau.VoltageCurrentList = ctx.DauChannelVoltageCurrents.Where(item => item.WindTurbineID == turbineId && item.DauID == dauId).ToList();
                
            }
            return dau;
        }


        public static WindDAU GetDAUByTrubineIdAndDauName(string turbineId, string dauName)
        {
            WindDAU dau = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                dau = ctx.DAUnits.FirstOrDefault(p => p.WindTurbineID == turbineId && p.DAUName == dauName);
                if (dau == null)
                    return dau;
                dau.RotSpeedChannelList = ctx.DAURotSpdChannels.Where(item => item.WindTurbineID == turbineId && item.DauID == dau.DauID).ToList();
                dau.DAUChannelList = ctx.DAUVibChannels.Where(item => item.WindTurbineID == turbineId && item.DauID == dau.DauID).ToList();
                dau.ProcessChannelList = ctx.DAUProcessChannels.Where(item => item.WindTurbineID == turbineId && item.DauID == dau.DauID).ToList();
                dau.VoltageCurrentList = ctx.DauChannelVoltageCurrents.Where(item => item.WindTurbineID == turbineId && item.DauID == dau.DauID).ToList();

            }
            return dau;
        }


        /// <summary>
        /// 根据机组id，采集单元id删除dau。
        /// </summary>
        /// <param name="trubineId"></param>
        /// <returns></returns>
        public static bool DeleteDAUByTrubineIdAndDauId(string trubineId,string dauId)
        {
            DauManagement.DeleteUltrasonicChannel(trubineId, dauId);
            int count = 0;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.DAUnits.Remove(ctx.DAUnits.FirstOrDefault(obj => obj.WindTurbineID == trubineId && obj.DauID == dauId));
                try
                {
                    ctx.DAURotSpdChannels.RemoveRange(ctx.DAURotSpdChannels.Where(t => t.WindTurbineID == trubineId && t.DauID == dauId));
                    ctx.DAUVibChannels.RemoveRange(ctx.DAUVibChannels.Where(t => t.WindTurbineID == trubineId && t.DauID == dauId));
                    ctx.DAUProcessChannels.RemoveRange(ctx.DAUProcessChannels.Where(t => t.WindTurbineID == trubineId && t.DauID == dauId));

                    ctx.DauChannelVoltageCurrents.RemoveRange(ctx.DauChannelVoltageCurrents.Where(t => t.WindTurbineID == trubineId && t.DauID == dauId));
                }
                catch { }
                count = ctx.SaveChanges();
            }

            return count > 0;
        }

        /// <summary>
        /// 根据机组ID获取采集单元列表。
        /// </summary>
        /// <param name="turbineId"></param>
        /// <returns></returns>
        public static List<WindDAU> GetDAUListById(string turbineId)
        {
            List<WindDAU> daulist = new List<WindDAU>();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                try
                {
                    daulist = ctx.DAUnits.Where(obj => obj.WindTurbineID == turbineId).ToList();
                }
                catch(Exception ex)
                {

                }
                daulist.ForEach(obj =>
                {
                    obj.RotSpeedChannelList = ctx.DAURotSpdChannels.Where(item => item.WindTurbineID == turbineId && item.DauID == obj.DauID).ToList();
                    obj.DAUChannelList = ctx.DAUVibChannels.Where(item => item.WindTurbineID == turbineId && item.DauID == obj.DauID).ToList();
                    obj.ProcessChannelList = ctx.DAUProcessChannels.Where(item => item.WindTurbineID == turbineId && item.DauID == obj.DauID).ToList();
                    obj.VoltageCurrentList = ctx.DauChannelVoltageCurrents.Where(item => item.WindTurbineID == turbineId && item.DauID == obj.DauID).ToList();
                });
            };
            return daulist;
        }

        internal static int GetLastDauID(string turbineID)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                WindDAU lastDau = ctx.DAUnits.Where(p => p.WindTurbineID == turbineID).OrderByDescending(item => item.DauID).FirstOrDefault();
                if (lastDau != null)
                {
                    return Convert.ToInt32(lastDau.DauID);
                }
            }
            return 1;
        }

        /// <summary>
        /// 获取超声螺栓监测设备
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        internal static WindDAU GetBoltDevice(string turbineID)
        {
            WindDAU dau = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                dau = ctx.DAUnits.FirstOrDefault(obj => obj.WindTurbineID == turbineID && obj.DAUType == EnumDAUType.Ultrasonic);
            }
            return dau;
        }

        //public List<WindDAU> GetDAUListData(string turbineId)
        //{
        //    List<WindDAU> turbineDauList = GetDAUById(turbineId);
        //    turbineDauList.ForEach(obj =>
        //    {
        //        string windTurbineName = null;
        //        using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
        //        {
        //            windTurbineName = ctx.DevWindTurbines.FirstOrDefault(p => p.WindTurbineID == dauManager.WindTurbineID).WindTurbineName;
        //        }
        //        obj.win
        //    })
        //}
        /// <summary>
        /// 
        /// </summary>
        /// <param name="edit"></param>
        /// <param name="windParkId"></param>
        /// <returns></returns>
        public BaseTableModel GetDAUListByWindTurbineId(string turbineId)
        {
            BaseTableModel tableModel = new BaseTableModel();
            List<WindDAU> daulists = GetDAUListById(turbineId);
            tableModel.tableName = "DAUInfoTable";
            List<Rows> rows = new List<Rows>();
            for (int i = 0; i < daulists.Count; i++)
            {
                Rows cells = new Rows();
                cells.cells = CreateDAUListTableCell(daulists[i]);
                rows.Add(cells);
            }
            tableModel.rows = rows.ToArray();
            return tableModel;
        }
        /// <summary>
        /// 采集单元表格。
        /// </summary>
        /// <param name="dauManager"></param>
        /// <param name="edit"></param>
        /// <returns></returns>
        private Cell[] CreateDAUListTableCell(WindDAU dauManager)
        {
            List<Cell> cellList = new List<Cell>();
            //
            Cell cell00 = new Cell();
            cell00.type = "radio";
            cell00.title = dauManager.DauID;
            //机组ID
            Cell cell01 = new Cell();
            cell01.displayValue = dauManager.WindTurbineID;
            //采集单元ID
            Cell cell02 = new Cell();
            cell02.displayValue = dauManager.DauID;
            //机组名称
            Cell cell03 = new Cell();
            //cell02.displayValue = dauManager.DAUName;
            string windTurbineName = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                windTurbineName = ctx.DevWindTurbines.FirstOrDefault(p => p.WindTurbineID == dauManager.WindTurbineID).WindTurbineName;
            }
            cell03.displayValue = windTurbineName;
            //DAU名称
            Cell cell04 = new Cell();
            cell04.displayValue = dauManager.DAUName;
            //IP地址
            Cell cell05 = new Cell();
            cell05.displayValue = dauManager.IP;
            //采集间隔(分钟)
            Cell cell06 = new Cell();
            cell06.displayValue = dauManager.DataAcquisitionInterval.ToString();
            //状态
            Cell cell07 = new Cell();
            string isable = dauManager.IsAvailable == true ? "启用" : "禁用";
            cell07.type = "span";
            cell07.title = "nowStatus";
            cell07.displayValue = dauManager.IsAvailable == true ? "启用" : "禁用";
            //编辑
            Cell cell08 = new Cell();
            cell08.type = "btn";
            cell08.displayValue = "编辑";
            cell08.onclick = "showDauSetting('" + dauManager.WindTurbineID + "','" + dauManager.DauID + "','" + windTurbineName + "','" + dauManager.DAUName + "','" + dauManager.IP + "','" + dauManager.DataAcquisitionInterval + "','" + isable + "')";
            cell08.style = "btn btn-primary btn-sm btnEdit";
            //删除
            //Cell cell08 = new Cell();
            //cell08.type = "btn";
            //cell08.displayValue = "删除";
            //cell08.onclick = "deleteDAU('" + dauManager.WindTurbineID + "','" + dauManager.DauID + "',this)";
            //cell08.style = "btn btn-danger btn-sm btnDelete";
            ////名称
            //Cell cell09 = new Cell();
            //cell09.displayValue = dauManager.Name;
            cellList.Add(cell00);
            cellList.Add(cell01);
            cellList.Add(cell02);
            //cellList.Add(cell03);
            cellList.Add(cell04);
            cellList.Add(cell05);
            cellList.Add(cell06);
            cellList.Add(cell07);
            cellList.Add(cell08);
            //cellList.Add(cell09);
            return cellList.ToArray();
        }

        public static List<DAUChannelV2> GetDAUVibChannelList(string windturbineId,string DAUID)
        {
            List<DAUChannelV2> dauChannel = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                dauChannel = ctx.DAUVibChannels.Where(item => item.WindTurbineID == windturbineId && item.DauID == DAUID).ToList();
            }
            return dauChannel;
        }

        public static List<DAUChannel_VoltageCurrent> GetDAUVoltageCurrentChannelList(string windturbineId, string DAUID)
        {
            List<DAUChannel_VoltageCurrent> dauChannel = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                dauChannel = ctx.DauChannelVoltageCurrents.Where(item => item.WindTurbineID == windturbineId && item.DauID == DAUID).ToList();
            }
            return dauChannel;
        }

        /// <summary>
        /// 根据采集单元ID和机组ID获取采集单元实时状态列表。
        /// </summary>
        /// <param name="_windTurbineId"></param>
        /// <returns></returns>
        public static RTAlarmStatus_DAU GetDAURTAlarmStatusByWindTurbineIdAndDAUID(string _windTurbineId,string DAUID)
        {
            RTAlarmStatus_DAU alarmDau = null;
            using (CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                alarmDau = ctx.AlarmStatusRTDAUs.FirstOrDefault(obj => obj.WindTurbineID == _windTurbineId && obj.DauID == DAUID);
                if (alarmDau != null)
                    alarmDau.sensorRTList = ctx.AlarmStatusRTSensors.Where(item => item.WindTurbineID == _windTurbineId && item.DauID == DAUID).ToList();
            }
            return alarmDau;
        }
    }
}