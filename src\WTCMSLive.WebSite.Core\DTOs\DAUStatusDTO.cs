using CMSFramework.BusinessEntity;

namespace WTCMSLive.WebSite.Core.DTOs
{
    /// <summary>
    /// DAU状态信息DTO
    /// </summary>
    public class DAUStatusInfoDTO
    {
        /// <summary>
        /// 机组ID
        /// </summary>
        public string WindTurbineID { get; set; }

        /// <summary>
        /// DAU ID
        /// </summary>
        public string DAUID { get; set; }

        /// <summary>
        /// DAU名称
        /// </summary>
        public string DAUName { get; set; }

        /// <summary>
        /// DAU报警状态
        /// </summary>
        public int AlarmState { get; set; }

        /// <summary>
        /// DAU报警状态描述
        /// </summary>
        public string AlarmStateDescription { get; set; }

        /// <summary>
        /// 状态更新时间
        /// </summary>
        public DateTime StatusUpdateTime { get; set; }

        /// <summary>
        /// 传感器通道状态列表
        /// </summary>
        public List<DAUChannelStatusDTO> ChannelStatusList { get; set; } = new List<DAUChannelStatusDTO>();

        /// <summary>
        /// 转速通道状态列表
        /// </summary>
        public List<DAURotSpeedStatusDTO> RotSpeedStatusList { get; set; } = new List<DAURotSpeedStatusDTO>();
    }

    /// <summary>
    /// DAU通道状态DTO
    /// </summary>
    public class DAUChannelStatusDTO
    {
        /// <summary>
        /// 通道号
        /// </summary>
        public int ChannelNumber { get; set; }

        /// <summary>
        /// 测量位置ID
        /// </summary>
        public string MeasLocationID { get; set; }

        /// <summary>
        /// 测量位置名称
        /// </summary>
        public string MeasLocationName { get; set; }

        /// <summary>
        /// 通道报警状态
        /// </summary>
        public int AlarmState { get; set; }

        /// <summary>
        /// 通道报警状态描述
        /// </summary>
        public string AlarmStateDescription { get; set; }

        /// <summary>
        /// DC数据值
        /// </summary>
        public double DCDataValue { get; set; }

        /// <summary>
        /// 状态更新时间
        /// </summary>
        public DateTime StatusUpdateTime { get; set; }
    }

    /// <summary>
    /// DAU转速通道状态DTO
    /// </summary>
    public class DAURotSpeedStatusDTO
    {
        /// <summary>
        /// 通道号
        /// </summary>
        public int ChannelNumber { get; set; }

        /// <summary>
        /// 测量位置ID
        /// </summary>
        public string MeasLocationID { get; set; }

        /// <summary>
        /// 测量位置名称
        /// </summary>
        public string MeasLocationName { get; set; }

        /// <summary>
        /// 通道报警状态
        /// </summary>
        public int AlarmState { get; set; }

        /// <summary>
        /// 通道报警状态描述
        /// </summary>
        public string AlarmStateDescription { get; set; }

        /// <summary>
        /// 转速值
        /// </summary>
        public double RotSpeedValue { get; set; }

        /// <summary>
        /// 状态更新时间
        /// </summary>
        public DateTime StatusUpdateTime { get; set; }
    }
}
