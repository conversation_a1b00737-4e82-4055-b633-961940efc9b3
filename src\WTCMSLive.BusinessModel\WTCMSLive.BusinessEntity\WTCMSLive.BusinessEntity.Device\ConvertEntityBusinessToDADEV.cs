﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.BusinessEntity;
using WTCMSLive.Entity.Enum;
using WTCMSLive.Entity.Models;
using WTCMSLive.IDALService;

namespace WTCMSLive.BusinessEntityConvert
{
    // Author: ZhangMai
    // Create: 2013-09-02
    /// <summary>
    /// 实体转换， 从业务层实体转换为DA层实体
    /// </summary>
    public static class ConvertEntityBusinessToDADEV
    {
        static ISystemService sysSvc = AppFramework.ServiceBus.ServiceLocator.GetService<ISystemService>();
        static IMDFService mdfSvc = AppFramework.ServiceBus.ServiceLocator.GetService<IMDFService>();
        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 风场实体转换
        /// </summary>
        /// <param name="_windPark"></param>
        /// <returns></returns>
        public static DevWindPark ConvertDevWindPark(WindPark _windPark)
        {
            if (_windPark == null)
            {
                return null;
            }

            DevWindPark devWindpark = new DevWindPark();

            devWindpark.Address = _windPark.Address;
            devWindpark.ContactMan = _windPark.ContactMan;
            devWindpark.ContactTel = _windPark.ContactTel;
            devWindpark.Description = _windPark.Description;
            //devWindpark.devwindturbines = _windPark.WindTurbineList;
            devWindpark.OperationalDate = _windPark.OperationalDate;
            devWindpark.PostCode = _windPark.PostCode;
            //devWindpark.systemrunninglogs = null;
            devWindpark.WindParkCode = _windPark.WindParkCode;
            //风场ID为复合ID 结构是：公司字母+三位有效编号
            if (!string.IsNullOrEmpty(_windPark.WindParkID))
            {
                devWindpark.WindParkID = _windPark.WindParkID;
            }

            devWindpark.WindParkName = _windPark.WindParkName;

            return devWindpark;
        }

        public static DevWindTurbine ConvertDevWindTurbine(WindTurbine _tur)
        {
            if (_tur == null)
            {
                return null;
            }

            DevWindTurbine devWindTurbine = new DevWindTurbine();

            //devWindTurbine.IsAcquisitionPower = _tur.IsAcquisitionPower == true ? "1" : "0";
            devWindTurbine.MinWorkingRotSpeed = _tur.MinWorkingRotSpeed;
            devWindTurbine.OperationalDate = _tur.OperationalDate;

            if (!string.IsNullOrEmpty(_tur.WindParkID))
            {
                devWindTurbine.WindParkID = _tur.WindParkID;
            }

            devWindTurbine.WindTurbineCode = _tur.WindTurbineCode;

            devWindTurbine.WindTurbineID =_tur.WindTurbineID;
            devWindTurbine.WindTurbineModel = _tur.WindTurbineModel;
            devWindTurbine.WindTurbineName = _tur.WindTurbineName;
            devWindTurbine.AlarmStatusRTTurbine = new AlarmStatusRTTurbine();
            devWindTurbine.AlarmStatusRTTurbine.WindTurbineID = _tur.WindTurbineID;

            if (_tur.TurbineRTAlarmStatus != null)
            {
                devWindTurbine.AlarmStatusRTTurbine = ConvertBusinessEntityToDARTStatus.ConvertDeviceRTAlarmStatus(_tur.TurbineRTAlarmStatus);
            }

            if (_tur.TurComponentList != null)
            {
                if (_tur.TurComponentList.Count != 0)
                {
                    foreach (WindTurbineComponent turRT in _tur.TurComponentList)
                    {
                        turRT.WindTurbineID = devWindTurbine.WindTurbineID.ToString();
                    }
                }
            }

            devWindTurbine.DevTurComponents = ConvertWindTurbineComponentList(_tur.TurComponentList);

            if (_tur.MeasLocSVMList != null && devWindTurbine.DevTurComponents.Count > 0)
            {
                //找到部件机舱
                foreach (DevTurComponent com in devWindTurbine.DevTurComponents.OrderBy(item=>item.OrderSeq))
                {
                    if (com.ComponentType == "机舱"|| com.ComponentType=="塔筒")
                    {
                        foreach (WTCMSLive.BusinessEntity.SVM.MeasLoc_SVM meas in _tur.MeasLocSVMList)
                        {
                            com.SVMMeasLocations.Add(ConvertEntityBusinessToDASVM.ConvertMeasLocation(meas));
                        }
                        break;
                    }
                }
            }
            if (_tur.ProcessMeasLocList != null)
            {
                if (_tur.ProcessMeasLocList.Count != 0)
                {
                    foreach (MeasLoc_Process measLocProcess in _tur.ProcessMeasLocList)
                    {
                        measLocProcess.WindTurbineID = devWindTurbine.WindTurbineID.ToString();
                    }
                }
            }

            devWindTurbine.DevMeasLocProcesses = ConvertDevMeasLocProcessList(_tur.ProcessMeasLocList);

            if (_tur.Mcs != null)
            {
                _tur.Mcs.WindTurbineID = _tur.WindTurbineID;
                _tur.Mcs.MCSID = _tur.WindTurbineID;
                devWindTurbine.MCSystem = ConvertEntityBusinessToDADEV.ConvertMCSystem(_tur.Mcs);
            }

            if (ConvertDevMeasLocRotSpd(_tur.RotSpdMeasLoc) != null)
            {
                _tur.RotSpdMeasLoc.WindTurbineID = _tur.WindTurbineID;
                devWindTurbine.DevMeasLocRotSpds.Add(ConvertDevMeasLocRotSpd(_tur.RotSpdMeasLoc));
            }

            devWindTurbine.DAUnit = null;
            return devWindTurbine;
        }


        public static List<DevWindTurbine> ConvertDevWindTurbineList(List<WindTurbine> _turList)
        {
            if (_turList == null)
            {
                return null;
            }

            List<DevWindTurbine> devWindTurbineList = new List<DevWindTurbine>();

            foreach (WindTurbine item in _turList)
            {
                DevWindTurbine devWindTurbine = ConvertDevWindTurbine(item);

                devWindTurbineList.Add(devWindTurbine);
            }

            return devWindTurbineList;
        }

        public static DevMeasLocProcess ConvertDevMeasLocProcess(MeasLoc_Process _pro)
        {
            if (_pro == null)
            {
                return null;
            }

            DevMeasLocProcess devMeasLocProcess = new DevMeasLocProcess();

            devMeasLocProcess.FieldBusType =(sbyte)_pro.FieldBusType;

            if (!string.IsNullOrEmpty(_pro.MeasLocationID))
            {
                devMeasLocProcess.MeasLocationID = _pro.MeasLocationID;
            }

            devMeasLocProcess.MeasLocationName = _pro.MeasLocName;
            devMeasLocProcess.OrderSeq = _pro.OrderSeq;
            devMeasLocProcess.ParamTypeCode = short.Parse(_pro.Param_Type_Code);

            if (!string.IsNullOrEmpty(_pro.WindTurbineID))
            {
                devMeasLocProcess.WindTurbineID = _pro.WindTurbineID;
            }
            return devMeasLocProcess;
        }

        public static List<DevMeasLocProcess> ConvertDevMeasLocProcessList(List<MeasLoc_Process> _pro)
        {
            if (_pro == null)
            {
                return null;
            }

            List<DevMeasLocProcess> list = new List<DevMeasLocProcess>();

            foreach (MeasLoc_Process item in _pro)
            {

                //DevMeasLocProcess devMeasLocProcess = new DevMeasLocProcess();

                //devMeasLocProcess.dauprocesschannels = null;
                //devMeasLocProcess.FieldBusType = Convert.ToInt32(item.FieldBusType).ToString();
                //devMeasLocProcess.mcsregisters = null;

                //if (!string.IsNullOrEmpty(item.MeasLocationID))
                //{
                //    devMeasLocProcess.MeasLocationID = int.Parse(item.MeasLocationID);
                //}

                //devMeasLocProcess.MeasLocationName = item.MeasLocName;
                //devMeasLocProcess.OrderSeq = item.OrderSeq;

                //if (!string.IsNullOrEmpty(item.WindTurbineID))
                //{
                //    devMeasLocProcess.WindTurbineID = int.Parse(item.WindTurbineID);
                //}

                // modified by whr time 2013-10-9
                list.Add(
                    ConvertDevMeasLocProcess(item)
                    );
            }

            return list;
        }


        public static DevMeasLocRotSpd ConvertDevMeasLocRotSpd(MeasLoc_RotSpd _rotSpd)
        {
            if (_rotSpd == null)
            {
                return null;
            }

            DevMeasLocRotSpd devMeasLocRotSpd = new DevMeasLocRotSpd();

            devMeasLocRotSpd.GearRatio = (decimal)_rotSpd.GearRatio;

            if (!string.IsNullOrEmpty(_rotSpd.MeasLocationID))
            {
                devMeasLocRotSpd.MeasLocationID = _rotSpd.MeasLocationID;
            }

            devMeasLocRotSpd.MeasLocationName = _rotSpd.MeasLocName;

            if (!string.IsNullOrEmpty(_rotSpd.WindTurbineID))
            {
                devMeasLocRotSpd.WindTurbineID = _rotSpd.WindTurbineID;
            }

            devMeasLocRotSpd.CoderLineCounts = Convert.ToInt32(_rotSpd.LineCounts);

            return devMeasLocRotSpd;
        }
        /// <summary>
        /// 转换转速测量定义
        /// </summary>
        /// <param name="_list">MeasLoc_RotSpd</param>
        /// <returns></returns>
        public static List<DevMeasLocRotSpd> ConvertDevMeasLocRotSpdList(List<MeasLoc_RotSpd> _list)
        {
            List<DevMeasLocRotSpd> devMeasLocRotSpdList = new List<DevMeasLocRotSpd>();

            foreach (MeasLoc_RotSpd item in _list)
            {
                DevMeasLocRotSpd entity = ConvertDevMeasLocRotSpd(item);

                if (entity != null)
                {
                    devMeasLocRotSpdList.Add(entity);
                }
            }

            return devMeasLocRotSpdList;
        }

        public static DevMeasLocVibration ConvertDevMeasLocVibration(MeasLoc_Vib _vib)
        {
            DevMeasLocVibration devMeasLocVibration = new DevMeasLocVibration();

            if (!string.IsNullOrEmpty(_vib.WindTurbineID))
            {
                devMeasLocVibration.WindTurbineID = _vib.WindTurbineID;
            }
            if (!string.IsNullOrEmpty(_vib.ComponentID))
            {
                devMeasLocVibration.ComponentID = _vib.ComponentID;
            }
            devMeasLocVibration.GearRatio = (decimal)_vib.GearRatio;

            if (!string.IsNullOrEmpty(_vib.MeasLocationID))
            {
                devMeasLocVibration.MeasLocationID = _vib.MeasLocationID;
            }
            devMeasLocVibration.Code = _vib.MeasLocationID.Substring(_vib.WindTurbineID.Length);
            devMeasLocVibration.MeasLocationName = _vib.MeasLocName;
            devMeasLocVibration.OrderSeq = _vib.OrderSeq;
            devMeasLocVibration.Orientation = _vib.Orientation;
            devMeasLocVibration.SectionName = _vib.SectionName;

            if (!string.IsNullOrEmpty(_vib.WindTurbineID))
            {
                devMeasLocVibration.WindTurbineID = _vib.WindTurbineID;
            }
            if (_vib.LocRTAlarmStatus != null)
            {
                AlarmStatusRTMeasLoc alarmStatusRTMeasLoc = new AlarmStatusRTMeasLoc();

                if (!string.IsNullOrEmpty(_vib.LocRTAlarmStatus.WindTurbineID))
                {
                    alarmStatusRTMeasLoc.WindTurbineID = _vib.LocRTAlarmStatus.WindTurbineID;
                }
                if (!string.IsNullOrEmpty(_vib.ComponentID))
                {
                    alarmStatusRTMeasLoc.ComponentID = _vib.ComponentID;
                }
                if (!string.IsNullOrEmpty(_vib.MeasLocationID))
                {
                    alarmStatusRTMeasLoc.MeasLocationID = _vib.MeasLocationID;
                }

                alarmStatusRTMeasLoc.AlarmDegree = (short)_vib.LocRTAlarmStatus.AlarmDegree;
                alarmStatusRTMeasLoc.AlarmUpdateTime = _vib.LocRTAlarmStatus.AlarmUpdateTime;
                devMeasLocVibration.AlarmStatusRTMeasLoc = alarmStatusRTMeasLoc;
            }


            return devMeasLocVibration;
        }

        public static List<DevMeasLocVibration> ConvertDevMeasLocVibList(List<MeasLoc_Vib> _list)
        {
            if (_list == null)
            {
                return null;
            }

            List<DevMeasLocVibration> measList = new List<DevMeasLocVibration>();

            foreach (MeasLoc_Vib item in _list)
            {
                DevMeasLocVibration entity = ConvertDevMeasLocVibration(item);
                if (entity != null)
                {
                    measList.Add(entity);
                }
            }

            return measList;
        }

        public static MCSystem ConvertMCSystem(MCS _mcs)
        {
            if (_mcs == null)
            {
                return null;
            }

            MCSystem mcSystem = new MCSystem();

            mcSystem.FieldBusType = _mcs.FieldBusType;
            mcSystem.MCSIP = _mcs.MCSIP;
            mcSystem.MCSPort = _mcs.MCSPort;
            if (_mcs.MCSChannelStateList != null || _mcs.MCSChannelValueList != null)
            {
                List<MCSRegister> mcsRegisterList = new List<MCSRegister>();
                if (_mcs.MCSChannelStateList != null)
                {
                    foreach (MCSChannelStateParam item in _mcs.MCSChannelStateList)
                    {
                        item.MCSID = _mcs.MCSID;
                        mcsRegisterList.Add(ConvertMCSRegisterState(item));
                    }
                }
                if (_mcs.MCSChannelValueList != null)
                {
                    foreach (MCSChannelValueParam item in _mcs.MCSChannelValueList)
                    {
                        item.MCSID = _mcs.MCSID;
                        mcsRegisterList.Add(ConvertMCSRegisterValue(item));
                    }
                }

                mcSystem.MCSRegisters = mcsRegisterList;
            }

            if (_mcs.WindTurbineID != null)
            {
                mcSystem.WindTurbineID = _mcs.WindTurbineID;
            }

            return mcSystem;
        }

        public static MCSRegister ConvertMCSRegister(MCSChannel _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            MCSRegister mcRegister = new MCSRegister();

            mcRegister.ByteArrayType = (short)_entity.ByteArrayType;
            mcRegister.ChannelNumber = int.Parse(_entity.ChannelNumber);
            mcRegister.DataType = (short)_entity.DataType;

            if (!string.IsNullOrEmpty(_entity.MeasLocProcessID))
            {
                mcRegister.MeasLocationID = _entity.MeasLocProcessID;
            }

            mcRegister.ParamMeaning = _entity.ParamMeaning;
            mcRegister.RegisterStorageType = (short)_entity.RegisterStorageType;
            mcRegister.RegisterType = (short)_entity.RegisterType;

            if (!string.IsNullOrEmpty(_entity.MCSID))
            {
                mcRegister.WindTurbineID = _entity.MCSID;
            }
            mcRegister.MCSRegisterState = null;
            mcRegister.MCSRegisterValue = null;

            return mcRegister;
        }

        public static MCSRegister ConvertMCSRegisterState(MCSChannelStateParam _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            MCSRegister mcRegister = new MCSRegister();
            mcRegister.ByteArrayType = (short)_entity.ByteArrayType;
            mcRegister.ChannelNumber = int.Parse(_entity.ChannelNumber);
            mcRegister.DataType = (short)_entity.DataType;

            if (!string.IsNullOrEmpty(_entity.MeasLocProcessID))
            {
                mcRegister.MeasLocationID = _entity.MeasLocProcessID;
            }

            mcRegister.ParamMeaning = _entity.ParamMeaning;
            mcRegister.RegisterStorageType = (short)_entity.RegisterStorageType;
            mcRegister.RegisterType = (short)_entity.RegisterType;

            if (!string.IsNullOrEmpty(_entity.MCSID))
            {
                mcRegister.WindTurbineID = _entity.MCSID;
            }

            MCSRegisterState mcRegisterState = new MCSRegisterState();
            mcRegisterState.JudgeType = (short)_entity.JudgeType;
            mcRegisterState.ParamMeaning = _entity.ParamMeaning;
            string stateValue = string.Join(",", _entity.StateValues);
            //string strSign = "";
            //foreach (short item in _entity.StateValues)
            //{
            //    stateValue += strSign + item.ToString();
            //    strSign = ",";
            //}
            mcRegisterState.StateValues = stateValue;
            if (!string.IsNullOrEmpty(_entity.MCSID))
            {
                mcRegisterState.WindTurbineID = _entity.MCSID;
            }
            mcRegister.MCSRegisterState = mcRegisterState;

            return mcRegister;
        }

        public static MCSRegister ConvertMCSRegisterValue(MCSChannelValueParam _entity)
        {
            if (_entity == null) return null;

            MCSRegister mcRegister = new MCSRegister();

            mcRegister.ByteArrayType = (short)(_entity.ByteArrayType);
            mcRegister.ChannelNumber = int.Parse(_entity.ChannelNumber);
            mcRegister.DataType = (short)(_entity.DataType);

            if (!string.IsNullOrEmpty(_entity.MeasLocProcessID))
            {
                mcRegister.MeasLocationID = _entity.MeasLocProcessID;
            }

            mcRegister.ParamMeaning = _entity.ParamMeaning;
            mcRegister.RegisterStorageType = (short)(_entity.RegisterStorageType);
            mcRegister.RegisterType = (short)(_entity.RegisterType);

            if (!string.IsNullOrEmpty(_entity.MCSID))
            {
                mcRegister.WindTurbineID = _entity.MCSID;
            }
            // 数据寄存器
            MCSRegisterValue mcRegisterValue = new MCSRegisterValue();
            mcRegisterValue.ConvertCoefficient = (decimal)_entity.Coeff;
            mcRegisterValue.ParamMeaning = _entity.ParamMeaning;
            if (!string.IsNullOrEmpty(_entity.MCSID))
            {
                mcRegisterValue.WindTurbineID = _entity.MCSID;
            }

            mcRegister.MCSRegisterValue = mcRegisterValue;

            // 状态寄存器

            return mcRegister;
        }

        public static OViewTurStyle ConvertOViewTurStyle(TurbineStyle _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            OViewTurStyle ovTurStyle = new OViewTurStyle();

            ovTurStyle.StyleText = _entity.StyleText;
            ovTurStyle.StyleVersion = _entity.StyleVersion;
            ovTurStyle.TurbineModel = _entity.TurbineModel;

            return ovTurStyle;
        }

        /// <summary>
        /// 查找特征值编号
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        private static string FindEigenValueCode(EigenValue_FreqBand _entity)
        {
            if (_entity.FreqBandCode != null)
            {
                EigenValue_FreqBand fb = EigenValue_FreqBand.EigenValueFreqBandList()
             .Find(g => g.FreqBandCode == _entity.FreqBandCode);
                if (fb != null)
                {
                    return fb.EigenValueCode;
                }
            }
            if (_entity.EigenValueName != null)
            {
                EigenValue_FreqBand fb = EigenValue_FreqBand.EigenValueFreqBandList()
                            .Find(g => g.EigenValueName == _entity.EigenValueName);
                if (fb != null)
                {
                    return fb.EigenValueCode;
                }
            }
            return null;
        }

        /// <summary>
        /// 转换机组部件
        /// </summary>
        /// <param name="_comp">WindTurbineComponent</param>
        /// <returns></returns>
        public static DevTurComponent ConvertDevTurComponent(WindTurbineComponent _comp)
        {
            DevTurComponent devTurComponent = new DevTurComponent();

            devTurComponent.AlarmStatusRTComponent = null;

            if (_comp.ComponentID != null)
            {
                devTurComponent.ComponentID = _comp.ComponentID;
            }

            devTurComponent.ComponentModel = _comp.ComponentModel;
            devTurComponent.ComponentType = _comp.ComponentName;
            if (_comp.VibMeasLocList != null && _comp.VibMeasLocList.Count > 0)
            {
                devTurComponent.WindTurbineID = _comp.VibMeasLocList.First().WindTurbineID;
            }
            // 添加振动测量位置
            devTurComponent.DevMeasLocVibrations = ConvertDevMeasLocVibList(_comp.VibMeasLocList);
            devTurComponent.Manufacturer = _comp.CompManufacturer;
            devTurComponent.OrderSeq = _comp.OrderSeq;

            if (_comp.WindTurbineID != null)
            {
                devTurComponent.WindTurbineID = _comp.WindTurbineID;
            }
            if (_comp.CompRTAlarmStatus != null)
            {
                AlarmStatusRTComponent alarmStatusRTComponent = new AlarmStatusRTComponent();

                alarmStatusRTComponent.ComponentID = devTurComponent.ComponentID;

                if (_comp.WindTurbineID != null)
                {
                    alarmStatusRTComponent.WindTurbineID = _comp.WindTurbineID;
                }

                alarmStatusRTComponent.AlarmUpdateTime = DateTime.Now;

                alarmStatusRTComponent.AlarmDegree = (short)_comp.CompRTAlarmStatus.AlarmDegree;

                devTurComponent.AlarmStatusRTComponent = alarmStatusRTComponent;
            }

            return devTurComponent;
        }

        /// <summary>
        /// 转换机组部件列表
        /// </summary>
        /// <param name="_list">WindTurbineComponent</param>
        /// <returns></returns>
        public static List<DevTurComponent> ConvertWindTurbineComponentList(List<WindTurbineComponent> _list)
        {
            if (_list == null)
            {
                return null;
            }

            List<DevTurComponent> _DevTurComponentList = new List<DevTurComponent>();

            foreach (WindTurbineComponent item in _list)
            {
                DevTurComponent entity = ConvertDevTurComponent(item);
                _DevTurComponentList.Add(entity);
            }

            return _DevTurComponentList;
        }

        #region  部件相关 
        /// <summary>
        ///  机型部件列表实体转换
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static List<WindTurbineComponent> ConvertTurbineModelComponentList(List<WTMComp> list)
        {
            List<WindTurbineComponent> myDevList = new List<WindTurbineComponent>();
            foreach (WTMComp myModel in list)
            {
                WindTurbineComponent turComponent = new WindTurbineComponent();
                turComponent.ComponentName = myModel.ComponentType;// Werida.Common.EnumHelper.GetDescription(comModel.ComType);
                turComponent.ComponentModel = myModel.ComponentModel;
                turComponent.CompManufacturer = myModel.Manufacturer;
                myDevList.Add(turComponent);
            }
            return myDevList;
        }
        /// <summary>
        /// 部件实时表转换
        /// </summary>
        /// <param name="_windTurbine"></param>
        /// <returns></returns>
        public static List<AlarmStatusRTComponent> ConvertComponentStatus(WindTurbine _windTurbine)
        {
            List<AlarmStatusRTComponent> List = null;
            if (_windTurbine != null && _windTurbine.TurComponentList != null && _windTurbine.TurComponentList.Count() > 0)
            {
                List = new List<AlarmStatusRTComponent>();
                foreach (WindTurbineComponent component in _windTurbine.TurComponentList)
                {
                    AlarmStatusRTComponent myComponentStatus = new AlarmStatusRTComponent();
                    myComponentStatus.WindTurbineID = _windTurbine.WindTurbineID;
                    myComponentStatus.ComponentID = component.ComponentID;
                    myComponentStatus.AlarmUpdateTime = DateTime.Now;
                    List.Add(myComponentStatus);
                }
            }
            return List;
        }

        #endregion

        #region 报警定义相关
        /// <summary>
        /// 添加报警定义信息
        /// </summary>
        /// <param name="myDefinList"></param>
        /// <param name="_MeasLocType"></param>
        public static List<MDFAlarmDefThresholdGroup> ConvertBusinessToAlarmGroup(List<AlarmDefinition> myDefinList, string _MeasLocType)
        { 
            AlarmDefinition myDefinition = myDefinList[0];
            MDFAlarmDef myAlarmDef = new MDFAlarmDef();
            myAlarmDef.WindTurbineID = myDefinition.WindTurbineID;
            myAlarmDef.MeasLocType =sbyte.Parse(_MeasLocType);
            myAlarmDef.MeasLocationID = myDefinition.MeasLocationID;
            myAlarmDef.EigenValueID = myDefinition.EigenValueName;
            myAlarmDef.WorkConParameter = sbyte.Parse(myDefinition.OutPowerBandCode.ToString());
            myAlarmDef.ThresholdGroup = System.Guid.NewGuid().ToString("N");
            myAlarmDef.UpperLimitValue = decimal.Parse(myDefinition.MaxOutPower.ToString());
            myAlarmDef.LowerLimitValue = decimal.Parse(myDefinition.MinOutPower.ToString());
            mdfSvc.AddMDFAlarmDef(myAlarmDef);

            List<MDFAlarmDefThresholdGroup> myGroupList = new List<MDFAlarmDefThresholdGroup>();
            foreach(AlarmDefinition myDefin in myDefinList)
            {
                //判断报警定义是否存在
                MDFAlarmDefThresholdGroup myGroup = new MDFAlarmDefThresholdGroup();
                myGroup.AlarmDegree = short.Parse(myDefin.AlarmDegree);
                myGroup.ThresholdValue = decimal.Parse(myDefin.AlarmValue.ToString());
                myGroup.WindTurbineID = myDefinition.WindTurbineID;
                myGroup.ThresholdGroup = myAlarmDef.ThresholdGroup;
                myGroupList.Add(myGroup);
            }
            return myGroupList;
        }
        /// <summary>
        /// 修改报警阈值
        /// </summary>
        /// <param name="myDefinList"></param>
        public static List<MDFAlarmDefThresholdGroup> ConvertBusinessToMDFAlarmDefThresholdGroup(List<AlarmDefinition> myDefinList)
        {
            List<MDFAlarmDefThresholdGroup> list = new List<MDFAlarmDefThresholdGroup>();
            foreach (AlarmDefinition myDefin in myDefinList)
            {
                MDFAlarmDefThresholdGroup myGroup = new MDFAlarmDefThresholdGroup();
                myGroup.WindTurbineID = myDefin.WindTurbineID;
                myGroup.ThresholdGroup = myDefin.MeasDefinitionID;
                myGroup.AlarmDegree = short.Parse(myDefin.AlarmDegree);
                myGroup.ThresholdValue = decimal.Parse(myDefin.AlarmValue.ToString());
                list.Add(myGroup);
            }
            return list;
        }
        /// <summary>
        /// 删除报警定义
        /// </summary>
        /// <param name="myDefinition"></param>
        public static List<MDFAlarmDef> ConvertBusinessToAlarmDefinition(AlarmDefinition myDefinition)
        {
            MDFAlarmDef myMDFAlarmDef = new MDFAlarmDef();
            myMDFAlarmDef.WindTurbineID = myDefinition.WindTurbineID;
            myMDFAlarmDef.MeasLocationID = myDefinition.MeasLocationID;
            myMDFAlarmDef.EigenValueID = myDefinition.EigenValueID;
            myMDFAlarmDef.WorkConParameter =sbyte.Parse(myDefinition.OutPowerBandCode.ToString());
            myMDFAlarmDef.ThresholdGroup = myDefinition.MeasDefinitionID;
            return new List<MDFAlarmDef>() { myMDFAlarmDef };
        }
        #endregion

    }
}
