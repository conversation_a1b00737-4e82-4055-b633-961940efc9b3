using CMSFramework.BusinessEntity;

namespace WTCMSLive.WebSite.Core.DTOs
{
    /// <summary>
    /// DAU运行日志DTO
    /// </summary>
    public class DAULogDTO
    {
        /// <summary>
        /// 机组ID
        /// </summary>
        public string WindTurbineID { get; set; }

        /// <summary>
        /// DAU ID
        /// </summary>
        public string DauId { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 报警状态值
        /// </summary>
        public int AlarmState { get; set; }

        /// <summary>
        /// 报警状态描述
        /// </summary>
        public string AlarmStateDescription { get; set; }

        /// <summary>
        /// 日志标题/描述
        /// </summary>
        public string LogTitle { get; set; }

        /// <summary>
        /// 格式化的事件时间字符串
        /// </summary>
        public string EventTimeFormatted { get; set; }
    }
}
