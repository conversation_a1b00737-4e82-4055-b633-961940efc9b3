import{O as w}from"./index-CzSbT6op.js";import{u as I}from"./account-fT7nLAGf.js";import{dk as _,r as d,c as g,b as h,o as v,m as u}from"./index-BjOW8S1L.js";import{_ as b}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";import"./styleChecker-CFtINSLw.js";import"./index-7iPMz_Qy.js";import"./initDefaultProps-P4j1rGDC.js";import"./index-DTxROkTj.js";import"./shallowequal-gCpTBdTi.js";import"./index-BJEkaghg.js";const y={class:"container"},P={__name:"password",setup(x){const l=I(),m=_();let s=window.localStorage.getItem("user"),t={};s&&(t=JSON.parse(s));const c=d({username:t.username,userId:t.userId}),a=d(null),p=async r=>{const o={...r,account:t.userId},e=await l.fetchChangePasswordr(o);e&&e.code==1?(u.success("修改密码成功！请重新登录！"),m.logout()):u.error("修改密码失败！")},f=[{title:"账号",dataIndex:"userId",formItemWidth:300,autocomplete:"userId",disabled:!0},{title:"用户名",dataIndex:"username",formItemWidth:300,isrequired:!0,autocomplete:"username"},{title:"密码",dataIndex:"oldPassword",inputType:"password",visibilityToggle:!1,isrequired:!0,formItemWidth:300,autocomplete:"current-password"},{title:"新密码",dataIndex:"password1",inputType:"password",visibilityToggle:!1,isrequired:!0,formItemWidth:300,autocomplete:"new-password"},{title:"再次输入",dataIndex:"newPassword",inputType:"password",visibilityToggle:!1,isrequired:!0,formItemWidth:300,autocomplete:"new-password",validateRules:[{validator:async(r,o)=>{var n;const e=(n=a.value)==null?void 0:n.getFieldsValue();if(!e||!e.password1||!e.newPassword)return;let i=e.password1===e.newPassword;return i?Promise.resolve(i):Promise.reject(new Error("两次输入的密码不一致！"))},trigger:"change"}]}];return(r,o)=>(v(),g("div",y,[h(w,{ref_key:"formModalRef",ref:a,titleCol:f,initFormData:c.value,onSubmit:p,buttonsAlign:"center"},null,8,["initFormData"])]))}},E=b(P,[["__scopeId","data-v-925e75ba"]]);export{E as default};
