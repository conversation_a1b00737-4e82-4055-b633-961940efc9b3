import{dk as y,y as b,r as B,j as p,c as S,i as n,b as o,d as t,a as x,u as F,o as k,g as I,t as N,m as i,dr as R}from"./index-BjOW8S1L.js";import{_ as T}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{F as U,_ as V}from"./index-CpBSPak5.js";import{I as $,p as q}from"./index-DTxROkTj.js";import{B as z}from"./index-7iPMz_Qy.js";import"./styleChecker-CFtINSLw.js";import"./initDefaultProps-P4j1rGDC.js";const L="/logo.svg",P={class:"login-container"},j={__name:"Login",setup(A){const m=y(),c=x();F();const a=b({username:"",password:""}),r=B(!1),d=async()=>{try{r.value=!0;let e=await m.fetchlogin(a);e.code==1?(i.success("登录成功"),R(),await new Promise(s=>setTimeout(s,0)),c.push("/")):i.error(e.msg)}catch(e){console.error("登录失败:",e),i.error(e.message||"登录失败，请稍后重试")}finally{r.value=!1}},g=p(()=>[{required:!0,message:"账号不能为空!",trigger:["change"]},{pattern:/\S/,message:"账号不能为空字符串!"}]),_=p(()=>[{required:!0,message:"密码不能为空!",trigger:["change"]},{pattern:/\S/,message:"密码不能为空字符串!"}]);return(e,s)=>{const f=$,l=V,v=q,h=z,w=U;return k(),S("div",P,[s[2]||(s[2]=n("div",{class:"logoBox"},[n("div",{class:"header"},[n("img",{src:L,class:"logo",alt:"logo"}),n("span",{class:"title"},"配置网站")])],-1)),o(w,{class:"user-layout-login",model:a,onFinish:d},{default:t(()=>[o(l,{name:"username",rules:g.value},{default:t(()=>[o(f,{size:"large",type:"text",autocomplete:"username",placeholder:e.$t("user.login.username.placeholder"),value:a.username,"onUpdate:value":s[0]||(s[0]=u=>a.username=u)},null,8,["placeholder","value"])]),_:1},8,["rules"]),o(l,{name:"password",rules:_.value},{default:t(()=>[o(v,{size:"large",placeholder:e.$t("user.login.password.placeholder"),autocomplete:"current-password",value:a.password,"onUpdate:value":s[1]||(s[1]=u=>a.password=u)},null,8,["placeholder","value"])]),_:1},8,["rules"]),o(l,{style:{"margin-top":"24px"}},{default:t(()=>[o(h,{type:"primary",htmlType:"submit",class:"login-button",loading:r.value,disabled:r.value},{default:t(()=>[I(N(e.$t("user.login.login")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["model"])])}}},M=T(j,[["__scopeId","data-v-c07766a6"]]);export{M as default};
