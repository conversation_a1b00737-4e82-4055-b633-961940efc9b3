﻿
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Reflection;
using System.ComponentModel;
using AppFramework.Utility;
using CMSFramework.BusinessEntity;
using System;

namespace WTCMSLive.BusinessModel
{
    /// <summary>
    /// 公共参考数据
    /// </summary>
    public static class CommonRefData
    {
        /// <summary>
        /// 获取信号类型
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, string>> GetSignalTypeList()
        {
            return new List<KeyValuePair<string, string>> { 
                new KeyValuePair<string, string>("加速度","加速度"), 
            };
        }


        /// <summary>
        /// 获取下限频率列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, float>> GetLowerLimitFreqList()
        {
            return new List<KeyValuePair<string, float>> { 
                new KeyValuePair<string, float>("0.16 Hz",0.16F), 
                new KeyValuePair<string, float>("2 Hz", 2), 
                new KeyValuePair<string, float>("10 Hz", 10),
                new KeyValuePair<string, float>("50 Hz", 50), 
                new KeyValuePair<string, float>("100 Hz", 100), 
                new KeyValuePair<string, float>("1.25K Hz", 1250), 
                new KeyValuePair<string, float>("2.5K Hz", 2500), 
                new KeyValuePair<string, float>("5K Hz", 5000), 
            };
        }


        /// <summary>
        /// 获取上限频率列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, float>> GetUpperLimitFreqList()
        {
            return new List<KeyValuePair<string, float>> { 
                new KeyValuePair<string, float>("1K Hz", 1000),
                new KeyValuePair<string, float>("2K Hz", 2000),
                new KeyValuePair<string, float>("4K Hz", 4000),
                new KeyValuePair<string, float>("5K Hz", 5000),
                new KeyValuePair<string, float>("8K Hz", 8000),
                new KeyValuePair<string, float>("10k Hz", 10000),
                new KeyValuePair<string, float>("16K Hz", 16000),
                new KeyValuePair<string, float>("25k Hz", 25000),
                new KeyValuePair<string, float>("40k Hz", 40000),
                new KeyValuePair<string, float>("50k Hz", 50000),
                new KeyValuePair<string, float>("100 Hz", 100F),
                new KeyValuePair<string, float>("200 Hz", 200),
                new KeyValuePair<string, float>("500 Hz", 500),
               // new KeyValuePair<string, float>("2K Hz", 2000),
            };
        }

        public static List<KeyValuePair<string, float>> GetAMS_Process_UpperLimitFreqList()
        {
            return new List<KeyValuePair<string, float>> { 
                new KeyValuePair<string, float>("1k Hz", 1000),
                new KeyValuePair<string, float>("2k Hz", 2000),
                new KeyValuePair<string, float>("4k Hz", 4000), 
                new KeyValuePair<string, float>("8k Hz", 8000),
                new KeyValuePair<string, float>("20k Hz", 20000), 
                new KeyValuePair<string, float>("100 Hz", 100F),
                new KeyValuePair<string, float>("200 Hz", 200), 
                new KeyValuePair<string, float>("500 Hz", 500)
            };
        }

        public static List<KeyValuePair<string, float>> GetAMS_CMS_UpperLimitFreqList()
        {
            return new List<KeyValuePair<string, float>> { 
                new KeyValuePair<string, float>("1 Hz", 1F),
                new KeyValuePair<string, float>("10 Hz", 10F),
                new KeyValuePair<string, float>("100 Hz", 100F),
                new KeyValuePair<string, float>("200 Hz", 200), 
                new KeyValuePair<string, float>("500 Hz", 500),
                new KeyValuePair<string, float>("1k Hz", 1000), 
                new KeyValuePair<string, float>("2k Hz", 2000), 
                new KeyValuePair<string, float>("4k Hz", 4000), 
                new KeyValuePair<string, float>("5k Hz", 5000), 
                new KeyValuePair<string, float>("8k Hz", 8000),
                new KeyValuePair<string, float>("10k Hz", 10000), 
                new KeyValuePair<string, float>("20k Hz", 20000), 
                new KeyValuePair<string, float>("40k Hz", 40000), 
            };
        }


        /// <summary>
        /// 获取包络上限频率列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, float>> GetParamUpperLimitFreqList()
        {
            return new List<KeyValuePair<string, float>> { 
                new KeyValuePair<string, float>("500 Hz", 500),
                new KeyValuePair<string, float>("1K Hz", 1000), 
                new KeyValuePair<string, float>("2K Hz", 2000)
            };
        }


        /// <summary>
        /// 获取采样时间长度列表， 单位：秒
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, int>> GetSampleTimeLengthList()
        {
            return new List<KeyValuePair<string, int>> { 
                new KeyValuePair<string, int>("1 秒", 1),
                new KeyValuePair<string, int>("3 秒", 3),
                new KeyValuePair<string, int>("4 秒", 4),
                new KeyValuePair<string, int>("5 秒", 5),
                new KeyValuePair<string, int>("10 秒", 10),
                new KeyValuePair<string, int>("15 秒", 15),
                new KeyValuePair<string, int>("20 秒", 20),
                new KeyValuePair<string, int>("30 秒", 30),
                new KeyValuePair<string, int>("40 秒", 40),
                new KeyValuePair<string, int>("50 秒", 50),
                new KeyValuePair<string, int>("90 秒", 90),
            };
        }

        /// <summary>
        /// 获取晃度仪支持的采样频率列表，单位：Hz
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, float>> GetSVMAcquisitionFrequency()
        {
            return new List<KeyValuePair<string, float>> { 
                new KeyValuePair<string, float>("0.25 Hz", 0.25f),
                new KeyValuePair<string, float>("0.5 Hz", 0.5f),
                new KeyValuePair<string, float>("1 Hz", 1),
                new KeyValuePair<string, float>("2 Hz", 2),
                new KeyValuePair<string, float>("4 Hz", 4),
                new KeyValuePair<string, float>("8 Hz", 8),
                new KeyValuePair<string, float>("16 Hz", 16),
                new KeyValuePair<string, float>("32 Hz", 32),
                new KeyValuePair<string, float>("64 Hz", 64),
                new KeyValuePair<string, float>("128 Hz", 128),
                new KeyValuePair<string, float>("256 Hz", 256),
            };
        }


        /// <summary>
        /// 获取每周期采样点数
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, int>> GetDotNumberPerCycleList()
        {
            return new List<KeyValuePair<string, int>> { 
                new KeyValuePair<string, int>("8", 8),
                new KeyValuePair<string, int>("16", 16),
                new KeyValuePair<string, int>("32", 32),
                new KeyValuePair<string, int>("64", 64),
                new KeyValuePair<string, int>("128", 128),
                new KeyValuePair<string, int>("256", 256),
            };
        }


        /// <summary>
        /// 获取采样周期数
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, int>> GetCycleNumberList()
        {
            return new List<KeyValuePair<string, int>> { 
                new KeyValuePair<string, int>("10", 10),
                new KeyValuePair<string, int>("20", 20),
                new KeyValuePair<string, int>("32", 32),
                new KeyValuePair<string, int>("50", 50),
                new KeyValuePair<string, int>("64", 64),
                new KeyValuePair<string, int>("100", 100),
                new KeyValuePair<string, int>("128", 128),
                new KeyValuePair<string, int>("200", 200),
                new KeyValuePair<string, int>("256", 256),
            };
        }



        /// <summary>
        /// 获取包络带宽列表
        /// 包络解调高通滤波器和包络带宽之间的约束关系是：
        /// 包络带宽不能大于包络高通滤波器的截止频率，
        /// 比如：高通滤波器的截止频率500Hz，则包络带宽的选择不能大于500Hz。
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, int>> GetEnvelopeBandWidthList()
        {
            return new List<KeyValuePair<string, int>> { 
                new KeyValuePair<string, int>("10 Hz", 10),
                new KeyValuePair<string, int>("20 Hz", 20),
                new KeyValuePair<string, int>("50 Hz", 50),
                new KeyValuePair<string, int>("100 Hz", 100),
                new KeyValuePair<string, int>("200 Hz", 200),
                new KeyValuePair<string, int>("500 Hz", 500),
                new KeyValuePair<string, int>("800 Hz", 800),
                new KeyValuePair<string, int>("1K Hz", 1000),
                new KeyValuePair<string, int>("2K Hz", 2000),
                new KeyValuePair<string, int>("5K Hz", 5000),
                new KeyValuePair<string, int>("10K Hz", 10000),
            };
        }



        /// <summary>
        /// 获取包络滤波器列表
        /// 500Hz, 1KHz, 2KHz, 5KHz，10KHz，20KHz 
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, int>> GetEnvelopeFilterList()
        {
            return new List<KeyValuePair<string, int>> { 
                new KeyValuePair<string, int>("500 Hz", 500),
                new KeyValuePair<string, int>("1K Hz", 1000),
                new KeyValuePair<string, int>("2K Hz", 2000),
                new KeyValuePair<string, int>("5K Hz", 5000),
                new KeyValuePair<string, int>("10K Hz", 10000),
                //new KeyValuePair<string, int>("20K Hz", 20000),
            };
        }


        /// <summary>
        /// 获取分析阶次列表
        /// 阶次包络设置选项
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, float>> GetAnalysisOrderList()
        {
            return new List<KeyValuePair<string, float>> { 
                new KeyValuePair<string, float>("12.5 阶", 12.5F),
                new KeyValuePair<string, float>("25 阶", 25), 
                new KeyValuePair<string, float>("50 阶", 50), 
                new KeyValuePair<string, float>("100 阶", 100),
                new KeyValuePair<string, float>("200 阶", 200), 
                new KeyValuePair<string, float>("400 阶", 400), 
            };
        }


        /// <summary>
        /// 获取测量位置方向列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, string>> GetLocOrientionList()
        {
            return new List<KeyValuePair<string, string>> { 
                new KeyValuePair<string, string>("水平","水平"), 
                new KeyValuePair<string, string>("水平3点","水平3点"), 
                new KeyValuePair<string, string>("水平9点","水平9点"),
                new KeyValuePair<string, string>("垂直","垂直"),
                new KeyValuePair<string, string>("垂直6点","垂直6点"),
                new KeyValuePair<string, string>("垂直12点","垂直12点"),
                new KeyValuePair<string, string>("轴向","轴向"),
                new KeyValuePair<string, string>("径向","径向"),
            };
        }



        /// <summary>
        /// 获取部件截面列表
        /// </summary>
        /// <param name="_comType"></param>
        /// <returns></returns>
        public static List<KeyValuePair<string, string>> GetComSectionList(string _comType)
        {
            if (_comType == "齿轮箱")
            {
                return new List<KeyValuePair<string, string>> { 
                    new KeyValuePair<string, string>("无","无"), 
                    new KeyValuePair<string, string>("输入轴","输入轴"), 
                    new KeyValuePair<string, string>("输出轴","输出轴"), 
                    new KeyValuePair<string, string>("主轴承","主轴承"), 
                    new KeyValuePair<string, string>("第一级","第一级"),
                    new KeyValuePair<string, string>("第二级","第二级"),
                    new KeyValuePair<string, string>("第三级","第三级"),
                    new KeyValuePair<string, string>("内齿圈","内齿圈"), 
                    new KeyValuePair<string, string>("低速轴","低速轴"),
                    new KeyValuePair<string, string>("中间轴","中间轴"),
                    new KeyValuePair<string, string>("高速轴","高速轴"),
                };

            }

            return new List<KeyValuePair<string, string>> { 
                    new KeyValuePair<string, string>("无","无"), 
                    new KeyValuePair<string, string>("驱动端","驱动端"), 
                    new KeyValuePair<string, string>("非驱动端","非驱动端"),
                };
        }

        /// <summary>
        /// 获取工况数据来源列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, EnumWorkConDataSource>> GetWorkConditionDataSourceList()
        {
            return new List<KeyValuePair<string, EnumWorkConDataSource>>
            {
                new KeyValuePair<string, EnumWorkConDataSource>(
                    EnumHelper.GetDescription(EnumWorkConDataSource.ModbusOnTcp),EnumWorkConDataSource.ModbusOnTcp), 

                new KeyValuePair<string, EnumWorkConDataSource>(
                    EnumHelper.GetDescription(EnumWorkConDataSource.WindDAU),EnumWorkConDataSource.WindDAU),  
            };
        }

        /// <summary>
        /// 获取特征值计算基于的波形类型列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string,EnumWaveFormType>> GetBaseWaveTypeList()
        {
            return new List<KeyValuePair<string, EnumWaveFormType>>
            {
                new KeyValuePair<string, EnumWaveFormType>(                    
                    EnumHelper.GetDescription(EnumWaveFormType.WDF_Time),EnumWaveFormType.WDF_Time), 
                new KeyValuePair<string, EnumWaveFormType>(
                    EnumHelper.GetDescription(EnumWaveFormType.WDF_OrderEnvelope), EnumWaveFormType.WDF_OrderEnvelope), 
            };
        }

        /// <summary>
        /// 获取寄存器类型列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, EnumMCSChannelType>> GetMCSChannelTypeList()
        {
            return new List<KeyValuePair<string, EnumMCSChannelType>>
            {
                new KeyValuePair<string, EnumMCSChannelType>(                    
                    EnumHelper.GetDescription(EnumMCSChannelType.Input_Register),EnumMCSChannelType.Input_Register),
                new KeyValuePair<string, EnumMCSChannelType>(
                    EnumHelper.GetDescription(EnumMCSChannelType.Hold_Register), EnumMCSChannelType.Hold_Register), 
            };
        }

        /// <summary>
        /// 获取寄存器数据储存方式列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, EnumMCSChannelStorageType>> GetMCSChannelStorageTypeList()
        {
            return new List<KeyValuePair<string, EnumMCSChannelStorageType>>
            {
                new KeyValuePair<string, EnumMCSChannelStorageType>(                    
                    EnumHelper.GetDescription(EnumMCSChannelStorageType.DataInteger),EnumMCSChannelStorageType.DataInteger),
                new KeyValuePair<string, EnumMCSChannelStorageType>(
                    EnumHelper.GetDescription(EnumMCSChannelStorageType.DataFloat), EnumMCSChannelStorageType.DataFloat), 
            };
        }

        /// <summary>
        /// 获取寄存器字节序模式列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, EnumMCSChannelByteArrayType>> GetMCSChannelByteArrayTypeList()
        {
            return new List<KeyValuePair<string, EnumMCSChannelByteArrayType>>
            {
                new KeyValuePair<string, EnumMCSChannelByteArrayType>(                    
                    EnumHelper.GetDescription(EnumMCSChannelByteArrayType.Big_Endian),EnumMCSChannelByteArrayType.Big_Endian),
                new KeyValuePair<string, EnumMCSChannelByteArrayType>(
                    EnumHelper.GetDescription(EnumMCSChannelByteArrayType.Little_Endian), EnumMCSChannelByteArrayType.Little_Endian), 
            };
        }

        /// <summary>
        /// 获取寄存器判断方式列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, EnumMCSChannelJudgeType>> GetMCSChannelJudgeTypeList()
        {
            return new List<KeyValuePair<string, EnumMCSChannelJudgeType>>
            {
                new KeyValuePair<string, EnumMCSChannelJudgeType>(                    
                    EnumHelper.GetDescription(EnumMCSChannelJudgeType.Equal),EnumMCSChannelJudgeType.Equal),
                new KeyValuePair<string, EnumMCSChannelJudgeType>(
                    EnumHelper.GetDescription(EnumMCSChannelJudgeType.NotEqual), EnumMCSChannelJudgeType.NotEqual),
                new KeyValuePair<string, EnumMCSChannelJudgeType>(
                    EnumHelper.GetDescription(EnumMCSChannelJudgeType.InBound), EnumMCSChannelJudgeType.InBound), 
                new KeyValuePair<string, EnumMCSChannelJudgeType>(
                    EnumHelper.GetDescription(EnumMCSChannelJudgeType.OutBound), EnumMCSChannelJudgeType.OutBound), 
            };
        }

        /// <summary>
        /// 获取寄存器数据类型类型列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, EnumMCSChannelDataType>> GetMCSChannelDataTypeList()
        {
            return new List<KeyValuePair<string, EnumMCSChannelDataType>>
            {
                new KeyValuePair<string, EnumMCSChannelDataType>(                    
                    EnumHelper.GetDescription(EnumMCSChannelDataType.Data),EnumMCSChannelDataType.Data),
                new KeyValuePair<string, EnumMCSChannelDataType>(
                    EnumHelper.GetDescription(EnumMCSChannelDataType.Status), EnumMCSChannelDataType.Status), 
            };
        }

        /// <summary>
        /// 获取边频簇列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, byte>> GetSideFreqNumberList()
        {
            return new List<KeyValuePair<string, byte>> { 
                new KeyValuePair<string, byte>("1 - 2 X", 2), 
                new KeyValuePair<string, byte>("1 - 3 X", 3), 
                new KeyValuePair<string, byte>("1 - 4 X", 4),
                new KeyValuePair<string, byte>("1 - 5 X", 5)
            };
        }


        /// <summary>
        /// 获取谐波簇列表
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, byte>> GetHarmonicNumberList()
        {
            return new List<KeyValuePair<string, byte>> { 
                new KeyValuePair<string, byte>("1 - 2 X", 2), 
                new KeyValuePair<string, byte>("1 - 3 X", 3), 
                new KeyValuePair<string, byte>("1 - 4 X", 4),
                new KeyValuePair<string, byte>("1 - 5 X", 5),
                new KeyValuePair<string, byte>("1 - 6 X", 6),
                new KeyValuePair<string, byte>("1 - 7 X", 7),
                new KeyValuePair<string, byte>("1 - 8 X", 8),
                new KeyValuePair<string, byte>("1 - 9 X", 9),
                new KeyValuePair<string, byte>("1 - 10 X", 10)
            };
        }

        /// <summary>
        /// 获取当前数据库版本
        /// </summary>
        /// <returns></returns>
        public static int GetCurrentDBVersion()
        {
            // 获取当前的数据库版本
            Global global = TemplateManage.GetGlobal();
            string dbVersionStr = global.DatabaseVersion.Replace(".", "");
            int dbVersion = Convert.ToInt32(dbVersionStr);
            return dbVersion;
        }


        public static Dictionary<string, int> MeasEVType = new Dictionary<string, int>()
        {
                {"DC",1},        //直流分量
	            {"RMS",2},       //有效值
	            {"PK",3},        //峰值
	            {"PPK",4},       //峰峰值
	            {"KTS",5},       //峭度指标
	            {"CF",6},        //峰值指标
	            {"SK",7},        //偏度指标
	            {"IF",8},       //脉冲指标
	            {"LF",9},        //裕度指标
	            {"SF",10},      //波形指标
	            {"gPKm",11},     //包络最大值
	            {"gPKc",12},     //包络最小值
	            {"gPKmean",13},  //包络平均值
	            {"FBE",14},      //频带特征值
	            {"NBE",15},      //窄带特征值
	            {"SpdMax",16},   //转速最大值
	            {"SpdMin",17},   //转速最小值
	            {"SpdMean",18},  //转速平均值
                {"Order_RMS",19}, //阶次波形的有效值
                {"Order_PK",20}, //阶次波形的峰值
                {"Order_KTS",21},//阶次波形的峭度指标
                {"Order_SK",22}, //阶次波形的偏度指标
                {"Order_CF",23}, //峰值指标
	            {"SingleOrder",24}, //单一阶窄带特征值
	            {"HFC",25},      //谐频簇窄带特征值
	            {"SFC",26},      //边频簇窄带特征值
	            {"WorkEnv",27},	//工况特征值 [ADD] 触发采集项目
	            {"TDR",28},       //叶片有效能量
	            {"TDK",29},       //叶片冲击系数
	            {"TDF",30},       //叶片呼啸因子
	            {"BSC",31},       //叶片谱相关系数
	            {"PB ",32},       //桨距角偏差系数
	            {"TSI1",33},      //塔筒晃动系数
	            {"TSI2",34},      //塔筒晃动系数
	            {"TSI3",35},       //塔筒晃动系数
	            {"ABSAVG",36}, //平均幅值
	            {"RAVG",37}, //方根幅值
	            {"MAX",38},       //最大值
	            {"MIN",39},        //最小值
	            {"Temperature",40}, //温度
	            {"AVG",41},     //减去均值之后再求均值
	            {"X_Angle",42},   //X轴角度
	            {"Y_Angle",43},    //Y轴角度
	            {"Spectrum_peak",44}, //频谱峰值
	            {"Spectrum_peak_Freq",45}, //频谱峰值频率
        };
    }
}