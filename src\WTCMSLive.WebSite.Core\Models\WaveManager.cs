﻿using CMSFramework.BusinessEntity;
using CMSFramework.EF;
using CMSFramework.FSDB;
using System.ComponentModel;
using System.Runtime.Serialization;

namespace WTCMSLive.WebSite.Models
{
    public class WaveManager
    {
        public VibWaveFormData GetWaveFromData(string windTurbineId, string _measLocID, EnumWaveFormType _WFType, DateTime _acqTime)
        {
            VibWaveFormData waveFormData = null;
            // 小时
            waveFormData = GetVibWaveFormData(EnumDataSource.HourDB, windTurbineId, _measLocID, _WFType, _acqTime);
            if (waveFormData == null)
            {
                // 天
                waveFormData = GetVibWaveFormData(EnumDataSource.DayDB, windTurbineId, _measLocID, _WFType, _acqTime);
            }
            if (waveFormData == null)
            {
                // 历史库获取
                waveFormData = GetVibWaveFormData(EnumDataSource.HisDB, windTurbineId, _measLocID, _WFType, _acqTime);
            }
            if (waveFormData == null)
            {
                // 报警
                waveFormData = GetVibWaveFormData(EnumDataSource.AlarmDB, windTurbineId, _measLocID, _WFType, _acqTime);
            }
            return waveFormData;
        }
        private static VibWaveFormData GetVibWaveFormData(EnumDataSource dbType, string windTurbineId, string _measLocID, EnumWaveFormType _WFType, DateTime _acqTime)
        {
            VibWaveFormData waveFormData = null;

            using (WFDataContext ctx = WFDataContext.Factory(dbType, ConfigInfo.DBConnName))
            {
                //ctx.Database.Log = (str) => { System.Diagnostics.Debug.Write(str); };

                waveFormData = ctx.VibWaveForms.FirstOrDefault(wf =>
                    wf.AcquisitionTime == _acqTime && wf.MeasLocationID == _measLocID
                    && wf.WindTurbineID == windTurbineId);
            }
            return waveFormData;
        }

        /// <summary>
        /// 获取振动波形数组
        /// </summary>
        /// <param name="_waveDataPath"></param>
        /// <param name="_waveDefID"></param>
        /// <returns></returns>
        private byte[] GetVibWaveBytes(string _waveDataPath, string _waveDefID)
        {
            using (IFSDBStorage firebird = FSDBStorageFactory.GetFSDBStorage(_waveDataPath))
            {
                return firebird.GetVibWave(int.Parse(_waveDefID));
            }
        }
        /// <summary>
        /// 根据机组id、测量位置id、波形类型、采集时间 获取波形数组
        /// </summary>
        /// <param name="windTurbineId"></param>
        /// <param name="_measLocID"></param>
        /// <param name="_WFType"></param>
        /// <param name="_acqTime"></param>
        /// <returns></returns>
        public byte[] GetWaveFormData(VibWaveFormData waveFormData)
        {
            if (waveFormData != null)
            {
                return GetVibWaveBytes(waveFormData.WaveDataPath, waveFormData.WaveDefinitionID);
            }
            return null;
        }

        public enum MyEnumWaveFormType
        {
            [EnumMember]
            [Description("加速度")]
            Acceleration = 0,

            [EnumMember]
            [Description("速度")]
            Velocity = 1,

            [EnumMember]
            [Description("位移")]
            Displacement = 2,

            [EnumMember]
            [Description("电压")]
            Voltage = 3,

            [EnumMember]
            [Description("电流")]
            Current = 4,
        }
    }
    
}