using Microsoft.AspNetCore.SignalR;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core.Models.DTOs;
using WTCMSLive.WebSite.Core.Services;

namespace RealtimePerf.Hubs;

public sealed class ServerPerformanceHub : Hub
{
    private readonly ILogger<ServerPerformanceHub> _logger;
    private readonly IServiceLogService _serviceLogService;

    public ServerPerformanceHub(
        ILogger<ServerPerformanceHub> logger,
        IServiceLogService serviceLogService)
    {
        _logger = logger;
        _serviceLogService = serviceLogService;
    }

    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("[ServerPerformanceHub]客户端连接: {ConnectionId}", Context.ConnectionId);
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("[ServerPerformanceHub]客户端断开连接: {ConnectionId}, 异常: {Exception}",
            Context.ConnectionId, exception?.Message);

        // 清理该连接的所有实时日志监控
        await CleanupConnectionLogMonitors(Context.ConnectionId);

        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// 客户端请求加入特定组
    /// </summary>
    /// <param name="groupName">组名</param>
    /// <returns></returns>
    public async Task JoinGroup(string groupName)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("[ServerPerformanceHub]客户端 {ConnectionId} 加入组 {GroupName}",
            Context.ConnectionId, groupName);
    }

    /// <summary>
    /// 客户端请求离开特定组
    /// </summary>
    /// <param name="groupName">组名</param>
    /// <returns></returns>
    public async Task LeaveGroup(string groupName)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("[ServerPerformanceHub]客户端 {ConnectionId} 离开组 {GroupName}",
            Context.ConnectionId, groupName);
    }

    /// <summary>
    /// 获取当前连接的客户端数量
    /// </summary>
    /// <returns></returns>
    public async Task GetConnectionCount()
    {
        // 这里可以实现获取连接数的逻辑，如果需要的话
        await Clients.Caller.SendAsync("ConnectionCount", "Connected");
    }

    /// <summary>
    /// 开始实时日志监控
    /// </summary>
    /// <param name="serviceId">服务ID</param>
    /// <param name="logType">日志类型</param>
    /// <returns></returns>
    public async Task StartRealTimeLog(string serviceId, string logType)
    {
        try
        {
            _logger.LogInformation("[ServerPerformanceHub]开始实时日志监控: {ConnectionId}, {ServiceId}, {LogType}",
                Context.ConnectionId, serviceId, logType);

            var result = await _serviceLogService.StartRealTimeLogAsync(serviceId, logType, Context.ConnectionId);

            if (result)
            {
                await Clients.Caller.SendAsync("RealTimeLogStarted", new { serviceId, logType, success = true });
            }
            else
            {
                await Clients.Caller.SendAsync("RealTimeLogStarted", new { serviceId, logType, success = false, message = "启动失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[ServerPerformanceHub]开始实时日志监控失败: {ConnectionId}, {ServiceId}, {LogType}",
                Context.ConnectionId, serviceId, logType);
            await Clients.Caller.SendAsync("RealTimeLogStarted", new { serviceId, logType, success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// 停止实时日志监控
    /// </summary>
    /// <param name="serviceId">服务ID</param>
    /// <param name="logType">日志类型</param>
    /// <returns></returns>
    public async Task StopRealTimeLog(string serviceId, string logType)
    {
        try
        {
            _logger.LogInformation("[ServerPerformanceHub]停止实时日志监控: {ConnectionId}, {ServiceId}, {LogType}",
                Context.ConnectionId, serviceId, logType);

            var result = await _serviceLogService.StopRealTimeLogAsync(serviceId, logType, Context.ConnectionId);

            if (result)
            {
                await Clients.Caller.SendAsync("RealTimeLogStopped", new { serviceId, logType, success = true });
            }
            else
            {
                await Clients.Caller.SendAsync("RealTimeLogStopped", new { serviceId, logType, success = false, message = "停止失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[ServerPerformanceHub]停止实时日志监控失败: {ConnectionId}, {ServiceId}, {LogType}",
                Context.ConnectionId, serviceId, logType);
            await Clients.Caller.SendAsync("RealTimeLogStopped", new { serviceId, logType, success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// 清理连接的日志监控器
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns></returns>
    private async Task CleanupConnectionLogMonitors(string connectionId)
    {
        try
        {
            // 这里需要实现清理逻辑，由于ServiceLogService中没有提供清理特定连接的方法
            // 我们可以通过尝试停止所有可能的监控来实现
            // 实际实现中，可以在ServiceLogService中添加一个CleanupConnection方法
            _logger.LogInformation("[ServerPerformanceHub]清理连接的日志监控器: {ConnectionId}", connectionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[ServerPerformanceHub]清理连接的日志监控器失败: {ConnectionId}", connectionId);
        }
    }
}