﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.BusinessEntity;
using WTCMSLive.Entity.Models;

namespace WTCMSLive.BusinessEntityConvert
{
    public static class ConvertBusinessEntityToDARTStatus
    {
		/// <summary>
        /// 机组实时状态 
		/// </summary>
        /// <param name="_entity">DeviceRTAlarmStatus</param>
		/// <returns></returns>
        public static AlarmStatusRTTurbine ConvertDeviceRTAlarmStatus(DeviceRTAlarmStatus _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.AlarmStatusRTTurbine _DeviceRTAlarmStatus = new WTCMSLive.Entity.Models.AlarmStatusRTTurbine();

            _DeviceRTAlarmStatus.AlarmDegree = (short)_entity.AlarmDegree;
            _DeviceRTAlarmStatus.AlarmUpdateTime = _entity.AlarmUpdateTime;
            //_DeviceRTAlarmStatus.WindTurbineID = Convert.ToInt32(_entity.DevSegmentID);
            _DeviceRTAlarmStatus.WindTurbineID = _entity.WindTurbineID;
            //alarmStatusRT_Turbine.AlarmType;
            return _DeviceRTAlarmStatus;
        }

        public static WTCMSLive.Entity.Models.AlarmEvent ConvertAlarmEvent(WTCMSLive.BusinessEntity.AlarmEvent _alarmEvent)
        {
            if (_alarmEvent == null) return null;

            WTCMSLive.Entity.Models.AlarmEvent alarmEvent = new WTCMSLive.Entity.Models.AlarmEvent();

            if (_alarmEvent.WindTurbineID != null)
            {
                alarmEvent.WindTurbineID = _alarmEvent.WindTurbineID;
            }

            alarmEvent.AlarmTime = _alarmEvent.AlarmTime;
            alarmEvent.WindParkID = _alarmEvent.WindParkID;
            alarmEvent.WindTurbineID = _alarmEvent.WindTurbineID;
            alarmEvent.AlarmDegree = (short)_alarmEvent.AlarmDegree;
            alarmEvent.AlarmEventState = short.Parse(_alarmEvent.AlarmEventState);

            return alarmEvent;
        }

        public static AlarmEventItem ConvertAlarmEventItem(WTCMSLive.BusinessEntity.AlarmItem _alarmEvtItem)
        {
            if (_alarmEvtItem == null) return null;

            AlarmEventItem alarmItem = new AlarmEventItem();

            if (_alarmEvtItem.WindTurbineID != null)
            {
                alarmItem.WindTurbineID = _alarmEvtItem.WindTurbineID;
            }

            alarmItem.AlarmTime = _alarmEvtItem.AlarmTime;
            alarmItem.BeginTime = _alarmEvtItem.BeginTime;
            alarmItem.EndTime = _alarmEvtItem.EndTime;
            alarmItem.AlarmDegree = (short)_alarmEvtItem.AlarmDegree;

            return alarmItem;
        }

        /// <summary>
        /// 转换诊断报告
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.TurbineReportDiagnosi ConvertTurReportDiagnosis(TurbineDiagnosisReport _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.TurbineReportDiagnosi item = new WTCMSLive.Entity.Models.TurbineReportDiagnosi();

            item.Advice = _entity.Advice;
            item.AnalyzeConclusion = _entity.AnalyzeConclusion;
            item.DiagnosisTime = _entity.DiagnosisTime;
            item.DiagnosisUser = _entity.DiagnosisUser;
            item.ExtensionFileName = _entity.FileNameExtension;

            //item.IsUpload = 
            //item.ReportID =
            //item.ReportLocPath = 
            if (!string.IsNullOrEmpty(_entity.WindTurbineID.ToString()))
            {
                item.WindTurbineID = _entity.WindTurbineID;
            }
            item.WordReport = _entity.WordReport;

            return item;
        }

        /// <summary>
        /// 转换维修报告
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.TurbineReportMaintain ConvertTurReportMaintain(TurbineMaintainReport _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.TurbineReportMaintain item = new WTCMSLive.Entity.Models.TurbineReportMaintain();

            item.Advice = _entity.Advice;
            //item.AlarmEventHoldType = _entity.AnalyzeConclusion;
            //item.AlarmTime = _entity.DiagnosisTime;
            item.HandleTime = _entity.HandleTime;
            item.HandleUser = _entity.HandleUser;
            item.ExtensionFileName = _entity.FileNameExtension;

            //item.Hasreport = 
            //item.IsUpload = 
            //item.ReportID =
            //item.ReportLocPath = 
            if (!string.IsNullOrEmpty(_entity.WindTurbineID.ToString()))
            {
                item.WindTurbineID = _entity.WindTurbineID;
            }
            item.WordReport = _entity.WordReport;

            return item;
        }

        /// <summary>
        /// 转换部件
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.AlarmStatusRTComponent ConvertCompRTAlarmStatus(DeviceRTAlarmStatus _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.AlarmStatusRTComponent _DeviceRTAlarmStatus = new WTCMSLive.Entity.Models.AlarmStatusRTComponent();

            _DeviceRTAlarmStatus.AlarmDegree = (short)_entity.AlarmDegree;
            _DeviceRTAlarmStatus.AlarmUpdateTime = _entity.AlarmUpdateTime;
            _DeviceRTAlarmStatus.ComponentID = _entity.DevSegmentID;
            _DeviceRTAlarmStatus.WindTurbineID = _entity.WindTurbineID;
            //alarmStatusRT_Turbine.AlarmType;
            return _DeviceRTAlarmStatus;
        }

        
        /// <summary>
        /// 转换AlarmEventHandleLog  
        /// </summary>
        /// <param name="_entity">WTCMSLive.Entity.Models.AlarmEventHandleLog实体</param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.AlarmEventHandleLog ConvertAlarmEventHanLog(
            WTCMSLive.BusinessEntity.AlarmEventHandleLog _entity)
        {
            if (_entity == null) return null;

            WTCMSLive.Entity.Models.AlarmEventHandleLog item = new WTCMSLive.Entity.Models.AlarmEventHandleLog();

            item.AlarmTime = _entity.AlarmTime;
            item.HandleTime = _entity.HandleTime;
            item.HandleUser = _entity.HandleUser;

            if (!string.IsNullOrEmpty(_entity.WindTurbineID))
            {
                item.WindTurbineID = _entity.WindTurbineID;
            }

            item.WorkDescription = _entity.WorkDescription;

            return item;
        }

        /// <summary>
        /// 转换AlarmEventHandleLog列表
        /// </summary>
        /// <param name="_list">List<WTCMSLive.Entity.Models.AlarmEventHandleLog>列表</param>
        /// <returns></returns>
        public static List<WTCMSLive.Entity.Models.AlarmEventHandleLog> ConvertAlarmEventHanLogList(
            List<WTCMSLive.BusinessEntity.AlarmEventHandleLog> _list)
        {
            List<WTCMSLive.Entity.Models.AlarmEventHandleLog> itemList = new List<WTCMSLive.Entity.Models.AlarmEventHandleLog>();

            foreach (WTCMSLive.BusinessEntity.AlarmEventHandleLog item in _list)
            {
                itemList.Add(ConvertAlarmEventHanLog(item));
            }

            return itemList;
        }

        /// <summary>
        /// 转换机组总貌图样式
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static OViewTurStyle ConvertTurbineStyle(TurbineStyle _entity)
        {
            if (_entity == null)
            {
                return null;
            }
            OViewTurStyle oviewTurStyle = new OViewTurStyle();
            oviewTurStyle.StyleText = _entity.StyleText;
            oviewTurStyle.StyleVersion = _entity.StyleVersion;
            oviewTurStyle.TurbineModel = _entity.TurbineModel;
            return oviewTurStyle;
        }

        /// <summary>
        /// 转换机组图片
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static OViewTurImage ConvertTurbineImage(TurbineImage _entity)
        {
            if (_entity == null)
            {
                return null;
            }
            OViewTurImage oviewTurImage = new OViewTurImage();
            oviewTurImage.ImageData = _entity.ImageData;
            oviewTurImage.TurbineModel = _entity.TurbineModel;
            return oviewTurImage;
        }

        /// <summary>
        /// 转换标签组
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static OViewLabelGroup ConvertLabelGroup(LabelGroup _entity)
        {
            if (_entity == null)
            {
                return null;
            }
            OViewLabelGroup item = new OViewLabelGroup();
            item.Direction= _entity.Direction;
            if (!string.IsNullOrEmpty(_entity.LabelGroupID))
            {
                item.LabelGroupID = _entity.LabelGroupID;
            }
            item.LabelGroupName = _entity.LabelGroupName;
            item.LabelHeight= _entity.LabelHeight;
            item.LabelWidth= _entity.LabelWidth;
            item.PositionLeft= _entity.Left;
            item.PositionTop= _entity.Top;
            item.WindTurbineID = _entity.WindTurbineID;
            if (_entity.LabelDataList != null)
            {
                foreach (LabelData ld in _entity.LabelDataList)
                {
                    if (ld.LabelDataType == EnumLabelDataType.EVData)
                    {
                        OViewLabelEV evlabel = ConvertLabelData_EigenValue(ld as LabelData_EigenValue);
                        evlabel.WindTurbineID = item.WindTurbineID;
                        item.OViewLabelEVs.Add(evlabel);
                    }
                    else if (ld.LabelDataType == EnumLabelDataType.RotSpeedData)
                    {
                        OViewLabelRotSpd spdLabel = ConvertLabelData_RotSpeed(ld as LabelData_RotSpeed);
                        spdLabel.WindTurbineID = item.WindTurbineID;
                        item.OViewLabelRotSpds.Add(spdLabel);
                    }
                    else if (ld.LabelDataType == EnumLabelDataType.WorkingCond)
                    {
                        OViewLabelWorkCondition wcLabel = ConvertLabelData_WorkCond(ld as LabelData_WorkCond);
                        wcLabel.WindTurbineID = item.WindTurbineID;
                        item.OViewLabelWorkConditions.Add(wcLabel);
                    }
                }
            }
            if (_entity.LableTransLoc != null)
            {
                OViewLabelSensor sensorLabel = ConvertLable_TransLocation(_entity.LableTransLoc);
                sensorLabel.WindTurbineID = item.WindTurbineID;
                item.OViewLabelSensors.Add(sensorLabel);
            }

            return item;
        }

        public static List<OViewLabelGroup> ConvertLabelGroupList(List<LabelGroup> _entityList)
        {
            List<OViewLabelGroup> ovLabelGroup = new List<OViewLabelGroup>();
            foreach (LabelGroup item in _entityList)
            {
                ovLabelGroup.Add(ConvertLabelGroup(item));
            }

            return ovLabelGroup;
        }

        /// <summary>
        /// 转换特征值数据标签
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static OViewLabelEV ConvertLabelData_EigenValue(LabelData_EigenValue _entity)
        {
            if (_entity == null)
            {
                return null;
            }
            OViewLabelEV item = new OViewLabelEV();
            item.Border = _entity.BorderWidth;
            item.DataName= _entity.DataName;
            item.EigenValueID = _entity.EigenValueID;
            item.EigenValueType = (short)_entity.EigenValueCategory;
            if (!string.IsNullOrEmpty(_entity.LabelGroupID))
            {
                item.LabelGroupID = _entity.LabelGroupID;
            }
            item.OrderSeq= _entity.Order;
            return item;
        }

        public static List<OViewLabelEV> ConvertLabelData_EigenValueList(List<LabelData_EigenValue> _entityList)
        {
            List<OViewLabelEV> ovLabelEV = new List<OViewLabelEV>();
            foreach (LabelData_EigenValue item in _entityList)
            {
                ovLabelEV.Add(ConvertLabelData_EigenValue(item));
            }
            return ovLabelEV;
        }

        /// <summary>
        /// 转换传感器位置标签
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static OViewLabelSensor ConvertLable_TransLocation(Lable_TransLocation _entity)
        {
            if (_entity == null)
            {
                return null;
            }
            OViewLabelSensor item = new OViewLabelSensor();
            if (!string.IsNullOrEmpty(_entity.LabelGroupID))
            {
                item.LabelGroupID =_entity.LabelGroupID;
            }
            item.MeasLocationID = _entity.MeasLocationID;
            item.SensorHeight =(int) _entity.TranHeight;
            item.SensorWidth = (int)_entity.TranWidth;
            item.SensorLocType= (short)_entity.TransStyleType;
            item.SensorPosX = (int)_entity.TranX;
            item.SensorPosY= (int)_entity.TranY;

            return item;
        }

        /// <summary>
        /// 转换转速数据标签
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static OViewLabelRotSpd ConvertLabelData_RotSpeed(LabelData_RotSpeed _entity)
        {
            if (_entity == null)
            {
                return null;
            }
            OViewLabelRotSpd item = new OViewLabelRotSpd();
            if (!string.IsNullOrEmpty(_entity.LabelGroupID))
            {
                item.LabelGroupID = _entity.LabelGroupID;
            }
            item.MeasLocationID = _entity.MeasLocationID;
            item.Border = _entity.BorderWidth;
            item.DataName = _entity.DataName;
            item.OrderSeq = _entity.Order;


            return item;
        }

        public static List<OViewLabelRotSpd> ConvertLabelData_RotSpeedList(List<LabelData_RotSpeed> _entityList)
        {
            List<OViewLabelRotSpd> itemList = new List<OViewLabelRotSpd>();
            foreach (LabelData_RotSpeed item in _entityList)
            {
                itemList.Add(ConvertLabelData_RotSpeed(item));
            }
            return itemList;
        }

        /// <summary>
        /// 转换工况数据标签
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static OViewLabelWorkCondition ConvertLabelData_WorkCond(LabelData_WorkCond _entity)
        {
            if (_entity == null)
            {
                return null;
            }
            OViewLabelWorkCondition item = new OViewLabelWorkCondition();
            if (!string.IsNullOrEmpty(_entity.LabelGroupID))
            {
                item.LabelGroupID = _entity.LabelGroupID;
            }
            item.MeasLocationID = _entity.MeasLocationID;
            item.Border = _entity.BorderWidth;
            item.DataName = _entity.DataName;
            item.OrderSeq = _entity.Order;

            return item;
        }

        public static List<OViewLabelWorkCondition> ConvertLabelData_WorkCondList(List<LabelData_WorkCond> _entityList)
        {
            List<OViewLabelWorkCondition> itemList = new List<OViewLabelWorkCondition>();
            foreach (LabelData_WorkCond item in _entityList)
            {
                itemList.Add(ConvertLabelData_WorkCond(item));
            }
            return itemList;
        }

        public static AlarmStatusRTMeasLoc ConvertDeviceRTAlarmStatusLoc(DeviceRTAlarmStatus _entity)
        {
            AlarmStatusRTMeasLoc item = null;

            if (_entity != null)
            {
                item = new AlarmStatusRTMeasLoc();
                item.AlarmDegree = short.Parse(_entity.AlarmDegree.ToString());
                item.ComponentID = _entity.ComponentID;
                item.MeasLocationID = _entity.DevSegmentID;
                item.WindTurbineID = _entity.WindTurbineID;
                item.AlarmUpdateTime = _entity.AlarmUpdateTime;
            }

            return item;
        }

        public static List<AlarmStatusRTMeasLoc> ConvertDeviceRTAlarmStatusLocList(List<DeviceRTAlarmStatus> _list)
        {
            List<AlarmStatusRTMeasLoc> itemList = new List<AlarmStatusRTMeasLoc>();

            if (_list != null)
            {
                foreach (DeviceRTAlarmStatus item in _list)
                {
                    AlarmStatusRTMeasLoc entity = ConvertDeviceRTAlarmStatusLoc(item);
                    
                    if (entity != null)
                    {
                        // 添加测量位置名称
                        entity.DevMeasLocVibration = new DevMeasLocVibration();
                        entity.DevMeasLocVibration.ComponentID = item.ComponentID;
                        entity.DevMeasLocVibration.MeasLocationName = item.DevSegmentName;
                        entity.DevMeasLocVibration.WindTurbineID = item.WindTurbineID;
                        itemList.Add(entity);
                    }
                }
            }

            return itemList;
        }
        /// <summary>
        /// 转换晃度测量位置报警状态
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static AlarmStatusRTMeasLocSVM ConvertDeviceRTAlarmStatusLocSVM(DeviceRTAlarmStatus _entity)
        {
            AlarmStatusRTMeasLocSVM item = null;

            if (_entity != null)
            {
                item = new AlarmStatusRTMeasLocSVM();
                item.AlarmDegree = short.Parse(_entity.AlarmDegree.ToString());
                item.ComponentID = _entity.ComponentID;
                item.MeasLocationID = _entity.DevSegmentID;
                item.WindTurbineID = _entity.WindTurbineID;
                item.AlarmUpdateTime = _entity.AlarmUpdateTime;
            }
            return item;
        }
        private static AlarmType GetAlarmType(short _alarmDegree)
        {
            AlarmType type = AlarmType.AlarmType_Unknown;

            if (_alarmDegree == (short)AlarmType.AlarmType_Alarm.AlarmDegree)
            {
                type = AlarmType.AlarmType_Alarm;
            }
            else if (_alarmDegree == (short)AlarmType.AlarmType_CommunicationError.AlarmDegree)
            {
                type = AlarmType.AlarmType_CommunicationError;
            }
            else if (_alarmDegree == (short)AlarmType.AlarmType_Normal.AlarmDegree)
            {
                type = AlarmType.AlarmType_Normal;
            }
            else if (_alarmDegree == (short)AlarmType.AlarmType_Warning.AlarmDegree)
            {
                type = AlarmType.AlarmType_Warning;
            }

            return type;

        }
        #region 诊断任务
        public static WTCMSLive.Entity.Models.DiagnosisAssignment ConvertDiagnosisAssignment(WTCMSLive.BusinessEntity.DiagnosisAssignment _entity)
        {
            WTCMSLive.Entity.Models.DiagnosisAssignment diagnosisAssignment = new Entity.Models.DiagnosisAssignment();
            if (null != _entity)
            {
                diagnosisAssignment.AssignmentStatus = (short?)_entity.AssignmentStatus;
                diagnosisAssignment.AssignmentType = (short?)_entity.AssignmentType;
                diagnosisAssignment.CreateTime = _entity.CreateTime;
                diagnosisAssignment.Diagnosiser = _entity.Diagnosiser;
                diagnosisAssignment.DiagnosisTime = _entity.DiagnosisTime;
                diagnosisAssignment.DiagnosisHandleLogs = ConvertDiagnosisHandleLogList(_entity.DiagnosisHandleLogsList);
                diagnosisAssignment.HitchDegree = (short?)_entity.HitchDegree.AlarmDegree;
                diagnosisAssignment.WindParkID = _entity.WindParkID;
                diagnosisAssignment.WindTurbineID = _entity.WindTurbineID;
            }
            return diagnosisAssignment;
        }

        public static List<WTCMSLive.Entity.Models.DiagnosisAssignment> ConvertDiagnosisAssignmentList(List<WTCMSLive.BusinessEntity.DiagnosisAssignment> _entity)
        {
            List<WTCMSLive.Entity.Models.DiagnosisAssignment> itemList = new List<WTCMSLive.Entity.Models.DiagnosisAssignment>();
            foreach (WTCMSLive.BusinessEntity.DiagnosisAssignment item in _entity)
            {
                itemList.Add(ConvertDiagnosisAssignment(item));
            }
            return itemList;
        }
        /// <summary>
        /// 诊断任务日志
        /// </summary>
        /// <param name="_entity">业务实体</param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.DiagnosisHandleLog ConvertDiagnosisHandleLog(WTCMSLive.BusinessEntity.DiagnosisHandleLog _entity)
        {
            WTCMSLive.Entity.Models.DiagnosisHandleLog diagnosisHandleLog = new Entity.Models.DiagnosisHandleLog();
            diagnosisHandleLog.WindTurbineID = _entity.WindTurbineID;
            diagnosisHandleLog.CreateTime = _entity.CreateTime;
            diagnosisHandleLog.HandleTime = _entity.HandleTime;
            diagnosisHandleLog.HandleUser = _entity.HandleUser;
            diagnosisHandleLog.WorkDescription = _entity.WorkDescription;
            return diagnosisHandleLog;               
        }

        public static List<WTCMSLive.Entity.Models.DiagnosisHandleLog> ConvertDiagnosisHandleLogList(List<WTCMSLive.BusinessEntity.DiagnosisHandleLog> _entity)
        {
            List<WTCMSLive.Entity.Models.DiagnosisHandleLog> itemList = new List<WTCMSLive.Entity.Models.DiagnosisHandleLog>();
            if (_entity == null)
            {
                return itemList;
            }
            foreach (WTCMSLive.BusinessEntity.DiagnosisHandleLog item in _entity)
            {
                itemList.Add(ConvertDiagnosisHandleLog(item));
            }
            return itemList;
        }
        #endregion
    }
}
