﻿using CMSFramework.BusinessEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace WTCMSLive.BusinessModel
{
    /// <summary>
    /// 采集单元实时状态管理
    /// </summary>
    public class DAUConditionManagement
    {
        #region 采集单元实时状态管理
        /// <summary>
        /// 根据风电场ID获取采集单元实时状态列表
        /// </summary>
        /// <param name="_WindParkId"></param>
        /// <returns></returns>
        public static List<RTAlarmStatus_DAU> GetDAURTAlarmStatusByWindParkId(string _WindParkId)
        {
            List<RTAlarmStatus_DAU> list = null;
           
            using (CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                //list = ctx.AlarmStatusRTDAUs.Where(item => item.WindTurbineID.Contains(_WindParkId)).ToList();
                list = ctx.AlarmStatusRTDAUs.ToList();
                list = list.Where(t => t.WindTurbineID.Contains(_WindParkId)).ToList();
            }
            return list;
        }
        /// <summary>
        /// 获取采集单元实时状态列表
        /// </summary>
        /// <returns></returns>
        public static List<RTAlarmStatus_DAU> GetDAURTAlarmStatusList()
        {
            List<RTAlarmStatus_DAU> list = null;
            using (CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                list = ctx.AlarmStatusRTDAUs.ToList();
            }
            return list;
        }

        /// <summary>
        /// 根据机组ID取得机组对应采集单元实时状态列表
        /// </summary>
        /// <param name="_windTurbineId"></param>
        /// <returns></returns>
        public static RTAlarmStatus_DAU GetDAURTAlarmStatusByWindTurbineId(string _windTurbineId)
        {
            RTAlarmStatus_DAU alarmDau = null;
            using (CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                alarmDau = ctx.AlarmStatusRTDAUs.Find(_windTurbineId);
                if (alarmDau!=null)
                    alarmDau.sensorRTList = ctx.AlarmStatusRTSensors.Where(item => item.WindTurbineID == _windTurbineId).ToList();
            }
            return alarmDau;
        }
        #endregion
    }
}
