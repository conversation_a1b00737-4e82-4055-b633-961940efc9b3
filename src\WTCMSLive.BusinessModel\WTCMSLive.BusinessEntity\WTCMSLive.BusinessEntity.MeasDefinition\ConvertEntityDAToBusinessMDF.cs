﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.BusinessEntity;
using WTCMSLive.Entity.Models;
using WTCMSLive.IDALService;

namespace WTCMSLive.BusinessEntityConvert
{
    // Author: whr
    // Create: 2013-09-02
    /// <summary>
    /// 实体转换， 从DA层实体转换为业务层实体
    /// </summary>
    public static class ConvertEntityDAToBusinessMDF
    {
        static IDeviceService devSvc = AppFramework.ServiceBus.ServiceLocator.GetService<IDeviceService>();
        private static ISVMMeasDefService svmMeasDefService = AppFramework.ServiceBus.ServiceLocator.GetService<ISVMMeasDefService>();
        /// <summary>
        /// 测量定义实体转换
        /// </summary>
        /// <param name="_windPark"></param>
        /// <returns></returns>
        public static WTCMSLive.BusinessEntity.MeasDefinition ConvertMeasDefinition(WTCMSLive.Entity.Models.MeasDefinition _entity)
        {
            if (_entity == null) return null;

            WTCMSLive.BusinessEntity.MeasDefinition mdf = new WTCMSLive.BusinessEntity.MeasDefinition();

            mdf.MeasDefinitionID = _entity.MeasDefinitionID.ToString();
            mdf.WindParkID = _entity.WindParkID.ToString();
            mdf.WindTurbineID = _entity.WindTurbineID.ToString();
            mdf.MeasDefinitionName = _entity.MeasurementDefinitionName;
            mdf.OrderSeq = (int)_entity.OrderSeq;
            mdf.IsAvailable = _entity.IsAvailable == 1 ? true : false;

            List<MeasLoc_RotSpd> locList = new List<MeasLoc_RotSpd>();
            if (_entity.MDFWaveDefRotSpds != null)
            {
                foreach (MDFWaveDefRotSpd item in _entity.MDFWaveDefRotSpds)
                {
                    MeasLoc_RotSpd loc =
                        ConvertEntityDAToBusinessDEV.ConvertMeasLoc_RotSpd(item.DevMeasLocRotSpd);

                    locList.Add(loc);
                }
            }
            mdf.MeasLocation_RotSpd = locList;

            List<MeasLoc_Process> proList = new List<MeasLoc_Process>();
            if (_entity.MDFWorkConditions != null)
            {
                foreach (MDFWorkCondition item in _entity.MDFWorkConditions)
                {
                    proList.Add(ConvertEntityDAToBusinessDEV.ConvertMeasLoc_Process(item.DevMeasLocProcess));
                }
            }
            mdf.MeasLocList_Process = proList;

            if (_entity.MDFWaveDefinitions != null)
            {
                mdf.WaveDefinitionList = ConvertVibWaveDefinitionList(_entity.MDFWaveDefinitions.ToList());
            }
            if (_entity.SVMWaveDefinitions != null)
            {
                mdf.WaveDefListSVM = ConvertWaveDefListSVM(_entity.SVMWaveDefinitions.ToList());
            }
            return mdf;
        }

        private static List<WaveDef_SVM> ConvertWaveDefListSVM(List<SVMWaveDefinition> _entityList)
        {
            List<WaveDef_SVM> SVMDefList = new List<WaveDef_SVM>();
            for (int i = 0; i < _entityList.Count; i++)
            {
                Entity.Models.SVMMeasLocation wdf = svmMeasDefService.GetSVMMeaslocByMeasLocID(_entityList[i].MeasLocationID);
                WaveDef_SVM SVMDef = new WaveDef_SVM();
                SVMDef.MeasLocationName = wdf.MeasurementLocationName;
                SVMDef.MeasDefinitionID = _entityList[i].MeasDefinitionID.ToString();
                SVMDef.MeasLocationID = _entityList[i].MeasLocationID.ToString();
                SVMDef.WaveDefinitionName = _entityList[i].WaveDefinitionName;
                SVMDef.WindTurbineID = _entityList[i].WindTurbineID.ToString();
                SVMDef.ParamType = (BusinessEntity.SVM.EnumSVMLocOriention)_entityList[i].ParamType;
                SVMDef.WaveDefinitionID = _entityList[i].WaveDefinitionID.ToString();
                SVMDef.SampleRate = (float)_entityList[i].SampleRate;
                SVMDef.SampleLength = _entityList[i].SampleLength;
                SVMDefList.Add(SVMDef);
            }
            return SVMDefList;
        }



        private static List<BusinessEntity.SVM.SVMWaveDefinition_ACC> ConvertSVMWaveDefList(List<SVMWaveDefinition> _entityList)
        {
            List<BusinessEntity.SVM.SVMWaveDefinition_ACC> SVMDefList = new List<BusinessEntity.SVM.SVMWaveDefinition_ACC>();
            for (int i = 0; i < _entityList.Count; i++)
            {
                Entity.Models.SVMMeasLocation wdf = svmMeasDefService.GetSVMMeaslocByMeasLocID(_entityList[i].MeasLocationID);
                BusinessEntity.SVM.SVMWaveDefinition_ACC SVMDef = new BusinessEntity.SVM.SVMWaveDefinition_ACC();
                SVMDef.MeasLocationName = wdf.MeasurementLocationName;
                SVMDef.MeasDefinitionID = _entityList[i].MeasDefinitionID.ToString();
                SVMDef.MeasLocationID = _entityList[i].MeasLocationID.ToString();
                SVMDef.WaveDefinitionName = _entityList[i].WaveDefinitionName;
                SVMDef.WindTurbineID = _entityList[i].WindTurbineID.ToString();
                SVMDef.WaveDefinitionID = _entityList[i].WaveDefinitionID.ToString();
                SVMDefList.Add(SVMDef);
            }
            return SVMDefList;
        }

        public static List<WaveDefinition> ConvertVibWaveDefinitionList(List<WTCMSLive.Entity.Models.MDFWaveDefinition> _entityList)
        {
            List<WaveDefinition> waveDefinitionList = new List<WaveDefinition>();
            foreach (WTCMSLive.Entity.Models.MDFWaveDefinition item in _entityList)
            {
                waveDefinitionList.Add(ConvertVibWaveDefinition(item));
            }
            return waveDefinitionList;
        }

        /// <summary>
        /// 波形定义实体转换
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static WaveDefinition ConvertVibWaveDefinition(WTCMSLive.Entity.Models.MDFWaveDefinition _entity)
        {
            if (_entity == null) return null;

            WaveDefinition vibWDF = new WaveDefinition();

            vibWDF.WindTurbineID = _entity.WindTurbineID.ToString();
            vibWDF.WaveDefinitionID = _entity.WaveDefinitionID.ToString();
            vibWDF.MeasDefinitionID = _entity.MeasDefinitionID.ToString();
            vibWDF.MeasLocationID = _entity.MeasLocationID.ToString();
            vibWDF.WaveDefinitionName = _entity.WaveDefinitionName;
            vibWDF.WaveType = _entity.WaveType.ToString();
            vibWDF.SignalType = "加速度";
            vibWDF.WaveDefParamID = _entity.WaveParamID.ToString();
            if (_entity.DevMeasLocVibration != null)
            {
                vibWDF.VibMeasLoc = ConvertEntityDAToBusinessDEV.ConvertMeasLoc_Vib(_entity.DevMeasLocVibration);
            }
           
            return vibWDF;
        }

        /// <summary>
        /// 有效性规则实体转换
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static MeasActiveRule ConvertMeasActiveRule(WTCMSLive.Entity.Models.MDFMeasActiveRule _entity)
        {
            if (_entity == null) return null;

            MeasActiveRule measActiveRule = new MeasActiveRule();

            measActiveRule.MeasDefinitionID = _entity.MeasDefinitionID.ToString();
            measActiveRule.MeasLocationId = _entity.MeasLocationID.ToString();
            measActiveRule.IsAvailable = _entity.IsAvailable == 1 ? true : false;
            measActiveRule.LowerLimiValue = (float)_entity.LowerLimitValue;
            measActiveRule.UpperLimitValue = (float)_entity.UpperLimitValue;
            measActiveRule.SpecificValue = _entity.SpecifcValue;
            measActiveRule.RuleType = (EnumActiveRuleType)(Convert.ToInt32(_entity.RuleType));

            return measActiveRule;
        }

        /// <summary>
        /// 时域波形定义参数
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static WaveDefParam_Time ConvertWDFParamTime(WTCMSLive.Entity.Models.WDFParamTime _entity)
        {
            if (_entity == null) return null;

            WaveDefParam_Time wdfParaTime = new WaveDefParam_Time();

            wdfParaTime.WaveDefParamID = _entity.WaveDefParamID.ToString();
            wdfParaTime.WaveDefParamName = _entity.WaveDefParamName;
            wdfParaTime.LowerLimitFreqency = (float)_entity.LowerLimitFreqency;
            wdfParaTime.UpperLimitFreqency = (float)_entity.UpperLimitFreqency;
            wdfParaTime.SampleLength = Convert.ToInt32(_entity.SampleLength);

            return wdfParaTime;
        }

        public static List<WaveDefParam_Time> ConvertWDFParamTimeList(List<WDFParamTime> _entityList)
        {
            List<WaveDefParam_Time> wdfTimeList = new List<WaveDefParam_Time>();

            _entityList.ForEach(
                item =>
                {
                    wdfTimeList.Add(
                        ConvertWDFParamTime(item)
                        );

                }
                );
            return wdfTimeList;
        }

        /// <summary>
        /// 阶次包络波形定义参数
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static WaveDefParam_OrderEnv ConvertWDFParamOrderEvp(WTCMSLive.Entity.Models.WDFParamOrderEvp _entity)
        {
            if (_entity == null) return null;

            WaveDefParam_OrderEnv wdfParaOrderEvp = new WaveDefParam_OrderEnv();

            wdfParaOrderEvp.WaveDefParamID = _entity.WaveDefParamID.ToString();
            wdfParaOrderEvp.WaveDefParamName = _entity.WaveDefParamName;
            wdfParaOrderEvp.EnvBandWidth = (float)_entity.EnvBandWidth;
            wdfParaOrderEvp.EnvFiterFreq = (float)_entity.EnvFiterFreq;
            wdfParaOrderEvp.CycleNumber = Convert.ToInt32(_entity.CycleNumber);

            return wdfParaOrderEvp;
        }

        public static List<WaveDefParam_OrderEnv> ConvertWDFParamOrderEvpList(List<WTCMSLive.Entity.Models.WDFParamOrderEvp> _entitys)
        {
            List<WaveDefParam_OrderEnv> wdfOrderEvpList = new List<WaveDefParam_OrderEnv>();

            _entitys.ForEach(
                item =>
                {
                    wdfOrderEvpList.Add(
                        ConvertWDFParamOrderEvp(item)
                        );
                }
                );
            return wdfOrderEvpList;
        }


        public static WaveDefParam_Envlope ConvertWDFParamEnvlope(WTCMSLive.Entity.Models.WDFParamEnvlope _entity)
        {
            if (_entity == null) return null;

            WaveDefParam_Envlope wdfParaEvp = new WaveDefParam_Envlope();

            wdfParaEvp.WaveDefParamID = _entity.WaveDefParamID.ToString();
            wdfParaEvp.WaveDefParamName = _entity.WaveDefParamName;
            wdfParaEvp.EnvBandWidth = (float)_entity.EnvBandWidth;
            wdfParaEvp.EnvFiterFreq = (float)_entity.EnvFiterFreq;
            wdfParaEvp.SampleLength = Convert.ToInt32(_entity.SampleLength);

            return wdfParaEvp;
        }

        /// <summary>
        /// 转换转速波形定义
        /// </summary>
        /// <param name="wdf"></param>
        /// <returns></returns>
        public static WaveDef_RotSpd ConvertRotSpdWaveDefinition(MDFWaveDefRotSpd wdf)
        {
            if (wdf == null) return null;
            WaveDef_RotSpd wdfRS = new WaveDef_RotSpd();
            wdfRS.WindTurbineID = wdf.WindTurbineID.ToString();
            wdfRS.MeasDefinitionID = wdf.MeasDefinitionID.ToString();
            wdfRS.MeasLoc_RotSpdID = wdf.MeasLocationID.ToString();
            wdfRS.WaveLineCounts = wdf.WaveLineCounts.Value;
            return wdfRS;
        }

        /// <summary>
        /// 转换过程量波形定义
        /// </summary>
        /// <param name="wdf"></param>
        /// <returns></returns>
        public static WaveDef_Process ConvertProcessWaveDefinition(MDFWorkCondition wdf)
        {
            if (wdf == null) return null;
            WaveDef_Process wdfProc = new WaveDef_Process();
            wdfProc.WindTurbineID = wdf.WindTurbineID.ToString();
            wdfProc.MeasDefinitionID = wdf.MeasDefinitionID.ToString();
            wdfProc.MeasLocationID = wdf.MeasLocationID.ToString();
            if (wdf.UpperLimitFreqency != null)
            {
                wdfProc.UpperLimitFreqency = (float)wdf.UpperLimitFreqency;
            }
            if (wdf.SampleLength != null)
            {
                wdfProc.SampleLength = (int)wdf.SampleLength;
            }
            return wdfProc;
        }
    }
}
