﻿using CMSFramework.BusinessEntity;
using WTCMSLive.BusinessModel;

namespace WTCMSLive.WebSite.Models
{
    public class TreeManager
    {
        public List<TreeModel> GetTreeModel(string? parkID = null)
        {
            List<TreeModel> treeModelList = new List<TreeModel>();
            //取得风场列表
            List<WindPark> dataList = DevTreeManagement.GetWindParkList();
            if (string.IsNullOrEmpty(parkID))
            {
                dataList = dataList.Where(t => t.WindParkID != "HN999").ToList();
            }
            else
            {
                dataList = dataList.Where(t=>t.WindParkID == parkID).ToList();
            }
            
            List<WindTurbine> turList = DevTreeManagement.GetTurbinesList();
            List<AlarmStatus_Turbine> statusRTList = DevRTStateManagement.GetTurRTAList();
            List<AlarmStatus_BladeCom> statusRTFreezeList = DevRTStateManagement.GetAlarmIceList();

            // sunqi  todo 
            //if (isCMS==false)
            //   statusRTFreezeList = DevRTStateManagement.GetTurRTFreezeAList();

            List<DiagnosisAssignment> diaList = AlrmRecordManagement.GetRegularDiagnosisList();
            //if (dataList.Count > 1)
            //{
            //    //集团公司级
            //    TreeModel companyModel = new TreeModel();
            //    companyModel.id = "company";
            //    companyModel.type = "root";
            //    companyModel.displayValue = Resources.Message.CompanyName;
            //    companyModel.ChildNode = new TreeModel[0];
            //    companyModel.href = "javascript:trubineNodeClick('/OverViewPage/Index')";
            //    //companyModel.href = "/OverViewPage/Index";
            //    companyModel.ChildNode = new TreeModel[] { };
            //    treeModelList.Add(companyModel);
            //}
            for (int i = 0; i < dataList.Count; i++)
            {
                //添加风场级节点
                TreeModel windPark = new TreeModel();
                windPark.id = dataList[i].WindParkID;
                windPark.key = dataList[i].WindParkID;
                windPark.title = dataList[i].WindParkName;
                windPark.displayValue = dataList[i].WindParkName;
                windPark.type = "park";
                //windPark.href = "javascript:trubineNodeClick('" + "/WindPark/Index/" + windPark.id + "/WindPark" + "')";
                //windPark.href = "/WindPark/Index/"+windPark.id;
                windPark.children = CreateChildTree(dataList[i], turList, statusRTList, statusRTFreezeList, diaList, "/WindTurbine/Index/");
                treeModelList.Add(windPark);
            }
            return treeModelList;
        }
        public List<TreeModel> GetWindTurbineTreeModel()
        {
            List<TreeModel> treeModelList = new List<TreeModel>();
            //取得风场列表
            List<WindPark> dataList = DevTreeManagement.GetWindParkList();

            List<WindTurbine> turList = DevTreeManagement.GetTurbinesList();
            List<AlarmStatus_Turbine> statusRTList = DevRTStateManagement.GetTurRTAList();
            List<AlarmStatus_BladeCom> statusRTFreezeList = new List<AlarmStatus_BladeCom>();
            //if(isCMS==false)
            //    statusRTFreezeList = DevRTStateManagement.GetTurRTFreezeAList();
            List<DiagnosisAssignment> diaList = AlrmRecordManagement.GetRegularDiagnosisList();
            //if (dataList.Count > 1)
            //{
            //    //集团公司级
            //    TreeModel companyModel = new TreeModel();
            //    companyModel.id = "company";
            //    companyModel.type = "root";
            //    companyModel.displayValue = Resources.Message.CompanyName;
            //    companyModel.ChildNode = new TreeModel[0];
            //    companyModel.href = "javascript:trubineNodeClick('/WindTurbine/DevWindPark')";
            //    //companyModel.href = "/OverViewPage/Index";
            //    companyModel.ChildNode = new TreeModel[] { };
            //    treeModelList.Add(companyModel);
            //}
            for (int i = 0; i < dataList.Count; i++)
            {
                //添加风场级节点
                TreeModel windPark = new TreeModel();
                windPark.id = dataList[i].WindParkID;
                windPark.displayValue = dataList[i].WindParkName;
                windPark.type = "root";
                windPark.href = "javascript:trubineNodeClick('" + "/Setting/ShowSettingPage/" + windPark.id + "/" + "')";
                //windPark.href = "/WindPark/Index/"+windPark.id;
                windPark.children = CreateChildTree(dataList[i], turList, statusRTList, statusRTFreezeList, diaList, "/Setting/ShowSettingPage/");
                treeModelList.Add(windPark);
            }
            return treeModelList;
        }
        /// <summary>
        /// 获取指定级别的机组设备树
        /// </summary>
        /// <param name="v"></param>
        /// <returns></returns>
        internal object GetTreeModel(EnumAlarmDegree alrmLevel)
        {
            List<TreeModel> treeModelList = new List<TreeModel>();
            //取得风场列表
            List<WindPark> dataList = DevTreeManagement.GetWindParkList();

            List<WindTurbine> turList = DevTreeManagement.GetTurbinesList();

            List<AlarmStatus_Turbine> statusRTList = DevRTStateManagement.GetTurRTAList();
            List<AlarmStatus_BladeCom> statusRTFreezeList = new List<AlarmStatus_BladeCom>();
            //if (isCMS == false)
            //    statusRTFreezeList = DevRTStateManagement.GetTurRTFreezeAList();

            //移除指定类型之外的机组状态
            statusRTList.RemoveAll(item => item.AlarmDegree != alrmLevel);

            List<DiagnosisAssignment> diaList = AlrmRecordManagement.GetRegularDiagnosisList();
            for (int i = 0; i < dataList.Count; i++)
            {
                //添加风场级节点
                TreeModel windPark = new TreeModel();
                windPark.id = dataList[i].WindParkID;
                windPark.displayValue = dataList[i].WindParkName;
                windPark.type = "root";
                windPark.href = "javascript:trubineNodeClick('" + "/AlarmReport/ShowParkAlarmReoprt/" + windPark.id + "/WindPark" + "')";

                windPark.children = CreateChildAlarmTree(dataList[i], turList, statusRTList, statusRTFreezeList, diaList, "/AlarmReport/ShowTurbineAlarmReport/");

                treeModelList.Add(windPark);
            }
            return treeModelList;
        }
        private TreeModel[] CreateChildAlarmTree(WindPark windPark, List<WindTurbine> turList, List<AlarmStatus_Turbine> statusRTList, List<AlarmStatus_BladeCom> statusRTFreezeList, List<DiagnosisAssignment> diaList, string strURl)
        {
            List<TreeModel> treeModelList = new List<TreeModel>();
            List<WindTurbine> windTurbineList = turList.FindAll(c => c.WindParkID == windPark.WindParkID).OrderBy(item => item.WindTurbineName).ToList();
            //Added by zxk @2018-1-29 加入覆冰状态
            List<AlarmEvent> eventList = DevRTStateManagement.GetAlarmEventList(windPark.WindParkID).OrderBy(item => item.AlarmTime).ToList();
            for (int i = 0; i < windTurbineList.Count; i++)
            {
                WindTurbine windTurbine = windTurbineList[i];
                TreeModel turbineNode = new TreeModel();
                turbineNode.id = windPark.WindParkID + '/' + windTurbine.WindTurbineID;
                turbineNode.displayValue = windTurbine.WindTurbineName;
                //AlarmEvent bvmiceStatus = eventList.Find(item => item.WindTurbineID == windTurbine.WindTurbineID);
                AlarmStatus_Turbine devStatus = statusRTList.Find(item => item.DevSegmentID == windTurbine.WindTurbineID);
                //AlarmStatus_BladeCom devFreezeStatus = statusRTFreezeList.Find(item => item.WindTurbineID == windTurbine.WindTurbineID);
                AlarmStatus_BladeCom devFreezeStatus = statusRTFreezeList.Find(item => item.WindTurbineID == windTurbine.WindTurbineID);
                //if (devStatus == null)
                //{
                //    continue;
                //}
                if (devStatus != null)
                {
                    turbineNode.realStatus = AppFramework.Utility.EnumHelper.GetDescription(devStatus.AlarmDegree);
                }
                else 
                {
                    turbineNode.realStatus = "无";
                }
                turbineNode.bvmiceStatus = "无";
                if (devFreezeStatus != null)
                {
                    if (devFreezeStatus.IceAlarmDegree != EnumAlarmDegree.AlarmDeg_Normal && devFreezeStatus.IceAlarmDegree != EnumAlarmDegree.AlarmDeg_Unknown)
                    {
                        turbineNode.bvmiceStatus = "覆冰";
                    }
                }
               /* if (devFreezeStatus != null) 
                {
                    if (bvmiceStatus.AlarmDegree == (int)EnumAlarmDegree.AlarmDeg_Freeze)
                    {
                        turbineNode.bvmiceStatus = "覆冰";
                    }
                    else
                    {
                        turbineNode.bvmiceStatus = "无";
                    }
                }
                else
                {
                    turbineNode.bvmiceStatus = "无";
                }*/
                //DiagnosisAssignment turDia = diaList.Find(c => c.WindTurbineID.ToString() == windTurbine.WindTurbineID);
                //if (turDia != null)
                //{
                //    turbineNode.diagStatus = AppFramework.Utility.EnumHelper.GetDescription((EnumAlarmDegree)turDia.HitchDegree);
                //}
                turbineNode.href = "javascript:trubineNodeClick('" + strURl + windPark.WindParkID + '/' + windTurbine.WindTurbineID + "')";
                //turbineNode.href = "/WindTurbine/Index/"+ windPark.WindParkID + '/' + windTurbine.WindTurbineID; 
                turbineNode.children = new TreeModel[] { };
                //Modify by zxk @2018-1-29 此处改为覆冰状态
                turbineNode.title = "机组状态: " + turbineNode.realStatus + "  覆冰状态：" + turbineNode.bvmiceStatus;
                //if (turbineNode.diagStatus == null || turbineNode.diagStatus == "")
                //{
                //    turbineNode.title = "机组状态: " + turbineNode.realStatus + "  诊断状态：" + "无";
                //}
                //else
                //{
                //    turbineNode.title = "机组状态: " + turbineNode.realStatus + "  诊断状态：" + turbineNode.diagStatus;
                //}
                treeModelList.Add(turbineNode);
            }

            return treeModelList.ToArray();
        }

        //根据风场取得机组列表 
        private TreeModel[] CreateChildTree(WindPark windPark, List<WindTurbine> turList, List<AlarmStatus_Turbine> statusRTList, List<AlarmStatus_BladeCom> statusRTFreezeList, List<DiagnosisAssignment> diaList, string strURl)
        {
            List<TreeModel> treeModelList = new List<TreeModel>();
            List<WindTurbine> windTurbineList = turList.FindAll(c => c.WindParkID == windPark.WindParkID).OrderBy(item => item.WindTurbineID).ToList();
            //Added by zxk @2018-1-29 加入覆冰状态
            List<AlarmEvent> eventList = DevRTStateManagement.GetAlarmEventList(windPark.WindParkID).OrderBy(item => item.AlarmTime).ToList();
            for (int i = 0; i < windTurbineList.Count; i++)
            {
                WindTurbine windTurbine = windTurbineList[i];
                TreeModel turbineNode = new TreeModel();
                turbineNode.id = windTurbine.WindTurbineID;
                turbineNode.key = windTurbine.WindTurbineID;
                turbineNode.title = windTurbine.WindTurbineName;
                turbineNode.displayValue = windTurbine.WindTurbineName;
                turbineNode.type = "device";
                AlarmEvent bvmiceStatus = eventList.Find(item => item.WindTurbineID == windTurbine.WindTurbineID);
                AlarmStatus_Turbine devStatus = statusRTList.Find(item => item.DevSegmentID == windTurbine.WindTurbineID);
                AlarmStatus_BladeCom devFreezeStatus = statusRTFreezeList.Find(item => item.WindTurbineID == windTurbine.WindTurbineID);

                if (devStatus != null)
                {
                    turbineNode.realStatus = AppFramework.Utility.EnumHelper.GetDescription(devStatus.AlarmDegree);
                }
                else 
                {
                    turbineNode.realStatus = "无";
                }
                turbineNode.bvmiceStatus = "无";
                if (devFreezeStatus != null)
                {
                    if (devFreezeStatus.IceAlarmDegree != EnumAlarmDegree.AlarmDeg_Normal && devFreezeStatus.IceAlarmDegree != EnumAlarmDegree.AlarmDeg_Unknown) {
                        turbineNode.bvmiceStatus = "覆冰";
                    }
                }
                //DiagnosisAssignment turDia = diaList.Find(c => c.WindTurbineID.ToString() == windTurbine.WindTurbineID);
                //if (turDia != null)
                //{
                //    turbineNode.diagStatus = AppFramework.Utility.EnumHelper.GetDescription((EnumAlarmDegree)turDia.HitchDegree);
                //}
                //turbineNode.href = "javascript:trubineNodeClick('" + strURl + windPark.WindParkID + '/' + windTurbine.WindTurbineID + "')";
                //turbineNode.href = "/WindTurbine/Index/"+ windPark.WindParkID + '/' + windTurbine.WindTurbineID; 
                turbineNode.children = new TreeModel[] { };
                //Modify by zxk @2018-1-29 此处改为覆冰状态
                turbineNode.statusTitle = "机组状态: " + turbineNode.realStatus + "  覆冰状态：" + turbineNode.bvmiceStatus;
                //if (turbineNode.diagStatus == null || turbineNode.diagStatus == "")
                //{
                //    turbineNode.title = "机组状态: " + turbineNode.realStatus + "  诊断状态：" + "无";
                //}
                //else {
                //    turbineNode.title = "机组状态: " + turbineNode.realStatus + "  诊断状态：" + turbineNode.diagStatus;
                //}
                treeModelList.Add(turbineNode);
            }

            return treeModelList.ToArray();
        }
    }
}