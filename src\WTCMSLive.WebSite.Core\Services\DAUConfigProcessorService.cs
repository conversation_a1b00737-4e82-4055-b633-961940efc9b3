using CMSFramework.BusinessEntity;
using Microsoft.EntityFrameworkCore;
using WTCMSLive.WebSite.Core.DTOs;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// DAU配置处理后台服务
    /// 从队列中读取DAU配置数据并存储到数据库
    /// </summary>
    public class DAUConfigProcessorService : BackgroundService
    {
        private readonly IDAUConfigQueueService _queueService;
        private readonly ILogger<DAUConfigProcessorService> _logger;
        private readonly IConfiguration _configuration;

        public DAUConfigProcessorService(
            IDAUConfigQueueService queueService,
            ILogger<DAUConfigProcessorService> logger,
            IConfiguration configuration)
        {
            _queueService = queueService;
            _logger = logger;
            _configuration = configuration;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("[DAUConfigProcessorService]DAU配置处理服务已启动");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // 从队列中读取数据（阻塞等待）
                    var config = await _queueService.DequeueAsync(stoppingToken);

                    // 处理配置数据
                    await ProcessDAUConfigAsync(config);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("[DAUConfigProcessorService]服务正在停止");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[DAUConfigProcessorService]处理DAU配置时发生未预期的错误");
                    // 继续处理下一条数据，不中断服务
                    await Task.Delay(1000, stoppingToken); // 短暂延迟避免快速失败循环
                }
            }

            _logger.LogInformation("[DAUConfigProcessorService]DAU配置处理服务已停止");
        }

        /// <summary>
        /// 处理DAU配置数据
        /// </summary>
        private async Task ProcessDAUConfigAsync(DAUConfigDTO config)
        {
            if (config == null)
            {
                _logger.LogWarning("[ProcessDAUConfigAsync]接收到空的配置数据");
                return;
            }

            // 验证必要字段
            if (string.IsNullOrWhiteSpace(config.WindParkID) || 
                string.IsNullOrWhiteSpace(config.WindTurbienID))
            {
                _logger.LogWarning("[ProcessDAUConfigAsync]配置数据缺少必要字段: WindParkID={WindParkID}, WindTurbineID={WindTurbineID}",
                    config.WindParkID, config.WindTurbienID);
                return;
            }

            try
            {
                _logger.LogInformation("[ProcessDAUConfigAsync]开始处理DAU配置: WindParkID={WindParkID}, WindTurbineID={WindTurbineID}, IP={IP}",
                    config.WindParkID, config.WindTurbienID, config.DAUIP);

                // 1. 处理风场
                await ProcessWindParkAsync(config);

                // 2. 处理机组
                await ProcessWindTurbineAsync(config);

                // 3. 处理DAU
                await ProcessDAUAsync(config);

                _logger.LogInformation("[ProcessDAUConfigAsync]成功处理DAU配置: WindParkID={WindParkID}, WindTurbineID={WindTurbineID}",
                    config.WindParkID, config.WindTurbienID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ProcessDAUConfigAsync]处理DAU配置失败: WindParkID={WindParkID}, WindTurbineID={WindTurbineID}",
                    config.WindParkID, config.WindTurbienID);
            }
        }

        /// <summary>
        /// 处理风场数据
        /// </summary>
        private async Task ProcessWindParkAsync(DAUConfigDTO config)
        {
            try
            {
                using (var ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                {
                    var existingPark = await ctx.DevWindParks.FindAsync(config.WindParkID);

                    if (existingPark == null)
                    {
                        // 创建新风场
                        var newPark = new WindPark
                        {
                            WindParkID = config.WindParkID,
                            WindParkName = config.WindParkID, // 使用ID作为名称
                            WindParkCode = config.WindParkID,
                            OperationalDate = DateTime.Now,
                            WindTurbineList = new List<WindTurbine>()
                        };

                        ctx.DevWindParks.Add(newPark);
                        await ctx.SaveChangesAsync();

                        _logger.LogInformation("[ProcessWindParkAsync]创建新风场: WindParkID={WindParkID}", config.WindParkID);
                    }
                    else
                    {
                        _logger.LogDebug("[ProcessWindParkAsync]风场已存在: WindParkID={WindParkID}", config.WindParkID);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ProcessWindParkAsync]处理风场数据失败: WindParkID={WindParkID}", config.WindParkID);
                throw;
            }
        }

        /// <summary>
        /// 处理机组数据
        /// </summary>
        private async Task ProcessWindTurbineAsync(DAUConfigDTO config)
        {
            try
            {
                using (var ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                {
                    var existingTurbine = await ctx.DevWindTurbines
                        .FirstOrDefaultAsync(t => t.WindTurbineID == config.WindTurbienID);

                    if (existingTurbine == null)
                    {
                        // 创建新机组
                        var newTurbine = new WindTurbine
                        {
                            WindTurbineID = config.WindTurbienID,
                            WindTurbineName = string.IsNullOrWhiteSpace(config.WindTurbienName) 
                                ? config.WindTurbienID 
                                : config.WindTurbienName,
                            WindParkID = config.WindParkID,
                            WindTurbineModel = "Unknown",
                            OperationalDate = DateTime.Now,
                            TurComponentList = new List<WindTurbineComponent>()
                        };

                        ctx.DevWindTurbines.Add(newTurbine);
                        await ctx.SaveChangesAsync();

                        _logger.LogInformation("[ProcessWindTurbineAsync]创建新机组: WindTurbineID={WindTurbineID}, WindTurbineName={WindTurbineName}",
                            config.WindTurbienID, newTurbine.WindTurbineName);
                    }
                    else
                    {
                        // 更新机组名称（如果提供了新名称）
                        if (!string.IsNullOrWhiteSpace(config.WindTurbienName) && 
                            existingTurbine.WindTurbineName != config.WindTurbienName)
                        {
                            existingTurbine.WindTurbineName = config.WindTurbienName;
                            ctx.Entry(existingTurbine).State = EntityState.Modified;
                            await ctx.SaveChangesAsync();

                            _logger.LogInformation("[ProcessWindTurbineAsync]更新机组名称: WindTurbineID={WindTurbineID}, NewName={NewName}",
                                config.WindTurbienID, config.WindTurbienName);
                        }
                        else
                        {
                            _logger.LogDebug("[ProcessWindTurbineAsync]机组已存在: WindTurbineID={WindTurbineID}", config.WindTurbienID);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ProcessWindTurbineAsync]处理机组数据失败: WindTurbineID={WindTurbineID}", config.WindTurbienID);
                throw;
            }
        }

        /// <summary>
        /// 处理DAU数据
        /// </summary>
        private async Task ProcessDAUAsync(DAUConfigDTO config)
        {
            try
            {
                using (var ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    // 默认使用DauID = "1"
                    string dauId = "1";
                    
                    var existingDAU = await ctx.DAUnits
                        .FirstOrDefaultAsync(d => d.WindTurbineID == config.WindTurbienID && d.DauID == dauId);

                    // 解析端口号
                    int port = 8000; // 默认端口
                    if (!string.IsNullOrWhiteSpace(config.DAUPort) && int.TryParse(config.DAUPort, out int parsedPort))
                    {
                        port = parsedPort;
                    }

                    // 判断是否可用
                    bool isAvailable = string.IsNullOrWhiteSpace(config.DAUStatus) || 
                                      !config.DAUStatus.Equals("Offline", StringComparison.OrdinalIgnoreCase);

                    if (existingDAU == null)
                    {
                        // 创建新DAU
                        var turbineName = string.IsNullOrWhiteSpace(config.WindTurbienName) 
                            ? config.WindTurbienID 
                            : config.WindTurbienName;

                        var newDAU = new WindDAU
                        {
                            WindTurbineID = config.WindTurbienID,
                            DauID = dauId,
                            DAUName = $"{turbineName}-DAU{dauId}",
                            WindParkID = config.WindParkID,
                            IP = config.DAUIP ?? "0.0.0.0",
                            Port = port,
                            IsAvailable = isAvailable,
                            DAUType = EnumDAUType.Vibration, // 默认振动监测类型
                            DeviceID = 1,
                            DataAcquisitionInterval = 60,
                            TrendSaveInterval = 60,
                            WaveSaveInterval = 0,
                            MeasDefVersion = 0,
                            DAUMeasDefVersion = 0,
                            DAUSoftwareVersion = "0",
                            DAUChannelList = new List<DAUChannelV2>(),
                            RotSpeedChannelList = new List<DAUChannel_RotSpeed>(),
                            ProcessChannelList = new List<DAUChannel_Process>(),
                            VoltageCurrentList = new List<DAUChannel_VoltageCurrent>()
                        };

                        ctx.DAUnits.Add(newDAU);
                        await ctx.SaveChangesAsync();

                        _logger.LogInformation("[ProcessDAUAsync]创建新DAU: WindTurbineID={WindTurbineID}, DauID={DauID}, IP={IP}, Port={Port}",
                            config.WindTurbienID, dauId, config.DAUIP, port);
                    }
                    else
                    {
                        // 更新DAU信息
                        bool hasChanges = false;

                        if (!string.IsNullOrWhiteSpace(config.DAUIP) && existingDAU.IP != config.DAUIP)
                        {
                            existingDAU.IP = config.DAUIP;
                            hasChanges = true;
                        }

                        if (existingDAU.Port != port)
                        {
                            existingDAU.Port = port;
                            hasChanges = true;
                        }

                        if (existingDAU.IsAvailable != isAvailable)
                        {
                            existingDAU.IsAvailable = isAvailable;
                            hasChanges = true;
                        }

                        if (hasChanges)
                        {
                            ctx.Entry(existingDAU).State = EntityState.Modified;
                            await ctx.SaveChangesAsync();

                            _logger.LogInformation("[ProcessDAUAsync]更新DAU信息: WindTurbineID={WindTurbineID}, DauID={DauID}, IP={IP}, Port={Port}, IsAvailable={IsAvailable}",
                                config.WindTurbienID, dauId, config.DAUIP, port, isAvailable);
                        }
                        else
                        {
                            _logger.LogDebug("[ProcessDAUAsync]DAU信息无变化: WindTurbineID={WindTurbineID}, DauID={DauID}",
                                config.WindTurbienID, dauId);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ProcessDAUAsync]处理DAU数据失败: WindTurbineID={WindTurbineID}", config.WindTurbienID);
                throw;
            }
        }
    }
}

