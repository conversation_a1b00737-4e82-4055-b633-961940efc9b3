import{_ as a}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{R as n}from"./index-DgvSlLW6.js";import{B as p}from"./index-7iPMz_Qy.js";import{f as c,d as o,o as m,b as _,g as i}from"./index-BjOW8S1L.js";import"./initDefaultProps-P4j1rGDC.js";const u={name:"Exception500",methods:{toHome(){this.$router.push({path:"/"})}}};function f(l,t,d,x,h,e){const r=p,s=n;return m(),c(s,{status:"500",title:"500","sub-title":"Sorry, the server is reporting an error."},{extra:o(()=>[_(r,{type:"primary",onClick:e.toHome},{default:o(()=>t[0]||(t[0]=[i(" Back Home ",-1)])),_:1,__:[0]},8,["onClick"])]),_:1})}const C=a(u,[["render",f]]);export{C as default};
