﻿using CMSFramework.BusinessEntity;
using CMSFramework.DevTreeEntities;
using WTCMSLive.BusinessModel;

namespace WTCMSLive.WebSite.Models
{
    public class WarnManager
    {

        public static List<AlarmDefinitonUIModel> alarmDefinitonUIModels(string turbineID, string MeasLocType)
        {
            List<AlarmDefinitonUIModel> list = new List<AlarmDefinitonUIModel>();
            EnumMeasLocType MeasType = MeasLocType == "0" ? EnumMeasLocType.SVMAlarmDef : EnumMeasLocType.VibAlarmDef;
            List<AlarmDefinition> alarmDef = AlarmDefinitionManage.GetMDFAlarmDefListByTurbineId(turbineID, MeasType);

            //List<AlarmDefinition> groupDef = AlarmDefinitionManage.GetAlarmDefListByTurID(turbineID);
            List<MeasLoc_Vib> myMeasLocList = new List<MeasLoc_Vib>();
            if (MeasLocType == "1")
            {
                myMeasLocList = DevTreeManagement.GetVibMeasLocationByTurId(turbineID).ToList();

                List<MeasLoc_Modbus> modbusList = SVMManagement.GetMeasLoc_ModbusListByTurID(turbineID);
                foreach (var it in modbusList)
                {
                    myMeasLocList.Add(new MeasLoc_Vib()
                    {
                        ComponentID = it.ComponentID,
                        MeasLocName = it.MeasLocName,
                        Orientation = it.Orientation,
                        SectionName = it.SectionName,
                        MeasLocationID = it.MeasLocationID,

                    });

                }
            }
            else
            {
                SVMManagement.GetMeasLoc_SVMListByTurID(turbineID).ForEach(item =>
                    myMeasLocList.Add(new MeasLoc_Vib()
                    {
                        MeasLocationID = item.MeasLocationID,
                        MeasLocName = item.MeasLocName,
                        OrderSeq = item.OrderSeq,
                        SectionName = item.SectionName
                    }));
            }
            //if (myMeasLocList.Count == 0)
            //{
            //    return list;
            //}
            List<KeyValuePair<int, string>> parmeTypelist = EnumWorkCondParamTypeHelper.GetParamTypeList();
            foreach (AlarmDefinition myDef in alarmDef)
            {
                bool isAcceleratedSpeed = false;
                AlarmDefinitonUIModel myModel = new AlarmDefinitonUIModel();
                myModel.WindTurbineID = myDef.WindTurbineID.ToString();
                myModel.MeasLocationID = myDef.MeasLocationID.ToString();
                myModel.EigenValueID = myDef.EigenValueID;
                string EigenCode = myDef.EigenValueID.IndexOf("&&") > -1 ? myDef.EigenValueID.Split(new string[] { "&&" }, StringSplitOptions.None)[1] : "";
                if (string.IsNullOrEmpty(EigenCode))
                    myModel.EigenValueName = "";
                else
                {
                    if (MeasLocType == "1")
                    {
                        string eigenUnit = "";
                        if (EigenCode.IndexOf("VRMS") > -1)
                        {
                            eigenUnit = " (mm/s)";
                        }
                        else if (EigenCode.IndexOf("RMS") > -1 || EigenCode.IndexOf("PK") > -1 || EigenCode.IndexOf("PPK") > -1)
                        {
                            eigenUnit = " (m/s^2)";
                        }
                        myModel.EigenValueName = EigenValueManage.GetFreBandByCode(EigenCode) + eigenUnit;
                        if (string.IsNullOrEmpty(myModel.EigenValueName))
                        {
                            myModel.EigenValueName = EigenCode;

                        }

                    }
                    else
                    {
                        List<EigenValueData_SVM> EigenValueSVM = EigenValueManage.GetSVMPublicEigenValueList(turbineID, myModel.MeasLocationID);
                        if (EigenValueSVM != null)
                        {
                            //return "[]";
                            EigenValueData_SVM myValue = EigenValueSVM.Find(item => (myModel.MeasLocationID + "&&" + item.EigenValueID) == myDef.EigenValueID);
                            myModel.EigenValueName = AppFramework.Utility.EnumHelper.GetDescription(myValue.EigenValueType);
                            if (myModel.EigenValueName.IndexOf("加速度") > -1)
                            {
                                myModel.EigenValueName += " (mg)";
                            }
                            if (myModel.EigenValueName.IndexOf("角") > -1)
                            {
                                myModel.EigenValueName += " (°)";
                            }
                        }

                    }
                }
                myModel.ThresholdGroup = myDef.ThresholdGroup;
                myModel.WorkConditionParamTypeCode = myDef.WorkConParameter.ToString();
                myModel.UpperLimitValue = myDef.UpperLimitValue;
                myModel.LowerLimitValue = myDef.LowerLimitValue;
                MeasLoc_Vib vib = myMeasLocList.Find(i => i.MeasLocationID == myModel.MeasLocationID);

                if (vib == null)
                {
                    continue;
                }

                myModel.MeasLocationName = vib.MeasLocName;
                if (MeasLocType == "0")
                {
                    //myModel.MeasLocationName += vib.SectionName == "TOP" ? "(塔顶)" : "(基础)";
                }

                isAcceleratedSpeed = MeasLocType == "0" && myModel.MeasLocationName.IndexOf("加速度") > -1 ? true : false;
                myModel.MeasLocationID = myDef.MeasLocationID;
                var parmeType = parmeTypelist.Find(con => con.Key.ToString() == myModel.WorkConditionParamTypeCode);
                if (!string.IsNullOrEmpty(parmeType.Value))
                    myModel.WorkConParameterName = parmeType.Value;
                double? WarnValue = null;
                double? AlarmValue = null;
                double? FXWarnValue = null;
                double? FXAlarmValue = null;
                AlarmDefThreshold alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning && item.ThresholdValueType == EnumThresholdValueType.Forward);
                if (alarmThresholdparame != null)
                {
                    WarnValue = (double)alarmThresholdparame.ThresholdValue;
                    //if (isAcceleratedSpeed)
                    //{
                    //    WarnValue *= 1000;
                    //}
                }
                alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm && item.ThresholdValueType == EnumThresholdValueType.Forward);
                if (alarmThresholdparame != null)
                {
                    AlarmValue = (double)alarmThresholdparame.ThresholdValue;
                    //if (isAcceleratedSpeed)
                    //{
                    //    AlarmValue *= 1000;
                    //}
                }
                alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning && item.ThresholdValueType == EnumThresholdValueType.Reverse);
                if (alarmThresholdparame != null)
                {
                    FXWarnValue = alarmThresholdparame.ThresholdValue;
                    //if (isAcceleratedSpeed)
                    //{
                    //    FXWarnValue *= 1000;
                    //}
                }
                alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm && item.ThresholdValueType == EnumThresholdValueType.Reverse);
                if (alarmThresholdparame != null)
                {
                    FXAlarmValue = alarmThresholdparame.ThresholdValue;
                    //if (isAcceleratedSpeed)
                    //{
                    //    FXAlarmValue *= 1000;
                    //}
                }

                myModel.WarnValue = WarnValue;
                myModel.AlarmValue = AlarmValue;

                myModel.ReverseWarnValue = FXWarnValue;
                myModel.ReverseAlarmValue = FXAlarmValue;
                list.Add(myModel);
            }

            // 添加油液报警查询
            if (MeasLocType.Equals("0"))
            {
                List<AlarmDefinition> oilAlarmDef = AlarmDefinitionManage.GetMDFAlarmDefListByTurbineId(turbineID, EnumMeasLocType.OilAlarmDef);
                // 油液测量位置
                Dictionary<string, string> oilMeasloc = new Dictionary<string, string>()
                {
                    {"ViscositySensor","油液粘度" },
                    {"WaterSensor","油液水分" },
                    {"WearParticleSensor","油液磨粒" },
                };
                Dictionary<string, string> oilEv = new Dictionary<string, string>()
                {
                    {"EqualNKinematicViscosity","40℃等效粘度" },
                    {"WaterContent","水含量" },
                    {"FeConcentration","铁磁磨粒浓度" },
                    {"NonFeConcentration","非铁磁磨粒浓度" },
                };
                foreach (var myDef in oilAlarmDef)
                {
                    AlarmDefinitonUIModel myModel = new AlarmDefinitonUIModel();
                    myModel.WindTurbineID = myDef.WindTurbineID.ToString();
                    myModel.MeasLocationID = myDef.MeasLocationID.ToString();
                    myModel.EigenValueID = myDef.EigenValueID;
                    myModel.ThresholdGroup = myDef.ThresholdGroup;
                    myModel.WorkConditionParamTypeCode = myDef.WorkConParameter.ToString();
                    myModel.UpperLimitValue = myDef.UpperLimitValue;
                    myModel.LowerLimitValue = myDef.LowerLimitValue;
                    //MeasLoc_Vib vib = myOilMeasLocList.Find(i => i.MeasLocationID == myModel.MeasLocationID);
                    //myModel.MeasLocationName = vib.MeasLocName;
                    var _oilmeasloc = myDef.MeasLocationID.Replace(turbineID, "");
                    if (oilMeasloc.ContainsKey(_oilmeasloc))
                    {
                        myModel.MeasLocationName = oilMeasloc[_oilmeasloc];
                    }

                    var _oilEv = myDef.EigenValueID.Split(new[] { "&&" }, StringSplitOptions.None)[1];
                    if (oilEv.ContainsKey(_oilEv))
                    {
                        myModel.EigenValueName = oilEv[_oilEv];
                    }

                    myModel.MeasLocationID = myDef.MeasLocationID;
                    var parmeType = parmeTypelist.Find(con => con.Key.ToString() == myModel.WorkConditionParamTypeCode);
                    if (!string.IsNullOrEmpty(parmeType.Value))
                        myModel.WorkConParameterName = parmeType.Value;
                    double? WarnValue = null;
                    double? AlarmValue = null;
                    double? FXWarnValue = null;
                    double? FXAlarmValue = null;
                    AlarmDefThreshold alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning && item.ThresholdValueType == EnumThresholdValueType.Forward);
                    if (alarmThresholdparame != null)
                    {
                        WarnValue = (double)alarmThresholdparame.ThresholdValue;
                    }
                    alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm && item.ThresholdValueType == EnumThresholdValueType.Forward);
                    if (alarmThresholdparame != null)
                    {
                        AlarmValue = (double)alarmThresholdparame.ThresholdValue;
                    }
                    alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning && item.ThresholdValueType == EnumThresholdValueType.Reverse);
                    if (alarmThresholdparame != null)
                    {
                        FXWarnValue = alarmThresholdparame.ThresholdValue;
                    }
                    alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm && item.ThresholdValueType == EnumThresholdValueType.Reverse);
                    if (alarmThresholdparame != null)
                    {
                        FXAlarmValue = alarmThresholdparame.ThresholdValue;
                    }

                    myModel.WarnValue = WarnValue;
                    myModel.AlarmValue = AlarmValue;

                    myModel.ReverseWarnValue = FXWarnValue;
                    myModel.ReverseAlarmValue = FXAlarmValue;
                    list.Add(myModel);
                }
            }
            return list;
        }
    }
}