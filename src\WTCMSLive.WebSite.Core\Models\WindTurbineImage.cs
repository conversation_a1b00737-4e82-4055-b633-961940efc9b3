﻿using CMSFramework.BusinessEntity;
using Newtonsoft.Json.Linq;
using System.Xml;
using WTCMSLive.BusinessModel;

namespace WTCMSLive.WebSite.Models
{
    /// <summary>
    /// 风机总貌图
    /// </summary>
    public class WindTurbineImage
    {
        //protected global::System.Web.UI.HtmlControls.HtmlImage turImg;
        public string GetWindTurbineImage()
        {
            string file = string.Empty;
            XmlDocument xmlDoc = new XmlDocument();
            XmlTextReader textReader = new XmlTextReader(@"F:\LocalSource\WTCMSLive.WebSite\WTCMSLive.WebSite\Models\Resource\WindTurbine.xml");
            textReader.Read();
            xmlDoc.Load(textReader);
            XmlNode node = xmlDoc.SelectSingleNode("svg");
            file = node.InnerXml;
            return file;
        }


        //创建总貌图的背景图，返回路径
        public string CreateTurbineImage()
        {
            XmlDocument xmlDoc = new XmlDocument();
            XmlTextReader textReader = new XmlTextReader(@"F:\LocalSource\WTCMSLive.WebSite\WTCMSLive.WebSite\Models\Resource\Image.svg");
            textReader.Read();
            xmlDoc.Load(textReader);
            textReader.Close();

            string str = xmlDoc.InnerXml;
            return str;
        }

        //根据机组ID取得对应的总貌图样式
        public string CreateTurbineImage(string _turbineID)
        {
            string str = string.Empty;
            byte[] img = DevTreeManagement.GetTurbineImageV2(_turbineID);
            if (img == null)
            {
                return null;
            }
            str = System.Text.Encoding.Default.GetString(img);
            if (!str.Contains("svg"))
            {
                return string.Empty;
            }
            return str.Replace("\r\n", "");
        }

        /// <summary>
        /// 获取叶片总貌图数据
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <returns></returns>
        public List<BVMOverView> GetBVMOverView(string _turbineID)
        {
            EigenValueManager ev = new EigenValueManager();
            List<EigenValueData_Vib> sortDataList = ev.GetRTEigenValueList(_turbineID);
            List<BVMOverView> bvmList = new List<BVMOverView>();
            BVMOverView bvm = new BVMOverView
            {
                Blade1 = new string[3],// TDR,TDK,TDF
                Blade2 = new string[3],
                Blade3 = new string[3],
                BSC = new string[3],
                TSI = string.Empty,
                PB = string.Empty,
                Time = string.Empty
            };
            if (sortDataList.Count > 0) {
                bvm.Time = sortDataList[0].AcquisitionTime.ToString();
            }
            foreach (var data in sortDataList)
            {
                if (data.EigenValueID.Contains("BLD1") || data.EigenValueID.Contains("ONE"))
                {
                    if (data.EigenValueID.EndsWith("TDR"))
                    //if(Regex.IsMatch(data.EigenValueID, @"TDR$"))
                    {
                        bvm.Blade1[0] = data.Eigen_Value.ToString("F3");
                    }
                    if (data.EigenValueID.EndsWith("TDK"))
                    {
                        bvm.Blade1[1] = data.Eigen_Value.ToString("F3");
                    }
                    if (data.EigenValueID.EndsWith("TDF"))
                    {
                        bvm.Blade1[2] = data.Eigen_Value.ToString("F3");
                    }
                    if (data.EigenValueID.Contains("BSC"))
                    {
                        bvm.BSC[0] = data.Eigen_Value.ToString("F3");
                    }
                    else if (data.EigenValueID.Contains("PB"))
                    {
                        bvm.PB = data.Eigen_Value.ToString("F3");
                    }
                }
                else if (data.EigenValueID.Contains("BLD2") || data.EigenValueID.Contains("TWO"))
                {
                    if (data.EigenValueID.EndsWith("TDR"))
                    {
                        bvm.Blade2[0] = data.Eigen_Value.ToString("F3");
                    }
                    if (data.EigenValueID.EndsWith("TDK"))
                    {
                        bvm.Blade2[1] = data.Eigen_Value.ToString("F3");
                    }
                    if (data.EigenValueID.EndsWith("TDF"))
                    {
                        bvm.Blade2[2] = data.Eigen_Value.ToString("F3");
                    }
                    if (data.EigenValueID.Contains("BSC"))
                    {
                        bvm.BSC[1] = data.Eigen_Value.ToString("F3");
                    }
                }
                else if (data.EigenValueID.Contains("BLD3") || data.EigenValueID.Contains("THR"))
                {
                    if (data.EigenValueID.EndsWith("TDR"))
                    {
                        bvm.Blade3[0] = data.Eigen_Value.ToString("F3");
                    }
                    if (data.EigenValueID.EndsWith("TDK"))
                    {
                        bvm.Blade3[1] = data.Eigen_Value.ToString("F3");
                    }
                    if (data.EigenValueID.EndsWith("TDF"))
                    {
                        bvm.Blade3[2] = data.Eigen_Value.ToString("F3");
                    }
                    if (data.EigenValueID.Contains("BSC"))
                    {
                        bvm.BSC[2] = data.Eigen_Value.ToString("F3");
                    }
                } else if (data.EigenValueID.Contains("TSI3"))
                {
                    bvm.TSI = data.Eigen_Value.ToString("F3");
                }
            }
            bvmList.Add(bvm);
            return bvmList;
        }

        //public  bool SaveTrubineImage(string _turbineType, string _turbineImg)
        //{
        //    TurbineStyle image = new TurbineStyle();
        //    image.TurbineModel = _turbineType;
        //    image.StyleVersion = "2.0";
        //    image.StyleText = System.Text.Encoding.Default.GetBytes(_turbineImg.Replace("/r/n", ""));
        //    try
        //    {
        //        DevTreeManagement.AddTurbineStyle(image);
        //        //WindTurbineCondition.UpdateTurbineImage(image);
        //    }
        //    catch (Exception)
        //    {
        //        //记录log
        //        return false;
        //    }
        //    return true;
        //}

        // 20181218 by SUNQI 方法从依赖中copy
        /// <summary>
        /// 获取中貌图中的Group
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <returns></returns>
        private static List<LabelGroup> GetLabelGroupList(string _turbineId,bool isSuper)
        {
            List<LabelGroup> list = null;
            List<EigenValueData_Vib> eigenList = null;
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                eigenList = ctx.EVData_Vibs.Where(item => item.WindTurbineID == _turbineId).ToList();
            }
            //如果不是超级用户，只显示vdi特征值。
            List<EigenValueData_Vib> resEigenList = new List<EigenValueData_Vib>();
            if (!isSuper)
            {
                //1. 获取VDI特征值ID
                List<AlarmDefinition> alarmDefList = AlarmDefConfig.GetAlarmListByTurID(_turbineId);
                foreach (var items in eigenList)
                {
                    var data = alarmDefList.FirstOrDefault(obj => obj.EigenValueID == items.EigenValueID);
                    if (data != null)
                    {
                        resEigenList.Add(items);
                    }
                }
            }else
            {
                resEigenList = eigenList;
            }
            using (CMSFramework.EF.OViewContext ctx = new CMSFramework.EF.OViewContext(ConfigInfo.DBConnName))
            {
                list = ctx.OViewLabelGroups.Where(item => item.WindTurbineID == _turbineId).ToList();
                List<LabelData_EigenValue> lableList = ctx.OViewLabelEVs.Where(item => item.WindTurbineID == _turbineId).ToList();
                list.ForEach(item => {
                    item.LabelDataList = lableList.OfType<LabelData>().Where(lab => lab.LabelGroupID == item.LabelGroupID).ToList();
                    item.LabelDataList.ForEach(lable => {
                        ((LabelData_EigenValue)lable).EVData = resEigenList.FindAll(ev => ev.EigenValueID == ((LabelData_EigenValue)lable).EigenValueID).OrderByDescending(ev => ev.AlarmDegree).FirstOrDefault();
                    });
                });
            }
            return list;
        }

        //标签组

        public string GetLabelGroup(string turbineId,bool isSuper, out string DataTime)
        {
            DataTime = "";
            List<LabelGroup> labelGroupList = GetLabelGroupList(turbineId, isSuper);
            //取得机组下报警定义列表
            List<AlarmDefinition> alarmDef = AlarmDefinitionManage.GetMDFAlarmDefListByTurbineId(turbineId, EnumMeasLocType.VibAlarmDef);
            List<UILabelGroup> labelListUI = new List<UILabelGroup>();

            var park = DevTreeManagement.GetWindParkByTurID(turbineId);
            var turbine = DevTreeManagement.GetWindTurbine(turbineId);
            for (int i = 0; i < labelGroupList.Count; i++)
            {
                LabelGroup label = labelGroupList[i];
                UILabelGroup labelUI = new UILabelGroup
                {
                    ID = label.LabelGroupID,
                    Name = label.LabelGroupName,
                    labelX = label.Left,
                    labelY = label.Top,
                    Width = label.LabelWidth,
                    Height = label.LabelHeight,
                    Direction = label.Direction,
                    //传感器位置总貌图暂时未提供，先不做处理
                    //if (label.LableTransLoc != null)
                    //{
                    //    labelUI.sensorX = Convert.ToInt32(label.LableTransLoc.TranX);
                    //    labelUI.sensorY = Convert.ToInt32(label.LableTransLoc.TranY);
                    //}
                    //else {
                    //    labelUI.sensorX = 0;
                    //    labelUI.sensorY = 0;
                    //}
                    sensorX = 0,
                    sensorY = 0,
                    DataList = new List<UILabelData>()
                };
                for (int j = 0; j < label.LabelDataList.Count; j++)
                {
                    UILabelData data = new UILabelData();
                    data.DataName = EigenValueManage.GetFreBandByCode(label.LabelDataList[j].DataName);
                    if (label.LabelDataList[j].LabelDataType == EnumLabelDataType.EVData)
                    {
                        double waringPec = 0;
                        double alarmPec = 0;
                        double process = 0;
                        LabelData_EigenValue labelDataValue = label.LabelDataList[j] as LabelData_EigenValue;
                        if (labelDataValue.EVData == null)
                        {
                            continue;
                        }
                        data.DataValue = labelDataValue.EVData.Eigen_Value.ToString("F3");
                        //数据单位 新增VRMS
                        data.UnitName = ((EigenValueData_Vib)labelDataValue.EVData).EigenValueCode.IndexOf("VRMS") > -1 ? "mm/s" : "m/s^2";
                        if (string.IsNullOrEmpty(DataTime))
                            DataTime = labelDataValue.EVData.AcquisitionTime.ToString();
                        for (int alarm = 0; alarm < alarmDef.Count; alarm++)
                        {
                            if (alarmDef[alarm].EigenValueID == labelDataValue.EVData.EigenValueID)
                            {
                                AlarmDefThreshold Threshold = alarmDef[alarm].AlarmDefThresholdGroup.Find(alvalue => alvalue.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning);
                                if (Threshold != null)
                                {
                                    waringPec = (double)Threshold.ThresholdValue;
                                }
                                Threshold = alarmDef[alarm].AlarmDefThresholdGroup.Find(alvalue => alvalue.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm);
                                if (Threshold != null)
                                {
                                    waringPec = (double)Threshold.ThresholdValue;
                                }
                            }
                        }
                        //状态为报警
                        if (labelDataValue.EVData.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm)
                        {
                            process = labelDataValue.EVData.Eigen_Value * 1.3;
                        }
                        else
                        {
                            process = alarmPec * 1.3;
                        }
                        data.waringValue = ((waringPec / process) * 100).ToString("F2");
                        data.alarmValue = ((alarmPec / process) * 100).ToString("F2");
                        data.status = ((labelDataValue.EVData.Eigen_Value / process) * 100).ToString("F2");
                        data.statusColor = Utility.GetColorFromAlarmType(labelDataValue.EVData.AlarmDegree);
                    }
                    data.LabelGroupID = label.LabelDataList[j].LabelGroupID;
                    data.LabelDataID = label.LabelDataList[j].LabelDataID;
                    data.DataName = label.LabelDataList[j].DataName;// EigenValueManage.GetFreBandByCode(label.LabelDataList[j].DataName);
                    data.DataName = EigenValueManage.GetFreBandByCode(label.LabelDataList[j].DataName);
                    data.Order = label.LabelDataList[j].Order;
                    data.LabelDataType = (int)label.LabelDataList[j].LabelDataType;
                    labelUI.DataList.Add(data);
                }
                // 添加实时特征值


                // 检查是否存在对应的测点
                foreach (string key in EvdataDic.evRTDic.Keys)
                {
                    if (key.Contains($"{park.WindParkName}_{turbine.WindTurbineName}"))
                    {

                        if (EvdataDic.evRTDic[key]["tags"]["MeasurePos"].Value<string>() == labelUI.Name)
                        {
                            UILabelData _data = new UILabelData()
                            {
                                DataName = EvdataDic.evRTDic[key]["tags"]["FeatureType"].Value<string>(),
                                DataValue = (EvdataDic.evRTDic[key]["fields"]["feature"].Value<double>()).ToString("F3"),
                                LabelDataType = 1,
                                statusColor = "#00FF00",
                                LabelDataID = labelUI.Name + EvdataDic.evRTDic[key]["tags"]["FeatureType"].Value<string>(),
                                UnitName = "m/s^2",
                            };
                            labelUI.DataList.Add(_data);
                            break;
                        }
                    }
                }
                labelListUI.Add(labelUI);
            }
            return labelListUI.ToJson();
        }
        //机组工况实时状态
        public string GetTurStatusByID(string turbineId)
        {
            List<WorkingConditionData> _workCondList = RealTimeDataManage.GetAllWorkingCondDataListByTurbineID(turbineId);
            List<WorkingCondition> workingConditionList = new List<WorkingCondition>();
            for (int i = 0; i < _workCondList.Count; i++)
            {
                WorkingCondition condition = new WorkingCondition();
                EnumWorkCondition_ParamType ParmaType = _workCondList[i].Param_Type_Code;
                condition.Name = AppFramework.Utility.EnumHelper.GetDescription(ParmaType);
                condition.Value = _workCondList[i].Param_Value.ToString("0.00") + EnumWorkCondParamTypeHelper.GetEngUnit(ParmaType);
                workingConditionList.Add(condition);
            }
            double? RotValue = RealTimeDataManage.GetRotSpdRTDAUByTurID(turbineId);
            if (RotValue != null)
            {
                WorkingCondition rotSpdRT = new WorkingCondition();
                rotSpdRT.Name = "发电机转速(CMS)";
                rotSpdRT.Value = RotValue.Value.ToString("0.00") + "RPM";
                workingConditionList.Add(rotSpdRT);
            }
            return workingConditionList.ToJson();
        }

        //获取机组工况列表
        public string GetWorkCondLabelListByTurId(string turbineID)
        {
            return "";
        }

        //组成机组的标签组
        public string GetSettingLabel(string turbineId)
        {
            List<UILabelGroup> labelList = new List<UILabelGroup>();
            //测量位置  图片大小750*500 px
            List<MeasLoc_Vib> measlocList = new List<MeasLoc_Vib>();
            measlocList = DevTreeManagement.GetVibMeasLocationByTurId(turbineId);
            //特征值列表
            for (int i = 0; i < measlocList.Count; i++)
            {
                //设置传感器位置和坐标
                UILabelGroup label = SetsensorLoc(measlocList[i]);
                //对应标签组
                List<EigenValueData_Vib> dataList = new List<EigenValueData_Vib>();
                dataList = null;// EigenValueManage.GetEigenValueList(turbineId, measlocList[i].MeasLocationID);
                label.DataList = SetLabelGroup(dataList);
                //总貌图配置时，lable 根据传感器位置计算，后台不需要设定

                labelList.Add(label);
            }
            return labelList.ToJson();
        }

        private List<UILabelData> SetLabelGroup(List<EigenValueData_Vib> dataList)
        {
            List<UILabelData> labeldataList = new List<UILabelData>();
            for (int i = 0; i < dataList.Count; i++)
            {
                UILabelData labelData = new UILabelData();
                labelData.DataName = dataList[i].EigenValueCode;//EigenValueManage.GetFreBandByCode(dataList[i].EigenValueCode);
                labeldataList.Add(labelData);
            }
            return labeldataList;
        }

        public UILabelGroup SetsensorLoc(MeasLoc_Vib measLoc_Vib)
        {
            UILabelGroup labelGroup = new UILabelGroup();
            //根据测量位置，对传感器进行模糊指定，方便设置。
            switch (measLoc_Vib.MeasLocName)
            {
                case "主轴前轴承径向水平":
                    labelGroup.sensorX = 264;
                    labelGroup.sensorY = 150;
                    break;
                case "主轴后轴承径向垂直":
                    labelGroup.sensorX = 300;
                    labelGroup.sensorY = 150;
                    break;
                case "齿轮箱一级内齿圈径向垂直":
                    labelGroup.sensorX = 300;
                    labelGroup.sensorY = 150;
                    break;
                case "齿轮箱二级内齿圈径向垂直":
                    labelGroup.sensorX = 300;
                    labelGroup.sensorY = 150;
                    break;
                case "齿轮箱输出端径向垂直":
                    labelGroup.sensorX = 300;
                    labelGroup.sensorY = 150;
                    break;
                case "发电机驱动端径向垂直":
                    labelGroup.sensorX = 300;
                    labelGroup.sensorY = 150;
                    break;
                case "发电机非驱动端径向垂直":
                    labelGroup.sensorX = 300;
                    labelGroup.sensorY = 150;
                    break;
                default:
                    labelGroup.sensorX = 100;
                    labelGroup.sensorY = 150;
                    break;
            }
            labelGroup.sensorID = measLoc_Vib.MeasLocationID;
            labelGroup.title = measLoc_Vib.MeasLocName;
            labelGroup.sensorWidth = 10;
            labelGroup.sensorHeight = 10;

            //传感器类型
            //labelGroup.sensorType = measLoc_Vib.
            return labelGroup;
        }
    }


    /// <summary>
    /// 机组工况状态
    /// </summary>
    public class WorkingCondition
    {
        /// <summary>
        /// 工况名
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 工况数据值
        /// </summary>
        public string Value { get; set; }
        /// <summary>
        /// 工况使用样式
        /// </summary>
        public string css { get; set; }
        /// <summary>
        /// 工况级别
        /// </summary>
        public string level { get; set; }
    }
    /// <summary>
    /// 标签组
    /// </summary>
    public class UILabelGroup
    {
        /// <summary>
        /// 标签组ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 标签组名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 标签组X坐标
        /// </summary>
        public int labelX { get; set; }
        /// <summary>
        /// 标签组Y坐标
        /// </summary>
        public int labelY { get; set; }
        /// <summary>
        /// 标签组Y坐标
        /// </summary>
        public int Width { get; set; }
        /// <summary>
        /// 标签组Y坐标
        /// </summary>
        public int Height { get; set; }
        /// <summary>
        /// 展开方向?前台可能不需要
        /// </summary>
        public string Direction { get; set; }

        public List<UILabelData> DataList { get; set; }
        /// <summary>
        /// 传感器类型(指定传感器显示方向)
        /// </summary>
        public string sensorType { get; set; }
        /// <summary>
        /// 传感器ID 唯一识别
        /// </summary>
        public string sensorID { get; set; }
        /// <summary>
        /// 传感器X坐标
        /// </summary>
        public int sensorX { get; set; }
        /// <summary>
        /// 传感器Y坐标
        /// </summary>
        public int sensorY { get; set; }
        /// <summary>
        /// 传感器宽度
        /// </summary>
        public int sensorWidth { get; set; }
        /// <summary>
        /// 传感器高度
        /// </summary>
        public int sensorHeight { get; set; }
        /// <summary>
        /// title 测量位置
        /// </summary>
        public string title { get; set; }

    }
    public class UILabelData
    {
        //-----------------------------------------------------------------------------------------------------------------------
        /// <summary>
        /// DataLabelGroupID 标签组ID
        /// </summary>
        public string LabelGroupID
        {
            get;
            set;
        }


        //-----------------------------------------------------------------------------------------------------------------------
        /// <summary>
        /// LabelDataID 标签数据ID
        /// </summary>
        public string LabelDataID
        {
            get;
            set;
        }

        //-----------------------------------------------------------------------------------------------------------------------
        /// <summary>
        /// DataName 数据名称
        /// </summary>
        public string DataName
        {
            get;
            set;
        }

        //-----------------------------------------------------------------------------------------------------------------------
        /// <summary>
        /// Order 顺序号
        /// </summary>
        public int Order
        {
            get;
            set;
        }
        /// <summary>
        /// 数据标签类别
        /// </summary>
        public int LabelDataType
        {
            get;
            set;
        }
        /// <summary>
        /// 数据值
        /// </summary>
        public string DataValue
        {
            get;
            set;
        }
        public string waringValue
        { get; set; }
        public string alarmValue
        { get; set; }
        public string status
        { get; set; }
        public string statusColor
        { get; set; }

        public string UnitName
        {
            get;
            set;
        }
    }
}