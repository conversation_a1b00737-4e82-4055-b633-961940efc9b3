import{u as q,W as z}from"./table-RP3jLHlo.js";import{O as K}from"./index-CzSbT6op.js";import{W as j}from"./index-QXLii0rw.js";import{r as l,u as G,y as $,j as B,w as H,h as J,f as C,d as w,o as h,i as Q,b as E,s as X,g as Y,c as Z,m as n}from"./index-BjOW8S1L.js";import{u as ee}from"./configMainControl-Ca1Knjmi.js";import{S as te,d as R,a as ae,t as oe}from"./tools-zTE6InS0.js";import{B as le}from"./index-7iPMz_Qy.js";import{M as se}from"./index-BnSFuLp6.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./ActionButton-C_grUmdF.js";import"./styleChecker-CFtINSLw.js";import"./initDefaultProps-P4j1rGDC.js";import"./shallowequal-gCpTBdTi.js";import"./index-DTxROkTj.js";import"./index-kP-mINcM.js";import"./index-BJEkaghg.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";const ne={key:2},S=320,Se={__name:"mainControl",setup(ie){const x=q(),u=ee(),y=(t={isform:!1})=>[{title:"设备名称",dataIndex:"windTurbineID",inputType:"select",selectOptions:[],formItemWidth:S,isrequired:!0,...t&&t.isform?{}:{customRender:({record:e})=>e&&e.windTurbineName?e.windTurbineName:""}},{title:"主控IP",dataIndex:"mcsip",columnOperate:{type:"ip"},formItemWidth:S,validateRules:R({type:"ip",title:"主控IP",required:!0})},{title:"端口号",dataIndex:"mcsPort",formItemWidth:S,validateRules:R({type:"port",title:"端口号",required:!0})}],T=l(!1),D=G(),c=l(""),o=l(""),A=l(),_=l(!1),k=l({}),d=l([]),i=l([]),m=l(D.params.id),g=l([]),p=$({tableColumns:y(),deviceList:[]}),O=B(()=>o.value==="batchAdd"||o.value==="batchEdit"?"1200px":"600px"),L=B(()=>o.value==="batchEdit"?["windTurbineID"]:["windTurbineID"]),M=async t=>{if(_.value=!0,v(),m.value){let e=await x.fetchDevTreedDevicelist({windParkID:m.value});p.deviceList=ae(e,{label:"windTurbineName",value:"windTurbineID"})}_.value=!1},v=async t=>{if(m.value){const e=await u.fetchMCSGetMCSInfoList({windParkID:m.value});d.value=e}};H(()=>D.params.id,t=>{m.value=t,M()}),J(()=>{D.params.id&&M()});const I=()=>{T.value=!0},r=()=>{T.value=!1,i.value=[],k.value={},o.value="",c.value="",g.value=[]},N=t=>{const{title:e,operateType:a,tableKey:s}=t;o.value=a,c.value="批量增加主控信息";let b=y({isform:!0});b[0].selectOptions=p.deviceList,i.value=[...b],I()},P=t=>{o.value="batchEdit",c.value="批量修改主控信息";let e=y({isform:!0});e[0].selectOptions=p.deviceList,e[0].disabled=!0,i.value=[...e],g.value=d.value.filter(a=>t.includes(a.windTurbineID)),I()},F=async(t={})=>{const{selectedkeys:e}=t,a=await u.fetchBatchDeleteMCS(e);a&&a.code===1?(v(),r(),n.success("删除成功")):n.error("删除失败:"+a.msg)},W=t=>{const{rowData:e,title:a,operateType:s,tableKey:b}=t;o.value=s,k.value=e,c.value="编辑主控信息";let f=y({isform:!0});f[0].selectOptions=p.deviceList,i.value=[...f],I()},U=async t=>{const e=await u.fetchBatchEditMCS([t]);e&&e.code===1?(v(),r(),n.success("提交成功")):n.error("提交失败:"+e.msg)},V=async t=>{let e=oe(t);if(o.value==="batchEdit"){const s=await u.fetchBatchEditMCS(e);s&&s.code===1?(v(),r(),n.success("提交成功")):n.error("提交失败:"+s.msg);return}const a=await u.fetchBatchAddMCS(e);a&&a.code===1?(v(),r(),n.success("提交成功")):n.error("提交失败:"+a.msg)};return(t,e)=>{const a=le,s=se,b=te;return h(),C(b,{spinning:_.value,size:"large"},{default:w(()=>[Q("div",null,[E(z,{ref:"table",noBatchApply:!0,size:"default","table-key":"0","table-title":"主控信息列表","table-columns":p.tableColumns,"table-operate":["edit","delete","batchDelete","batchAdd"],"record-key":"windTurbineID","table-datas":d.value,onAddRow:N,onDeleteRow:F,onEditRow:W},{rightButtons:w(({selectedRowKeys:f})=>[d.value&&d.value.length?(h(),C(a,{key:0,type:"primary",onClick:re=>P(f),disabled:!f.length},{default:w(()=>e[0]||(e[0]=[Y(" 批量修改 ",-1)])),_:2,__:[0]},1032,["onClick","disabled"])):X("",!0)]),_:1},8,["table-columns","table-datas"]),E(s,{maskClosable:!1,width:O.value,open:T.value,title:c.value,footer:"",onCancel:r},{default:w(()=>[o.value==="add"||o.value==="edit"?(h(),C(K,{key:0,titleCol:i.value,form:A.value,initFormData:k.value,onSubmit:U},null,8,["titleCol","form","initFormData"])):o.value==="batchAdd"||o.value=="batchEdit"?(h(),C(j,{key:1,size:"default","table-key":"0","table-columns":i.value,"table-operate":o.value=="batchEdit"?["copyUp","noAdd"]:["copyUp","delete"],"table-datas":g.value,"noCopyUp-keys":L.value,onSubmit:V,onCancel:r},null,8,["table-columns","table-operate","table-datas","noCopyUp-keys"])):(h(),Z("div",ne))]),_:1},8,["width","open","title"])])]),_:1},8,["spinning"])}}};export{Se as default};
