﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace WindCMS.EF.WTParameter
{
    /// <summary>
    /// 存储塔筒的法兰参数，每条记录对应一层法兰
    /// </summary>
    public partial class WtParametersFlange
    {
        /// <summary>
        /// 法兰的唯一标识符
        /// </summary>
        public int FlangeId { get; set; }
        /// <summary>
        /// 对应的部件ID（外键）
        /// </summary>
        public int CompId { get; set; }
        /// <summary>
        /// 法兰层数（第几层法兰）
        /// </summary>
        public int FlangeLevel { get; set; }
        /// <summary>
        /// 法兰螺栓厂家
        /// </summary>
        public string? BoltManufacturer { get; set; }
        /// <summary>
        /// 法兰螺栓数量
        /// </summary>
        public int? BoltCount { get; set; }
        /// <summary>
        /// 法兰螺栓型号
        /// </summary>
        public string? BoltModel { get; set; }
        /// <summary>
        /// 法兰螺栓长度（单位：mm）
        /// </summary>
        public decimal? BoltLength { get; set; }
        /// <summary>
        /// 法兰螺栓直径（单位：mm）
        /// </summary>
        public decimal? BoltDiameter { get; set; }
        /// <summary>
        /// 夹持长度（单位： mm）
        /// </summary>
        public decimal? ClampingLength { get; set; }
        /// <summary>
        /// 法兰螺栓预紧力标定系数
        /// </summary>
        public decimal? BoltPreTensionForce { get; set; }
        /// <summary>
        /// 部件类型
        /// </summary>
        public string? CompType { get; set; }
        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime? CreatedAt { get; set; }
        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        [JsonIgnore]
        public virtual WtBladeParameter? Comp { get; set; }
        [JsonIgnore]
        public virtual WtTowerParameter? CompNavigation { get; set; }
    }
}
