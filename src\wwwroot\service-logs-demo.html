<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务日志管理演示</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.1/signalr.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
        }
        .section-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
            font-weight: bold;
            color: #333;
        }
        .section-content {
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        input, select, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .log-container {
            background-color: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-entry.error {
            color: #f48771;
        }
        .log-entry.warn {
            color: #dcdcaa;
        }
        .log-entry.info {
            color: #9cdcfe;
        }
        .log-entry.debug {
            color: #608b4e;
        }
        .file-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .file-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .file-item:hover {
            background-color: #f8f9fa;
        }
        .file-item.selected {
            background-color: #e3f2fd;
        }
        .file-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        .col {
            flex: 1;
        }
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            z-index: 1000;
        }
        .connection-status.connected {
            background-color: #28a745;
        }
        .connection-status.disconnected {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">未连接</div>
    
    <div class="container">
        <div class="header">
            <h1>🔍 服务日志管理系统</h1>
            <p>实时日志监控 & 历史日志查看</p>
        </div>
        
        <div class="content">
            <!-- 服务选择区域 -->
            <div class="section">
                <div class="section-header">📋 服务选择</div>
                <div class="section-content">
                    <div class="row">
                        <div class="col">
                            <label for="serviceSelect">选择服务:</label>
                            <select id="serviceSelect">
                                <option value="">请选择服务...</option>
                            </select>
                        </div>
                        <div class="col">
                            <label for="logTypeSelect">日志类型:</label>
                            <select id="logTypeSelect">
                                <option value="debuggerdlog">调试日志 (DebugLog)</option>
                                <option value="errorlog">错误日志 (ErrorLog)</option>
                            </select>
                        </div>
                    </div>
                    <button onclick="checkLogDirectory()">检查日志目录</button>
                    <div id="directoryStatus"></div>
                </div>
            </div>

            <!-- 实时日志区域 -->
            <div class="section">
                <div class="section-header">📡 实时日志监控</div>
                <div class="section-content">
                    <div class="row">
                        <div class="col">
                            <button id="startRealTimeBtn" onclick="startRealTimeLog()" class="btn-success">开始实时日志</button>
                        </div>
                        <div class="col">
                            <button id="stopRealTimeBtn" onclick="stopRealTimeLog()" class="btn-danger" disabled>停止实时日志</button>
                        </div>
                        <div class="col">
                            <button onclick="clearRealTimeLog()" class="btn-warning">清空日志</button>
                        </div>
                    </div>
                    <div id="realTimeStatus"></div>
                    <div class="log-container" id="realTimeLogContainer">
                        等待日志数据...
                    </div>
                </div>
            </div>

            <!-- 历史日志区域 -->
            <div class="section">
                <div class="section-header">📚 历史日志查看</div>
                <div class="section-content">
                    <div class="row">
                        <div class="col">
                            <label for="startDate">开始日期:</label>
                            <input type="date" id="startDate">
                        </div>
                        <div class="col">
                            <label for="endDate">结束日期:</label>
                            <input type="date" id="endDate">
                        </div>
                        <div class="col">
                            <label>&nbsp;</label>
                            <button onclick="loadLogFiles()">查询日志文件</button>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col">
                            <label>日志文件列表:</label>
                            <div class="file-list" id="logFileList">
                                请先查询日志文件...
                            </div>
                        </div>
                        <div class="col">
                            <label>日志内容:</label>
                            <div class="log-container" id="historyLogContainer">
                                请选择日志文件查看内容...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let connection = null;
        let services = [];
        let selectedFileName = null;
        let isRealTimeActive = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSignalR();
            loadServices();
            
            // 设置默认日期
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            document.getElementById('startDate').value = yesterday.toISOString().split('T')[0];
        });

        // 初始化SignalR连接
        async function initSignalR() {
            connection = new signalR.HubConnectionBuilder()
                .withUrl("/Hubs/ServerPerformanceHub")
                .build();

            // 监听实时日志数据
            connection.on("ReceiveServiceLog", function (logItem) {
                addRealTimeLogEntry(logItem);
            });

            // 监听实时日志状态
            connection.on("RealTimeLogStarted", function (result) {
                if (result.success) {
                    showStatus('realTimeStatus', '实时日志监控已启动', 'success');
                    document.getElementById('startRealTimeBtn').disabled = true;
                    document.getElementById('stopRealTimeBtn').disabled = false;
                    isRealTimeActive = true;
                } else {
                    showStatus('realTimeStatus', '启动实时日志监控失败: ' + result.message, 'error');
                }
            });

            connection.on("RealTimeLogStopped", function (result) {
                if (result.success) {
                    showStatus('realTimeStatus', '实时日志监控已停止', 'info');
                    document.getElementById('startRealTimeBtn').disabled = false;
                    document.getElementById('stopRealTimeBtn').disabled = true;
                    isRealTimeActive = false;
                } else {
                    showStatus('realTimeStatus', '停止实时日志监控失败: ' + result.message, 'error');
                }
            });

            connection.onclose(function () {
                updateConnectionStatus(false);
                isRealTimeActive = false;
                document.getElementById('startRealTimeBtn').disabled = false;
                document.getElementById('stopRealTimeBtn').disabled = true;
            });

            try {
                await connection.start();
                updateConnectionStatus(true);
                console.log("SignalR连接成功");
            } catch (err) {
                console.error("SignalR连接失败:", err);
                updateConnectionStatus(false);
            }
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');
            if (connected) {
                statusElement.textContent = '已连接';
                statusElement.className = 'connection-status connected';
            } else {
                statusElement.textContent = '未连接';
                statusElement.className = 'connection-status disconnected';
            }
        }

        // 加载服务列表
        async function loadServices() {
            try {
                const response = await fetch('/api/ServerConf/GetServiceConfigs');
                const result = await response.json();
                
                if (result.code === 1) {
                    services = result.data;
                    const select = document.getElementById('serviceSelect');
                    select.innerHTML = '<option value="">请选择服务...</option>';
                    
                    services.forEach(service => {
                        const option = document.createElement('option');
                        option.value = service.serviceId;
                        option.textContent = `${service.serviceName} (${service.serviceId})`;
                        select.appendChild(option);
                    });
                } else {
                    console.error('加载服务列表失败:', result.msg);
                }
            } catch (error) {
                console.error('加载服务列表失败:', error);
            }
        }

        // 检查日志目录
        async function checkLogDirectory() {
            const serviceId = document.getElementById('serviceSelect').value;
            const logType = document.getElementById('logTypeSelect').value;
            
            if (!serviceId) {
                showStatus('directoryStatus', '请先选择服务', 'error');
                return;
            }

            try {
                const response = await fetch(`/api/ServerConf/CheckLogDirectory?serviceId=${serviceId}&logType=${logType}`);
                const result = await response.json();
                
                if (result.code === 1) {
                    if (result.data) {
                        showStatus('directoryStatus', '日志目录存在，可以查看日志', 'success');
                    } else {
                        showStatus('directoryStatus', '日志目录不存在，请检查服务配置', 'error');
                    }
                } else {
                    showStatus('directoryStatus', '检查失败: ' + result.msg, 'error');
                }
            } catch (error) {
                showStatus('directoryStatus', '检查失败: ' + error.message, 'error');
            }
        }

        // 开始实时日志
        async function startRealTimeLog() {
            const serviceId = document.getElementById('serviceSelect').value;
            const logType = document.getElementById('logTypeSelect').value;
            
            if (!serviceId) {
                showStatus('realTimeStatus', '请先选择服务', 'error');
                return;
            }

            if (!connection || connection.state !== signalR.HubConnectionState.Connected) {
                showStatus('realTimeStatus', 'SignalR连接未建立', 'error');
                return;
            }

            try {
                await connection.invoke("StartRealTimeLog", serviceId, logType);
                showStatus('realTimeStatus', '正在启动实时日志监控...', 'info');
            } catch (error) {
                showStatus('realTimeStatus', '启动失败: ' + error.message, 'error');
            }
        }

        // 停止实时日志
        async function stopRealTimeLog() {
            const serviceId = document.getElementById('serviceSelect').value;
            const logType = document.getElementById('logTypeSelect').value;
            
            if (!connection || connection.state !== signalR.HubConnectionState.Connected) {
                showStatus('realTimeStatus', 'SignalR连接未建立', 'error');
                return;
            }

            try {
                await connection.invoke("StopRealTimeLog", serviceId, logType);
                showStatus('realTimeStatus', '正在停止实时日志监控...', 'info');
            } catch (error) {
                showStatus('realTimeStatus', '停止失败: ' + error.message, 'error');
            }
        }

        // 清空实时日志
        function clearRealTimeLog() {
            document.getElementById('realTimeLogContainer').innerHTML = '';
        }

        // 添加实时日志条目
        function addRealTimeLogEntry(logItem) {
            const container = document.getElementById('realTimeLogContainer');
            const timestamp = new Date(logItem.timestamp).toLocaleString();
            const level = logItem.level.toLowerCase();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.textContent = `[${timestamp}] [${logItem.level}] ${logItem.content}`;
            
            container.appendChild(logEntry);
            container.scrollTop = container.scrollHeight;
        }

        // 加载日志文件列表
        async function loadLogFiles() {
            const serviceId = document.getElementById('serviceSelect').value;
            const logType = document.getElementById('logTypeSelect').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            if (!serviceId) {
                alert('请先选择服务');
                return;
            }

            try {
                let url = `/api/ServerConf/GetLogFiles?serviceId=${serviceId}&logType=${logType}`;
                if (startDate) url += `&startTime=${startDate}T00:00:00`;
                if (endDate) url += `&endTime=${endDate}T23:59:59`;

                const response = await fetch(url);
                const result = await response.json();
                
                if (result.code === 1) {
                    displayLogFiles(result.data);
                } else {
                    document.getElementById('logFileList').innerHTML = `<div style="padding: 20px; text-align: center; color: #666;">获取失败: ${result.msg}</div>`;
                }
            } catch (error) {
                document.getElementById('logFileList').innerHTML = `<div style="padding: 20px; text-align: center; color: #666;">获取失败: ${error.message}</div>`;
            }
        }

        // 显示日志文件列表
        function displayLogFiles(files) {
            const container = document.getElementById('logFileList');
            
            if (files.length === 0) {
                container.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">没有找到日志文件</div>';
                return;
            }

            container.innerHTML = '';
            files.forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.onclick = () => selectLogFile(file.fileName, fileItem);
                
                fileItem.innerHTML = `
                    <div><strong>${file.fileName}</strong></div>
                    <div class="file-info">
                        大小: ${file.fileSizeFormatted} | 
                        修改时间: ${new Date(file.modifiedTime).toLocaleString()}
                    </div>
                `;
                
                container.appendChild(fileItem);
            });
        }

        // 选择日志文件
        async function selectLogFile(fileName, element) {
            // 更新选中状态
            document.querySelectorAll('.file-item').forEach(item => item.classList.remove('selected'));
            element.classList.add('selected');
            
            selectedFileName = fileName;
            
            // 加载文件内容
            const serviceId = document.getElementById('serviceSelect').value;
            const logType = document.getElementById('logTypeSelect').value;
            
            try {
                const response = await fetch(`/api/ServerConf/GetLogContent?serviceId=${serviceId}&logType=${logType}&fileName=${fileName}`);
                const result = await response.json();
                
                if (result.code === 1) {
                    document.getElementById('historyLogContainer').textContent = result.data.content;
                } else {
                    document.getElementById('historyLogContainer').textContent = `加载失败: ${result.msg}`;
                }
            } catch (error) {
                document.getElementById('historyLogContainer').textContent = `加载失败: ${error.message}`;
            }
        }

        // 显示状态消息
        function showStatus(containerId, message, type) {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="status ${type}">${message}</div>`;
            
            // 3秒后自动清除
            setTimeout(() => {
                container.innerHTML = '';
            }, 3000);
        }
    </script>
</body>
</html>
