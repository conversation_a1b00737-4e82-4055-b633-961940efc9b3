import{c as Q,W as M}from"./table-RP3jLHlo.js";import{N as X}from"./noPermission-C8WghqCu.js";import{r as g,u as Z,z as p,y as B,w as ee,B as te,A as f,f as _,x as c,d,o as i,i as v,b,c as h,F as H,e as ae,g as R,t as le,s as A}from"./index-BjOW8S1L.js";import{u as re}from"./configDevice-B6u3cHuO.js";import{u as ne}from"./statusMonitor-eEhdeKkD.js";import{u as se}from"./devTree-Dwa9wLl9.js";import{S as oe,g as o,c as E}from"./tools-zTE6InS0.js";import{_ as me}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as ie,a as ue}from"./index-D82yULGq.js";import"./index-CzSbT6op.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";import"./styleChecker-CFtINSLw.js";import"./index-7iPMz_Qy.js";import"./initDefaultProps-P4j1rGDC.js";import"./index-DTxROkTj.js";import"./shallowequal-gCpTBdTi.js";import"./index-BJEkaghg.js";import"./ActionButton-C_grUmdF.js";import"./index-kP-mINcM.js";const ce={class:"border"},de={class:"componentStatusBox"},ge=["src","title","alt"],pe=["src","title","alt"],fe={__name:"status",setup(De){const P=re(),x=ne(),z=se();let L=window.localStorage.getItem("role")?JSON.parse(window.localStorage.getItem("role")).moduleIds:[],T=window.localStorage.getItem("token")?L.includes("21"):!0;const I=(e={})=>[{label:"设备名称",value:e.windTurbineName},{label:"设备编号",value:e.windTurbineCode},{label:"设备型号",value:e.windTurbineModel},{label:"额定功率(KW)",value:e.ratedPower},{label:"设备厂商",value:e.manufacturer}],N=Z(),w=g(""),u=g(N.params.id),S=g(!1),y=g([p().subtract(3,"months"),p()]),m=B({filterData:[],tableData:[],tableData1:[],tableData2:[]}),k=g([I({})]),D=B({}),O=()=>{let e=z.findAncestorsWithNodes(u.value);e&&e.length&&e.length>1&&(w.value=e[e.length-2].id)},V=async e=>{if(u.value){const t=await P.fetchDeviceInfo({turbineID:u.value});k.value=I(t)}},W=async()=>{const e=await x.fetchGetTurbineStatusCount({TurbineID:u.value,WindParkID:w.value});if(!e||!e.length){m.filterData=[],m.tableData=[];return}let t=[],l=[],n="devSegmentName";e.forEach(r=>{l.indexOf(r[n])===-1&&r[n]&&r[n]!==""&&(l.push(r[n]),t.push({text:r[n],value:r[n]}))}),m.filterData=t,K(m.tableData),U(e)},Y=(e,t)=>{let l=`${e.devSegmentName}_${o(e.alarmDegree).text}`;return t&&(l=`${e.devSegmentName1}_${o(e.alarmDegree1).text}`),D[l]?D[l]:($(e),"/componentStatus/unknown.png")},K=e=>{!e||!e.length||e.forEach(t=>{$(t)})},$=async e=>{let t=`/componentStatus/${e.devSegmentName}_${o(e.alarmDegree).text}.png`,l=`/componentStatus/${e.devSegmentName1}_${o(e.alarmDegree1).text}.png`;const n=`${e.devSegmentName}_${o(e.alarmDegree).text}`,r=`${e.devSegmentName1}_${o(e.alarmDegree1).text}`,a=await E(t),s=await E(l),C=a?t:"/componentStatus/unknown.png",q=s?l:"/componentStatus/unknown.png";D[n]=C,D[r]=q},U=e=>{var l,n,r;if(!e||!e.length){m.tableData=[];return}const t=[];for(let a=0;a<e.length;a+=2)t.push({...e[a],devSegmentName1:(l=e[a+1])==null?void 0:l.devSegmentName,alarmUpdateTime1:(n=e[a+1])==null?void 0:n.alarmUpdateTime,alarmDegree1:(r=e[a+1])==null?void 0:r.alarmDegree});m.tableData=t},F=async()=>{S.value=!0,m.tableData2=await x.fetchGetSearchMonitorLog({turbineID:u.value,beginTime:y.value[0].format("YYYY-MM-DD"),endTime:y.value[1].format("YYYY-MM-DD")}),S.value=!1};ee(()=>N.params.id,e=>{e&&T&&(u.value=e,O(),F(),V(),W())},{immediate:!0});const G=te(()=>[{title:"部件",dataIndex:"devSegmentName",filters:m.filterData},{title:"部件状态",dataIndex:"alarmDegree",filters:[{text:"正常",value:3},{text:"注意",value:5},{text:"危险",value:6},{text:"未知",value:2}],otherColumn:!0},{title:"状态更新时间",dataIndex:"alarmUpdateTime",headerOperations:{sorter:!0,date:!0},customRender:({text:e,record:t})=>t.alarmUpdateTime?f("span",{},p(e).format("YYYY-MM-DD HH:mm:ss")):""},{title:"部件",dataIndex:"devSegmentName1"},{title:"部件状态",dataIndex:"alarmDegree1",otherColumn:!0},{title:"状态更新时间",dataIndex:"alarmUpdateTime1",customRender:({text:e,record:t})=>t.alarmUpdateTime1?f("span",{},p(e).format("YYYY-MM-DD HH:mm:ss")):""}]),j=e=>{const{data:t}=e,{filters:l,sorter:n}=t;let r=x.deviceStatusList;l&&l.devSegmentName&&(r=r.filter(a=>l.devSegmentName.includes(a.devSegmentName))),l&&l.alarmDegree&&(r=r.filter(a=>l.alarmDegree.includes(a.alarmDegree))),n&&n.field&&n.order&&(r=r.sort((a,s)=>{if(n.order==="ascend")return new Date(a.alarmUpdateTime).getTime()-new Date(s.alarmUpdateTime).getTime();if(n.order==="descend")return new Date(s.alarmUpdateTime).getTime()-new Date(a.alarmUpdateTime).getTime()})),U(r)},J=[{title:"状态",dataIndex:"alarmDegree",width:80,headerOperations:{filters:[{text:"正常",value:3},{text:"注意",value:5},{text:"危险",value:6},{text:"未知",value:2}]},customRender:({record:e})=>e.alarmDegree?f("span",{style:{color:o(e.alarmDegree).color}},o(e.alarmDegree).text):""},{title:"详情",dataIndex:"logTitle",customRender:({text:e,record:t})=>t.logTitle?f("p",{style:{textAlign:"left"}},e):""},{title:"状态更新时间",dataIndex:"eventTime",width:160,headerOperations:{sorter:!0},customRender:({text:e,record:t})=>t.eventTime?f("span",{},p(e).format("YYYY-MM-DD HH:mm:ss")):""}];return(e,t)=>{const l=ue,n=ie,r=oe;return c(T)?(i(),_(r,{key:0,spinning:S.value,size:"large"},{default:d(()=>[v("div",null,[b(Q,{tableTitle:"设备信息",defaultCollapse:!0,batchApply:!1},{content:d(()=>[v("div",ce,[b(n,{column:5,size:"small"},{default:d(()=>[(i(!0),h(H,null,ae(k.value,a=>(i(),_(l,{label:a.label,key:a.label},{default:d(()=>[R(le(a.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:1}),v("div",null,[v("div",de,[b(M,{ref:"table",size:"default","table-key":"0","table-title":"设备部件状态","table-columns":c(G),"table-operate":[],noBatchApply:!0,"record-key":"name","table-datas":m.tableData,onHandleTableChange:j},{otherColumn:d(({column:a,record:s,text:C})=>[s.alarmDegree?(i(),h(H,{key:0},[a.dataIndex==="alarmDegree"&&s.devSegmentName?(i(),h("img",{key:0,class:"componentImg",src:Y(s),title:`${s.devSegmentName}${c(o)(s.alarmDegree).text}`,alt:`${s.devSegmentName}${c(o)(s.alarmDegree).text}`},null,8,ge)):a.dataIndex==="alarmDegree1"&&s.devSegmentName1?(i(),h("img",{key:1,class:"componentImg",src:Y(s,"2"),title:`${s.devSegmentName1}${c(o)(s.alarmDegree1).text}`,alt:`${s.devSegmentName1}${c(o)(s.alarmDegree1).text}`},null,8,pe)):A("",!0)],64)):A("",!0)]),_:1},8,["table-columns","table-datas"])]),b(M,{ref:"table",size:"default","table-key":"0","table-title":"特征值报警日志","table-columns":J,"table-operate":[],noBatchApply:!0,"record-key":"name","table-datas":m.tableData2},null,8,["table-datas"])])])]),_:1},8,["spinning"])):(i(),_(X,{key:1},{default:d(()=>t[0]||(t[0]=[R(" 无权限 ",-1)])),_:1,__:[0]}))}}},Ae=me(fe,[["__scopeId","data-v-b10be1af"]]);export{Ae as default};
