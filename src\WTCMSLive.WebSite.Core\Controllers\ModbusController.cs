﻿using CMSFramework.BusinessEntity;
using WTCMSLive.BusinessModel;
using WTCMSLive.BusinessModel.TIM;
using WTCMSLive.WebSite.Models;
using Microsoft.AspNetCore.Mvc;
using Org.BouncyCastle.Ocsp;
using static System.Collections.Specialized.BitVector32;
using System;
using System.Linq;
using WTCMSLive.WebSite.Core.Models;
using System.Data;
using Microsoft.EntityFrameworkCore;
using CMSFramework.DAUEntities;
using WTCMSLive.WebSite.Core.Attributes;
using Microsoft.AspNetCore.Authorization;
using MySqlX.XDevAPI.Common;
using Org.BouncyCastle.Bcpg.OpenPgp;
using WTCMSLive.WebSite.Core.Services;
using WTCMSLive.WebSite.Core.DTOs;

namespace WTCMSLive.WebSite.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ModbusController : ControllerBase
    {

        /// <summary>
        /// 获取modbus设备类型
        /// </summary>
        /// <returns></returns>

        [HttpGet("GetModbusDevType")]
        public IActionResult GetSVMParamType()
        {
            List<KeyValuePair<int, string>> list = new List<KeyValuePair<int, string>>();
            foreach (EnumModbusDevType value in Enum.GetValues(typeof(EnumModbusDevType)))
            {
                list.Add(new KeyValuePair<int, string>((int)value, CommonUtility.GetDecscription(value)));
            }

            return Ok(list);
        }



        public string GetTimList(string turbineID)
        {
            //获取modbus设备
            List<ModbusUnit> modbuslist = TIMManagement.GetModbusunitList(turbineID);

            //
            List<TimCalibration> timUnitlist = TIMManagement.GetTimUnit(turbineID);

            //serialserver 串口服务器
            List<SerialServer> serialserverlist = TIMManagement.GetSerialServer(turbineID);

            // 获取晃度仪寄存器
            List<SVMRegister> svmRegister = SVMManagement.GetSVMRegisterByturbineID(turbineID);
            // 获取晃度仪测量定义
            List<MeasLoc_SVM> svmlist = SVMManagement.GetMeasLoc_SVMListByTurID(turbineID);

            //油液配置
            List<OilUnit> oillist = TIMManagement.GetOilconfigByturID(turbineID);

            // 获取不加名称
            List<WindTurbineComponent> complist = DevTreeManagement.GetComListByTurbineId(turbineID);

            //获取测量定义扩展
            List<MeasDefinition_Ex> measDefEX = MeasDefinitionManagement.GetMeasdefinitionEXListByTurID(turbineID);

            //mdfmoudbus  modbus测量定义
            List<ModbusDef> defmodbuslist = TIMManagement.GetModbusDef(turbineID);

            // 获取DAU名称
            List<WindDAU> daulist = DauManagement.GetDAUListByWindTurbineID(turbineID);
            // 获取测量定义名称
            List<MeasDefinition> measdlsit = MeasDefinitionManagement.GetMeasDefListByTurId(turbineID);

            // 特征值
            List<MeasDef_Ev_Vib> vibEv = EigenValueManage.GetMdfTimeDomainEvConf(turbineID);

            List<TIMModel> resdata = new List<TIMModel>();

            
            modbuslist.ForEach(item =>
            {
                TIMModel itemModel = new TIMModel()
                {
                    
                    WindTurbineID = item.WindTurbineID,
                    ModbusUnitID = item.ModbusUnitID,
                    CommunicationMode = item.CommunicationMode,
                    DauID = item.DauID,
                    modbusType = AppFramework.Utility.EnumHelper.GetDescription(item.ModbusDevType),
                    ModbusDevType = item.ModbusDevType,
                };

                //loclist 根据modbustype区分
                List<string> loclist = new List<string>();
                //compname 
                List<string> compListName = new List<string>();
                //截面名称
                List<string> sectionName = new List<string>();

                //特征值
                List<string> evlist = new List<string>();

                if(item.DauID != null)
                {
                    // DAU名称
                    itemModel.DauName = daulist.FirstOrDefault(p => p.DauID == item.DauID)?.DAUName;

                    if(item.ModbusDevType == EnumModbusDevType.DynamicSVM || item.ModbusDevType == EnumModbusDevType.StaticSVM|| item.ModbusDevType ==  EnumModbusDevType.StaticTIM)
                    {
                        // 获取寄存器
                        List<SVMRegister> svmregisterlist = svmRegister.Where(k => k.SVMID == item.ModbusUnitID).ToList();
                        svmregisterlist.ForEach(h =>
                        {
                            MeasLoc_SVM svmloc = svmlist.FirstOrDefault(g => g.MeasLocationID == h.SVMMeasLocId);
                            if (svmloc != null)
                            {
                                loclist.Add(svmloc.MeasLocName);
                                //部件截面
                                string compname = complist.FirstOrDefault(r => r.ComponentID == svmloc.ComponentID)?.ComponentName;
                                if (!string.IsNullOrEmpty(compname) && !compListName.Contains(compname))
                                {
                                    compListName.Add(compname);
                                }
                                if (!sectionName.Contains(svmloc.SectionName))
                                {
                                    sectionName.Add(svmloc.SectionName);
                                }
                                //Dictionary<string, string> selist = CodeProvide.GetSectionDic(compname);
                                //string sectionname = selist.FirstOrDefault(x => x.Value == svmloc.SectionName).Key;
                                //if(!string.IsNullOrEmpty(sectionname)&& !sectionName.Contains(sectionname))
                                //{
                                //    sectionName.Add(sectionname);
                                //}

                                var measvib = vibEv.Where(t => t.MeasLocationID == h.SVMMeasLocId).GroupBy(t=>t.Type).Select(t=>t.Key).ToList();
                                if(measvib!=null && measvib.Count > 0)
                                {
                                    foreach(var m in measvib)
                                    {
                                        if (!evlist.Contains(((int)m).ToString()))
                                        {
                                            evlist.Add(((int)m).ToString());
                                        }
                                    }
                                }
                                
                            }


                        });
                    }else if(item.ModbusDevType == EnumModbusDevType.SenseStarOil || item.ModbusDevType == EnumModbusDevType.WeridaOil)
                    {
                        OilUnit oilconf = oillist.FirstOrDefault(v => v.OilUnitID == item.ModbusUnitID);
                        if(oilconf != null)
                        {
                            if (oilconf.ViscositySensorEnabled) { loclist.Add("粘度"); };
                            if (oilconf.WaterSensorEnabled) { loclist.Add("水分"); };
                            if (oilconf.WearParticleSensorEnabled) { loclist.Add("磨粒"); };
                        }
                    }
                    
                }
                itemModel.loclist = string.Join(",",loclist);
                itemModel.CompName = string.Join(",", compListName);
                itemModel.SectionName = string.Join(",", sectionName);
                itemModel.evList = string.Join(",",evlist);
                

                // 添加类型判断 by sq
                if(item.CommunicationMode == EnumCommunicationMode.BySerialServer)
                {
                    itemModel.SerialServer = serialserverlist.FirstOrDefault(p => p.ModbusUnitID == item.ModbusUnitID);
                }

                itemModel.TimUnit = timUnitlist.FirstOrDefault(p=>p.TimUnitID == item.ModbusUnitID);

                var measd = defmodbuslist.FirstOrDefault(p => p.ModbusUnitID == item.ModbusUnitID);
                if(measd!= null)
                {
                    itemModel.Mdfmodbus = measd;
                    itemModel.Mdfex = measDefEX.FirstOrDefault(p => p.MeasDefinitionID == measd.MeasDefinitionID);

                    itemModel.MeasName = measdlsit.FirstOrDefault(p => p.MeasDefinitionID == measd.MeasDefinitionID)?.MeasDefinitionName;
                }
                if (itemModel.modbusType == "鑫世达油液传感器") {
                    itemModel.modbusType = "油液传感器";
                }

                resdata.Add(itemModel);
            });

            return resdata.ToJson();
        }
        [HttpPost]
        public string EditTIM()
        {
            string turbineID = Request.Form["turbineID"];
            string dauID = Request.Form["dauID"];
            string ModbusID = Request.Form["ModbusID"];
            string comIP = Request.Form["comIP"];
            string comType = Request.Form["comType"];
            int comPort = 0;
            if (!string.IsNullOrEmpty(Request.Form["comPort"]))
            {
                comPort = Convert.ToInt32(Request.Form["comPort"]);
            }
            double timeLength = 0;
            if (!string.IsNullOrEmpty(Request.Form["timeLength"]))
            {
                timeLength = Convert.ToDouble(Request.Form["timeLength"]);
            }

            float freq = 0;
            if (!string.IsNullOrEmpty(Request.Form["freq"]))
            {
                freq = (float)Convert.ToDecimal(Request.Form["freq"]);
            }

            // 获取已存在的对象
            // 可修改内容: 连接方式
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                var modbusunit = ctx.ModbusUnits.FirstOrDefault(obj => obj.ModbusUnitID == ModbusID && obj.WindTurbineID == turbineID);
                var serialServer = ctx.SerialServers.FirstOrDefault(obj => obj.ModbusUnitID == ModbusID && obj.WindTurbineID == turbineID);
                if (comType=="1")
                {
                    //需要删除或者不做操作
                    modbusunit.CommunicationMode = EnumCommunicationMode.BySerialPort;
                    ctx.ModbusUnits.Attach(modbusunit);
                    ctx.Entry(modbusunit).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                    if (serialServer != null)
                    {
                        ctx.SerialServers.Remove(serialServer);
                    }
                }
                else
                {
                    if (serialServer == null)
                    {
                        modbusunit.CommunicationMode = EnumCommunicationMode.BySerialServer;
                        ctx.ModbusUnits.Attach(modbusunit);
                        ctx.Entry(modbusunit).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                        ctx.SerialServers.Add(new SerialServer()
                        {
                            ModbusUnitID = ModbusID,
                            SerialServerIP = comIP,
                            SerialServerPort = comPort,
                            WindTurbineID = turbineID
                        });
                    }
                    else
                    {
                        serialServer.SerialServerIP = comIP;
                        serialServer.SerialServerPort = comPort;
                        ctx.SerialServers.Attach(serialServer);
                        ctx.Entry(serialServer).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                    }
                }
                ctx.SaveChanges();
            }
            // 可修改内容: 测量定义
            if (timeLength != 0)
            {
                SVMManagement.UpdateDefin(turbineID, freq, timeLength, ModbusID);
            }
            return GetTimList(turbineID);
        }


        /// <summary>
        /// 添加Modbus设备
        /// </summary>
        /// <param name="request">Modbus设备信息</param>
        /// <returns></returns>
        [HttpPost("AddModbusDevice")]
        [BatchOperation(nameof(BatchAddModbusDevice))]
        public IActionResult AddModbusDevice([FromBody] BatchAddModbusDeviceRequest request)
        {
            try
            {
                // 参数验证
                if (request?.SourceData == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                var modbus = request.SourceData;
                if (string.IsNullOrEmpty(modbus.TurbineID))
                {
                    return Ok(ApiResponse<string>.Error("机组ID不能为空"));
                }

                if (string.IsNullOrEmpty(modbus.ModbusID))
                {
                    return Ok(ApiResponse<string>.Error("ModbusID不能为空"));
                }

                if (string.IsNullOrEmpty(modbus.ModbusType))
                {
                    return Ok(ApiResponse<string>.Error("Modbus设备类型不能为空"));
                }

                // 检查ModbusID是否已存在
                List<ModbusUnit> existingModbusUnits = TIMManagement.GetModbusunitList(modbus.TurbineID);
                //ModbusUnit existingUnit = existingModbusUnits.FirstOrDefault(item => item.ModbusUnitID == modbus.ModbusID);
                //if (existingUnit != null)
                //{
                //    return Ok(ApiResponse<string>.Error($"ModbusID {modbus.ModbusID} 已存在"));
                //}

                // 使用事务添加Modbus设备，保证原子性
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            // 添加ModbusUnit
                            var modbusUnit = new ModbusUnit()
                            {
                                WindTurbineID = modbus.TurbineID,
                                DauID = modbus.DauID,
                                ModbusUnitID = modbus.ModbusID,
                                CommunicationMode = (EnumCommunicationMode)Convert.ToInt32(modbus.ComType),
                                ModbusDevType = (EnumModbusDevType)Convert.ToInt32(modbus.ModbusType),
                                ModbusDeviceName = modbus.ModbusDeviceName,
                            };
                            ctx.ModbusUnits.Add(modbusUnit);
                            ctx.SaveChanges();

                            // 根据通信类型添加相应配置
                            if (modbus.ComType == "0") // 串口服务器
                            {
                                if (!string.IsNullOrEmpty(modbus.ComIP) && modbus.ComPort.HasValue)
                                {
                                    ctx.SerialServers.Add(new SerialServer()
                                    {
                                        ModbusUnitID = modbus.ModbusID,
                                        SerialServerIP = modbus.ComIP,
                                        SerialServerPort = modbus.ComPort.Value,
                                        WindTurbineID = modbus.TurbineID,
                                        ModbusDevType = modbusUnit.ModbusDevType,
                                        ModbusDeviceID = modbusUnit.ModbusDeviceID,
                                    });
                                }
                            }
                            else if (modbus.ComType == "1") // 串口
                            {
                                if (!string.IsNullOrEmpty(modbus.PortName))
                                {
                                    ctx.SerialPorts.Add(new SerialPortParam()
                                    {
                                        WindTurbineID = modbus.TurbineID,
                                        ModbusUnitID = modbus.ModbusID,
                                        PortName = modbus.PortName,
                                        BaudRate = modbus.BaudRate,
                                        DataBit = modbus.DataBit,
                                        StopBit = modbus.StopBit,
                                        Parity = modbus.Parity,
                                        ModbusDevType = modbusUnit.ModbusDevType,
                                        ModbusDeviceID = modbusUnit.ModbusDeviceID,
                                    });
                                }
                            }

                            // 根据设备类型处理特殊逻辑
                            switch (modbus.ModbusType)
                            {
                                case "0": // 倾角仪
                                case "1": // 静态晃度仪
                                case "2": // 动态晃度仪
                                    // 确保塔筒部件存在
                                    List<WindTurbineComponent> componentList = DevTreeManagement.GetComListByTurbineId(modbus.TurbineID);
                                    WindTurbineComponent towerComponent = componentList.Find(i => i.ComponentName == "塔筒");
                                    if (towerComponent == null)
                                    {
                                        towerComponent = new WindTurbineComponent()
                                        {
                                            WindTurbineID = modbus.TurbineID,
                                            ComponentName = "塔筒",
                                            ComponentID = $"{modbus.TurbineID}TOW",
                                            CompManufacturer = "未知"
                                        };
                                        DevTreeManagement.AddDevComp(towerComponent);
                                    }

                                    // 添加SVM单元
                                    SVMManagement.AddSVM(new SVMUnit()
                                    {
                                        ComponentID = towerComponent.ComponentID,
                                        AssocWindTurbineID = modbus.TurbineID,
                                        SVMName = modbus.ModbusID,
                                        SVMID = modbus.ModbusID,
                                        ModbusAddress = modbus.ModbusID,
                                    });

                                    // 倾角仪需要添加标定信息
                                    if (modbus.ModbusType == "0")
                                    {
                                        TIMManagement.AddTimUnit(new TimCalibration()
                                        {
                                            WindTurbineID = modbus.TurbineID,
                                            TimUnitID = modbus.ModbusID,
                                            CalibAngleUpdateTime = DateTime.Now,
                                            CalibrationAngleX = 0,
                                            CalibrationAngleY = 0,
                                        });
                                    }
                                    break;

                                case "3": // 油液配置
                                    // 油液配置的特殊处理逻辑可以在这里添加
                                    break;
                            }

                            ctx.SaveChanges();
                            transaction.Commit();

                            CMSFramework.Logger.Logger.LogInfoMessage($"[AddModbusDevice]成功添加Modbus设备: TurbineID={modbus.TurbineID}, ModbusID={modbus.ModbusID}");
                            return Ok(ApiResponse<string>.Success("添加成功"));
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            CMSFramework.Logger.Logger.LogErrorMessage("[AddModbusDevice]添加Modbus设备失败", ex);
                            return Ok(ApiResponse<string>.Error($"添加失败: {ex.Message}"));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[AddModbusDevice]添加Modbus设备异常", ex);
                return Ok(ApiResponse<string>.Error($"操作异常: {ex.Message}"));
            }
        }

        /// <summary>
        /// 编辑Modbus设备
        /// </summary>
        /// <param name="request">编辑的Modbus设备信息</param>
        /// <returns></returns>
        [HttpPost("EditModbusDevice")]
        [BatchOperation(nameof(BatchEditModbusDevice))]
        public IActionResult EditModbusDevice([FromBody] BatchEditModbusDeviceRequest request)
        {
            try
            {
                // 参数验证
                if (request?.SourceData == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                var editModbus = request.SourceData;
                if (string.IsNullOrEmpty(editModbus.TurbineID))
                {
                    return Ok(ApiResponse<string>.Error("机组ID不能为空"));
                }

                if (string.IsNullOrEmpty(editModbus.ModbusID))
                {
                    return Ok(ApiResponse<string>.Error("ModbusID不能为空"));
                }

                // 使用事务编辑Modbus设备，保证原子性
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            // 查找现有的Modbus设备
                            var existingModbusUnit = ctx.ModbusUnits.FirstOrDefault(obj =>
                                obj.ModbusDeviceID == editModbus.ModbusDeviceID && obj.WindTurbineID == editModbus.TurbineID);

                            if (existingModbusUnit == null)
                            {
                                return Ok(ApiResponse<string>.Error($"未找到指定的Modbus设备: {editModbus.ModbusID}"));
                            }

                            // 更新ModbusUnit基本信息
                            if (!string.IsNullOrEmpty(editModbus.DauID))
                            {
                                existingModbusUnit.DauID = editModbus.DauID;
                            }

                            if (!string.IsNullOrEmpty(editModbus.ModbusType))
                            {
                                existingModbusUnit.ModbusDevType = (EnumModbusDevType)Convert.ToInt32(editModbus.ModbusType);
                            }

                            if (!string.IsNullOrEmpty(editModbus.ComType))
                            {
                                existingModbusUnit.CommunicationMode = (EnumCommunicationMode)Convert.ToInt32(editModbus.ComType);
                            }

                            if (!string.IsNullOrEmpty(editModbus.ModbusDeviceName))
                            {
                                existingModbusUnit.ModbusDeviceName = editModbus.ModbusDeviceName;
                            }

                            if (!string.IsNullOrEmpty(editModbus.ModbusID))
                            {
                                existingModbusUnit.ModbusUnitID = editModbus.ModbusID;
                            }

                            ctx.ModbusUnits.Attach(existingModbusUnit);
                            ctx.Entry(existingModbusUnit).State = Microsoft.EntityFrameworkCore.EntityState.Modified;

                            // 处理通信配置的更新
                            var existingSerialServer = ctx.SerialServers.FirstOrDefault(obj =>
                                obj.ModbusDeviceID == editModbus.ModbusDeviceID && obj.WindTurbineID == editModbus.TurbineID);
                            var existingSerialPort = ctx.SerialPorts.FirstOrDefault(obj =>
                                obj.ModbusDeviceID == editModbus.ModbusDeviceID && obj.WindTurbineID == editModbus.TurbineID);

                            if (editModbus.ComType == "0") // 串口服务器
                            {
                                // 删除可能存在的串口配置
                                if (existingSerialPort != null)
                                {
                                    ctx.SerialPorts.Remove(existingSerialPort);
                                }

                                // 更新或添加串口服务器配置
                                if (existingSerialServer != null)
                                {
                                    if (!string.IsNullOrEmpty(editModbus.ComIP))
                                    {
                                        existingSerialServer.SerialServerIP = editModbus.ComIP;
                                    }
                                    if (editModbus.ComPort.HasValue)
                                    {
                                        existingSerialServer.SerialServerPort = editModbus.ComPort.Value;
                                    }

                                    if (!string.IsNullOrEmpty(editModbus.ModbusID))
                                    {
                                        existingSerialServer.ModbusUnitID = editModbus.ModbusID;
                                    }
                                    ctx.SerialServers.Attach(existingSerialServer);
                                    ctx.Entry(existingSerialServer).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                                }
                                else if (!string.IsNullOrEmpty(editModbus.ComIP) && editModbus.ComPort.HasValue)
                                {
                                    ctx.SerialServers.Add(new SerialServer()
                                    {
                                        ModbusUnitID = editModbus.ModbusID,
                                        SerialServerIP = editModbus.ComIP,
                                        SerialServerPort = editModbus.ComPort.Value,
                                        WindTurbineID = editModbus.TurbineID,
                                        ModbusDevType = existingModbusUnit.ModbusDevType,
                                        ModbusDeviceID = existingModbusUnit.ModbusDeviceID,
                                    });
                                }
                            }
                            else if (editModbus.ComType == "1") // 串口
                            {
                                // 删除可能存在的串口服务器配置
                                if (existingSerialServer != null)
                                {
                                    ctx.SerialServers.Remove(existingSerialServer);
                                }

                                // 更新或添加串口配置
                                if (existingSerialPort != null)
                                {
                                    if (!string.IsNullOrEmpty(editModbus.PortName))
                                    {
                                        existingSerialPort.PortName = editModbus.PortName;
                                    }
                                    if (editModbus.BaudRate.HasValue)
                                    {
                                        existingSerialPort.BaudRate = editModbus.BaudRate;
                                    }
                                    if (editModbus.DataBit.HasValue)
                                    {
                                        existingSerialPort.DataBit = editModbus.DataBit;
                                    }
                                    if (editModbus.StopBit.HasValue)
                                    {
                                        existingSerialPort.StopBit = editModbus.StopBit;
                                    }
                                    if (editModbus.Parity.HasValue)
                                    {
                                        existingSerialPort.Parity = editModbus.Parity;
                                    }

                                    if (!string.IsNullOrEmpty(editModbus.ModbusID))
                                    {
                                        existingSerialPort.ModbusUnitID = editModbus.ModbusID;
                                    }
                                    ctx.SerialPorts.Attach(existingSerialPort);
                                    ctx.Entry(existingSerialPort).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                                }
                                else if (!string.IsNullOrEmpty(editModbus.PortName))
                                {
                                    ctx.SerialPorts.Add(new SerialPortParam()
                                    {
                                        WindTurbineID = editModbus.TurbineID,
                                        ModbusUnitID = editModbus.ModbusID,
                                        PortName = editModbus.PortName,
                                        BaudRate = editModbus.BaudRate,
                                        DataBit = editModbus.DataBit,
                                        StopBit = editModbus.StopBit,
                                        Parity = editModbus.Parity,
                                        ModbusDevType = existingModbusUnit.ModbusDevType,
                                        ModbusDeviceID = existingModbusUnit.ModbusDeviceID,
                                    });
                                }
                            }

                            ctx.SaveChanges();
                            transaction.Commit();

                            CMSFramework.Logger.Logger.LogInfoMessage($"[EditModbusDevice]成功编辑Modbus设备: TurbineID={editModbus.TurbineID}, ModbusID={editModbus.ModbusID}");
                            return Ok(ApiResponse<string>.Success("编辑成功"));
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            CMSFramework.Logger.Logger.LogErrorMessage("[EditModbusDevice]编辑Modbus设备失败", ex);
                            return Ok(ApiResponse<string>.Error($"编辑失败: {ex.Message}"));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditModbusDevice]编辑Modbus设备异常", ex);
                return Ok(ApiResponse<string>.Error($"操作异常: {ex.Message}"));
            }
        }

        /// <summary>
        /// 批量删除Modbus设备
        /// </summary>
        /// <param name="request">批量删除的设备信息</param>
        /// <returns></returns>
        [HttpPost("BatchDeleteModbusDevice")]
        [BatchOperation(nameof(BatchDeleteModbusDeviceFun))]
        public IActionResult BatchDeleteModbusDevice([FromBody] BatchDeleteModbusDeviceRequest request)
        {
            try
            {
                // 参数验证
                if (request?.SourceData == null || !request.SourceData.Any())
                {
                    return Ok(ApiResponse<string>.Error("删除设备列表不能为空"));
                }

                var batchDelete = request.SourceData;

                // 验证每个设备的必要参数
                foreach (var device in batchDelete)
                {
                    if (string.IsNullOrEmpty(device.TurbineID))
                    {
                        return Ok(ApiResponse<string>.Error("机组ID不能为空"));
                    }

                    // 判断是否设备被通道占用
                    ModbusChannel channel = TIMManagement.GetModbusChannelByID(device.TurbineID, device.ModbusDeviceID);
                    if (channel != null)
                    {
                        return Ok(ApiResponse<string>.Error("请先删除通道信息！"));
                    }

                    // 判断设备是否被测量定义占用
                    var measd = TIMManagement.GetModbusDefListBydeviceID(device.TurbineID, device.ModbusDeviceID);
                    if(measd!=null && measd.Count > 0)
                    {
                        return Ok(ApiResponse<string>.Error("请先删除绑定的测量定义！"));
                    }

                }

                int successCount = 0;
                int failCount = 0;
                List<string> errorMessages = new List<string>();

                // 使用事务批量删除，保证原子性
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            foreach (var device in batchDelete)
                            {
                                try
                                {
                                    // 查找要删除的Modbus设备
                                    var modbusUnit = ctx.ModbusUnits.FirstOrDefault(obj =>
                                        obj.ModbusDeviceID == device.ModbusDeviceID && obj.WindTurbineID == device.TurbineID);

                                    if (modbusUnit == null)
                                    {
                                        errorMessages.Add($"未找到设备: TurbineID={device.TurbineID}, ModbusID={device.ModbusDeviceID}");
                                        failCount++;
                                        continue;
                                    }

                                    // 删除相关的串口服务器配置
                                    var serialServers = ctx.SerialServers.Where(obj =>
                                        obj.ModbusDeviceID == device.ModbusDeviceID && obj.WindTurbineID == device.TurbineID).ToList();
                                    if (serialServers.Any())
                                    {
                                        ctx.SerialServers.RemoveRange(serialServers);
                                    }

                                    // 删除相关的串口配置
                                    var serialPorts = ctx.SerialPorts.Where(obj =>
                                        obj.ModbusDeviceID == device.ModbusDeviceID && obj.WindTurbineID == device.TurbineID).ToList();
                                    if (serialPorts.Any())
                                    {
                                        ctx.SerialPorts.RemoveRange(serialPorts);
                                    }

                                    //// 删除相关的SVM单元（如果存在）
                                    //using (CMSFramework.EF.DevContext devCtx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                                    //{
                                    //    var svmUnits = devCtx.SVMUnits.Where(obj =>
                                    //        obj.SVMID == device.ModbusID && obj.AssocWindTurbineID == device.TurbineID).ToList();
                                    //    if (svmUnits.Any())
                                    //    {
                                    //        devCtx.SVMUnits.RemoveRange(svmUnits);
                                    //        devCtx.SaveChanges();
                                    //    }
                                    //}

                                    //// 删除相关的TIM标定信息（如果存在）
                                    //var timCalibrations = ctx.TimCalibrations.Where(obj =>
                                    //    obj.TimUnitID == device.ModbusID && obj.WindTurbineID == device.TurbineID).ToList();
                                    //if (timCalibrations.Any())
                                    //{
                                    //    ctx.TimCalibrations.RemoveRange(timCalibrations);
                                    //}

                                    // 删除ModbusUnit
                                    ctx.ModbusUnits.Remove(modbusUnit);

                                    successCount++;
                                    CMSFramework.Logger.Logger.LogInfoMessage($"[BatchDeleteModbusDevice]成功删除Modbus设备: TurbineID={device.TurbineID}, ModbusID={device.ModbusDeviceID}");
                                }
                                catch (Exception ex)
                                {
                                    errorMessages.Add($"删除设备失败 TurbineID={device.TurbineID}, ModbusID={device.ModbusDeviceID}: {ex.Message}");
                                    failCount++;
                                    CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteModbusDevice]删除单个设备失败: TurbineID={device.TurbineID}, ModbusID={device.ModbusDeviceID}", ex);
                                }
                            }

                            // 如果有任何失败，回滚整个事务
                            if (failCount > 0)
                            {
                                transaction.Rollback();
                                string errorMsg = $"批量删除失败，成功: {successCount}，失败: {failCount}。错误详情: {string.Join("; ", errorMessages)}";
                                return Ok(ApiResponse<string>.Error(errorMsg));
                            }

                            ctx.SaveChanges();
                            transaction.Commit();

                            string successMsg = $"批量删除成功，共删除 {successCount} 个设备";
                            CMSFramework.Logger.Logger.LogInfoMessage($"[BatchDeleteModbusDevice]{successMsg}");
                            return Ok(ApiResponse<string>.Success(successMsg));
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            CMSFramework.Logger.Logger.LogErrorMessage("[BatchDeleteModbusDevice]批量删除Modbus设备失败", ex);
                            return Ok(ApiResponse<string>.Error($"批量删除失败: {ex.Message}"));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchDeleteModbusDevice]批量删除Modbus设备异常", ex);
                return Ok(ApiResponse<string>.Error($"操作异常: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取Modbus设备列表
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <returns></returns>
        [HttpGet("GetModbusDeviceList")]
        public IActionResult GetModbusDeviceList(string turbineID)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrEmpty(turbineID))
                {
                    return Ok(ApiResponse<List<ModbusDeviceListDTO>>.Error("机组ID不能为空"));
                }

                List<ModbusDeviceListDTO> result = new List<ModbusDeviceListDTO>();

                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    // 获取Modbus设备列表
                    var modbusUnits = ctx.ModbusUnits.Where(item => item.WindTurbineID == turbineID).ToList();

                    if (!modbusUnits.Any())
                    {
                        return Ok(result);
                    }

                    // 获取串口服务器配置
                    var serialServers = ctx.SerialServers.Where(item => item.WindTurbineID == turbineID).ToList();

                    // 获取串口配置
                    var serialPorts = ctx.SerialPorts.Where(item => item.WindTurbineID == turbineID).ToList();

                    foreach (var modbusUnit in modbusUnits)
                    {
                        var deviceDto = new ModbusDeviceListDTO
                        {
                            TurbineID = modbusUnit.WindTurbineID,
                            DauID = modbusUnit.DauID,
                            ModbusID = modbusUnit.ModbusUnitID,
                            ModbusType = ((int)modbusUnit.ModbusDevType).ToString(),
                            ModbusTypeName = modbusUnit.ModbusDevType.ToString(),
                            ComType = ((int)modbusUnit.CommunicationMode).ToString(),
                            ComTypeName = GetCommunicationModeName(modbusUnit.CommunicationMode),
                            ModbusDeviceID = modbusUnit.ModbusDeviceID,
                            ModbusDeviceName = modbusUnit.ModbusDeviceName,
                        };

                        // 根据通信类型获取相应的配置信息
                        if (modbusUnit.CommunicationMode == EnumCommunicationMode.BySerialServer)
                        {
                            var serialServer = serialServers.FirstOrDefault(s => s.ModbusUnitID == modbusUnit.ModbusUnitID);
                            if (serialServer != null)
                            {
                                deviceDto.ComIP = serialServer.SerialServerIP;
                                deviceDto.ComPort = serialServer.SerialServerPort;
                            }
                        }
                        else if (modbusUnit.CommunicationMode == EnumCommunicationMode.BySerialPort)
                        {
                            var serialPort = serialPorts.FirstOrDefault(s => s.ModbusUnitID == modbusUnit.ModbusUnitID);
                            if (serialPort != null)
                            {
                                deviceDto.PortName = serialPort.PortName;
                                deviceDto.BaudRate = serialPort.BaudRate;
                                deviceDto.DataBit = serialPort.DataBit;
                                deviceDto.Parity = serialPort.Parity;
                                deviceDto.StopBit = serialPort.StopBit;
                            }
                        }

                        result.Add(deviceDto);
                    }
                }

                CMSFramework.Logger.Logger.LogInfoMessage($"[GetModbusDeviceList]成功获取Modbus设备列表: TurbineID={turbineID}, 设备数量={result.Count}");
                return Ok(result);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetModbusDeviceList]获取Modbus设备列表异常", ex);
                return Ok(ApiResponse<List<ModbusDeviceListDTO>>.Error($"操作异常: {ex.Message}"));
            }
        }


    
        /// <summary>
        /// 获取通信模式名称
        /// </summary>
        /// <param name="communicationMode">通信模式枚举</param>
        /// <returns></returns>
        private string GetCommunicationModeName(EnumCommunicationMode communicationMode)
        {
            return communicationMode switch
            {
                EnumCommunicationMode.BySerialServer => "串口服务器",
                EnumCommunicationMode.BySerialPort => "串口",
                _ => "未知类型"
            };
        }

        [HttpPost]
        public string AddTIM()
        {
            string turbineID = Request.Form["turbineID"];
            string dauID = Request.Form["dauID"];
            string ModbusID = Request.Form["ModbusID"];
            string modbusType = Request.Form["modbusType"];
            
            string comIP = Request.Form["comIP"];

            string comType = Request.Form["comType"];
            int comPort = 0;
            if (!string.IsNullOrEmpty(Request.Form["comPort"]))
            {
                comPort = Convert.ToInt32(Request.Form["comPort"]);
            }
            //int timeSpan = Convert.ToInt32(Request.Form["timeSpan"]);

            string measLoc = Request.Form["measLoc"];
            string evList = Request.Form["evList"];
            string oilNum = Request.Form["oilNum"];

            string measdID = Request.Form["measdID"];
            //int timeLength = Convert.ToInt32(Request.Form["timeLength"]);
            //int freq = Convert.ToInt32(Request.Form["freq"]);

            string timSec = Request.Form["timsec"];
            string section = Request.Form["section"];
            double timeLength = 0;
            if (!string.IsNullOrEmpty(Request.Form["timeLength"]))
            {
                timeLength = Convert.ToDouble(Request.Form["timeLength"]);
            }

            float freq = 0;
            if (!string.IsNullOrEmpty(Request.Form["freq"]))
            {
                freq = (float)Convert.ToDecimal(Request.Form["freq"]);
            }

            //modbusID唯一判断
            string message = "state:{0},msg:'{1}'";
            List<ModbusUnit> modbuslist = TIMManagement.GetModbusunitList(turbineID);
            ModbusUnit modbusExit = modbuslist.FirstOrDefault(item => item.ModbusUnitID == ModbusID);
            if(modbusExit != null)
            {
                message = string.Format(message, 0, "添加失败,Modbus地址重复！");
                return "{" + message + "}";
            }

            TIMManagement.AddTimConf(turbineID, dauID, ModbusID, modbusType,comType, comIP, comPort, section, measdID, timeLength, freq, measLoc,oilNum,timSec,evList);

            return GetTimList(turbineID);
        }

        public string DelTim(string turbineID,string modbusID,string measdID,string modbusType)
        {
            TIMManagement.DelTimConf(turbineID,modbusID,measdID,modbusType);
            return GetTimList(turbineID);
        }

        //点击modbus设备添加按钮，查找是否有塔筒
        public string Showmodbus(string WindTurbineID) {
            List<WindTurbineComponent> turComList = new List<WindTurbineComponent>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                turComList = ctx.DevTurComponents.Where(item => item.WindTurbineID == WindTurbineID).ToList();
            }
            return turComList.ToJson();
        }

        public string GetSVMmeasloc(string turbineID,string compID,string sectionID)
        {
            compID = turbineID + "TOW";
            return SVMManagement.GetMeasLoc_SVMListByTurID(turbineID, compID, sectionID).ToJson();
        }


        /// <summary>
        /// 批量添加Modbus通道
        /// </summary>
        /// <param name="request">Modbus通道列表</param>
        /// <returns></returns>
        [HttpPost("AddModbusChannel")]
        [BatchOperation(nameof(BatchAddModbusChannel))]
        public IActionResult AddModbusChannel([FromBody] BatchAddModbusChannelRequest request)
        {
            try
            {
                // 参数验证
                if (request?.SourceData == null || !request.SourceData.Any())
                {
                    return Ok(ApiResponse<string>.Error("通道列表不能为空"));
                }

                var modbusChannels = request.SourceData;

                // 验证必填字段
                foreach (var channel in modbusChannels)
                {
                    if (string.IsNullOrEmpty(channel.WindTurbineID))
                    {
                        return Ok(ApiResponse<string>.Error("机组ID不能为空"));
                    }
                    if (string.IsNullOrEmpty(channel.ModbusDeviceID.ToString()))
                    {
                        return Ok(ApiResponse<string>.Error("Modbus设备ID不能为空"));
                    }
                    if (string.IsNullOrEmpty(channel.MeasLocationID))
                    {
                        return Ok(ApiResponse<string>.Error("测量位置ID不能为空"));
                    }
                }

                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            // 重复性检查
                            foreach (var channel in modbusChannels)
                            {
                                // 检查Modbus设备是否存在
                                var modbusUnit = ctx.ModbusUnits.FirstOrDefault(m =>
                                    m.WindTurbineID == channel.WindTurbineID &&
                                    m.ModbusDeviceID == channel.ModbusDeviceID);

                                if (modbusUnit == null)
                                {
                                    return Ok(ApiResponse<string>.Error($"Modbus设备不存在: {channel.ModbusDeviceID}"));
                                }

                                // 检查通道是否已存在
                                var existingChannel = ctx.ModbusChannelList.FirstOrDefault(c =>
                                    c.WindTurbineID == channel.WindTurbineID &&
                                    c.ChannelNumber == channel.ChannelNumber&&
                                    c.ModbusDeviceID == channel.ModbusDeviceID);

                                if (existingChannel != null)
                                {
                                    return Ok(ApiResponse<string>.Error($"通道{channel.ChannelNumber}已存在!"));
                                }
                            }

                            // 批量添加通道
                            foreach (var channel in modbusChannels)
                            {
                                var modbusUnit = ctx.ModbusUnits.FirstOrDefault(m =>
                                    m.WindTurbineID == channel.WindTurbineID &&
                                    m.ModbusDeviceID == channel.ModbusDeviceID);
                                var newChannel = new ModbusChannel
                                {
                                    WindTurbineID = channel.WindTurbineID,
                                    ChannelNumber = channel.ChannelNumber,
                                    MeasLocationID = channel.MeasLocationID,
                                    ModbusDeviceID = modbusUnit.ModbusDeviceID,
                                    //ModbusDevType = modbusUnit.ModbusDevType,
                                    //ModbusUnitID = modbusUnit.ModbusUnitID,
                                };

                                ctx.ModbusChannelList.Add(newChannel);
                            }

                            ctx.SaveChanges();
                            transaction.Commit();

                            CMSFramework.Logger.Logger.LogInfoMessage($"成功添加{modbusChannels.Count}个Modbus通道");
                            return Ok(ApiResponse<string>.Success($"Modbus通道添加成功"));
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            CMSFramework.Logger.Logger.LogErrorMessage("AddModbusChannel", ex);
                            return Ok(ApiResponse<string>.Error($"添加Modbus通道失败: {ex.Message}"));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("AddModbusChannel", ex);
                return Ok(ApiResponse<string>.Error($"系统错误: {ex.Message}"));
            }
        }

        /// <summary>
        /// 批量删除Modbus通道
        /// </summary>
        /// <param name="request">删除请求</param>
        /// <returns></returns>
        [HttpPost("BatchDeleteModbusChannel")]
        [BatchOperation(nameof(BatchDeleteModbusChannel))]
        public IActionResult BatchDeleteModbusChannel([FromBody] BatchDeleteModbusChannelRequest request)
        {
            try
            {
                // 参数验证
                if (request?.SourceData == null || !request.SourceData.Any())
                {
                    return Ok(ApiResponse<string>.Error("删除列表不能为空"));
                }

                var deleteRequest = request.SourceData;

                // 验证必填字段
                foreach (var channel in deleteRequest)
                {
                    if (string.IsNullOrEmpty(channel.WindTurbineID))
                    {
                        return Ok(ApiResponse<string>.Error("机组ID不能为空"));
                    }
                    if (string.IsNullOrEmpty(channel.ModbusDeviceID.ToString()))
                    {
                        return Ok(ApiResponse<string>.Error("Modbus设备ID不能为空"));
                    }

                    // 测量位置-波形是否占用
                    var wave = MeasDefinitionManagement.GetModbusWaveDefByTurId(channel.WindTurbineID,channel.ModbusDeviceID,channel.MeasLocationID);
                    if(wave!=null && wave.Count > 0)
                    {
                        return Ok(ApiResponse<string>.Error("请先删除波形定义！"));
                    }

                }

                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            int deletedCount = 0;

                            foreach (var channel in deleteRequest)
                            {
                                // 查找要删除的通道
                                var channelToDelete = ctx.ModbusChannelList.FirstOrDefault(c =>
                                    c.WindTurbineID == channel.WindTurbineID &&
                                    c.ChannelNumber == channel.ChannelNumber && c.ModbusDeviceID == channel.ModbusDeviceID);

                                if (channelToDelete != null)
                                {
                                    ctx.ModbusChannelList.Remove(channelToDelete);
                                    deletedCount++;
                                }
                            }

                            if (deletedCount == 0)
                            {
                                return Ok(ApiResponse<string>.Error("未找到要删除的通道"));
                            }

                            ctx.SaveChanges();
                            transaction.Commit();

                            CMSFramework.Logger.Logger.LogInfoMessage($"成功删除{deletedCount}个Modbus通道");
                            return Ok(ApiResponse<string>.Success($"成功删除{deletedCount}个Modbus通道"));
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            CMSFramework.Logger.Logger.LogErrorMessage("BatchDeleteModbusChannel", ex);
                            return Ok(ApiResponse<string>.Error($"删除Modbus通道失败: {ex.Message}"));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("BatchDeleteModbusChannel", ex);
                return Ok(ApiResponse<string>.Error($"系统错误: {ex.Message}"));
            }
        }

        /// <summary>
        /// 单条修改Modbus通道
        /// </summary>
        /// <param name="request">编辑请求</param>
        /// <returns></returns>
        [HttpPost("EditModbusChannel")]
        [BatchOperation(nameof(BatchEditModbusChannel))]
        public IActionResult EditModbusChannel([FromBody] BatchEditModbusChannelRequest request)
        {
            try
            {
                // 参数验证
                if (request?.SourceData == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                var editRequest = request.SourceData;
                if (string.IsNullOrEmpty(editRequest.WindTurbineID))
                {
                    return Ok(ApiResponse<string>.Error("机组ID不能为空"));
                }

                if (editRequest.ModbusDeviceID <= 0)
                {
                    return Ok(ApiResponse<string>.Error("Modbus设备ID不能为空"));
                }

                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            // 查找要编辑的通道
                            var channelToEdit = ctx.ModbusChannelList.FirstOrDefault(c =>
                                c.WindTurbineID == editRequest.WindTurbineID &&
                                c.ChannelNumber == editRequest.ChannelNumber && c.ModbusDeviceID == editRequest.ModbusDeviceID);

                            if (channelToEdit == null)
                            {
                                return Ok(ApiResponse<string>.Error("未找到要编辑的通道"));
                            }

                            // 更新字段
                            if (!string.IsNullOrEmpty(editRequest.MeasLocationID))
                            {
                                channelToEdit.MeasLocationID = editRequest.MeasLocationID;
                            }

                            if (!string.IsNullOrEmpty(editRequest.Description))
                            {
                                channelToEdit.Description = editRequest.Description;
                            }

                            ctx.ModbusChannelList.Attach(channelToEdit);
                            ctx.Entry(channelToEdit).State = EntityState.Modified;
                            ctx.SaveChanges();
                            transaction.Commit();

                            CMSFramework.Logger.Logger.LogInfoMessage($"成功编辑Modbus通道: 机组{editRequest.WindTurbineID}, 通道{editRequest.ChannelNumber}");
                            return Ok(ApiResponse<string>.Success("Modbus通道编辑成功"));
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            CMSFramework.Logger.Logger.LogErrorMessage("EditModbusChannel", ex);
                            return Ok(ApiResponse<string>.Error($"编辑Modbus通道失败: {ex.Message}"));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("EditModbusChannel", ex);
                return Ok(ApiResponse<string>.Error($"系统错误: {ex.Message}"));
            }
        }

        
        /// <summary>
        /// 查询Modbus通道列表
        /// </summary>
        /// <param name="turbineID">机组ID（可选）</param>
        /// <param name="modbusUnitID">Modbus设备ID（可选）</param>
        /// <returns></returns>
        [HttpGet("GetModbusChannelList")]
        public IActionResult GetModbusChannelList(string turbineID ,int? modbusDeviceID)
        {
            List<ModbusChannelListDTO> result = new List<ModbusChannelListDTO>();
            try
            {

                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    // 获取通道列表
                    var channels = ctx.ModbusChannelList.Where(t=>t.WindTurbineID == turbineID).ToList();

                    if(!string.IsNullOrEmpty(modbusDeviceID.ToString()))
                    {
                        channels = channels.Where(t=>t.ModbusDeviceID == modbusDeviceID).ToList();
                    }

                    if (!channels.Any())
                    {
                        return Ok(result);
                    }

                    // 获取Modbus设备信息用于关联
                    var modbusUnits = ctx.ModbusUnits.Where(t=>t.WindTurbineID == turbineID).ToList();

                    // 转换为DTO
                    foreach (var channel in channels)
                    {
                        // 查找对应的Modbus设备
                        var modbusUnit = modbusUnits.FirstOrDefault(m => m.ModbusDeviceID == channel.ModbusDeviceID);
                        var channelDto = new ModbusChannelListDTO
                        {
                            WindTurbineID = channel.WindTurbineID,
                            ModbusDeviceID = modbusUnit.ModbusDeviceID,
                            ChannelNumber = channel.ChannelNumber,
                            MeasLocationID = channel.MeasLocationID,
                            Description = channel.Description,
                            ModbusDevType = (int)modbusUnit.ModbusDevType,
                            ModbusDevTypeName = GetModbusDevTypeName(modbusUnit.ModbusDevType),
                            ModbusUnitID = modbusUnit.ModbusUnitID,
                            ModbusDeviceName = modbusUnit.ModbusDeviceName,
                        };

                        // 获取测量位置名称
                        using (CMSFramework.EF.DevContext devCtx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                        {
                            var measLoc = devCtx.DevMeasLocSVMs.FirstOrDefault(m => m.MeasLocationID == channel.MeasLocationID)?.MeasLocName;
                            if(measLoc == null)
                            {
                                measLoc = devCtx.MeasLoc_Modbus.FirstOrDefault(t => t.MeasLocationID == channel.MeasLocationID)?.MeasLocName;
                            }
                            channelDto.MeasLocationName = measLoc ?? "";
                        }

                        result.Add(channelDto);
                    }

                    return Ok(result);
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("GetModbusChannelList", ex);
                return Ok(result);
            }
        }

        /// <summary>
        /// 获取Modbus设备类型名称
        /// </summary>
        /// <param name="devType">设备类型枚举</param>
        /// <returns>设备类型名称</returns>
        private string GetModbusDevTypeName(EnumModbusDevType devType)
        {
            return CommonUtility.GetDecscription(devType);
        }



        /// <summary>
        /// 获取SVM和Modbus测量位置列表
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <returns></returns>
        [HttpGet("GetModbusMeasLocByDeviceID")]
        public IActionResult GetModbusMeasLocListByModbusDeviceID(string turbineID,int ModbusDeviceID)
        {
            try
            {
                if (string.IsNullOrEmpty(turbineID))
                {
                    return Ok(ApiResponse<List<UnifiedMeasLocDTO>>.Error("机组ID不能为空！"));
                }

                var result = new List<UnifiedMeasLocDTO>();
                var curmodbus = TIMManagement.GetModbusunitByID(turbineID, ModbusDeviceID);
                if(curmodbus == null)
                {
                    return Ok(ApiResponse<List<UnifiedMeasLocDTO>>.Error("Modbus设备不存在！"));
                }
                if(curmodbus.ModbusDevType == EnumModbusDevType.DynamicSVM || curmodbus.ModbusDevType == EnumModbusDevType.StaticTIM 
                    || curmodbus.ModbusDevType == EnumModbusDevType.StaticSVM)
                {
                    // 获取SVM测量位置
                    try
                    {

                        var svmList = SVMManagement.GetMeasLoc_SVMListByTurID(turbineID);
                        if (svmList != null)
                        {
                            foreach (var svm in svmList)
                            {
                                result.Add(new UnifiedMeasLocDTO
                                {
                                    MeasLocationID = svm.MeasLocationID,
                                    WindTurbineID = svm.WindTurbineID,
                                    MeasLocName = svm.MeasLocName,
                                    SectionName = svm.SectionName,
                                    Orientation = "",
                                    ComponentID = svm.ComponentID,
                                    MeasType = "svm",
                                    OrderSeq = 1,
                                    ComponentName = ""
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        CMSFramework.Logger.Logger.LogErrorMessage("[GetSVMMeasLocList]获取SVM测量位置失败", ex);
                    }
                }
                else
                {
                    // 获取Modbus测量位置（从MeasLoc_Vib表）
                    try
                    {
                        using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                        {
                            var modbusList = ctx.MeasLoc_Modbus
                                .Where(item => item.WindTurbineID == turbineID)
                                .ToList();

                            foreach (var modbus in modbusList)
                            {
                                result.Add(new UnifiedMeasLocDTO
                                {
                                    MeasLocationID = modbus.MeasLocationID,
                                    WindTurbineID = modbus.WindTurbineID,
                                    MeasLocName = modbus.MeasLocName,
                                    SectionName = modbus.SectionName,
                                    Orientation = modbus.Orientation,
                                    ComponentID = modbus.ComponentID,
                                    MeasType = "modbus",
                                    OrderSeq = modbus.OrderSeq,
                                    ComponentName = ""
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        CMSFramework.Logger.Logger.LogErrorMessage("[GetSVMMeasLocList]获取Modbus测量位置失败", ex);
                    }

                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetSVMMeasLocList]获取测量位置列表失败", ex);
                return Ok(new List<UnifiedMeasLocDTO>());
            }
        }

        #region 批量操作方法

        /// <summary>
        /// 批量添加Modbus设备到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddModbusDevice(string targetTurbineId, BatchAddModbusDeviceRequest request)
        {
            var result = new List<string>();

            try
            {
                var dau = DAUManager.GetDAUbyName(request.SourceData.TurbineID,request.SourceData.DauID,targetTurbineId);
                
                var sourceModbus = request.SourceData;

                // 创建新的Modbus设备，替换机组ID
                var newModbus = new ModbusDeviceDTO
                {
                    TurbineID = targetTurbineId,
                    DauID = dau?.DauID,
                    ModbusID = sourceModbus.ModbusID,
                    ModbusType = sourceModbus.ModbusType,
                    ComIP = sourceModbus.ComIP,
                    ComPort = sourceModbus.ComPort,
                    ComType = sourceModbus.ComType,
                    PortName = sourceModbus.PortName,
                    BaudRate = sourceModbus.BaudRate,
                    DataBit = sourceModbus.DataBit,
                    Parity = sourceModbus.Parity,
                    StopBit = sourceModbus.StopBit,
                    ModbusDeviceName = sourceModbus.ModbusDeviceName,
                };

                // 检查是否已存在相同的Modbus设备
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    var existingDevice = ctx.ModbusUnits.FirstOrDefault(m =>
                        m.WindTurbineID == targetTurbineId && m.ModbusUnitID == newModbus.ModbusID);

                    if (existingDevice != null)
                    {
                        result.Add($"Modbus设备 {newModbus.ModbusID} 已存在，跳过添加");
                        return result;
                    }

                    // 添加新设备
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            var modbusUnit = new ModbusUnit()
                            {
                                WindTurbineID = newModbus.TurbineID,
                                DauID = newModbus.DauID,
                                ModbusUnitID = newModbus.ModbusID,
                                CommunicationMode = (EnumCommunicationMode)Convert.ToInt32(newModbus.ComType),
                                ModbusDevType = (EnumModbusDevType)Convert.ToInt32(newModbus.ModbusType),
                                ModbusDeviceName = newModbus.ModbusDeviceName,
                            };

                            ctx.ModbusUnits.Add(modbusUnit);
                            ctx.SaveChanges();

                            // 添加串口服务器配置（如果需要）
                            if (!string.IsNullOrEmpty(newModbus.ComIP) && newModbus.ComPort.HasValue)
                            {
                                var serialServer = new SerialServer()
                                {
                                    WindTurbineID = newModbus.TurbineID,
                                    ModbusUnitID = newModbus.ModbusID,
                                    SerialServerIP = newModbus.ComIP,
                                    SerialServerPort = newModbus.ComPort.Value,
                                    ModbusDeviceID = modbusUnit.ModbusDeviceID,
                                    ModbusDevType = modbusUnit.ModbusDevType,
                                };
                                ctx.SerialServers.Add(serialServer);
                            }

                            // 添加串口配置（如果需要）
                            if (!string.IsNullOrEmpty(newModbus.PortName))
                            {
                                var serialPort = new SerialPortParam()
                                {
                                    WindTurbineID = newModbus.TurbineID,
                                    ModbusUnitID = newModbus.ModbusID,
                                    PortName = newModbus.PortName,
                                    BaudRate = newModbus.BaudRate ?? 9600,
                                    DataBit = newModbus.DataBit ?? 8,
                                    Parity = newModbus.Parity ?? 0,
                                    StopBit = newModbus.StopBit ?? 1,
                                    ModbusDevType = modbusUnit.ModbusDevType,
                                    ModbusDeviceID = modbusUnit.ModbusDeviceID
                                };
                                ctx.SerialPorts.Add(serialPort);
                            }

                            ctx.SaveChanges();
                            transaction.Commit();

                            result.Add($"成功添加Modbus设备 {newModbus.ModbusID}");
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            result.Add($"添加Modbus设备失败 - {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Add($"操作异常 - {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量编辑Modbus设备到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchEditModbusDevice(string targetTurbineId, BatchEditModbusDeviceRequest request)
        {
            var result = new List<string>();

            try
            {
                var sourceEdit = request.SourceData;

                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    // 在目标机组中查找对应的Modbus设备（通过ModbusUnitID匹配）
                    var targetDevice = ctx.ModbusUnits.FirstOrDefault(m =>
                        m.WindTurbineID == targetTurbineId && m.ModbusUnitID == sourceEdit.ModbusID);

                    if (targetDevice == null)
                    {
                        result.Add($"未找到Modbus设备 {sourceEdit.ModbusID}");
                        return result;
                    }

                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            // 更新设备信息
                            if (!string.IsNullOrEmpty(sourceEdit.DauID))
                                targetDevice.DauID = sourceEdit.DauID;
                            if (!string.IsNullOrEmpty(sourceEdit.ModbusType))
                                targetDevice.ModbusDevType = (EnumModbusDevType)Convert.ToInt32(sourceEdit.ModbusType);
                            if (!string.IsNullOrEmpty(sourceEdit.ComType))
                                targetDevice.CommunicationMode = (EnumCommunicationMode)Convert.ToInt32(sourceEdit.ComType);
                            if (!string.IsNullOrEmpty(sourceEdit.ModbusDeviceName))
                                targetDevice.ModbusDeviceName = sourceEdit.ModbusDeviceName.Replace(sourceEdit.TurbineID, targetTurbineId);

                            ctx.ModbusUnits.Update(targetDevice);
                            ctx.SaveChanges();
                            transaction.Commit();

                            result.Add($"成功编辑Modbus设备 {sourceEdit.ModbusID}");
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            result.Add($"编辑Modbus设备失败 - {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Add($"操作异常 - {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量删除Modbus设备到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteModbusDeviceFun(string targetTurbineId, BatchDeleteModbusDeviceRequest request)
        {
            var result = new List<string>();

            try
            {
                var sourceDevices = request.SourceData;

                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            foreach (var sourceDevice in sourceDevices)
                            {
                                // 在目标机组中查找对应的Modbus设备（通过ModbusDeviceID匹配）
                                var targetDevice = ctx.ModbusUnits.FirstOrDefault(m =>
                                    m.WindTurbineID == targetTurbineId && m.ModbusDeviceID == sourceDevice.ModbusDeviceID);

                                if (targetDevice == null)
                                {
                                    result.Add($"未找到Modbus设备 ID {sourceDevice.ModbusDeviceID}");
                                    continue;
                                }

                                // 删除相关的串口服务器配置
                                var serialServers = ctx.SerialServers.Where(s =>
                                    s.ModbusDeviceID == targetDevice.ModbusDeviceID && s.WindTurbineID == targetTurbineId).ToList();
                                if (serialServers.Any())
                                {
                                    ctx.SerialServers.RemoveRange(serialServers);
                                }

                                // 删除相关的串口配置
                                var serialPorts = ctx.SerialPorts.Where(p =>
                                    p.ModbusDeviceID == targetDevice.ModbusDeviceID && p.WindTurbineID == targetTurbineId).ToList();
                                if (serialPorts.Any())
                                {
                                    ctx.SerialPorts.RemoveRange(serialPorts);
                                }

                                // 删除Modbus设备
                                ctx.ModbusUnits.Remove(targetDevice);
                                result.Add($"成功删除Modbus设备 {targetDevice.ModbusUnitID}");
                            }

                            ctx.SaveChanges();
                            transaction.Commit();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            result.Add($"批量删除Modbus设备失败 - {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Add($"操作异常 - {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量添加Modbus通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddModbusChannel(string targetTurbineId, BatchAddModbusChannelRequest request)
        {
            var result = new List<string>();

            try
            {
                var sourceChannels = request.SourceData;

                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            foreach (var sourceChannel in sourceChannels)
                            {
                                // 查找当前的modbus设备
                                var curModbus = ctx.ModbusUnits.FirstOrDefault(t=>t.WindTurbineID == sourceChannel.WindTurbineID && t.ModbusDeviceID == sourceChannel.ModbusDeviceID);
                                // 在目标机组中查找对应的Modbus设备
                                var targetDevice = ctx.ModbusUnits.FirstOrDefault(m =>
                                    m.WindTurbineID == targetTurbineId && m.ModbusUnitID == curModbus.ModbusUnitID);

                                if (targetDevice == null)
                                {
                                    result.Add($"未找到Modbus设备 ID {curModbus.ModbusUnitID}");
                                    continue;
                                }

                                // 检查通道是否已存在
                                var existingChannel = ctx.ModbusChannelList.FirstOrDefault(c =>
                                    c.WindTurbineID == targetTurbineId &&
                                    c.ChannelNumber == sourceChannel.ChannelNumber &&
                                    c.ModbusDeviceID == targetDevice.ModbusDeviceID);

                                if (existingChannel != null)
                                {
                                    result.Add($"通道 {sourceChannel.ChannelNumber} 已存在，跳过添加");
                                    continue;
                                }

                                // 在目标机组中查找对应的测量位置（通过名称匹配）
                                string targetMeasLocationID = null;
                                using (CMSFramework.EF.DevContext devCtx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                                {
                                    // 先从源机组获取测量位置名称
                                    string sourceMeasLocName = null;
                                    var sourceMeasLocSVM = devCtx.DevMeasLocSVMs.FirstOrDefault(m => m.MeasLocationID == sourceChannel.MeasLocationID);
                                    if (sourceMeasLocSVM != null)
                                    {
                                        sourceMeasLocName = sourceMeasLocSVM.MeasLocName;
                                    }
                                    else
                                    {
                                        var sourceMeasLocModbus = devCtx.MeasLoc_Modbus.FirstOrDefault(m => m.MeasLocationID == sourceChannel.MeasLocationID);
                                        if (sourceMeasLocModbus != null)
                                        {
                                            sourceMeasLocName = sourceMeasLocModbus.MeasLocName;
                                        }
                                    }

                                    if (!string.IsNullOrEmpty(sourceMeasLocName))
                                    {
                                        // 在目标机组中查找同名的测量位置
                                        var targetMeasLocSVM = devCtx.DevMeasLocSVMs.FirstOrDefault(m =>
                                            m.WindTurbineID == targetTurbineId && m.MeasLocName == sourceMeasLocName);
                                        if (targetMeasLocSVM != null)
                                        {
                                            targetMeasLocationID = targetMeasLocSVM.MeasLocationID;
                                        }
                                        else
                                        {
                                            var targetMeasLocModbus = devCtx.MeasLoc_Modbus.FirstOrDefault(m =>
                                                m.WindTurbineID == targetTurbineId && m.MeasLocName == sourceMeasLocName);
                                            if (targetMeasLocModbus != null)
                                            {
                                                targetMeasLocationID = targetMeasLocModbus.MeasLocationID;
                                            }
                                        }
                                    }
                                }

                                if (string.IsNullOrEmpty(targetMeasLocationID))
                                {
                                    result.Add($"未找到对应的测量位置");
                                    continue;
                                }

                                // 创建新通道
                                var newChannel = new ModbusChannel
                                {
                                    WindTurbineID = targetTurbineId,
                                    ChannelNumber = sourceChannel.ChannelNumber,
                                    MeasLocationID = targetMeasLocationID,
                                    ModbusDeviceID = targetDevice.ModbusDeviceID,
                                    Description = sourceChannel.Description
                                };

                                ctx.ModbusChannelList.Add(newChannel);
                                result.Add($"成功添加Modbus通道 {sourceChannel.ChannelNumber}");
                            }

                            ctx.SaveChanges();
                            transaction.Commit();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            result.Add($"批量添加Modbus通道失败 - {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Add($"操作异常 - {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量删除Modbus通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteModbusChannel(string targetTurbineId, BatchDeleteModbusChannelRequest request)
        {
            var result = new List<string>();

            try
            {
                var sourceChannels = request.SourceData;

                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            foreach (var sourceChannel in sourceChannels)
                            {
                                // 在目标机组中查找对应的通道
                                var targetChannel = ctx.ModbusChannelList.FirstOrDefault(c =>
                                    c.WindTurbineID == targetTurbineId &&
                                    c.ChannelNumber == sourceChannel.ChannelNumber &&
                                    c.ModbusDeviceID == sourceChannel.ModbusDeviceID);

                                if (targetChannel != null)
                                {
                                    ctx.ModbusChannelList.Remove(targetChannel);
                                    result.Add($"成功删除Modbus通道 {sourceChannel.ChannelNumber}");
                                }
                                else
                                {
                                    result.Add($"未找到Modbus通道 {sourceChannel.ChannelNumber}");
                                }
                            }

                            ctx.SaveChanges();
                            transaction.Commit();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            result.Add($"批量删除Modbus通道失败 - {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Add($"操作异常 - {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量编辑Modbus通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchEditModbusChannel(string targetTurbineId, BatchEditModbusChannelRequest request)
        {
            var result = new List<string>();

            try
            {
                var sourceEdit = request.SourceData;

                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    // 在目标机组中查找对应的通道
                    var targetChannel = ctx.ModbusChannelList.FirstOrDefault(c =>
                        c.WindTurbineID == targetTurbineId &&
                        c.ChannelNumber == sourceEdit.ChannelNumber &&
                        c.ModbusDeviceID == sourceEdit.ModbusDeviceID);

                    if (targetChannel == null)
                    {
                        result.Add($"未找到Modbus通道 {sourceEdit.ChannelNumber}");
                        return result;
                    }

                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            // 在目标机组中查找对应的测量位置（如果需要更新）
                            if (!string.IsNullOrEmpty(sourceEdit.MeasLocationID))
                            {
                                string targetMeasLocationID = null;
                                using (CMSFramework.EF.DevContext devCtx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                                {
                                    // 先从源机组获取测量位置名称
                                    string sourceMeasLocName = null;
                                    var sourceMeasLocSVM = devCtx.DevMeasLocSVMs.FirstOrDefault(m => m.MeasLocationID == sourceEdit.MeasLocationID);
                                    if (sourceMeasLocSVM != null)
                                    {
                                        sourceMeasLocName = sourceMeasLocSVM.MeasLocName;
                                    }
                                    else
                                    {
                                        var sourceMeasLocModbus = devCtx.MeasLoc_Modbus.FirstOrDefault(m => m.MeasLocationID == sourceEdit.MeasLocationID);
                                        if (sourceMeasLocModbus != null)
                                        {
                                            sourceMeasLocName = sourceMeasLocModbus.MeasLocName;
                                        }
                                    }

                                    if (!string.IsNullOrEmpty(sourceMeasLocName))
                                    {
                                        // 在目标机组中查找同名的测量位置
                                        var targetMeasLocSVM = devCtx.DevMeasLocSVMs.FirstOrDefault(m =>
                                            m.WindTurbineID == targetTurbineId && m.MeasLocName == sourceMeasLocName);
                                        if (targetMeasLocSVM != null)
                                        {
                                            targetMeasLocationID = targetMeasLocSVM.MeasLocationID;
                                        }
                                        else
                                        {
                                            var targetMeasLocModbus = devCtx.MeasLoc_Modbus.FirstOrDefault(m =>
                                                m.WindTurbineID == targetTurbineId && m.MeasLocName == sourceMeasLocName);
                                            if (targetMeasLocModbus != null)
                                            {
                                                targetMeasLocationID = targetMeasLocModbus.MeasLocationID;
                                            }
                                        }
                                    }
                                }

                                if (!string.IsNullOrEmpty(targetMeasLocationID))
                                {
                                    targetChannel.MeasLocationID = targetMeasLocationID;
                                }
                            }

                            // 更新其他字段
                            if (!string.IsNullOrEmpty(sourceEdit.Description))
                            {
                                targetChannel.Description = sourceEdit.Description;
                            }

                            ctx.ModbusChannelList.Update(targetChannel);
                            ctx.SaveChanges();
                            transaction.Commit();

                            result.Add($"成功编辑Modbus通道 {sourceEdit.ChannelNumber}");
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            result.Add($"编辑Modbus通道失败 - {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Add($"操作异常 - {ex.Message}");
            }

            return result;
        }


        /// <summary>
        /// 获取串口名称
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetSerialName")]
        public IActionResult GetSerialName()
        {
            var portNames = System.IO.Ports.SerialPort.GetPortNames();
            return Ok(portNames);
        }
        #endregion

    }
}
