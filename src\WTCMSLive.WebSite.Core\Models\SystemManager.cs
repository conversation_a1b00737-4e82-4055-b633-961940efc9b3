﻿using WTCMSLive.BusinessModel;
using CMSFramework.BusinessEntity;

namespace WTCMSLive.WebSite.Models
{
    public class SystemManager
    {
        public BaseTableModel GetSysRunLogTable(DateTime _startTime, DateTime _endTime, EnumLogType _logType)
        {
            LogQueryCondition _logQC = new LogQueryCondition();
            _logQC.StartTime = _startTime;
            _logQC.EndTime = _endTime;
            _logQC.LogDB = (int)_logType;
            List<SystemRunningLog> logList=LogManagement.GetSystemRunningLogList(_logQC);
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "SystemRunningLog";
            List<Rows> rows = new List<Rows>();
            int logCount = 0;
            if (logList.Count > 1000)
            {
                logCount = 1000;
            }
            else {
                logCount = logList.Count;
            }
            for (int j = 0; j < logCount; j++)
            {
                Rows cells = new Rows();
                cells.cells = GetTurbineListTableCell(logList[j]);
                rows.Add(cells);
            }
            tableModel.rows = rows.ToArray();
            return tableModel;
        }

        private Cell[] GetTurbineListTableCell(SystemRunningLog systemRunningLog)
        {
            List<Cell> cellList = new List<Cell>();
           // Cell cell0 = new Cell();
            //cell0.displayValue = systemRunningLog.
            //时间
            Cell cell1 = new Cell();
            cell1.displayValue = systemRunningLog.EventTime.ToString("yyyy-MM-dd HH:mm:ss");
            cellList.Add(cell1);
            //操作者
            Cell cell2 = new Cell();
            cell2.displayValue = systemRunningLog.Operator;
            cellList.Add(cell2);
            //日志内容
            Cell cell3 = new Cell();
            cell3.displayValue = systemRunningLog.LogTitle + "：" + systemRunningLog.LogContent;
            cellList.Add(cell3);

            return cellList.ToArray();
        }
    }
}