﻿using Microsoft.AspNetCore.Mvc;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text;
using System.Text.Json;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.BusinessModel;
using CMSFramework.BusinessEntity;
using Microsoft.AspNetCore.Authorization;
using WTCMSLive.WebSite.Core.DTOs;

namespace WTCMSLive.WebSite.Core.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class NetTestController : ControllerBase
    {
        private readonly ILogger<NetTestController> _logger;

        public NetTestController(ILogger<NetTestController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 获取主控设备列表
        /// </summary>
        /// <param name="windParkId">风场ID</param>
        /// <returns>主控设备列表</returns>
        [HttpGet("GetMCSDeviceList")]
        public List<NetworkDeviceDTO> GetMCSDeviceList(string windParkId)
        {
            try
            {
                var deviceList = new List<NetworkDeviceDTO>();
                var turbineList = DevTreeManagement.GetTurbinesListByWindParkId(windParkId);
                var mcsList = DAUMCS.GetMCSByWindParkId(windParkId);

                foreach (var turbine in turbineList)
                {
                    var mcs = mcsList.Find(item => item.WindTurbineID == turbine.WindTurbineID);
                    if (mcs != null && !string.IsNullOrEmpty(mcs.MCSIP))
                    {
                        deviceList.Add(new NetworkDeviceDTO
                        {
                            DeviceId = turbine.WindTurbineID,
                            DeviceName = turbine.WindTurbineName,
                            DeviceType = "MCS",
                            IpAddress = mcs.MCSIP,
                            Port = mcs.MCSPort,
                            WindParkId = windParkId,
                            WindParkName = turbine.WindTurbineName,
                            // 预留字段
                            MacAddress = "0A:2B:4C:6D:8E:AF",
                            Gateway = "127.0.0.1",
                            SubnetMask = "127.0.0.1"
                        });
                    }
                }

                return deviceList.OrderBy(d => d.DeviceName).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetMCSDeviceList]获取主控设备列表失败: {WindParkId}", windParkId);
                return new List<NetworkDeviceDTO>();
            }
        }

        /// <summary>
        /// 获取DAU设备列表
        /// </summary>
        /// <param name="windParkId">风场ID</param>
        /// <returns>DAU设备列表</returns>
        [HttpGet("GetDAUDeviceList")]
        public List<NetworkDeviceDTO> GetDAUDeviceList(string windParkId)
        {
            try
            {
                var deviceList = new List<NetworkDeviceDTO>();
                var dauList = DauManagement.GetDAUListByWindParkID(windParkId);
                var turbineList = DevTreeManagement.GetTurbinesListByWindParkId(windParkId);

                foreach (var dau in dauList)
                {
                    if (!string.IsNullOrEmpty(dau.IP))
                    {
                        var turbine = turbineList.Find(t => t.WindTurbineID == dau.WindTurbineID);
                        deviceList.Add(new NetworkDeviceDTO
                        {
                            DeviceId = $"{dau.WindTurbineID}_{dau.DauID}",
                            DeviceName = $"{turbine?.WindTurbineName ?? dau.WindTurbineID}_{dau.DAUName}",
                            DeviceType = "DAU",
                            IpAddress = dau.IP,
                            Port = dau.Port,
                            WindParkId = windParkId,
                            WindParkName = turbine?.WindTurbineName ?? "",
                            // 预留字段
                            MacAddress = "0A:2B:4C:6D:8E:AF",
                            Gateway = "127.0.0.1",
                            SubnetMask = "127.0.0.1"
                        });
                    }
                }

                return deviceList.OrderBy(d => d.DeviceName).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetDAUDeviceList]获取DAU设备列表失败: {WindParkId}", windParkId);
                return new List<NetworkDeviceDTO>();
            }
        }

        /// <summary>
        /// 获取所有网络设备列表
        /// </summary>
        /// <param name="windParkId">风场ID</param>
        /// <returns>所有网络设备列表</returns>
        [HttpGet("GetAllDeviceList")]
        public List<NetworkDeviceDTO> GetAllDeviceList(string windParkId)
        {
            try
            {
                var allDevices = new List<NetworkDeviceDTO>();
                allDevices.AddRange(GetMCSDeviceList(windParkId));
                allDevices.AddRange(GetDAUDeviceList(windParkId));
                return allDevices.OrderBy(d => d.DeviceType).ThenBy(d => d.DeviceName).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetAllDeviceList]获取所有设备列表失败: {WindParkId}", windParkId);
                return new List<NetworkDeviceDTO>();
            }
        }

        /// <summary>
        /// 单个IP的Ping测试
        /// </summary>
        /// <param name="request">测试请求</param>
        /// <returns>Ping测试结果</returns>
        [HttpPost("SinglePingTest")]
        public IActionResult SinglePingTest([FromBody] SingleTestRequestDTO request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.IpAddress))
                {
                    return Ok(ApiResponse<PingTestResultDTO>.Error("IP地址不能为空"));
                }

                var result = PerformPingTest(request.IpAddress, "Manual", "Manual", "Manual");
                return Ok(ApiResponse<PingTestResultDTO>.Success(result, "Ping测试完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SinglePingTest]单个Ping测试失败: {IpAddress}", request?.IpAddress);
                return Ok(ApiResponse<PingTestResultDTO>.Error($"Ping测试失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 单个端口的Telnet测试
        /// </summary>
        /// <param name="request">测试请求</param>
        /// <returns>Telnet测试结果</returns>
        [HttpPost("SingleTelnetTest")]
        public IActionResult SingleTelnetTest([FromBody] SingleTestRequestDTO request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.IpAddress) || !request.Port.HasValue)
                {
                    return Ok(ApiResponse<TelnetTestResultDTO>.Error("IP地址和端口号不能为空"));
                }

                var result = PerformTelnetTest(request.IpAddress, request.Port.Value, "Manual", "Manual", "Manual");
                return Ok(ApiResponse<TelnetTestResultDTO>.Success(result, "Telnet测试完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SingleTelnetTest]单个Telnet测试失败: {IpAddress}:{Port}", request?.IpAddress, request?.Port);
                return Ok(ApiResponse<TelnetTestResultDTO>.Error($"Telnet测试失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 批量网络测试
        /// </summary>
        /// <param name="request">批量测试请求</param>
        /// <returns>批量测试结果</returns>
        [HttpPost("BatchNetworkTest")]
        public async Task<IActionResult> BatchNetworkTest([FromBody] BatchTestRequestDTO request)
        {
            try
            {
                if (request == null || request.Devices == null || !request.Devices.Any())
                {
                    return Ok(ApiResponse<BatchTestResultDTO>.Error("设备信息列表不能为空"));
                }

                var startTime = DateTime.Now;
                var result = new BatchTestResultDTO
                {
                    StartTime = startTime,
                    PingResults = new List<PingTestResultDTO>(),
                    TelnetResults = new List<TelnetTestResultDTO>()
                };

                // 过滤设备类型
                //var filteredDevices = request.Devices;
                //if (request.DeviceType != "ALL")
                //{
                //    filteredDevices = request.Devices.Where(d => d.DeviceType == request.DeviceType).ToList();
                //}

                // 验证设备信息
                var validDevices = new List<BatchTestDeviceDTO>();
                foreach (var device in request.Devices)
                {
                    if (!string.IsNullOrEmpty(device.IpAddress) && !string.IsNullOrEmpty(device.DeviceId))
                    {
                        validDevices.Add(device);
                    }
                    else
                    {
                        _logger.LogWarning("[BatchNetworkTest]跳过无效设备: {DeviceId}, IP: {IpAddress}",
                            device.DeviceId, device.IpAddress);
                    }
                }

                if (!validDevices.Any())
                {
                    return Ok(ApiResponse<BatchTestResultDTO>.Error("没有有效的设备信息"));
                }

                // 执行测试
                var tasks = new List<Task>();

                foreach (var device in validDevices)
                {
                    if (request.TestType == "ping" || request.TestType == "both")
                    {
                        tasks.Add(Task.Run(() =>
                        {
                            var pingResult = PerformPingTest(
                                device.IpAddress,
                                device.DeviceId,
                                device.DeviceName ?? device.DeviceId,
                                device.DeviceType ?? "Unknown"
                            );
                            lock (result.PingResults)
                            {
                                result.PingResults.Add(pingResult);
                            }
                        }));
                    }

                    if (request.TestType == "telnet" || request.TestType == "both")
                    {
                        tasks.Add(Task.Run(() =>
                        {
                            var telnetResult = PerformTelnetTest(
                                device.IpAddress,
                                device.Port > 0 ? device.Port : 502, // 默认端口502
                                device.DeviceId,
                                device.DeviceName ?? device.DeviceId,
                                device.DeviceType ?? "Unknown"
                            );
                            lock (result.TelnetResults)
                            {
                                result.TelnetResults.Add(telnetResult);
                            }
                        }));
                    }
                }

                await Task.WhenAll(tasks);

                // 计算统计信息
                result.EndTime = DateTime.Now;
                result.TotalDuration = (long)(result.EndTime - result.StartTime).TotalMilliseconds;
                result.TotalTests = result.PingResults.Count + result.TelnetResults.Count;
                result.SuccessCount = result.PingResults.Count(p => p.IsSuccess) + result.TelnetResults.Count(t => t.IsSuccess);
                result.FailureCount = result.TotalTests - result.SuccessCount;

                return Ok(ApiResponse<BatchTestResultDTO>.Success(result, "批量测试完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[BatchNetworkTest]批量网络测试失败");
                return Ok(ApiResponse<BatchTestResultDTO>.Error($"批量测试失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 导出测试结果为CSV
        /// </summary>
        /// <param name="request">导出请求</param>
        /// <returns>CSV文件</returns>
        [HttpPost("ExportTestResultToCsv")]
        public IActionResult ExportTestResultToCsv([FromBody] ExportCsvRequestDTO request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.TestResultData))
                {
                    return BadRequest("测试结果数据不能为空");
                }

                var result = JsonSerializer.Deserialize<BatchTestResultDTO>(request.TestResultData);
                if (result == null)
                {
                    return BadRequest("无效的测试结果数据");
                }

                var csvContent = new StringBuilder();
                var fileName = string.IsNullOrEmpty(request.FileName)
                    ? $"网络测试结果_{DateTime.Now:yyyyMMddHHmmss}.csv"
                    : request.FileName;

                // 添加CSV标题行
                if (request.ExportType == "ping" || request.ExportType == "both")
                {
                    csvContent.AppendLine("设备ID,设备名称,设备类型,IP地址,测试结果,平均延迟(ms),最小延迟(ms),最大延迟(ms),丢包率(%),发送包数,接收包数,错误信息,测试时间");

                    // 添加Ping测试数据
                    foreach (var pingResult in result.PingResults)
                    {
                        csvContent.AppendLine($"{pingResult.DeviceId},{pingResult.DeviceName},{pingResult.DeviceType},{pingResult.IpAddress}," +
                            $"{(pingResult.IsSuccess ? "成功" : "失败")},{pingResult.AverageLatency},{pingResult.MinLatency},{pingResult.MaxLatency}," +
                            $"{pingResult.PacketLoss},{pingResult.PacketsSent},{pingResult.PacketsReceived}," +
                            $"\"{pingResult.ErrorMessage}\",{pingResult.TestTime:yyyy-MM-dd HH:mm:ss}");
                    }

                    if (request.ExportType == "both")
                    {
                        csvContent.AppendLine();
                        csvContent.AppendLine();
                    }
                }

                if (request.ExportType == "telnet" || request.ExportType == "both")
                {
                    csvContent.AppendLine("设备ID,设备名称,设备类型,IP地址,端口,测试结果,连接延迟(ms),错误信息,测试时间");

                    // 添加Telnet测试数据
                    foreach (var telnetResult in result.TelnetResults)
                    {
                        csvContent.AppendLine($"{telnetResult.DeviceId},{telnetResult.DeviceName},{telnetResult.DeviceType},{telnetResult.IpAddress}," +
                            $"{telnetResult.Port},{(telnetResult.IsSuccess ? "成功" : "失败")},{telnetResult.ConnectionLatency}," +
                            $"\"{telnetResult.ErrorMessage}\",{telnetResult.TestTime:yyyy-MM-dd HH:mm:ss}");
                    }
                }

                // 添加统计信息
                csvContent.AppendLine();
                csvContent.AppendLine($"测试总数,{result.TotalTests}");
                csvContent.AppendLine($"成功数量,{result.SuccessCount}");
                csvContent.AppendLine($"失败数量,{result.FailureCount}");
                csvContent.AppendLine($"测试开始时间,{result.StartTime:yyyy-MM-dd HH:mm:ss}");
                csvContent.AppendLine($"测试结束时间,{result.EndTime:yyyy-MM-dd HH:mm:ss}");
                csvContent.AppendLine($"总耗时(ms),{result.TotalDuration}");

                // 设置响应头，使浏览器下载文件
                var bytes = Encoding.UTF8.GetBytes(csvContent.ToString());
                return File(bytes, "text/csv", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ExportTestResultToCsv]导出测试结果失败");
                return BadRequest($"导出失败: {ex.Message}");
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 执行Ping测试
        /// </summary>
        /// <param name="ipAddress">IP地址</param>
        /// <param name="deviceId">设备ID</param>
        /// <param name="deviceName">设备名称</param>
        /// <param name="deviceType">设备类型</param>
        /// <returns>Ping测试结果</returns>
        private PingTestResultDTO PerformPingTest(string ipAddress, string deviceId, string deviceName, string deviceType)
        {
            var result = new PingTestResultDTO
            {
                DeviceId = deviceId,
                DeviceName = deviceName,
                DeviceType = deviceType,
                IpAddress = ipAddress,
                TestTime = DateTime.Now,
                PacketsSent = 4,
                PacketsReceived = 0
            };

            try
            {
                using (var ping = new Ping())
                {
                    var latencies = new List<long>();
                    var timeout = 5000; // 5秒超时

                    for (int i = 0; i < result.PacketsSent; i++)
                    {
                        var reply = ping.Send(ipAddress, timeout);
                        if (reply.Status == IPStatus.Success)
                        {
                            result.PacketsReceived++;
                            latencies.Add(reply.RoundtripTime);
                        }

                        // 避免发送过快
                        if (i < result.PacketsSent - 1)
                        {
                            Thread.Sleep(100);
                        }
                    }

                    if (latencies.Any())
                    {
                        result.IsSuccess = true;
                        result.AverageLatency = (long)latencies.Average();
                        result.MinLatency = latencies.Min();
                        result.MaxLatency = latencies.Max();
                        result.PacketLoss = ((double)(result.PacketsSent - result.PacketsReceived) / result.PacketsSent) * 100;
                    }
                    else
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = "所有ping包都超时或失败";
                        result.PacketLoss = 100;
                    }
                }
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
                result.PacketLoss = 100;
                _logger.LogWarning(ex, "[PerformPingTest]Ping测试异常: {IpAddress}", ipAddress);
            }

            return result;
        }

        /// <summary>
        /// 执行Telnet测试
        /// </summary>
        /// <param name="ipAddress">IP地址</param>
        /// <param name="port">端口号</param>
        /// <param name="deviceId">设备ID</param>
        /// <param name="deviceName">设备名称</param>
        /// <param name="deviceType">设备类型</param>
        /// <returns>Telnet测试结果</returns>
        private TelnetTestResultDTO PerformTelnetTest(string ipAddress, int port, string deviceId, string deviceName, string deviceType)
        {
            var result = new TelnetTestResultDTO
            {
                DeviceId = deviceId,
                DeviceName = deviceName,
                DeviceType = deviceType,
                IpAddress = ipAddress,
                Port = port,
                TestTime = DateTime.Now
            };

            try
            {
                var startTime = DateTime.Now;
                using (var tcpClient = new TcpClient())
                {
                    var connectTask = tcpClient.ConnectAsync(ipAddress, port);
                    var timeout = TimeSpan.FromSeconds(10); // 10秒超时

                    if (connectTask.Wait(timeout))
                    {
                        result.IsSuccess = tcpClient.Connected;
                        result.ConnectionLatency = (long)(DateTime.Now - startTime).TotalMilliseconds;

                        if (!result.IsSuccess)
                        {
                            result.ErrorMessage = "连接失败";
                        }
                    }
                    else
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = "连接超时";
                        result.ConnectionLatency = (long)timeout.TotalMilliseconds;
                    }
                }
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
                result.ConnectionLatency = 0;
                _logger.LogWarning(ex, "[PerformTelnetTest]Telnet测试异常: {IpAddress}:{Port}", ipAddress, port);
            }

            return result;
        }

        /// <summary>
        /// 根据设备ID获取设备信息
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>设备信息</returns>
        private async Task<NetworkDeviceDTO> GetDeviceByIdAsync(string deviceId)
        {
            try
            {
                // 检查是否为DAU设备ID（格式：turbineId_dauId）
                if (deviceId.Contains("_"))
                {
                    var parts = deviceId.Split('_');
                    if (parts.Length == 2)
                    {
                        var turbineId = parts[0];
                        var dauId = parts[1];

                        var dauList = await Task.Run(() => DauManagement.GetDAUListByTurbineID(turbineId));
                        var dau = dauList?.FirstOrDefault(d => d.DauID == dauId);
                        if (dau != null)
                        {
                            var turbine = await Task.Run(() => DevTreeManagement.GetAllWindTurbine(turbineId));
                            return new NetworkDeviceDTO
                            {
                                DeviceId = deviceId,
                                DeviceName = $"{turbine?.WindTurbineName ?? turbineId}_{dau.DAUName}",
                                DeviceType = "DAU",
                                IpAddress = dau.IP,
                                Port = dau.Port,
                                WindParkId = dau.WindParkID,
                                WindParkName = turbine?.WindTurbineName ?? "",
                                MacAddress = "",
                                Gateway = "",
                                SubnetMask = ""
                            };
                        }
                    }
                }
                else
                {
                    // 尝试作为主控设备ID查找
                    var mcs = await Task.Run(() => DAUMCS.GetMCSByTurbineId(deviceId));
                    if (mcs != null)
                    {
                        var turbine = await Task.Run(() => DevTreeManagement.GetAllWindTurbine(deviceId));
                        return new NetworkDeviceDTO
                        {
                            DeviceId = deviceId,
                            DeviceName = turbine?.WindTurbineName ?? deviceId,
                            DeviceType = "MCS",
                            IpAddress = mcs.MCSIP,
                            Port = mcs.MCSPort,
                            WindParkId = turbine?.WindParkID ?? "",
                            WindParkName = turbine?.WindTurbineName ?? "",
                            MacAddress = "",
                            Gateway = "",
                            SubnetMask = ""
                        };
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetDeviceByIdAsync]获取设备信息失败: {DeviceId}", deviceId);
                return null;
            }
        }

        #endregion
    }
}
