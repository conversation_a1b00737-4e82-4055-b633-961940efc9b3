﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using AppFramework.Utility;
using CMSFramework.BusinessEntity;
using Microsoft.EntityFrameworkCore;

namespace WTCMSLive.BusinessModel
{
    public class WaveDefinitionManagement
    {
        /// <summary>
        /// 波形定义，删除
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_waveDefID"></param>
        public static void DeleteSVMWaveDef(WaveDef_SVM SVMWave)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ctx.SVMWaveDefinitions.Attach(SVMWave);
                ctx.Entry(SVMWave).State = EntityState.Deleted;
                ctx.SaveChanges();
            }
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                ctx.EVData_SVMs.RemoveRange(ctx.EVData_SVMs.Where(item => item.WindTurbineID == SVMWave.WindTurbineID && item.WaveDefinitionID == SVMWave.WaveDefinitionID));
                ctx.SaveChanges();
            }
            DauManagement.UpdateMeasDefVersion(SVMWave.WindTurbineID);
        }

        /// <summary>
        /// 通过测量定义ID获取晃度波形定义
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_measDefId"></param>
        /// <returns></returns>
        public static List<WaveDef_SVM> GetSVMWaveDefListByMdfId(string _turID, string _measDefId)
        {
            List<WaveDef_SVM> myList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                myList = ctx.SVMWaveDefinitions.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId).ToList();
            }
            if (myList.Count > 0)
            {
                List<WaveDef_SVM> myOrderList = new List<WaveDef_SVM>();
                foreach (EnumSVMParamType data in Enum.GetValues(typeof(EnumSVMParamType)))
                { 
                    WaveDef_SVM svm =  myList.Find(item=>item.WaveDefinitionName == AppFramework.Utility.EnumHelper.GetDescription(data));
                    if (svm != null)
                        myOrderList.Add(svm);
                }
                myList = myOrderList;
            }
            return myList;
        }

        /// <summary>
        /// 判断机组是否存在晃度波形定义
        /// </summary>
        /// <param name="_turID"></param>
        /// <returns></returns>
        public static bool IsExitSVMWaveDef(string _turID)
        {
            List<WaveDef_SVM> myList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                myList = ctx.SVMWaveDefinitions.Where(item => item.WindTurbineID == _turID).ToList();
            }
            return myList.Count > 0;
        }

        /// <summary>
        /// 根据ID获取加速度波形实体
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_wdfId"></param>
        /// <returns></returns>
        public static WaveDef_SVM GetSVMWaveDefByID(string _turID, string _wdfId)
        {
            WaveDef_SVM waveDef_SVM = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                waveDef_SVM = ctx.SVMWaveDefinitions.Where(item => item.WindTurbineID == _turID && item.WaveDefinitionID == _wdfId).FirstOrDefault();
            }
            return waveDef_SVM;
        }

        /// <summary>
        /// 查找是否存在角度波形定义
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_measId"></param>
        /// <param name="_locId"></param>
        /// <returns></returns>
        public static bool IsExistSVMWaveDef(string _turID, string _measId, string _locId)
        {
            WaveDef_SVM waveDef_SVM = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                waveDef_SVM = ctx.SVMWaveDefinitions.Where(item => item.WindTurbineID == _turID 
                                                            && item.MeasDefinitionID == _measId
                                                            && item.MeasLocationID == _locId).FirstOrDefault();
            }
            return waveDef_SVM != null;
        }
    }
}
