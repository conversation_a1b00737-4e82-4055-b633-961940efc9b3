using Microsoft.AspNetCore.Mvc.Filters;
using WTCMSLive.WebSite.Core.Attributes;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 批量操作服务接口
    /// </summary>
    public interface IBatchOperationService
    {
        /// <summary>
        /// 执行批量操作
        /// </summary>
        /// <param name="context">Action执行上下文</param>
        /// <param name="attribute">批量操作特性</param>
        /// <returns></returns>
        Task ExecuteBatchAsync(ActionExecutedContext context, BatchOperationAttribute attribute);
    }
}
