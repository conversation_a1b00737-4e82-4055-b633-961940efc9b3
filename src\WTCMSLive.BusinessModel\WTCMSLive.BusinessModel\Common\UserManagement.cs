﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using CMSFramework.BusinessEntity;
using System.Security.Cryptography;
using Microsoft.EntityFrameworkCore;

namespace WTCMSLive.BusinessModel
{
    public static class UserManagement
    {
        /// <summary>
        /// 删除用户
        /// </summary>
        /// <param name="_userID"></param>
        public static void DeleteUser(string _userID)
        {
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                ctx.SysUsers.Remove(ctx.SysUsers.Find(_userID));
                ctx.SaveChanges();
            }
        }
        
        /// <summary>
        /// 获取用户实体
        /// </summary>
        /// <param name="_userID"></param>
        /// <returns></returns>
        public static User GetUserById(string _userID)
        {
            User user = null;
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                user = ctx.SysUsers.Find(_userID);
                if (user != null)
                { 
                    var rolomapping = ctx.SysUserRoleMapping.FirstOrDefault(item=>item.UserID==_userID);
                    if (rolomapping!=null)
                        user.UserRole = ctx.SysRoles.FirstOrDefault(item => item.RoleID == rolomapping.RoleID);
                }
            }
            return user;
        }

        /// <summary>
        /// 获取用户列表
        /// </summary>
        /// <returns></returns>
        public static List<User> GetUserList()
        {
            List<User> list = new List<User>();
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                list = ctx.SysUsers.ToList();
                var rolemappingList = ctx.SysUserRoleMapping.ToList();
                var userroleList = ctx.SysRoles.ToList();
                list.ForEach(item => {
                   var roleMapping = rolemappingList.Find(map=>map.UserID==item.UserID);
                   if (roleMapping != null)
                       item.UserRole = userroleList.Find(role => role.RoleID == roleMapping.RoleID);
                });
            }
            return list;
        }
        
        /// <summary>
        /// 添加用户
        /// </summary>
        /// <param name="_user"></param>
        /// <param name="_roleId"></param>
        public static void AddUser(User _user, string _roleId)
        {
            // 用户状态默认正常
            _user.UserState = ConstDefine.USER_STATE_ENABLE;
            _user.PassWord = GetMd5Hash(_user.PassWord);
            UserRoleMapping map = new UserRoleMapping();
            map.UserID = _user.UserID;
            map.RoleID = _roleId;
            try
            {
                using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
                {
                    ctx.SysUsers.Add(_user);
                    ctx.SysUserRoleMapping.Add(map);
                    ctx.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                
            }

        }

        /// <summary>
        ///  修改用户
        /// </summary>
        /// <param name="_user"></param>
        /// <param name="_roleId"></param>
        public static void EditUser(User _user)
        {
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                ctx.SysUsers.Attach(_user);
                ctx.Entry(_user).State = EntityState.Modified;
                if (_user.UserRole != null && !string.IsNullOrEmpty(_user.UserRole.RoleID))
                {
                    var role = ctx.SysUserRoleMapping.FirstOrDefault(item => item.UserID == _user.UserID);
                    if (role.RoleID != _user.UserRole.RoleID)
                    {
                        //角色编辑后，需要删除老角色，然后添加新角色，RoleID是主键，所以EF不让修改
                        ctx.SysUserRoleMapping.Remove(role);
                        ctx.SysUserRoleMapping.Add(new UserRoleMapping()
                        {
                            UserID = _user.UserID,
                            RoleID = _user.UserRole.RoleID
                        });
                    }
                }
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 修改用户密码
        /// </summary>
        /// <param name="_userId"></param>
        /// <param name="_password"></param>
        public static void EditUserPassword(string _userId, string _password)
        {
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                User _user = ctx.SysUsers.Find(_userId);
                if (_user != null)
                {
                    _user.PassWord = GetMd5Hash(_password);
                    ctx.SysUsers.Attach(_user);
                    ctx.Entry(_user).State = EntityState.Modified;
                    ctx.SaveChanges();
                }
            }
        }

        /// <summary>
        /// 获取角色列表
        /// </summary>
        /// <returns></returns>
        public static List<Role> GetRoleList()
        {
            List<Role> list = new List<Role>();
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                list = ctx.SysRoles.ToList();
            }
            return list;
        }

        /// <summary>
        /// 是否存在UserId
        /// </summary>
        /// <param name="_userId"></param>
        /// <returns></returns>
        public static bool IsExistUserId(string _userId)
        {
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                User _user = ctx.SysUsers.Find(_userId);
                return _user != null;
            }
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="_userId"></param>
        /// <param name="_passWord"></param>
        /// <returns></returns>
        public static User Login(string _userId, string _passWord)
        {
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                User _user = ctx.SysUsers.Find(_userId);
                if (_user!=null && _user.PassWord == GetMd5Hash(_passWord))
                {
                    return _user;
                }
                return null;
            }
        }

        /// <summary>
        /// 添加角色
        /// </summary>
        /// <param name="_role"></param>
        /// <returns></returns>
        public static void AddRole(Role _role)
        {
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                ctx.SysRoles.Add(_role);
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 修改角色
        /// </summary>
        /// <param name="_role"></param>
        /// <returns></returns>
        public static void UpdateRole(Role _role)
        {
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                ctx.SysRoles.Attach(_role);
                ctx.Entry(_role).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 删除角色
        /// </summary>
        /// <param name="_roleId"></param>
        /// <returns></returns>
        public static void DeleteRole(string _roleId)
        {
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                ctx.SysRoles.Remove(ctx.SysRoles.Find(_roleId));
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 获取权限列表
        /// </summary>
        /// <returns></returns>
        public static List<Function> GetAllFunctions()
        {
            List<Function> list = new List<Function>();
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                list = ctx.SysFunctions.ToList();
            }
            return list;
        }
        
        /// <summary>
        /// 添加权限
        /// </summary>
        /// <param name="_function"></param>
        /// <returns></returns>
        public static void AddFunction(Function _function)
        {
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                ctx.SysFunctions.Add(_function);
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 删除权限
        /// </summary>
        /// <param name="_functionId"></param>
        /// <returns></returns>
        public static void DeleteFunction(string _functionId)
        {
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                ctx.SysFunctions.Remove(ctx.SysFunctions.Find(_functionId));
                ctx.SaveChanges();
            }
        }

        public static string GetMd5Hash(String str)
        {
            if (string.IsNullOrEmpty(str))
            {
                return null;
            }

            MD5 md5Hash = MD5.Create();

            // 将输入字符串转换为字节数组并计算哈希数据 
            byte[] data = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(str));

            // 创建一个 Stringbuilder 来收集字节并创建字符串 
            StringBuilder sBuilder = new StringBuilder();

            // 循环遍历哈希数据的每一个字节并格式化为十六进制字符串 
            for (int i = 0; i < data.Length; i++)
            {
                sBuilder.Append(data[i].ToString("x2"));
            }

            // 返回十六进制字符串 
            return sBuilder.ToString();
        }
    }
}
