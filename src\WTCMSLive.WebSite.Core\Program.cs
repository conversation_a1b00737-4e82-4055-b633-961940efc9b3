using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using RealtimePerf.Hubs;
using System.Text;
using System.Text.Json;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core.Services;
using WTCMSLive.WebSite.Core.Middleware;
using ProtoBuf.Meta;

namespace WTCMSLive.WebSite.Core
{
    public class Program
    {
        public static void Main(string[] args)
        {

            var builder = WebApplication.CreateBuilder(args);
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
            // Add services to the container.
            builder.Services.AddControllers(options =>
            {
                // 添加批量操作中间件
                options.Filters.Add<ActionContextMiddleware>();
            })
               .AddJsonOptions(options =>
               {
                   options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                   // 把 "" 当成 null
                   options.JsonSerializerOptions.NumberHandling =
                       System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString;
               });


            // Configure JWT Authentication
            var jwtSettings = builder.Configuration.GetSection("JwtSettings").Get<JwtSettings>();
            var key = Encoding.ASCII.GetBytes(jwtSettings.SecretKey);

            builder.Services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false;
                options.SaveToken = true;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidIssuer = jwtSettings.Issuer,
                    ValidAudience = jwtSettings.Audience,
                    ClockSkew = TimeSpan.Zero
                };
            });

            builder.Services.AddCors(o => o.AddPolicy("any", p =>
            p.WithOrigins("http://127.0.0.1:5500",
                               "http://127.0.0.1:803")
             .AllowAnyMethod()
             .AllowAnyHeader()
             .AllowCredentials()));

            builder.Services.AddSingleton(builder.Configuration);

            builder.Services.AddSignalR();

            // 注册性能数据存储服务
            builder.Services.AddSingleton<IMetricsStorageService, MetricsStorageService>();

            // 注册服务管理服务
            builder.Services.AddSingleton<IServiceManagementService, ServiceManagementService>();

            // 注册服务日志服务
            builder.Services.AddSingleton<IServiceLogService, ServiceLogService>();

            // 注册批量操作服务
            builder.Services.AddScoped<IBatchOperationService, BatchOperationService>();

            // 注册数据中心服务
            builder.Services.AddHttpClient<IDataCenterService, DataCenterService>();

            // HostedService
            builder.Services.AddSingleton<MetricsCollectorService>();
            builder.Services.AddHostedService(sp => sp.GetRequiredService<MetricsCollectorService>());

            // 注册DAU配置队列服务
            builder.Services.AddSingleton<IDAUConfigQueueService, DAUConfigQueueService>();

            // 注册DAU配置处理后台服务
            builder.Services.AddSingleton<DAUConfigProcessorService>();
            builder.Services.AddHostedService(sp => sp.GetRequiredService<DAUConfigProcessorService>());

            CMSFramework.BusinessEntity.ConfigInfo.SetConfiguration(builder.Services.BuildServiceProvider().GetRequiredService<IConfiguration>());

            var app = builder.Build();



            app.UseCors("any");

            app.UseHttpsRedirection();
            app.UseStaticFiles();
            app.UseAuthentication();

            app.UseRouting();

            app.UseAuthorization();
            app.MapControllers();
            app.MapHub<ServerPerformanceHub>("/Hubs/ServerPerformanceHub");
            // Vue files
            app.MapFallbackToFile("/index.html");

            app.Run();
        }
    }
}