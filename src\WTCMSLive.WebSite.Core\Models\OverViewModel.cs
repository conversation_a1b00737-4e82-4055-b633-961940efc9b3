﻿namespace WTCMSLive.WebSite.Models
{
    /// <summary>
    /// 总览页面表现使用model
    /// </summary>
    public class OverViewModel
    {
        public string name { get; set; }
        public string id { get; set; }
        public string description{ get; set; }
        public List<DataModel> DataList { get; set; }
        public List<FiterModel> FiterList { get; set; }
    }
    /// <summary>
    /// 状态
    /// </summary>
    public class DataModel
    {
        public string name { get; set; }
        public string id { get; set; }
        public string level { get; set; }
        public string des { get; set; }
        public string type { get; set; }
    }
    /// <summary>
    /// 过滤条件
    /// </summary>
    public class FiterModel
    {
        public string name { get; set; }
        public string className {get;set;}
        public int count { get; set; }
        public string id { get; set; }
    }
}