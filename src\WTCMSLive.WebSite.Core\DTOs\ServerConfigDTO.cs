using System.ComponentModel.DataAnnotations;

namespace WTCMSLive.WebSite.Core.DTOs
{
    /// <summary>
    /// 配置文件信息DTO
    /// </summary>
    public class ConfigFileInfoDTO
    {
        /// <summary>
        /// 文件名（包含扩展名）
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件完整路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 文件扩展名
        /// </summary>
        public string Extension { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModified { get; set; }
    }

    /// <summary>
    /// 配置文件内容DTO
    /// </summary>
    public class ConfigFileContentDTO
    {
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 文件编码
        /// </summary>
        public string Encoding { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModified { get; set; }
    }

    /// <summary>
    /// 保存配置文件请求DTO
    /// </summary>
    public class SaveConfigFileRequestDTO
    {
        /// <summary>
        /// 文件名
        /// </summary>
        [Required(ErrorMessage = "文件名不能为空")]
        public string FileName { get; set; }

        /// <summary>
        /// 文件内容
        /// </summary>
        [Required(ErrorMessage = "文件内容不能为空")]
        public string Content { get; set; }

        /// <summary>
        /// 文件编码（可选，默认UTF-8）
        /// </summary>
        public string Encoding { get; set; } = "UTF-8";
    }

    /// <summary>
    /// 上传配置文件响应DTO
    /// </summary>
    public class UploadConfigFileResponseDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 是否为替换操作
        /// </summary>
        public bool IsReplaced { get; set; }
    }
}
